// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct CollectionConfig : IFlatbufferConfig<CollectionConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CollectionConfig GetRootAsCollectionConfig(ByteBuffer _bb) { return GetRootAsCollectionConfig(_bb, new CollectionConfig()); }
  public static CollectionConfig GetRootAsCollectionConfig(ByteBuffer _bb, CollectionConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CollectionConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int TokensPerWildCard { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTokensPerWildCard(int tokens_per_wild_card) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, tokens_per_wild_card); return true; } else { return false; } }
  public FBConfig.DictIntInt? TokenByRarity(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictIntInt?)(new FBConfig.DictIntInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TokenByRarityLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int RareProbability { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRareProbability(int rare_probability) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, rare_probability); return true; } else { return false; } }
  public int FirstCardsNumber { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateFirstCardsNumber(int first_cards_number) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, first_cards_number); return true; } else { return false; } }

  public static Offset<FBConfig.CollectionConfig> CreateCollectionConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int tokens_per_wild_card = 0,
      VectorOffset token_by_rarityOffset = default(VectorOffset),
      int rare_probability = 0,
      int first_cards_number = 0) {
    builder.StartTable(5);
    CollectionConfig.AddFirstCardsNumber(builder, first_cards_number);
    CollectionConfig.AddRareProbability(builder, rare_probability);
    CollectionConfig.AddTokenByRarity(builder, token_by_rarityOffset);
    CollectionConfig.AddTokensPerWildCard(builder, tokens_per_wild_card);
    CollectionConfig.AddUid(builder, uidOffset);
    return CollectionConfig.EndCollectionConfig(builder);
  }

  public static void StartCollectionConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTokensPerWildCard(FlatBufferBuilder builder, int tokensPerWildCard) { builder.AddInt(1, tokensPerWildCard, 0); }
  public static void AddTokenByRarity(FlatBufferBuilder builder, VectorOffset tokenByRarityOffset) { builder.AddOffset(2, tokenByRarityOffset.Value, 0); }
  public static VectorOffset CreateTokenByRarityVector(FlatBufferBuilder builder, Offset<FBConfig.DictIntInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTokenByRarityVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictIntInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTokenByRarityVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictIntInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTokenByRarityVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictIntInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTokenByRarityVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddRareProbability(FlatBufferBuilder builder, int rareProbability) { builder.AddInt(3, rareProbability, 0); }
  public static void AddFirstCardsNumber(FlatBufferBuilder builder, int firstCardsNumber) { builder.AddInt(4, firstCardsNumber, 0); }
  public static Offset<FBConfig.CollectionConfig> EndCollectionConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.CollectionConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfCollectionConfig(FlatBufferBuilder builder, Offset<CollectionConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<CollectionConfig> o1, Offset<CollectionConfig> o2) =>
        new CollectionConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new CollectionConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static CollectionConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    CollectionConfig obj_ = new CollectionConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public CollectionConfigT UnPack() {
    var _o = new CollectionConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CollectionConfigT _o) {
    _o.Uid = this.Uid;
    _o.TokensPerWildCard = this.TokensPerWildCard;
    _o.TokenByRarity = new List<FBConfig.DictIntIntT>();
    for (var _j = 0; _j < this.TokenByRarityLength; ++_j) {_o.TokenByRarity.Add(this.TokenByRarity(_j).HasValue ? this.TokenByRarity(_j).Value.UnPack() : null);}
    _o.RareProbability = this.RareProbability;
    _o.FirstCardsNumber = this.FirstCardsNumber;
  }
  public static Offset<FBConfig.CollectionConfig> Pack(FlatBufferBuilder builder, CollectionConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.CollectionConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _token_by_rarity = default(VectorOffset);
    if (_o.TokenByRarity != null) {
      var __token_by_rarity = new Offset<FBConfig.DictIntInt>[_o.TokenByRarity.Count];
      for (var _j = 0; _j < __token_by_rarity.Length; ++_j) { __token_by_rarity[_j] = FBConfig.DictIntInt.Pack(builder, _o.TokenByRarity[_j]); }
      _token_by_rarity = CreateTokenByRarityVector(builder, __token_by_rarity);
    }
    return CreateCollectionConfig(
      builder,
      _uid,
      _o.TokensPerWildCard,
      _token_by_rarity,
      _o.RareProbability,
      _o.FirstCardsNumber);
  }
}

public class CollectionConfigT
{
  public string Uid { get; set; }
  public int TokensPerWildCard { get; set; }
  public List<FBConfig.DictIntIntT> TokenByRarity { get; set; }
  public int RareProbability { get; set; }
  public int FirstCardsNumber { get; set; }

  public CollectionConfigT() {
    this.Uid = null;
    this.TokensPerWildCard = 0;
    this.TokenByRarity = null;
    this.RareProbability = 0;
    this.FirstCardsNumber = 0;
  }
}

public struct CollectionConfigDict : IFlatbufferConfigDict<CollectionConfig, CollectionConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CollectionConfigDict GetRootAsCollectionConfigDict(ByteBuffer _bb) { return GetRootAsCollectionConfigDict(_bb, new CollectionConfigDict()); }
  public static CollectionConfigDict GetRootAsCollectionConfigDict(ByteBuffer _bb, CollectionConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CollectionConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.CollectionConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.CollectionConfig?)(new FBConfig.CollectionConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.CollectionConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.CollectionConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.CollectionConfigDict> CreateCollectionConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    CollectionConfigDict.AddValues(builder, valuesOffset);
    return CollectionConfigDict.EndCollectionConfigDict(builder);
  }

  public static void StartCollectionConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.CollectionConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.CollectionConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.CollectionConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.CollectionConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.CollectionConfigDict> EndCollectionConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CollectionConfigDict>(o);
  }
  public static void FinishCollectionConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CollectionConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedCollectionConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CollectionConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public CollectionConfigDictT UnPack() {
    var _o = new CollectionConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CollectionConfigDictT _o) {
    _o.Values = new List<FBConfig.CollectionConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.CollectionConfigDict> Pack(FlatBufferBuilder builder, CollectionConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.CollectionConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.CollectionConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.CollectionConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateCollectionConfigDict(
      builder,
      _values);
  }
}

public class CollectionConfigDictT
{
  public List<FBConfig.CollectionConfigT> Values { get; set; }

  public CollectionConfigDictT() {
    this.Values = null;
  }
  public static CollectionConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return CollectionConfigDict.GetRootAsCollectionConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    CollectionConfigDict.FinishCollectionConfigDictBuffer(fbb, CollectionConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
