// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct HelpingHandsConfig : IFlatbufferConfig<HelpingHandsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HelpingHandsConfig GetRootAsHelpingHandsConfig(ByteBuffer _bb) { return GetRootAsHelpingHandsConfig(_bb, new HelpingHandsConfig()); }
  public static HelpingHandsConfig GetRootAsHelpingHandsConfig(ByteBuffer _bb, HelpingHandsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HelpingHandsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string SpriteName { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSpriteNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetSpriteNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetSpriteNameArray() { return __p.__vector_as_array<byte>(6); }
  public string NameTextId { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameTextIdBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetNameTextIdBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetNameTextIdArray() { return __p.__vector_as_array<byte>(8); }
  public string DescTextId { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescTextIdBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetDescTextIdBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetDescTextIdArray() { return __p.__vector_as_array<byte>(10); }
  public string DetailsTextId { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDetailsTextIdBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetDetailsTextIdBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetDetailsTextIdArray() { return __p.__vector_as_array<byte>(12); }
  public string DropItemOverrideSpriteName { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDropItemOverrideSpriteNameBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetDropItemOverrideSpriteNameBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetDropItemOverrideSpriteNameArray() { return __p.__vector_as_array<byte>(14); }
  public string ImageUrl { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetImageUrlBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetImageUrlBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetImageUrlArray() { return __p.__vector_as_array<byte>(16); }
  public string FactoidMessage { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFactoidMessageBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetFactoidMessageBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetFactoidMessageArray() { return __p.__vector_as_array<byte>(18); }
  public string GrandpaMessage { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGrandpaMessageBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetGrandpaMessageBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetGrandpaMessageArray() { return __p.__vector_as_array<byte>(20); }
  public string FennecImageUrl { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFennecImageUrlBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetFennecImageUrlBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetFennecImageUrlArray() { return __p.__vector_as_array<byte>(22); }
  public string HashTags { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHashTagsBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetHashTagsBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetHashTagsArray() { return __p.__vector_as_array<byte>(24); }

  public static Offset<FBConfig.HelpingHandsConfig> CreateHelpingHandsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset sprite_nameOffset = default(StringOffset),
      StringOffset name_text_idOffset = default(StringOffset),
      StringOffset desc_text_idOffset = default(StringOffset),
      StringOffset details_text_idOffset = default(StringOffset),
      StringOffset drop_item_override_sprite_nameOffset = default(StringOffset),
      StringOffset image_urlOffset = default(StringOffset),
      StringOffset factoid_messageOffset = default(StringOffset),
      StringOffset grandpa_messageOffset = default(StringOffset),
      StringOffset fennec_image_urlOffset = default(StringOffset),
      StringOffset hash_tagsOffset = default(StringOffset)) {
    builder.StartTable(11);
    HelpingHandsConfig.AddHashTags(builder, hash_tagsOffset);
    HelpingHandsConfig.AddFennecImageUrl(builder, fennec_image_urlOffset);
    HelpingHandsConfig.AddGrandpaMessage(builder, grandpa_messageOffset);
    HelpingHandsConfig.AddFactoidMessage(builder, factoid_messageOffset);
    HelpingHandsConfig.AddImageUrl(builder, image_urlOffset);
    HelpingHandsConfig.AddDropItemOverrideSpriteName(builder, drop_item_override_sprite_nameOffset);
    HelpingHandsConfig.AddDetailsTextId(builder, details_text_idOffset);
    HelpingHandsConfig.AddDescTextId(builder, desc_text_idOffset);
    HelpingHandsConfig.AddNameTextId(builder, name_text_idOffset);
    HelpingHandsConfig.AddSpriteName(builder, sprite_nameOffset);
    HelpingHandsConfig.AddUid(builder, uidOffset);
    return HelpingHandsConfig.EndHelpingHandsConfig(builder);
  }

  public static void StartHelpingHandsConfig(FlatBufferBuilder builder) { builder.StartTable(11); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSpriteName(FlatBufferBuilder builder, StringOffset spriteNameOffset) { builder.AddOffset(1, spriteNameOffset.Value, 0); }
  public static void AddNameTextId(FlatBufferBuilder builder, StringOffset nameTextIdOffset) { builder.AddOffset(2, nameTextIdOffset.Value, 0); }
  public static void AddDescTextId(FlatBufferBuilder builder, StringOffset descTextIdOffset) { builder.AddOffset(3, descTextIdOffset.Value, 0); }
  public static void AddDetailsTextId(FlatBufferBuilder builder, StringOffset detailsTextIdOffset) { builder.AddOffset(4, detailsTextIdOffset.Value, 0); }
  public static void AddDropItemOverrideSpriteName(FlatBufferBuilder builder, StringOffset dropItemOverrideSpriteNameOffset) { builder.AddOffset(5, dropItemOverrideSpriteNameOffset.Value, 0); }
  public static void AddImageUrl(FlatBufferBuilder builder, StringOffset imageUrlOffset) { builder.AddOffset(6, imageUrlOffset.Value, 0); }
  public static void AddFactoidMessage(FlatBufferBuilder builder, StringOffset factoidMessageOffset) { builder.AddOffset(7, factoidMessageOffset.Value, 0); }
  public static void AddGrandpaMessage(FlatBufferBuilder builder, StringOffset grandpaMessageOffset) { builder.AddOffset(8, grandpaMessageOffset.Value, 0); }
  public static void AddFennecImageUrl(FlatBufferBuilder builder, StringOffset fennecImageUrlOffset) { builder.AddOffset(9, fennecImageUrlOffset.Value, 0); }
  public static void AddHashTags(FlatBufferBuilder builder, StringOffset hashTagsOffset) { builder.AddOffset(10, hashTagsOffset.Value, 0); }
  public static Offset<FBConfig.HelpingHandsConfig> EndHelpingHandsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.HelpingHandsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfHelpingHandsConfig(FlatBufferBuilder builder, Offset<HelpingHandsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<HelpingHandsConfig> o1, Offset<HelpingHandsConfig> o2) =>
        new HelpingHandsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new HelpingHandsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static HelpingHandsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    HelpingHandsConfig obj_ = new HelpingHandsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public HelpingHandsConfigT UnPack() {
    var _o = new HelpingHandsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HelpingHandsConfigT _o) {
    _o.Uid = this.Uid;
    _o.SpriteName = this.SpriteName;
    _o.NameTextId = this.NameTextId;
    _o.DescTextId = this.DescTextId;
    _o.DetailsTextId = this.DetailsTextId;
    _o.DropItemOverrideSpriteName = this.DropItemOverrideSpriteName;
    _o.ImageUrl = this.ImageUrl;
    _o.FactoidMessage = this.FactoidMessage;
    _o.GrandpaMessage = this.GrandpaMessage;
    _o.FennecImageUrl = this.FennecImageUrl;
    _o.HashTags = this.HashTags;
  }
  public static Offset<FBConfig.HelpingHandsConfig> Pack(FlatBufferBuilder builder, HelpingHandsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.HelpingHandsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _sprite_name = _o.SpriteName == null ? default(StringOffset) : builder.CreateString(_o.SpriteName);
    var _name_text_id = _o.NameTextId == null ? default(StringOffset) : builder.CreateString(_o.NameTextId);
    var _desc_text_id = _o.DescTextId == null ? default(StringOffset) : builder.CreateString(_o.DescTextId);
    var _details_text_id = _o.DetailsTextId == null ? default(StringOffset) : builder.CreateString(_o.DetailsTextId);
    var _drop_item_override_sprite_name = _o.DropItemOverrideSpriteName == null ? default(StringOffset) : builder.CreateString(_o.DropItemOverrideSpriteName);
    var _image_url = _o.ImageUrl == null ? default(StringOffset) : builder.CreateString(_o.ImageUrl);
    var _factoid_message = _o.FactoidMessage == null ? default(StringOffset) : builder.CreateString(_o.FactoidMessage);
    var _grandpa_message = _o.GrandpaMessage == null ? default(StringOffset) : builder.CreateString(_o.GrandpaMessage);
    var _fennec_image_url = _o.FennecImageUrl == null ? default(StringOffset) : builder.CreateString(_o.FennecImageUrl);
    var _hash_tags = _o.HashTags == null ? default(StringOffset) : builder.CreateString(_o.HashTags);
    return CreateHelpingHandsConfig(
      builder,
      _uid,
      _sprite_name,
      _name_text_id,
      _desc_text_id,
      _details_text_id,
      _drop_item_override_sprite_name,
      _image_url,
      _factoid_message,
      _grandpa_message,
      _fennec_image_url,
      _hash_tags);
  }
}

public class HelpingHandsConfigT
{
  public string Uid { get; set; }
  public string SpriteName { get; set; }
  public string NameTextId { get; set; }
  public string DescTextId { get; set; }
  public string DetailsTextId { get; set; }
  public string DropItemOverrideSpriteName { get; set; }
  public string ImageUrl { get; set; }
  public string FactoidMessage { get; set; }
  public string GrandpaMessage { get; set; }
  public string FennecImageUrl { get; set; }
  public string HashTags { get; set; }

  public HelpingHandsConfigT() {
    this.Uid = null;
    this.SpriteName = null;
    this.NameTextId = null;
    this.DescTextId = null;
    this.DetailsTextId = null;
    this.DropItemOverrideSpriteName = null;
    this.ImageUrl = null;
    this.FactoidMessage = null;
    this.GrandpaMessage = null;
    this.FennecImageUrl = null;
    this.HashTags = null;
  }
}

public struct HelpingHandsConfigDict : IFlatbufferConfigDict<HelpingHandsConfig, HelpingHandsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HelpingHandsConfigDict GetRootAsHelpingHandsConfigDict(ByteBuffer _bb) { return GetRootAsHelpingHandsConfigDict(_bb, new HelpingHandsConfigDict()); }
  public static HelpingHandsConfigDict GetRootAsHelpingHandsConfigDict(ByteBuffer _bb, HelpingHandsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HelpingHandsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.HelpingHandsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.HelpingHandsConfig?)(new FBConfig.HelpingHandsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.HelpingHandsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.HelpingHandsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.HelpingHandsConfigDict> CreateHelpingHandsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    HelpingHandsConfigDict.AddValues(builder, valuesOffset);
    return HelpingHandsConfigDict.EndHelpingHandsConfigDict(builder);
  }

  public static void StartHelpingHandsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.HelpingHandsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.HelpingHandsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.HelpingHandsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.HelpingHandsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.HelpingHandsConfigDict> EndHelpingHandsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.HelpingHandsConfigDict>(o);
  }
  public static void FinishHelpingHandsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HelpingHandsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedHelpingHandsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HelpingHandsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public HelpingHandsConfigDictT UnPack() {
    var _o = new HelpingHandsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HelpingHandsConfigDictT _o) {
    _o.Values = new List<FBConfig.HelpingHandsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.HelpingHandsConfigDict> Pack(FlatBufferBuilder builder, HelpingHandsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.HelpingHandsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.HelpingHandsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.HelpingHandsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateHelpingHandsConfigDict(
      builder,
      _values);
  }
}

public class HelpingHandsConfigDictT
{
  public List<FBConfig.HelpingHandsConfigT> Values { get; set; }

  public HelpingHandsConfigDictT() {
    this.Values = null;
  }
  public static HelpingHandsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return HelpingHandsConfigDict.GetRootAsHelpingHandsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    HelpingHandsConfigDict.FinishHelpingHandsConfigDictBuffer(fbb, HelpingHandsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
