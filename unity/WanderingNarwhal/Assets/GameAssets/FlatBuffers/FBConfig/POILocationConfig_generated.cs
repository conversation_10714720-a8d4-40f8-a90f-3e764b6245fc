// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct POILocationConfig : IFlatbufferConfig<POILocationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POILocationConfig GetRootAsPOILocationConfig(ByteBuffer _bb) { return GetRootAsPOILocationConfig(_bb, new POILocationConfig()); }
  public static POILocationConfig GetRootAsPOILocationConfig(ByteBuffer _bb, POILocationConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POILocationConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string Country { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCountryBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetCountryBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetCountryArray() { return __p.__vector_as_array<byte>(8); }
  public string TextUid { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTextUidBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetTextUidBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetTextUidArray() { return __p.__vector_as_array<byte>(10); }
  public string CountryName { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCountryNameBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetCountryNameBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetCountryNameArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.POILocationConfig> CreatePOILocationConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset countryOffset = default(StringOffset),
      StringOffset text_uidOffset = default(StringOffset),
      StringOffset country_nameOffset = default(StringOffset)) {
    builder.StartTable(5);
    POILocationConfig.AddCountryName(builder, country_nameOffset);
    POILocationConfig.AddTextUid(builder, text_uidOffset);
    POILocationConfig.AddCountry(builder, countryOffset);
    POILocationConfig.AddName(builder, nameOffset);
    POILocationConfig.AddUid(builder, uidOffset);
    return POILocationConfig.EndPOILocationConfig(builder);
  }

  public static void StartPOILocationConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddCountry(FlatBufferBuilder builder, StringOffset countryOffset) { builder.AddOffset(2, countryOffset.Value, 0); }
  public static void AddTextUid(FlatBufferBuilder builder, StringOffset textUidOffset) { builder.AddOffset(3, textUidOffset.Value, 0); }
  public static void AddCountryName(FlatBufferBuilder builder, StringOffset countryNameOffset) { builder.AddOffset(4, countryNameOffset.Value, 0); }
  public static Offset<FBConfig.POILocationConfig> EndPOILocationConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.POILocationConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfPOILocationConfig(FlatBufferBuilder builder, Offset<POILocationConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<POILocationConfig> o1, Offset<POILocationConfig> o2) =>
        new POILocationConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new POILocationConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static POILocationConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    POILocationConfig obj_ = new POILocationConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public POILocationConfigT UnPack() {
    var _o = new POILocationConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POILocationConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Country = this.Country;
    _o.TextUid = this.TextUid;
    _o.CountryName = this.CountryName;
  }
  public static Offset<FBConfig.POILocationConfig> Pack(FlatBufferBuilder builder, POILocationConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.POILocationConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _country = _o.Country == null ? default(StringOffset) : builder.CreateString(_o.Country);
    var _text_uid = _o.TextUid == null ? default(StringOffset) : builder.CreateString(_o.TextUid);
    var _country_name = _o.CountryName == null ? default(StringOffset) : builder.CreateString(_o.CountryName);
    return CreatePOILocationConfig(
      builder,
      _uid,
      _name,
      _country,
      _text_uid,
      _country_name);
  }
}

public class POILocationConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string Country { get; set; }
  public string TextUid { get; set; }
  public string CountryName { get; set; }

  public POILocationConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Country = null;
    this.TextUid = null;
    this.CountryName = null;
  }
}

public struct POILocationConfigDict : IFlatbufferConfigDict<POILocationConfig, POILocationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POILocationConfigDict GetRootAsPOILocationConfigDict(ByteBuffer _bb) { return GetRootAsPOILocationConfigDict(_bb, new POILocationConfigDict()); }
  public static POILocationConfigDict GetRootAsPOILocationConfigDict(ByteBuffer _bb, POILocationConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POILocationConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.POILocationConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.POILocationConfig?)(new FBConfig.POILocationConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.POILocationConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.POILocationConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.POILocationConfigDict> CreatePOILocationConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    POILocationConfigDict.AddValues(builder, valuesOffset);
    return POILocationConfigDict.EndPOILocationConfigDict(builder);
  }

  public static void StartPOILocationConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.POILocationConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.POILocationConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.POILocationConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.POILocationConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.POILocationConfigDict> EndPOILocationConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.POILocationConfigDict>(o);
  }
  public static void FinishPOILocationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POILocationConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedPOILocationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POILocationConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public POILocationConfigDictT UnPack() {
    var _o = new POILocationConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POILocationConfigDictT _o) {
    _o.Values = new List<FBConfig.POILocationConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.POILocationConfigDict> Pack(FlatBufferBuilder builder, POILocationConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.POILocationConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.POILocationConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.POILocationConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreatePOILocationConfigDict(
      builder,
      _values);
  }
}

public class POILocationConfigDictT
{
  public List<FBConfig.POILocationConfigT> Values { get; set; }

  public POILocationConfigDictT() {
    this.Values = null;
  }
  public static POILocationConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return POILocationConfigDict.GetRootAsPOILocationConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    POILocationConfigDict.FinishPOILocationConfigDictBuffer(fbb, POILocationConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
