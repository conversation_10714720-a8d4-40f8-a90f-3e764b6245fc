// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct EndlessTreasureConfigItem : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static EndlessTreasureConfigItem GetRootAsEndlessTreasureConfigItem(ByteBuffer _bb) { return GetRootAsEndlessTreasureConfigItem(_bb, new EndlessTreasureConfigItem()); }
  public static EndlessTreasureConfigItem GetRootAsEndlessTreasureConfigItem(ByteBuffer _bb, EndlessTreasureConfigItem obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public EndlessTreasureConfigItem __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int SortOrder { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSortOrder(int sort_order) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, sort_order); return true; } else { return false; } }
  public string Reward { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRewardBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetRewardBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetRewardArray() { return __p.__vector_as_array<byte>(8); }
  public string StoreItemUid { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetStoreItemUidBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetStoreItemUidBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetStoreItemUidArray() { return __p.__vector_as_array<byte>(10); }
  public string Visual { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetVisualBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetVisualBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetVisualArray() { return __p.__vector_as_array<byte>(12); }
  public int Discount { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDiscount(int discount) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, discount); return true; } else { return false; } }

  public static Offset<FBConfig.EndlessTreasureConfigItem> CreateEndlessTreasureConfigItem(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int sort_order = 0,
      StringOffset rewardOffset = default(StringOffset),
      StringOffset store_item_uidOffset = default(StringOffset),
      StringOffset visualOffset = default(StringOffset),
      int discount = 0) {
    builder.StartTable(6);
    EndlessTreasureConfigItem.AddDiscount(builder, discount);
    EndlessTreasureConfigItem.AddVisual(builder, visualOffset);
    EndlessTreasureConfigItem.AddStoreItemUid(builder, store_item_uidOffset);
    EndlessTreasureConfigItem.AddReward(builder, rewardOffset);
    EndlessTreasureConfigItem.AddSortOrder(builder, sort_order);
    EndlessTreasureConfigItem.AddUid(builder, uidOffset);
    return EndlessTreasureConfigItem.EndEndlessTreasureConfigItem(builder);
  }

  public static void StartEndlessTreasureConfigItem(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSortOrder(FlatBufferBuilder builder, int sortOrder) { builder.AddInt(1, sortOrder, 0); }
  public static void AddReward(FlatBufferBuilder builder, StringOffset rewardOffset) { builder.AddOffset(2, rewardOffset.Value, 0); }
  public static void AddStoreItemUid(FlatBufferBuilder builder, StringOffset storeItemUidOffset) { builder.AddOffset(3, storeItemUidOffset.Value, 0); }
  public static void AddVisual(FlatBufferBuilder builder, StringOffset visualOffset) { builder.AddOffset(4, visualOffset.Value, 0); }
  public static void AddDiscount(FlatBufferBuilder builder, int discount) { builder.AddInt(5, discount, 0); }
  public static Offset<FBConfig.EndlessTreasureConfigItem> EndEndlessTreasureConfigItem(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.EndlessTreasureConfigItem>(o);
  }

  public static VectorOffset CreateSortedVectorOfEndlessTreasureConfigItem(FlatBufferBuilder builder, Offset<EndlessTreasureConfigItem>[] offsets) {
    Array.Sort(offsets,
      (Offset<EndlessTreasureConfigItem> o1, Offset<EndlessTreasureConfigItem> o2) =>
        new EndlessTreasureConfigItem().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new EndlessTreasureConfigItem().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static EndlessTreasureConfigItem? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    EndlessTreasureConfigItem obj_ = new EndlessTreasureConfigItem();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public EndlessTreasureConfigItemT UnPack() {
    var _o = new EndlessTreasureConfigItemT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(EndlessTreasureConfigItemT _o) {
    _o.Uid = this.Uid;
    _o.SortOrder = this.SortOrder;
    _o.Reward = this.Reward;
    _o.StoreItemUid = this.StoreItemUid;
    _o.Visual = this.Visual;
    _o.Discount = this.Discount;
  }
  public static Offset<FBConfig.EndlessTreasureConfigItem> Pack(FlatBufferBuilder builder, EndlessTreasureConfigItemT _o) {
    if (_o == null) return default(Offset<FBConfig.EndlessTreasureConfigItem>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _reward = _o.Reward == null ? default(StringOffset) : builder.CreateString(_o.Reward);
    var _store_item_uid = _o.StoreItemUid == null ? default(StringOffset) : builder.CreateString(_o.StoreItemUid);
    var _visual = _o.Visual == null ? default(StringOffset) : builder.CreateString(_o.Visual);
    return CreateEndlessTreasureConfigItem(
      builder,
      _uid,
      _o.SortOrder,
      _reward,
      _store_item_uid,
      _visual,
      _o.Discount);
  }
}

public class EndlessTreasureConfigItemT
{
  public string Uid { get; set; }
  public int SortOrder { get; set; }
  public string Reward { get; set; }
  public string StoreItemUid { get; set; }
  public string Visual { get; set; }
  public int Discount { get; set; }

  public EndlessTreasureConfigItemT() {
    this.Uid = null;
    this.SortOrder = 0;
    this.Reward = null;
    this.StoreItemUid = null;
    this.Visual = null;
    this.Discount = 0;
  }
}

public struct EndlessTreasureConfig : IFlatbufferConfig<EndlessTreasureConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static EndlessTreasureConfig GetRootAsEndlessTreasureConfig(ByteBuffer _bb) { return GetRootAsEndlessTreasureConfig(_bb, new EndlessTreasureConfig()); }
  public static EndlessTreasureConfig GetRootAsEndlessTreasureConfig(ByteBuffer _bb, EndlessTreasureConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public EndlessTreasureConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.EndlessTreasureConfigItem? TreasuresList(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.EndlessTreasureConfigItem?)(new FBConfig.EndlessTreasureConfigItem()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TreasuresListLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.EndlessTreasureConfigItem? TreasuresListByKey(string key) { int o = __p.__offset(6); return o != 0 ? FBConfig.EndlessTreasureConfigItem.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.EndlessTreasureConfig> CreateEndlessTreasureConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset treasures_listOffset = default(VectorOffset)) {
    builder.StartTable(2);
    EndlessTreasureConfig.AddTreasuresList(builder, treasures_listOffset);
    EndlessTreasureConfig.AddUid(builder, uidOffset);
    return EndlessTreasureConfig.EndEndlessTreasureConfig(builder);
  }

  public static void StartEndlessTreasureConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTreasuresList(FlatBufferBuilder builder, VectorOffset treasuresListOffset) { builder.AddOffset(1, treasuresListOffset.Value, 0); }
  public static VectorOffset CreateTreasuresListVector(FlatBufferBuilder builder, Offset<FBConfig.EndlessTreasureConfigItem>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTreasuresListVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.EndlessTreasureConfigItem>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTreasuresListVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.EndlessTreasureConfigItem>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTreasuresListVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.EndlessTreasureConfigItem>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTreasuresListVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.EndlessTreasureConfig> EndEndlessTreasureConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.EndlessTreasureConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfEndlessTreasureConfig(FlatBufferBuilder builder, Offset<EndlessTreasureConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<EndlessTreasureConfig> o1, Offset<EndlessTreasureConfig> o2) =>
        new EndlessTreasureConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new EndlessTreasureConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static EndlessTreasureConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    EndlessTreasureConfig obj_ = new EndlessTreasureConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public EndlessTreasureConfigT UnPack() {
    var _o = new EndlessTreasureConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(EndlessTreasureConfigT _o) {
    _o.Uid = this.Uid;
    _o.TreasuresList = new List<FBConfig.EndlessTreasureConfigItemT>();
    for (var _j = 0; _j < this.TreasuresListLength; ++_j) {_o.TreasuresList.Add(this.TreasuresList(_j).HasValue ? this.TreasuresList(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.EndlessTreasureConfig> Pack(FlatBufferBuilder builder, EndlessTreasureConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.EndlessTreasureConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _treasures_list = default(VectorOffset);
    if (_o.TreasuresList != null) {
      var __treasures_list = new Offset<FBConfig.EndlessTreasureConfigItem>[_o.TreasuresList.Count];
      for (var _j = 0; _j < __treasures_list.Length; ++_j) { __treasures_list[_j] = FBConfig.EndlessTreasureConfigItem.Pack(builder, _o.TreasuresList[_j]); }
      _treasures_list = CreateTreasuresListVector(builder, __treasures_list);
    }
    return CreateEndlessTreasureConfig(
      builder,
      _uid,
      _treasures_list);
  }
}

public class EndlessTreasureConfigT
{
  public string Uid { get; set; }
  public List<FBConfig.EndlessTreasureConfigItemT> TreasuresList { get; set; }

  public EndlessTreasureConfigT() {
    this.Uid = null;
    this.TreasuresList = null;
  }
}

public struct EndlessTreasureConfigDict : IFlatbufferConfigDict<EndlessTreasureConfig, EndlessTreasureConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static EndlessTreasureConfigDict GetRootAsEndlessTreasureConfigDict(ByteBuffer _bb) { return GetRootAsEndlessTreasureConfigDict(_bb, new EndlessTreasureConfigDict()); }
  public static EndlessTreasureConfigDict GetRootAsEndlessTreasureConfigDict(ByteBuffer _bb, EndlessTreasureConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public EndlessTreasureConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.EndlessTreasureConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.EndlessTreasureConfig?)(new FBConfig.EndlessTreasureConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.EndlessTreasureConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.EndlessTreasureConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.EndlessTreasureConfigDict> CreateEndlessTreasureConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    EndlessTreasureConfigDict.AddValues(builder, valuesOffset);
    return EndlessTreasureConfigDict.EndEndlessTreasureConfigDict(builder);
  }

  public static void StartEndlessTreasureConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.EndlessTreasureConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.EndlessTreasureConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.EndlessTreasureConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.EndlessTreasureConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.EndlessTreasureConfigDict> EndEndlessTreasureConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.EndlessTreasureConfigDict>(o);
  }
  public static void FinishEndlessTreasureConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.EndlessTreasureConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedEndlessTreasureConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.EndlessTreasureConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public EndlessTreasureConfigDictT UnPack() {
    var _o = new EndlessTreasureConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(EndlessTreasureConfigDictT _o) {
    _o.Values = new List<FBConfig.EndlessTreasureConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.EndlessTreasureConfigDict> Pack(FlatBufferBuilder builder, EndlessTreasureConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.EndlessTreasureConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.EndlessTreasureConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.EndlessTreasureConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateEndlessTreasureConfigDict(
      builder,
      _values);
  }
}

public class EndlessTreasureConfigDictT
{
  public List<FBConfig.EndlessTreasureConfigT> Values { get; set; }

  public EndlessTreasureConfigDictT() {
    this.Values = null;
  }
  public static EndlessTreasureConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return EndlessTreasureConfigDict.GetRootAsEndlessTreasureConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    EndlessTreasureConfigDict.FinishEndlessTreasureConfigDictBuffer(fbb, EndlessTreasureConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
