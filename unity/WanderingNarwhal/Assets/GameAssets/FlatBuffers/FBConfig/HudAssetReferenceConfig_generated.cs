// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct HudAssetReferenceConfig : IFlatbufferConfig<HudAssetReferenceConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HudAssetReferenceConfig GetRootAsHudAssetReferenceConfig(ByteBuffer _bb) { return GetRootAsHudAssetReferenceConfig(_bb, new HudAssetReferenceConfig()); }
  public static HudAssetReferenceConfig GetRootAsHudAssetReferenceConfig(ByteBuffer _bb, HudAssetReferenceConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HudAssetReferenceConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Handler { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHandlerBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetHandlerBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetHandlerArray() { return __p.__vector_as_array<byte>(6); }
  public int GameplayType { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateGameplayType(int gameplay_type) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, gameplay_type); return true; } else { return false; } }

  public static Offset<FBConfig.HudAssetReferenceConfig> CreateHudAssetReferenceConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset handlerOffset = default(StringOffset),
      int gameplay_type = 0) {
    builder.StartTable(3);
    HudAssetReferenceConfig.AddGameplayType(builder, gameplay_type);
    HudAssetReferenceConfig.AddHandler(builder, handlerOffset);
    HudAssetReferenceConfig.AddUid(builder, uidOffset);
    return HudAssetReferenceConfig.EndHudAssetReferenceConfig(builder);
  }

  public static void StartHudAssetReferenceConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddHandler(FlatBufferBuilder builder, StringOffset handlerOffset) { builder.AddOffset(1, handlerOffset.Value, 0); }
  public static void AddGameplayType(FlatBufferBuilder builder, int gameplayType) { builder.AddInt(2, gameplayType, 0); }
  public static Offset<FBConfig.HudAssetReferenceConfig> EndHudAssetReferenceConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.HudAssetReferenceConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfHudAssetReferenceConfig(FlatBufferBuilder builder, Offset<HudAssetReferenceConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<HudAssetReferenceConfig> o1, Offset<HudAssetReferenceConfig> o2) =>
        new HudAssetReferenceConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new HudAssetReferenceConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static HudAssetReferenceConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    HudAssetReferenceConfig obj_ = new HudAssetReferenceConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public HudAssetReferenceConfigT UnPack() {
    var _o = new HudAssetReferenceConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HudAssetReferenceConfigT _o) {
    _o.Uid = this.Uid;
    _o.Handler = this.Handler;
    _o.GameplayType = this.GameplayType;
  }
  public static Offset<FBConfig.HudAssetReferenceConfig> Pack(FlatBufferBuilder builder, HudAssetReferenceConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.HudAssetReferenceConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _handler = _o.Handler == null ? default(StringOffset) : builder.CreateString(_o.Handler);
    return CreateHudAssetReferenceConfig(
      builder,
      _uid,
      _handler,
      _o.GameplayType);
  }
}

public class HudAssetReferenceConfigT
{
  public string Uid { get; set; }
  public string Handler { get; set; }
  public int GameplayType { get; set; }

  public HudAssetReferenceConfigT() {
    this.Uid = null;
    this.Handler = null;
    this.GameplayType = 0;
  }
}

public struct HudAssetReferenceConfigDict : IFlatbufferConfigDict<HudAssetReferenceConfig, HudAssetReferenceConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HudAssetReferenceConfigDict GetRootAsHudAssetReferenceConfigDict(ByteBuffer _bb) { return GetRootAsHudAssetReferenceConfigDict(_bb, new HudAssetReferenceConfigDict()); }
  public static HudAssetReferenceConfigDict GetRootAsHudAssetReferenceConfigDict(ByteBuffer _bb, HudAssetReferenceConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HudAssetReferenceConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.HudAssetReferenceConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.HudAssetReferenceConfig?)(new FBConfig.HudAssetReferenceConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.HudAssetReferenceConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.HudAssetReferenceConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.HudAssetReferenceConfigDict> CreateHudAssetReferenceConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    HudAssetReferenceConfigDict.AddValues(builder, valuesOffset);
    return HudAssetReferenceConfigDict.EndHudAssetReferenceConfigDict(builder);
  }

  public static void StartHudAssetReferenceConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.HudAssetReferenceConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.HudAssetReferenceConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.HudAssetReferenceConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.HudAssetReferenceConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.HudAssetReferenceConfigDict> EndHudAssetReferenceConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.HudAssetReferenceConfigDict>(o);
  }
  public static void FinishHudAssetReferenceConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HudAssetReferenceConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedHudAssetReferenceConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HudAssetReferenceConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public HudAssetReferenceConfigDictT UnPack() {
    var _o = new HudAssetReferenceConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HudAssetReferenceConfigDictT _o) {
    _o.Values = new List<FBConfig.HudAssetReferenceConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.HudAssetReferenceConfigDict> Pack(FlatBufferBuilder builder, HudAssetReferenceConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.HudAssetReferenceConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.HudAssetReferenceConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.HudAssetReferenceConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateHudAssetReferenceConfigDict(
      builder,
      _values);
  }
}

public class HudAssetReferenceConfigDictT
{
  public List<FBConfig.HudAssetReferenceConfigT> Values { get; set; }

  public HudAssetReferenceConfigDictT() {
    this.Values = null;
  }
  public static HudAssetReferenceConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return HudAssetReferenceConfigDict.GetRootAsHudAssetReferenceConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    HudAssetReferenceConfigDict.FinishHudAssetReferenceConfigDictBuffer(fbb, HudAssetReferenceConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
