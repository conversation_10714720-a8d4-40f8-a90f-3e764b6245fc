// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct RewardIcons : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static RewardIcons GetRootAsRewardIcons(ByteBuffer _bb) { return GetRootAsRewardIcons(_bb, new RewardIcons()); }
  public static RewardIcons GetRootAsRewardIcons(ByteBuffer _bb, RewardIcons obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public RewardIcons __assign(int _i, Byte<PERSON><PERSON>er _bb) { __init(_i, _bb); return this; }

  public string Icons(int j) { int o = __p.__offset(4); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int IconsLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.RewardIcons> CreateRewardIcons(FlatBufferBuilder builder,
      VectorOffset iconsOffset = default(VectorOffset)) {
    builder.StartTable(1);
    RewardIcons.AddIcons(builder, iconsOffset);
    return RewardIcons.EndRewardIcons(builder);
  }

  public static void StartRewardIcons(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddIcons(FlatBufferBuilder builder, VectorOffset iconsOffset) { builder.AddOffset(0, iconsOffset.Value, 0); }
  public static VectorOffset CreateIconsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateIconsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateIconsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateIconsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartIconsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.RewardIcons> EndRewardIcons(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.RewardIcons>(o);
  }
  public RewardIconsT UnPack() {
    var _o = new RewardIconsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(RewardIconsT _o) {
    _o.Icons = new List<string>();
    for (var _j = 0; _j < this.IconsLength; ++_j) {_o.Icons.Add(this.Icons(_j));}
  }
  public static Offset<FBConfig.RewardIcons> Pack(FlatBufferBuilder builder, RewardIconsT _o) {
    if (_o == null) return default(Offset<FBConfig.RewardIcons>);
    var _icons = default(VectorOffset);
    if (_o.Icons != null) {
      var __icons = new StringOffset[_o.Icons.Count];
      for (var _j = 0; _j < __icons.Length; ++_j) { __icons[_j] = builder.CreateString(_o.Icons[_j]); }
      _icons = CreateIconsVector(builder, __icons);
    }
    return CreateRewardIcons(
      builder,
      _icons);
  }
}

public class RewardIconsT
{
  public List<string> Icons { get; set; }

  public RewardIconsT() {
    this.Icons = null;
  }
}

public struct LevelConfig : IFlatbufferConfig<LevelConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelConfig GetRootAsLevelConfig(ByteBuffer _bb) { return GetRootAsLevelConfig(_bb, new LevelConfig()); }
  public static LevelConfig GetRootAsLevelConfig(ByteBuffer _bb, LevelConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string LocationUid { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLocationUidBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetLocationUidBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetLocationUidArray() { return __p.__vector_as_array<byte>(8); }
  public string FileNames(int j) { int o = __p.__offset(10); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int FileNamesLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Point? Position { get { int o = __p.__offset(12); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.LevelLink? Links(int j) { int o = __p.__offset(14); return o != 0 ? (FBConfig.LevelLink?)(new FBConfig.LevelLink()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int LinksLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Point? Size { get { int o = __p.__offset(16); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.RewardIcons? RewardIcons(int j) { int o = __p.__offset(18); return o != 0 ? (FBConfig.RewardIcons?)(new FBConfig.RewardIcons()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardIconsLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string StartupBoosts(int j) { int o = __p.__offset(20); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int StartupBoostsLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string EligibleBoosts(int j) { int o = __p.__offset(22); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int EligibleBoostsLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string PoiEntityUid { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPoiEntityUidBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetPoiEntityUidBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetPoiEntityUidArray() { return __p.__vector_as_array<byte>(24); }
  public string GoalsDescription(int j) { int o = __p.__offset(26); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int GoalsDescriptionLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int GoodTurns { get { int o = __p.__offset(28); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateGoodTurns(int good_turns) { int o = __p.__offset(28); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, good_turns); return true; } else { return false; } }
  public int BetterTurns { get { int o = __p.__offset(30); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBetterTurns(int better_turns) { int o = __p.__offset(30); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, better_turns); return true; } else { return false; } }
  public int BestTurns { get { int o = __p.__offset(32); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBestTurns(int best_turns) { int o = __p.__offset(32); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, best_turns); return true; } else { return false; } }
  public int GoodScore { get { int o = __p.__offset(34); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateGoodScore(int good_score) { int o = __p.__offset(34); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, good_score); return true; } else { return false; } }
  public int BetterScore { get { int o = __p.__offset(36); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBetterScore(int better_score) { int o = __p.__offset(36); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, better_score); return true; } else { return false; } }
  public int BestScore { get { int o = __p.__offset(38); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBestScore(int best_score) { int o = __p.__offset(38); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, best_score); return true; } else { return false; } }
  public FBConfig.DictStringInt? GoodTileKindGoalsFb(int j) { int o = __p.__offset(40); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int GoodTileKindGoalsFbLength { get { int o = __p.__offset(40); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? BetterTileKindGoalsFb(int j) { int o = __p.__offset(42); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int BetterTileKindGoalsFbLength { get { int o = __p.__offset(42); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? BestTileKindGoalsFb(int j) { int o = __p.__offset(44); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int BestTileKindGoalsFbLength { get { int o = __p.__offset(44); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string GoodReward { get { int o = __p.__offset(46); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGoodRewardBytes() { return __p.__vector_as_span<byte>(46, 1); }
#else
  public ArraySegment<byte>? GetGoodRewardBytes() { return __p.__vector_as_arraysegment(46); }
#endif
  public byte[] GetGoodRewardArray() { return __p.__vector_as_array<byte>(46); }
  public string BetterReward { get { int o = __p.__offset(48); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBetterRewardBytes() { return __p.__vector_as_span<byte>(48, 1); }
#else
  public ArraySegment<byte>? GetBetterRewardBytes() { return __p.__vector_as_arraysegment(48); }
#endif
  public byte[] GetBetterRewardArray() { return __p.__vector_as_array<byte>(48); }
  public string BestReward { get { int o = __p.__offset(50); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBestRewardBytes() { return __p.__vector_as_span<byte>(50, 1); }
#else
  public ArraySegment<byte>? GetBestRewardBytes() { return __p.__vector_as_arraysegment(50); }
#endif
  public byte[] GetBestRewardArray() { return __p.__vector_as_array<byte>(50); }
  public string RelatedTradingCardUid { get { int o = __p.__offset(52); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRelatedTradingCardUidBytes() { return __p.__vector_as_span<byte>(52, 1); }
#else
  public ArraySegment<byte>? GetRelatedTradingCardUidBytes() { return __p.__vector_as_arraysegment(52); }
#endif
  public byte[] GetRelatedTradingCardUidArray() { return __p.__vector_as_array<byte>(52); }
  public float ScoreCoefs(int j) { int o = __p.__offset(54); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int ScoreCoefsLength { get { int o = __p.__offset(54); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetScoreCoefsBytes() { return __p.__vector_as_span<float>(54, 4); }
#else
  public ArraySegment<byte>? GetScoreCoefsBytes() { return __p.__vector_as_arraysegment(54); }
#endif
  public float[] GetScoreCoefsArray() { return __p.__vector_as_array<float>(54); }
  public bool MutateScoreCoefs(int j, float score_coefs) { int o = __p.__offset(54); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, score_coefs); return true; } else { return false; } }
  public string WonderGoalId { get { int o = __p.__offset(56); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetWonderGoalIdBytes() { return __p.__vector_as_span<byte>(56, 1); }
#else
  public ArraySegment<byte>? GetWonderGoalIdBytes() { return __p.__vector_as_arraysegment(56); }
#endif
  public byte[] GetWonderGoalIdArray() { return __p.__vector_as_array<byte>(56); }
  public string DecorationUid { get { int o = __p.__offset(58); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDecorationUidBytes() { return __p.__vector_as_span<byte>(58, 1); }
#else
  public ArraySegment<byte>? GetDecorationUidBytes() { return __p.__vector_as_arraysegment(58); }
#endif
  public byte[] GetDecorationUidArray() { return __p.__vector_as_array<byte>(58); }
  public string RelatedQuestUid { get { int o = __p.__offset(60); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRelatedQuestUidBytes() { return __p.__vector_as_span<byte>(60, 1); }
#else
  public ArraySegment<byte>? GetRelatedQuestUidBytes() { return __p.__vector_as_arraysegment(60); }
#endif
  public byte[] GetRelatedQuestUidArray() { return __p.__vector_as_array<byte>(60); }
  public string RelatedQuestIcon { get { int o = __p.__offset(62); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRelatedQuestIconBytes() { return __p.__vector_as_span<byte>(62, 1); }
#else
  public ArraySegment<byte>? GetRelatedQuestIconBytes() { return __p.__vector_as_arraysegment(62); }
#endif
  public byte[] GetRelatedQuestIconArray() { return __p.__vector_as_array<byte>(62); }
  public string CompleteReward { get { int o = __p.__offset(64); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCompleteRewardBytes() { return __p.__vector_as_span<byte>(64, 1); }
#else
  public ArraySegment<byte>? GetCompleteRewardBytes() { return __p.__vector_as_arraysegment(64); }
#endif
  public byte[] GetCompleteRewardArray() { return __p.__vector_as_array<byte>(64); }
  public string GoodExtraGoals(int j) { int o = __p.__offset(66); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int GoodExtraGoalsLength { get { int o = __p.__offset(66); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string BetterExtraGoals(int j) { int o = __p.__offset(68); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int BetterExtraGoalsLength { get { int o = __p.__offset(68); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string BestExtraGoals(int j) { int o = __p.__offset(70); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int BestExtraGoalsLength { get { int o = __p.__offset(70); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int AssistLossStreakRange(int j) { int o = __p.__offset(72); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int AssistLossStreakRangeLength { get { int o = __p.__offset(72); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetAssistLossStreakRangeBytes() { return __p.__vector_as_span<int>(72, 4); }
#else
  public ArraySegment<byte>? GetAssistLossStreakRangeBytes() { return __p.__vector_as_arraysegment(72); }
#endif
  public int[] GetAssistLossStreakRangeArray() { return __p.__vector_as_array<int>(72); }
  public bool MutateAssistLossStreakRange(int j, int assist_loss_streak_range) { int o = __p.__offset(72); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, assist_loss_streak_range); return true; } else { return false; } }
  public float SortOrder { get { int o = __p.__offset(74); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateSortOrder(float sort_order) { int o = __p.__offset(74); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, sort_order); return true; } else { return false; } }
  public int VisitorTileState { get { int o = __p.__offset(76); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateVisitorTileState(int visitor_tile_state) { int o = __p.__offset(76); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, visitor_tile_state); return true; } else { return false; } }
  public float ReportingLevelNum { get { int o = __p.__offset(78); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateReportingLevelNum(float reporting_level_num) { int o = __p.__offset(78); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, reporting_level_num); return true; } else { return false; } }
  public int TargetWinRate { get { int o = __p.__offset(80); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTargetWinRate(int target_win_rate) { int o = __p.__offset(80); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, target_win_rate); return true; } else { return false; } }
  public int TargetWinRateT2 { get { int o = __p.__offset(82); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTargetWinRateT2(int target_win_rate_t2) { int o = __p.__offset(82); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, target_win_rate_t2); return true; } else { return false; } }
  public int TargetWinRateT3 { get { int o = __p.__offset(84); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTargetWinRateT3(int target_win_rate_t3) { int o = __p.__offset(84); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, target_win_rate_t3); return true; } else { return false; } }
  public int ExtraBoostersTurn { get { int o = __p.__offset(86); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateExtraBoostersTurn(int extra_boosters_turn) { int o = __p.__offset(86); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, extra_boosters_turn); return true; } else { return false; } }
  public int Difficulty { get { int o = __p.__offset(88); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDifficulty(int difficulty) { int o = __p.__offset(88); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, difficulty); return true; } else { return false; } }
  public string AssistSystemUid { get { int o = __p.__offset(90); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAssistSystemUidBytes() { return __p.__vector_as_span<byte>(90, 1); }
#else
  public ArraySegment<byte>? GetAssistSystemUidBytes() { return __p.__vector_as_arraysegment(90); }
#endif
  public byte[] GetAssistSystemUidArray() { return __p.__vector_as_array<byte>(90); }
  public string Hash { get { int o = __p.__offset(92); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHashBytes() { return __p.__vector_as_span<byte>(92, 1); }
#else
  public ArraySegment<byte>? GetHashBytes() { return __p.__vector_as_arraysegment(92); }
#endif
  public byte[] GetHashArray() { return __p.__vector_as_array<byte>(92); }

  public static Offset<FBConfig.LevelConfig> CreateLevelConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset location_uidOffset = default(StringOffset),
      VectorOffset file_namesOffset = default(VectorOffset),
      Offset<FBConfig.Point> positionOffset = default(Offset<FBConfig.Point>),
      VectorOffset linksOffset = default(VectorOffset),
      Offset<FBConfig.Point> sizeOffset = default(Offset<FBConfig.Point>),
      VectorOffset reward_iconsOffset = default(VectorOffset),
      VectorOffset startup_boostsOffset = default(VectorOffset),
      VectorOffset eligible_boostsOffset = default(VectorOffset),
      StringOffset poi_entity_uidOffset = default(StringOffset),
      VectorOffset goals_descriptionOffset = default(VectorOffset),
      int good_turns = 0,
      int better_turns = 0,
      int best_turns = 0,
      int good_score = 0,
      int better_score = 0,
      int best_score = 0,
      VectorOffset good_tile_kind_goals_fbOffset = default(VectorOffset),
      VectorOffset better_tile_kind_goals_fbOffset = default(VectorOffset),
      VectorOffset best_tile_kind_goals_fbOffset = default(VectorOffset),
      StringOffset good_rewardOffset = default(StringOffset),
      StringOffset better_rewardOffset = default(StringOffset),
      StringOffset best_rewardOffset = default(StringOffset),
      StringOffset related_trading_card_uidOffset = default(StringOffset),
      VectorOffset score_coefsOffset = default(VectorOffset),
      StringOffset wonder_goal_idOffset = default(StringOffset),
      StringOffset decoration_uidOffset = default(StringOffset),
      StringOffset related_quest_uidOffset = default(StringOffset),
      StringOffset related_quest_iconOffset = default(StringOffset),
      StringOffset complete_rewardOffset = default(StringOffset),
      VectorOffset good_extra_goalsOffset = default(VectorOffset),
      VectorOffset better_extra_goalsOffset = default(VectorOffset),
      VectorOffset best_extra_goalsOffset = default(VectorOffset),
      VectorOffset assist_loss_streak_rangeOffset = default(VectorOffset),
      float sort_order = 0.0f,
      int visitor_tile_state = 0,
      float reporting_level_num = 0.0f,
      int target_win_rate = 0,
      int target_win_rate_t2 = 0,
      int target_win_rate_t3 = 0,
      int extra_boosters_turn = 0,
      int difficulty = 0,
      StringOffset assist_system_uidOffset = default(StringOffset),
      StringOffset hashOffset = default(StringOffset)) {
    builder.StartTable(45);
    LevelConfig.AddHash(builder, hashOffset);
    LevelConfig.AddAssistSystemUid(builder, assist_system_uidOffset);
    LevelConfig.AddDifficulty(builder, difficulty);
    LevelConfig.AddExtraBoostersTurn(builder, extra_boosters_turn);
    LevelConfig.AddTargetWinRateT3(builder, target_win_rate_t3);
    LevelConfig.AddTargetWinRateT2(builder, target_win_rate_t2);
    LevelConfig.AddTargetWinRate(builder, target_win_rate);
    LevelConfig.AddReportingLevelNum(builder, reporting_level_num);
    LevelConfig.AddVisitorTileState(builder, visitor_tile_state);
    LevelConfig.AddSortOrder(builder, sort_order);
    LevelConfig.AddAssistLossStreakRange(builder, assist_loss_streak_rangeOffset);
    LevelConfig.AddBestExtraGoals(builder, best_extra_goalsOffset);
    LevelConfig.AddBetterExtraGoals(builder, better_extra_goalsOffset);
    LevelConfig.AddGoodExtraGoals(builder, good_extra_goalsOffset);
    LevelConfig.AddCompleteReward(builder, complete_rewardOffset);
    LevelConfig.AddRelatedQuestIcon(builder, related_quest_iconOffset);
    LevelConfig.AddRelatedQuestUid(builder, related_quest_uidOffset);
    LevelConfig.AddDecorationUid(builder, decoration_uidOffset);
    LevelConfig.AddWonderGoalId(builder, wonder_goal_idOffset);
    LevelConfig.AddScoreCoefs(builder, score_coefsOffset);
    LevelConfig.AddRelatedTradingCardUid(builder, related_trading_card_uidOffset);
    LevelConfig.AddBestReward(builder, best_rewardOffset);
    LevelConfig.AddBetterReward(builder, better_rewardOffset);
    LevelConfig.AddGoodReward(builder, good_rewardOffset);
    LevelConfig.AddBestTileKindGoalsFb(builder, best_tile_kind_goals_fbOffset);
    LevelConfig.AddBetterTileKindGoalsFb(builder, better_tile_kind_goals_fbOffset);
    LevelConfig.AddGoodTileKindGoalsFb(builder, good_tile_kind_goals_fbOffset);
    LevelConfig.AddBestScore(builder, best_score);
    LevelConfig.AddBetterScore(builder, better_score);
    LevelConfig.AddGoodScore(builder, good_score);
    LevelConfig.AddBestTurns(builder, best_turns);
    LevelConfig.AddBetterTurns(builder, better_turns);
    LevelConfig.AddGoodTurns(builder, good_turns);
    LevelConfig.AddGoalsDescription(builder, goals_descriptionOffset);
    LevelConfig.AddPoiEntityUid(builder, poi_entity_uidOffset);
    LevelConfig.AddEligibleBoosts(builder, eligible_boostsOffset);
    LevelConfig.AddStartupBoosts(builder, startup_boostsOffset);
    LevelConfig.AddRewardIcons(builder, reward_iconsOffset);
    LevelConfig.AddSize(builder, sizeOffset);
    LevelConfig.AddLinks(builder, linksOffset);
    LevelConfig.AddPosition(builder, positionOffset);
    LevelConfig.AddFileNames(builder, file_namesOffset);
    LevelConfig.AddLocationUid(builder, location_uidOffset);
    LevelConfig.AddName(builder, nameOffset);
    LevelConfig.AddUid(builder, uidOffset);
    return LevelConfig.EndLevelConfig(builder);
  }

  public static void StartLevelConfig(FlatBufferBuilder builder) { builder.StartTable(45); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddLocationUid(FlatBufferBuilder builder, StringOffset locationUidOffset) { builder.AddOffset(2, locationUidOffset.Value, 0); }
  public static void AddFileNames(FlatBufferBuilder builder, VectorOffset fileNamesOffset) { builder.AddOffset(3, fileNamesOffset.Value, 0); }
  public static VectorOffset CreateFileNamesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateFileNamesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFileNamesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFileNamesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartFileNamesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPosition(FlatBufferBuilder builder, Offset<FBConfig.Point> positionOffset) { builder.AddOffset(4, positionOffset.Value, 0); }
  public static void AddLinks(FlatBufferBuilder builder, VectorOffset linksOffset) { builder.AddOffset(5, linksOffset.Value, 0); }
  public static VectorOffset CreateLinksVector(FlatBufferBuilder builder, Offset<FBConfig.LevelLink>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLinksVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LevelLink>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLinksVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LevelLink>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLinksVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LevelLink>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLinksVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSize(FlatBufferBuilder builder, Offset<FBConfig.Point> sizeOffset) { builder.AddOffset(6, sizeOffset.Value, 0); }
  public static void AddRewardIcons(FlatBufferBuilder builder, VectorOffset rewardIconsOffset) { builder.AddOffset(7, rewardIconsOffset.Value, 0); }
  public static VectorOffset CreateRewardIconsVector(FlatBufferBuilder builder, Offset<FBConfig.RewardIcons>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardIconsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.RewardIcons>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardIconsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.RewardIcons>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardIconsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.RewardIcons>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardIconsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddStartupBoosts(FlatBufferBuilder builder, VectorOffset startupBoostsOffset) { builder.AddOffset(8, startupBoostsOffset.Value, 0); }
  public static VectorOffset CreateStartupBoostsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateStartupBoostsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStartupBoostsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStartupBoostsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartStartupBoostsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddEligibleBoosts(FlatBufferBuilder builder, VectorOffset eligibleBoostsOffset) { builder.AddOffset(9, eligibleBoostsOffset.Value, 0); }
  public static VectorOffset CreateEligibleBoostsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateEligibleBoostsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEligibleBoostsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEligibleBoostsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartEligibleBoostsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPoiEntityUid(FlatBufferBuilder builder, StringOffset poiEntityUidOffset) { builder.AddOffset(10, poiEntityUidOffset.Value, 0); }
  public static void AddGoalsDescription(FlatBufferBuilder builder, VectorOffset goalsDescriptionOffset) { builder.AddOffset(11, goalsDescriptionOffset.Value, 0); }
  public static VectorOffset CreateGoalsDescriptionVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGoalsDescriptionVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoalsDescriptionVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoalsDescriptionVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGoalsDescriptionVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddGoodTurns(FlatBufferBuilder builder, int goodTurns) { builder.AddInt(12, goodTurns, 0); }
  public static void AddBetterTurns(FlatBufferBuilder builder, int betterTurns) { builder.AddInt(13, betterTurns, 0); }
  public static void AddBestTurns(FlatBufferBuilder builder, int bestTurns) { builder.AddInt(14, bestTurns, 0); }
  public static void AddGoodScore(FlatBufferBuilder builder, int goodScore) { builder.AddInt(15, goodScore, 0); }
  public static void AddBetterScore(FlatBufferBuilder builder, int betterScore) { builder.AddInt(16, betterScore, 0); }
  public static void AddBestScore(FlatBufferBuilder builder, int bestScore) { builder.AddInt(17, bestScore, 0); }
  public static void AddGoodTileKindGoalsFb(FlatBufferBuilder builder, VectorOffset goodTileKindGoalsFbOffset) { builder.AddOffset(18, goodTileKindGoalsFbOffset.Value, 0); }
  public static VectorOffset CreateGoodTileKindGoalsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGoodTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGoodTileKindGoalsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBetterTileKindGoalsFb(FlatBufferBuilder builder, VectorOffset betterTileKindGoalsFbOffset) { builder.AddOffset(19, betterTileKindGoalsFbOffset.Value, 0); }
  public static VectorOffset CreateBetterTileKindGoalsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBetterTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBetterTileKindGoalsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBestTileKindGoalsFb(FlatBufferBuilder builder, VectorOffset bestTileKindGoalsFbOffset) { builder.AddOffset(20, bestTileKindGoalsFbOffset.Value, 0); }
  public static VectorOffset CreateBestTileKindGoalsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBestTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBestTileKindGoalsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddGoodReward(FlatBufferBuilder builder, StringOffset goodRewardOffset) { builder.AddOffset(21, goodRewardOffset.Value, 0); }
  public static void AddBetterReward(FlatBufferBuilder builder, StringOffset betterRewardOffset) { builder.AddOffset(22, betterRewardOffset.Value, 0); }
  public static void AddBestReward(FlatBufferBuilder builder, StringOffset bestRewardOffset) { builder.AddOffset(23, bestRewardOffset.Value, 0); }
  public static void AddRelatedTradingCardUid(FlatBufferBuilder builder, StringOffset relatedTradingCardUidOffset) { builder.AddOffset(24, relatedTradingCardUidOffset.Value, 0); }
  public static void AddScoreCoefs(FlatBufferBuilder builder, VectorOffset scoreCoefsOffset) { builder.AddOffset(25, scoreCoefsOffset.Value, 0); }
  public static VectorOffset CreateScoreCoefsVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateScoreCoefsVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreCoefsVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreCoefsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartScoreCoefsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddWonderGoalId(FlatBufferBuilder builder, StringOffset wonderGoalIdOffset) { builder.AddOffset(26, wonderGoalIdOffset.Value, 0); }
  public static void AddDecorationUid(FlatBufferBuilder builder, StringOffset decorationUidOffset) { builder.AddOffset(27, decorationUidOffset.Value, 0); }
  public static void AddRelatedQuestUid(FlatBufferBuilder builder, StringOffset relatedQuestUidOffset) { builder.AddOffset(28, relatedQuestUidOffset.Value, 0); }
  public static void AddRelatedQuestIcon(FlatBufferBuilder builder, StringOffset relatedQuestIconOffset) { builder.AddOffset(29, relatedQuestIconOffset.Value, 0); }
  public static void AddCompleteReward(FlatBufferBuilder builder, StringOffset completeRewardOffset) { builder.AddOffset(30, completeRewardOffset.Value, 0); }
  public static void AddGoodExtraGoals(FlatBufferBuilder builder, VectorOffset goodExtraGoalsOffset) { builder.AddOffset(31, goodExtraGoalsOffset.Value, 0); }
  public static VectorOffset CreateGoodExtraGoalsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGoodExtraGoalsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodExtraGoalsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodExtraGoalsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGoodExtraGoalsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBetterExtraGoals(FlatBufferBuilder builder, VectorOffset betterExtraGoalsOffset) { builder.AddOffset(32, betterExtraGoalsOffset.Value, 0); }
  public static VectorOffset CreateBetterExtraGoalsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBetterExtraGoalsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterExtraGoalsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterExtraGoalsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBetterExtraGoalsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBestExtraGoals(FlatBufferBuilder builder, VectorOffset bestExtraGoalsOffset) { builder.AddOffset(33, bestExtraGoalsOffset.Value, 0); }
  public static VectorOffset CreateBestExtraGoalsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBestExtraGoalsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestExtraGoalsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestExtraGoalsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBestExtraGoalsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAssistLossStreakRange(FlatBufferBuilder builder, VectorOffset assistLossStreakRangeOffset) { builder.AddOffset(34, assistLossStreakRangeOffset.Value, 0); }
  public static VectorOffset CreateAssistLossStreakRangeVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateAssistLossStreakRangeVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssistLossStreakRangeVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssistLossStreakRangeVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAssistLossStreakRangeVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSortOrder(FlatBufferBuilder builder, float sortOrder) { builder.AddFloat(35, sortOrder, 0.0f); }
  public static void AddVisitorTileState(FlatBufferBuilder builder, int visitorTileState) { builder.AddInt(36, visitorTileState, 0); }
  public static void AddReportingLevelNum(FlatBufferBuilder builder, float reportingLevelNum) { builder.AddFloat(37, reportingLevelNum, 0.0f); }
  public static void AddTargetWinRate(FlatBufferBuilder builder, int targetWinRate) { builder.AddInt(38, targetWinRate, 0); }
  public static void AddTargetWinRateT2(FlatBufferBuilder builder, int targetWinRateT2) { builder.AddInt(39, targetWinRateT2, 0); }
  public static void AddTargetWinRateT3(FlatBufferBuilder builder, int targetWinRateT3) { builder.AddInt(40, targetWinRateT3, 0); }
  public static void AddExtraBoostersTurn(FlatBufferBuilder builder, int extraBoostersTurn) { builder.AddInt(41, extraBoostersTurn, 0); }
  public static void AddDifficulty(FlatBufferBuilder builder, int difficulty) { builder.AddInt(42, difficulty, 0); }
  public static void AddAssistSystemUid(FlatBufferBuilder builder, StringOffset assistSystemUidOffset) { builder.AddOffset(43, assistSystemUidOffset.Value, 0); }
  public static void AddHash(FlatBufferBuilder builder, StringOffset hashOffset) { builder.AddOffset(44, hashOffset.Value, 0); }
  public static Offset<FBConfig.LevelConfig> EndLevelConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LevelConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLevelConfig(FlatBufferBuilder builder, Offset<LevelConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LevelConfig> o1, Offset<LevelConfig> o2) =>
        new LevelConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LevelConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LevelConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LevelConfig obj_ = new LevelConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LevelConfigT UnPack() {
    var _o = new LevelConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.LocationUid = this.LocationUid;
    _o.FileNames = new List<string>();
    for (var _j = 0; _j < this.FileNamesLength; ++_j) {_o.FileNames.Add(this.FileNames(_j));}
    _o.Position = this.Position.HasValue ? this.Position.Value.UnPack() : null;
    _o.Links = new List<FBConfig.LevelLinkT>();
    for (var _j = 0; _j < this.LinksLength; ++_j) {_o.Links.Add(this.Links(_j).HasValue ? this.Links(_j).Value.UnPack() : null);}
    _o.Size = this.Size.HasValue ? this.Size.Value.UnPack() : null;
    _o.RewardIcons = new List<FBConfig.RewardIconsT>();
    for (var _j = 0; _j < this.RewardIconsLength; ++_j) {_o.RewardIcons.Add(this.RewardIcons(_j).HasValue ? this.RewardIcons(_j).Value.UnPack() : null);}
    _o.StartupBoosts = new List<string>();
    for (var _j = 0; _j < this.StartupBoostsLength; ++_j) {_o.StartupBoosts.Add(this.StartupBoosts(_j));}
    _o.EligibleBoosts = new List<string>();
    for (var _j = 0; _j < this.EligibleBoostsLength; ++_j) {_o.EligibleBoosts.Add(this.EligibleBoosts(_j));}
    _o.PoiEntityUid = this.PoiEntityUid;
    _o.GoalsDescription = new List<string>();
    for (var _j = 0; _j < this.GoalsDescriptionLength; ++_j) {_o.GoalsDescription.Add(this.GoalsDescription(_j));}
    _o.GoodTurns = this.GoodTurns;
    _o.BetterTurns = this.BetterTurns;
    _o.BestTurns = this.BestTurns;
    _o.GoodScore = this.GoodScore;
    _o.BetterScore = this.BetterScore;
    _o.BestScore = this.BestScore;
    _o.GoodTileKindGoalsFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.GoodTileKindGoalsFbLength; ++_j) {_o.GoodTileKindGoalsFb.Add(this.GoodTileKindGoalsFb(_j).HasValue ? this.GoodTileKindGoalsFb(_j).Value.UnPack() : null);}
    _o.BetterTileKindGoalsFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.BetterTileKindGoalsFbLength; ++_j) {_o.BetterTileKindGoalsFb.Add(this.BetterTileKindGoalsFb(_j).HasValue ? this.BetterTileKindGoalsFb(_j).Value.UnPack() : null);}
    _o.BestTileKindGoalsFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.BestTileKindGoalsFbLength; ++_j) {_o.BestTileKindGoalsFb.Add(this.BestTileKindGoalsFb(_j).HasValue ? this.BestTileKindGoalsFb(_j).Value.UnPack() : null);}
    _o.GoodReward = this.GoodReward;
    _o.BetterReward = this.BetterReward;
    _o.BestReward = this.BestReward;
    _o.RelatedTradingCardUid = this.RelatedTradingCardUid;
    _o.ScoreCoefs = new List<float>();
    for (var _j = 0; _j < this.ScoreCoefsLength; ++_j) {_o.ScoreCoefs.Add(this.ScoreCoefs(_j));}
    _o.WonderGoalId = this.WonderGoalId;
    _o.DecorationUid = this.DecorationUid;
    _o.RelatedQuestUid = this.RelatedQuestUid;
    _o.RelatedQuestIcon = this.RelatedQuestIcon;
    _o.CompleteReward = this.CompleteReward;
    _o.GoodExtraGoals = new List<string>();
    for (var _j = 0; _j < this.GoodExtraGoalsLength; ++_j) {_o.GoodExtraGoals.Add(this.GoodExtraGoals(_j));}
    _o.BetterExtraGoals = new List<string>();
    for (var _j = 0; _j < this.BetterExtraGoalsLength; ++_j) {_o.BetterExtraGoals.Add(this.BetterExtraGoals(_j));}
    _o.BestExtraGoals = new List<string>();
    for (var _j = 0; _j < this.BestExtraGoalsLength; ++_j) {_o.BestExtraGoals.Add(this.BestExtraGoals(_j));}
    _o.AssistLossStreakRange = new List<int>();
    for (var _j = 0; _j < this.AssistLossStreakRangeLength; ++_j) {_o.AssistLossStreakRange.Add(this.AssistLossStreakRange(_j));}
    _o.SortOrder = this.SortOrder;
    _o.VisitorTileState = this.VisitorTileState;
    _o.ReportingLevelNum = this.ReportingLevelNum;
    _o.TargetWinRate = this.TargetWinRate;
    _o.TargetWinRateT2 = this.TargetWinRateT2;
    _o.TargetWinRateT3 = this.TargetWinRateT3;
    _o.ExtraBoostersTurn = this.ExtraBoostersTurn;
    _o.Difficulty = this.Difficulty;
    _o.AssistSystemUid = this.AssistSystemUid;
    _o.Hash = this.Hash;
  }
  public static Offset<FBConfig.LevelConfig> Pack(FlatBufferBuilder builder, LevelConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _location_uid = _o.LocationUid == null ? default(StringOffset) : builder.CreateString(_o.LocationUid);
    var _file_names = default(VectorOffset);
    if (_o.FileNames != null) {
      var __file_names = new StringOffset[_o.FileNames.Count];
      for (var _j = 0; _j < __file_names.Length; ++_j) { __file_names[_j] = builder.CreateString(_o.FileNames[_j]); }
      _file_names = CreateFileNamesVector(builder, __file_names);
    }
    var _position = _o.Position == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Position);
    var _links = default(VectorOffset);
    if (_o.Links != null) {
      var __links = new Offset<FBConfig.LevelLink>[_o.Links.Count];
      for (var _j = 0; _j < __links.Length; ++_j) { __links[_j] = FBConfig.LevelLink.Pack(builder, _o.Links[_j]); }
      _links = CreateLinksVector(builder, __links);
    }
    var _size = _o.Size == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Size);
    var _reward_icons = default(VectorOffset);
    if (_o.RewardIcons != null) {
      var __reward_icons = new Offset<FBConfig.RewardIcons>[_o.RewardIcons.Count];
      for (var _j = 0; _j < __reward_icons.Length; ++_j) { __reward_icons[_j] = FBConfig.RewardIcons.Pack(builder, _o.RewardIcons[_j]); }
      _reward_icons = CreateRewardIconsVector(builder, __reward_icons);
    }
    var _startup_boosts = default(VectorOffset);
    if (_o.StartupBoosts != null) {
      var __startup_boosts = new StringOffset[_o.StartupBoosts.Count];
      for (var _j = 0; _j < __startup_boosts.Length; ++_j) { __startup_boosts[_j] = builder.CreateString(_o.StartupBoosts[_j]); }
      _startup_boosts = CreateStartupBoostsVector(builder, __startup_boosts);
    }
    var _eligible_boosts = default(VectorOffset);
    if (_o.EligibleBoosts != null) {
      var __eligible_boosts = new StringOffset[_o.EligibleBoosts.Count];
      for (var _j = 0; _j < __eligible_boosts.Length; ++_j) { __eligible_boosts[_j] = builder.CreateString(_o.EligibleBoosts[_j]); }
      _eligible_boosts = CreateEligibleBoostsVector(builder, __eligible_boosts);
    }
    var _poi_entity_uid = _o.PoiEntityUid == null ? default(StringOffset) : builder.CreateString(_o.PoiEntityUid);
    var _goals_description = default(VectorOffset);
    if (_o.GoalsDescription != null) {
      var __goals_description = new StringOffset[_o.GoalsDescription.Count];
      for (var _j = 0; _j < __goals_description.Length; ++_j) { __goals_description[_j] = builder.CreateString(_o.GoalsDescription[_j]); }
      _goals_description = CreateGoalsDescriptionVector(builder, __goals_description);
    }
    var _good_tile_kind_goals_fb = default(VectorOffset);
    if (_o.GoodTileKindGoalsFb != null) {
      var __good_tile_kind_goals_fb = new Offset<FBConfig.DictStringInt>[_o.GoodTileKindGoalsFb.Count];
      for (var _j = 0; _j < __good_tile_kind_goals_fb.Length; ++_j) { __good_tile_kind_goals_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.GoodTileKindGoalsFb[_j]); }
      _good_tile_kind_goals_fb = CreateGoodTileKindGoalsFbVector(builder, __good_tile_kind_goals_fb);
    }
    var _better_tile_kind_goals_fb = default(VectorOffset);
    if (_o.BetterTileKindGoalsFb != null) {
      var __better_tile_kind_goals_fb = new Offset<FBConfig.DictStringInt>[_o.BetterTileKindGoalsFb.Count];
      for (var _j = 0; _j < __better_tile_kind_goals_fb.Length; ++_j) { __better_tile_kind_goals_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.BetterTileKindGoalsFb[_j]); }
      _better_tile_kind_goals_fb = CreateBetterTileKindGoalsFbVector(builder, __better_tile_kind_goals_fb);
    }
    var _best_tile_kind_goals_fb = default(VectorOffset);
    if (_o.BestTileKindGoalsFb != null) {
      var __best_tile_kind_goals_fb = new Offset<FBConfig.DictStringInt>[_o.BestTileKindGoalsFb.Count];
      for (var _j = 0; _j < __best_tile_kind_goals_fb.Length; ++_j) { __best_tile_kind_goals_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.BestTileKindGoalsFb[_j]); }
      _best_tile_kind_goals_fb = CreateBestTileKindGoalsFbVector(builder, __best_tile_kind_goals_fb);
    }
    var _good_reward = _o.GoodReward == null ? default(StringOffset) : builder.CreateString(_o.GoodReward);
    var _better_reward = _o.BetterReward == null ? default(StringOffset) : builder.CreateString(_o.BetterReward);
    var _best_reward = _o.BestReward == null ? default(StringOffset) : builder.CreateString(_o.BestReward);
    var _related_trading_card_uid = _o.RelatedTradingCardUid == null ? default(StringOffset) : builder.CreateString(_o.RelatedTradingCardUid);
    var _score_coefs = default(VectorOffset);
    if (_o.ScoreCoefs != null) {
      var __score_coefs = _o.ScoreCoefs.ToArray();
      _score_coefs = CreateScoreCoefsVector(builder, __score_coefs);
    }
    var _wonder_goal_id = _o.WonderGoalId == null ? default(StringOffset) : builder.CreateString(_o.WonderGoalId);
    var _decoration_uid = _o.DecorationUid == null ? default(StringOffset) : builder.CreateString(_o.DecorationUid);
    var _related_quest_uid = _o.RelatedQuestUid == null ? default(StringOffset) : builder.CreateString(_o.RelatedQuestUid);
    var _related_quest_icon = _o.RelatedQuestIcon == null ? default(StringOffset) : builder.CreateString(_o.RelatedQuestIcon);
    var _complete_reward = _o.CompleteReward == null ? default(StringOffset) : builder.CreateString(_o.CompleteReward);
    var _good_extra_goals = default(VectorOffset);
    if (_o.GoodExtraGoals != null) {
      var __good_extra_goals = new StringOffset[_o.GoodExtraGoals.Count];
      for (var _j = 0; _j < __good_extra_goals.Length; ++_j) { __good_extra_goals[_j] = builder.CreateString(_o.GoodExtraGoals[_j]); }
      _good_extra_goals = CreateGoodExtraGoalsVector(builder, __good_extra_goals);
    }
    var _better_extra_goals = default(VectorOffset);
    if (_o.BetterExtraGoals != null) {
      var __better_extra_goals = new StringOffset[_o.BetterExtraGoals.Count];
      for (var _j = 0; _j < __better_extra_goals.Length; ++_j) { __better_extra_goals[_j] = builder.CreateString(_o.BetterExtraGoals[_j]); }
      _better_extra_goals = CreateBetterExtraGoalsVector(builder, __better_extra_goals);
    }
    var _best_extra_goals = default(VectorOffset);
    if (_o.BestExtraGoals != null) {
      var __best_extra_goals = new StringOffset[_o.BestExtraGoals.Count];
      for (var _j = 0; _j < __best_extra_goals.Length; ++_j) { __best_extra_goals[_j] = builder.CreateString(_o.BestExtraGoals[_j]); }
      _best_extra_goals = CreateBestExtraGoalsVector(builder, __best_extra_goals);
    }
    var _assist_loss_streak_range = default(VectorOffset);
    if (_o.AssistLossStreakRange != null) {
      var __assist_loss_streak_range = _o.AssistLossStreakRange.ToArray();
      _assist_loss_streak_range = CreateAssistLossStreakRangeVector(builder, __assist_loss_streak_range);
    }
    var _assist_system_uid = _o.AssistSystemUid == null ? default(StringOffset) : builder.CreateString(_o.AssistSystemUid);
    var _hash = _o.Hash == null ? default(StringOffset) : builder.CreateString(_o.Hash);
    return CreateLevelConfig(
      builder,
      _uid,
      _name,
      _location_uid,
      _file_names,
      _position,
      _links,
      _size,
      _reward_icons,
      _startup_boosts,
      _eligible_boosts,
      _poi_entity_uid,
      _goals_description,
      _o.GoodTurns,
      _o.BetterTurns,
      _o.BestTurns,
      _o.GoodScore,
      _o.BetterScore,
      _o.BestScore,
      _good_tile_kind_goals_fb,
      _better_tile_kind_goals_fb,
      _best_tile_kind_goals_fb,
      _good_reward,
      _better_reward,
      _best_reward,
      _related_trading_card_uid,
      _score_coefs,
      _wonder_goal_id,
      _decoration_uid,
      _related_quest_uid,
      _related_quest_icon,
      _complete_reward,
      _good_extra_goals,
      _better_extra_goals,
      _best_extra_goals,
      _assist_loss_streak_range,
      _o.SortOrder,
      _o.VisitorTileState,
      _o.ReportingLevelNum,
      _o.TargetWinRate,
      _o.TargetWinRateT2,
      _o.TargetWinRateT3,
      _o.ExtraBoostersTurn,
      _o.Difficulty,
      _assist_system_uid,
      _hash);
  }
}

public class LevelConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string LocationUid { get; set; }
  public List<string> FileNames { get; set; }
  public FBConfig.PointT Position { get; set; }
  public List<FBConfig.LevelLinkT> Links { get; set; }
  public FBConfig.PointT Size { get; set; }
  public List<FBConfig.RewardIconsT> RewardIcons { get; set; }
  public List<string> StartupBoosts { get; set; }
  public List<string> EligibleBoosts { get; set; }
  public string PoiEntityUid { get; set; }
  public List<string> GoalsDescription { get; set; }
  public int GoodTurns { get; set; }
  public int BetterTurns { get; set; }
  public int BestTurns { get; set; }
  public int GoodScore { get; set; }
  public int BetterScore { get; set; }
  public int BestScore { get; set; }
  public List<FBConfig.DictStringIntT> GoodTileKindGoalsFb { get; set; }
  public List<FBConfig.DictStringIntT> BetterTileKindGoalsFb { get; set; }
  public List<FBConfig.DictStringIntT> BestTileKindGoalsFb { get; set; }
  public string GoodReward { get; set; }
  public string BetterReward { get; set; }
  public string BestReward { get; set; }
  public string RelatedTradingCardUid { get; set; }
  public List<float> ScoreCoefs { get; set; }
  public string WonderGoalId { get; set; }
  public string DecorationUid { get; set; }
  public string RelatedQuestUid { get; set; }
  public string RelatedQuestIcon { get; set; }
  public string CompleteReward { get; set; }
  public List<string> GoodExtraGoals { get; set; }
  public List<string> BetterExtraGoals { get; set; }
  public List<string> BestExtraGoals { get; set; }
  public List<int> AssistLossStreakRange { get; set; }
  public float SortOrder { get; set; }
  public int VisitorTileState { get; set; }
  public float ReportingLevelNum { get; set; }
  public int TargetWinRate { get; set; }
  public int TargetWinRateT2 { get; set; }
  public int TargetWinRateT3 { get; set; }
  public int ExtraBoostersTurn { get; set; }
  public int Difficulty { get; set; }
  public string AssistSystemUid { get; set; }
  public string Hash { get; set; }

  public LevelConfigT() {
    this.Uid = null;
    this.Name = null;
    this.LocationUid = null;
    this.FileNames = null;
    this.Position = null;
    this.Links = null;
    this.Size = null;
    this.RewardIcons = null;
    this.StartupBoosts = null;
    this.EligibleBoosts = null;
    this.PoiEntityUid = null;
    this.GoalsDescription = null;
    this.GoodTurns = 0;
    this.BetterTurns = 0;
    this.BestTurns = 0;
    this.GoodScore = 0;
    this.BetterScore = 0;
    this.BestScore = 0;
    this.GoodTileKindGoalsFb = null;
    this.BetterTileKindGoalsFb = null;
    this.BestTileKindGoalsFb = null;
    this.GoodReward = null;
    this.BetterReward = null;
    this.BestReward = null;
    this.RelatedTradingCardUid = null;
    this.ScoreCoefs = null;
    this.WonderGoalId = null;
    this.DecorationUid = null;
    this.RelatedQuestUid = null;
    this.RelatedQuestIcon = null;
    this.CompleteReward = null;
    this.GoodExtraGoals = null;
    this.BetterExtraGoals = null;
    this.BestExtraGoals = null;
    this.AssistLossStreakRange = null;
    this.SortOrder = 0.0f;
    this.VisitorTileState = 0;
    this.ReportingLevelNum = 0.0f;
    this.TargetWinRate = 0;
    this.TargetWinRateT2 = 0;
    this.TargetWinRateT3 = 0;
    this.ExtraBoostersTurn = 0;
    this.Difficulty = 0;
    this.AssistSystemUid = null;
    this.Hash = null;
  }
}

public struct LevelConfigDict : IFlatbufferConfigDict<LevelConfig, LevelConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelConfigDict GetRootAsLevelConfigDict(ByteBuffer _bb) { return GetRootAsLevelConfigDict(_bb, new LevelConfigDict()); }
  public static LevelConfigDict GetRootAsLevelConfigDict(ByteBuffer _bb, LevelConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LevelConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LevelConfig?)(new FBConfig.LevelConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LevelConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LevelConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LevelConfigDict> CreateLevelConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LevelConfigDict.AddValues(builder, valuesOffset);
    return LevelConfigDict.EndLevelConfigDict(builder);
  }

  public static void StartLevelConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LevelConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LevelConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LevelConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LevelConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelConfigDict> EndLevelConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LevelConfigDict>(o);
  }
  public static void FinishLevelConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLevelConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LevelConfigDictT UnPack() {
    var _o = new LevelConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelConfigDictT _o) {
    _o.Values = new List<FBConfig.LevelConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LevelConfigDict> Pack(FlatBufferBuilder builder, LevelConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LevelConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LevelConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLevelConfigDict(
      builder,
      _values);
  }
}

public class LevelConfigDictT
{
  public List<FBConfig.LevelConfigT> Values { get; set; }

  public LevelConfigDictT() {
    this.Values = null;
  }
  public static LevelConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LevelConfigDict.GetRootAsLevelConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LevelConfigDict.FinishLevelConfigDictBuffer(fbb, LevelConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
