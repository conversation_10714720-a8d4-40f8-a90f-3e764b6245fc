// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DictStringFloat : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DictStringFloat GetRootAsDictStringFloat(ByteBuffer _bb) { return GetRootAsDictStringFloat(_bb, new DictStringFloat()); }
  public static DictStringFloat GetRootAsDictStringFloat(ByteBuffer _bb, DictStringFloat obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DictStringFloat __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Key { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetKeyBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetKeyBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetKeyArray() { return __p.__vector_as_array<byte>(4); }
  public float Value { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateValue(float value) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, value); return true; } else { return false; } }

  public static Offset<FBConfig.DictStringFloat> CreateDictStringFloat(FlatBufferBuilder builder,
      StringOffset keyOffset = default(StringOffset),
      float value = 0.0f) {
    builder.StartTable(2);
    DictStringFloat.AddValue(builder, value);
    DictStringFloat.AddKey(builder, keyOffset);
    return DictStringFloat.EndDictStringFloat(builder);
  }

  public static void StartDictStringFloat(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddKey(FlatBufferBuilder builder, StringOffset keyOffset) { builder.AddOffset(0, keyOffset.Value, 0); }
  public static void AddValue(FlatBufferBuilder builder, float value) { builder.AddFloat(1, value, 0.0f); }
  public static Offset<FBConfig.DictStringFloat> EndDictStringFloat(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DictStringFloat>(o);
  }
  public DictStringFloatT UnPack() {
    var _o = new DictStringFloatT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DictStringFloatT _o) {
    _o.Key = this.Key;
    _o.Value = this.Value;
  }
  public static Offset<FBConfig.DictStringFloat> Pack(FlatBufferBuilder builder, DictStringFloatT _o) {
    if (_o == null) return default(Offset<FBConfig.DictStringFloat>);
    var _key = _o.Key == null ? default(StringOffset) : builder.CreateString(_o.Key);
    return CreateDictStringFloat(
      builder,
      _key,
      _o.Value);
  }
}

public class DictStringFloatT
{
  public string Key { get; set; }
  public float Value { get; set; }

  public DictStringFloatT() {
    this.Key = null;
    this.Value = 0.0f;
  }
}


}
