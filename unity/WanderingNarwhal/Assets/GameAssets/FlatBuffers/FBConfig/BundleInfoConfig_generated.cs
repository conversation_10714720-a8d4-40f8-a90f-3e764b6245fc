// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct BundleInfoConfig : IFlatbufferConfig<BundleInfoConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BundleInfoConfig GetRootAsBundleInfoConfig(ByteBuffer _bb) { return GetRootAsBundleInfoConfig(_bb, new BundleInfoConfig()); }
  public static BundleInfoConfig GetRootAsBundleInfoConfig(ByteBuffer _bb, BundleInfoConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BundleInfoConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Version { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateVersion(int version) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, version); return true; } else { return false; } }
  public string Dependencies(int j) { int o = __p.__offset(8); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int DependenciesLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string Hash { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHashBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetHashBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetHashArray() { return __p.__vector_as_array<byte>(10); }
  public string BundleType { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBundleTypeBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetBundleTypeBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetBundleTypeArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.BundleInfoConfig> CreateBundleInfoConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int version = 0,
      VectorOffset dependenciesOffset = default(VectorOffset),
      StringOffset hashOffset = default(StringOffset),
      StringOffset bundle_typeOffset = default(StringOffset)) {
    builder.StartTable(5);
    BundleInfoConfig.AddBundleType(builder, bundle_typeOffset);
    BundleInfoConfig.AddHash(builder, hashOffset);
    BundleInfoConfig.AddDependencies(builder, dependenciesOffset);
    BundleInfoConfig.AddVersion(builder, version);
    BundleInfoConfig.AddUid(builder, uidOffset);
    return BundleInfoConfig.EndBundleInfoConfig(builder);
  }

  public static void StartBundleInfoConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddVersion(FlatBufferBuilder builder, int version) { builder.AddInt(1, version, 0); }
  public static void AddDependencies(FlatBufferBuilder builder, VectorOffset dependenciesOffset) { builder.AddOffset(2, dependenciesOffset.Value, 0); }
  public static VectorOffset CreateDependenciesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDependenciesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddHash(FlatBufferBuilder builder, StringOffset hashOffset) { builder.AddOffset(3, hashOffset.Value, 0); }
  public static void AddBundleType(FlatBufferBuilder builder, StringOffset bundleTypeOffset) { builder.AddOffset(4, bundleTypeOffset.Value, 0); }
  public static Offset<FBConfig.BundleInfoConfig> EndBundleInfoConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.BundleInfoConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfBundleInfoConfig(FlatBufferBuilder builder, Offset<BundleInfoConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<BundleInfoConfig> o1, Offset<BundleInfoConfig> o2) =>
        new BundleInfoConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new BundleInfoConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static BundleInfoConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    BundleInfoConfig obj_ = new BundleInfoConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public BundleInfoConfigT UnPack() {
    var _o = new BundleInfoConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BundleInfoConfigT _o) {
    _o.Uid = this.Uid;
    _o.Version = this.Version;
    _o.Dependencies = new List<string>();
    for (var _j = 0; _j < this.DependenciesLength; ++_j) {_o.Dependencies.Add(this.Dependencies(_j));}
    _o.Hash = this.Hash;
    _o.BundleType = this.BundleType;
  }
  public static Offset<FBConfig.BundleInfoConfig> Pack(FlatBufferBuilder builder, BundleInfoConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.BundleInfoConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _dependencies = default(VectorOffset);
    if (_o.Dependencies != null) {
      var __dependencies = new StringOffset[_o.Dependencies.Count];
      for (var _j = 0; _j < __dependencies.Length; ++_j) { __dependencies[_j] = builder.CreateString(_o.Dependencies[_j]); }
      _dependencies = CreateDependenciesVector(builder, __dependencies);
    }
    var _hash = _o.Hash == null ? default(StringOffset) : builder.CreateString(_o.Hash);
    var _bundle_type = _o.BundleType == null ? default(StringOffset) : builder.CreateString(_o.BundleType);
    return CreateBundleInfoConfig(
      builder,
      _uid,
      _o.Version,
      _dependencies,
      _hash,
      _bundle_type);
  }
}

public class BundleInfoConfigT
{
  public string Uid { get; set; }
  public int Version { get; set; }
  public List<string> Dependencies { get; set; }
  public string Hash { get; set; }
  public string BundleType { get; set; }

  public BundleInfoConfigT() {
    this.Uid = null;
    this.Version = 0;
    this.Dependencies = null;
    this.Hash = null;
    this.BundleType = null;
  }
}

public struct BundleInfoConfigDict : IFlatbufferConfigDict<BundleInfoConfig, BundleInfoConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BundleInfoConfigDict GetRootAsBundleInfoConfigDict(ByteBuffer _bb) { return GetRootAsBundleInfoConfigDict(_bb, new BundleInfoConfigDict()); }
  public static BundleInfoConfigDict GetRootAsBundleInfoConfigDict(ByteBuffer _bb, BundleInfoConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BundleInfoConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.BundleInfoConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.BundleInfoConfig?)(new FBConfig.BundleInfoConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.BundleInfoConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.BundleInfoConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.BundleInfoConfigDict> CreateBundleInfoConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    BundleInfoConfigDict.AddValues(builder, valuesOffset);
    return BundleInfoConfigDict.EndBundleInfoConfigDict(builder);
  }

  public static void StartBundleInfoConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.BundleInfoConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.BundleInfoConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.BundleInfoConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.BundleInfoConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.BundleInfoConfigDict> EndBundleInfoConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.BundleInfoConfigDict>(o);
  }
  public static void FinishBundleInfoConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.BundleInfoConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedBundleInfoConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.BundleInfoConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public BundleInfoConfigDictT UnPack() {
    var _o = new BundleInfoConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BundleInfoConfigDictT _o) {
    _o.Values = new List<FBConfig.BundleInfoConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.BundleInfoConfigDict> Pack(FlatBufferBuilder builder, BundleInfoConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.BundleInfoConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.BundleInfoConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.BundleInfoConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateBundleInfoConfigDict(
      builder,
      _values);
  }
}

public class BundleInfoConfigDictT
{
  public List<FBConfig.BundleInfoConfigT> Values { get; set; }

  public BundleInfoConfigDictT() {
    this.Values = null;
  }
  public static BundleInfoConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return BundleInfoConfigDict.GetRootAsBundleInfoConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    BundleInfoConfigDict.FinishBundleInfoConfigDictBuffer(fbb, BundleInfoConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
