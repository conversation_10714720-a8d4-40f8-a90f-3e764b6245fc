// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct VIPProductsMetaConfg : IFlatbufferConfig<VIPProductsMetaConfgT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static VIPProductsMetaConfg GetRootAsVIPProductsMetaConfg(ByteBuffer _bb) { return GetRootAsVIPProductsMetaConfg(_bb, new VIPProductsMetaConfg()); }
  public static VIPProductsMetaConfg GetRootAsVIPProductsMetaConfg(ByteBuffer _bb, VIPProductsMetaConfg obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public VIPProductsMetaConfg __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int VipCoinsPerCity { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateVipCoinsPerCity(int vip_coins_per_city) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, vip_coins_per_city); return true; } else { return false; } }
  public string FallbackSupportEmail { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFallbackSupportEmailBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetFallbackSupportEmailBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetFallbackSupportEmailArray() { return __p.__vector_as_array<byte>(8); }
  public string DevAppToken { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDevAppTokenBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetDevAppTokenBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetDevAppTokenArray() { return __p.__vector_as_array<byte>(10); }
  public string ProdAppToken { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetProdAppTokenBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetProdAppTokenBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetProdAppTokenArray() { return __p.__vector_as_array<byte>(12); }
  public bool EnableHud { get { int o = __p.__offset(14); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateEnableHud(bool enable_hud) { int o = __p.__offset(14); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(enable_hud ? 1 : 0)); return true; } else { return false; } }
  public int VipCoinsPerWonder { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateVipCoinsPerWonder(int vip_coins_per_wonder) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, vip_coins_per_wonder); return true; } else { return false; } }

  public static Offset<FBConfig.VIPProductsMetaConfg> CreateVIPProductsMetaConfg(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int vip_coins_per_city = 0,
      StringOffset fallback_support_emailOffset = default(StringOffset),
      StringOffset dev_app_tokenOffset = default(StringOffset),
      StringOffset prod_app_tokenOffset = default(StringOffset),
      bool enable_hud = false,
      int vip_coins_per_wonder = 0) {
    builder.StartTable(7);
    VIPProductsMetaConfg.AddVipCoinsPerWonder(builder, vip_coins_per_wonder);
    VIPProductsMetaConfg.AddProdAppToken(builder, prod_app_tokenOffset);
    VIPProductsMetaConfg.AddDevAppToken(builder, dev_app_tokenOffset);
    VIPProductsMetaConfg.AddFallbackSupportEmail(builder, fallback_support_emailOffset);
    VIPProductsMetaConfg.AddVipCoinsPerCity(builder, vip_coins_per_city);
    VIPProductsMetaConfg.AddUid(builder, uidOffset);
    VIPProductsMetaConfg.AddEnableHud(builder, enable_hud);
    return VIPProductsMetaConfg.EndVIPProductsMetaConfg(builder);
  }

  public static void StartVIPProductsMetaConfg(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddVipCoinsPerCity(FlatBufferBuilder builder, int vipCoinsPerCity) { builder.AddInt(1, vipCoinsPerCity, 0); }
  public static void AddFallbackSupportEmail(FlatBufferBuilder builder, StringOffset fallbackSupportEmailOffset) { builder.AddOffset(2, fallbackSupportEmailOffset.Value, 0); }
  public static void AddDevAppToken(FlatBufferBuilder builder, StringOffset devAppTokenOffset) { builder.AddOffset(3, devAppTokenOffset.Value, 0); }
  public static void AddProdAppToken(FlatBufferBuilder builder, StringOffset prodAppTokenOffset) { builder.AddOffset(4, prodAppTokenOffset.Value, 0); }
  public static void AddEnableHud(FlatBufferBuilder builder, bool enableHud) { builder.AddBool(5, enableHud, false); }
  public static void AddVipCoinsPerWonder(FlatBufferBuilder builder, int vipCoinsPerWonder) { builder.AddInt(6, vipCoinsPerWonder, 0); }
  public static Offset<FBConfig.VIPProductsMetaConfg> EndVIPProductsMetaConfg(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.VIPProductsMetaConfg>(o);
  }

  public static VectorOffset CreateSortedVectorOfVIPProductsMetaConfg(FlatBufferBuilder builder, Offset<VIPProductsMetaConfg>[] offsets) {
    Array.Sort(offsets,
      (Offset<VIPProductsMetaConfg> o1, Offset<VIPProductsMetaConfg> o2) =>
        new VIPProductsMetaConfg().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new VIPProductsMetaConfg().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static VIPProductsMetaConfg? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    VIPProductsMetaConfg obj_ = new VIPProductsMetaConfg();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public VIPProductsMetaConfgT UnPack() {
    var _o = new VIPProductsMetaConfgT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(VIPProductsMetaConfgT _o) {
    _o.Uid = this.Uid;
    _o.VipCoinsPerCity = this.VipCoinsPerCity;
    _o.FallbackSupportEmail = this.FallbackSupportEmail;
    _o.DevAppToken = this.DevAppToken;
    _o.ProdAppToken = this.ProdAppToken;
    _o.EnableHud = this.EnableHud;
    _o.VipCoinsPerWonder = this.VipCoinsPerWonder;
  }
  public static Offset<FBConfig.VIPProductsMetaConfg> Pack(FlatBufferBuilder builder, VIPProductsMetaConfgT _o) {
    if (_o == null) return default(Offset<FBConfig.VIPProductsMetaConfg>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _fallback_support_email = _o.FallbackSupportEmail == null ? default(StringOffset) : builder.CreateString(_o.FallbackSupportEmail);
    var _dev_app_token = _o.DevAppToken == null ? default(StringOffset) : builder.CreateString(_o.DevAppToken);
    var _prod_app_token = _o.ProdAppToken == null ? default(StringOffset) : builder.CreateString(_o.ProdAppToken);
    return CreateVIPProductsMetaConfg(
      builder,
      _uid,
      _o.VipCoinsPerCity,
      _fallback_support_email,
      _dev_app_token,
      _prod_app_token,
      _o.EnableHud,
      _o.VipCoinsPerWonder);
  }
}

public class VIPProductsMetaConfgT
{
  public string Uid { get; set; }
  public int VipCoinsPerCity { get; set; }
  public string FallbackSupportEmail { get; set; }
  public string DevAppToken { get; set; }
  public string ProdAppToken { get; set; }
  public bool EnableHud { get; set; }
  public int VipCoinsPerWonder { get; set; }

  public VIPProductsMetaConfgT() {
    this.Uid = null;
    this.VipCoinsPerCity = 0;
    this.FallbackSupportEmail = null;
    this.DevAppToken = null;
    this.ProdAppToken = null;
    this.EnableHud = false;
    this.VipCoinsPerWonder = 0;
  }
}

public struct VIPProductsMetaConfgDict : IFlatbufferConfigDict<VIPProductsMetaConfg, VIPProductsMetaConfgT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static VIPProductsMetaConfgDict GetRootAsVIPProductsMetaConfgDict(ByteBuffer _bb) { return GetRootAsVIPProductsMetaConfgDict(_bb, new VIPProductsMetaConfgDict()); }
  public static VIPProductsMetaConfgDict GetRootAsVIPProductsMetaConfgDict(ByteBuffer _bb, VIPProductsMetaConfgDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public VIPProductsMetaConfgDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.VIPProductsMetaConfg? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.VIPProductsMetaConfg?)(new FBConfig.VIPProductsMetaConfg()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.VIPProductsMetaConfg? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.VIPProductsMetaConfg.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.VIPProductsMetaConfgDict> CreateVIPProductsMetaConfgDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    VIPProductsMetaConfgDict.AddValues(builder, valuesOffset);
    return VIPProductsMetaConfgDict.EndVIPProductsMetaConfgDict(builder);
  }

  public static void StartVIPProductsMetaConfgDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsMetaConfg>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsMetaConfg>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.VIPProductsMetaConfg>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.VIPProductsMetaConfg>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.VIPProductsMetaConfgDict> EndVIPProductsMetaConfgDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.VIPProductsMetaConfgDict>(o);
  }
  public static void FinishVIPProductsMetaConfgDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsMetaConfgDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedVIPProductsMetaConfgDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsMetaConfgDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public VIPProductsMetaConfgDictT UnPack() {
    var _o = new VIPProductsMetaConfgDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(VIPProductsMetaConfgDictT _o) {
    _o.Values = new List<FBConfig.VIPProductsMetaConfgT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.VIPProductsMetaConfgDict> Pack(FlatBufferBuilder builder, VIPProductsMetaConfgDictT _o) {
    if (_o == null) return default(Offset<FBConfig.VIPProductsMetaConfgDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.VIPProductsMetaConfg>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.VIPProductsMetaConfg.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateVIPProductsMetaConfgDict(
      builder,
      _values);
  }
}

public class VIPProductsMetaConfgDictT
{
  public List<FBConfig.VIPProductsMetaConfgT> Values { get; set; }

  public VIPProductsMetaConfgDictT() {
    this.Values = null;
  }
  public static VIPProductsMetaConfgDictT DeserializeFromBinary(byte[] fbBuffer) {
    return VIPProductsMetaConfgDict.GetRootAsVIPProductsMetaConfgDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    VIPProductsMetaConfgDict.FinishVIPProductsMetaConfgDictBuffer(fbb, VIPProductsMetaConfgDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
