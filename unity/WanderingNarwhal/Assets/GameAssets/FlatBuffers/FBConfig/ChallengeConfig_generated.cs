// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ChallengeConfig : IFlatbufferConfig<ChallengeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeConfig GetRootAsChallengeConfig(ByteBuffer _bb) { return GetRootAsChallengeConfig(_bb, new ChallengeConfig()); }
  public static ChallengeConfig GetRootAsChallengeConfig(ByteBuffer _bb, ChallengeConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int TotalWinsCount { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTotalWinsCount(int total_wins_count) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, total_wins_count); return true; } else { return false; } }
  public int MaxActiveChallenges { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxActiveChallenges(int max_active_challenges) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_active_challenges); return true; } else { return false; } }
  public bool Enabled { get { int o = __p.__offset(10); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateEnabled(bool enabled) { int o = __p.__offset(10); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(enabled ? 1 : 0)); return true; } else { return false; } }
  public bool TriviaEnabled { get { int o = __p.__offset(12); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateTriviaEnabled(bool trivia_enabled) { int o = __p.__offset(12); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(trivia_enabled ? 1 : 0)); return true; } else { return false; } }
  public int SdbEffectValue { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSdbEffectValue(int sdb_effect_value) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, sdb_effect_value); return true; } else { return false; } }
  public int ChallengeMaxRounds { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateChallengeMaxRounds(int challenge_max_rounds) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, challenge_max_rounds); return true; } else { return false; } }
  public int ChallengeExpirationHours { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateChallengeExpirationHours(int challenge_expiration_hours) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, challenge_expiration_hours); return true; } else { return false; } }
  public int ChallengeNudgeHours { get { int o = __p.__offset(20); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateChallengeNudgeHours(int challenge_nudge_hours) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, challenge_nudge_hours); return true; } else { return false; } }
  public int SuggestionExpirationHours { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSuggestionExpirationHours(int suggestion_expiration_hours) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, suggestion_expiration_hours); return true; } else { return false; } }

  public static Offset<FBConfig.ChallengeConfig> CreateChallengeConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int total_wins_count = 0,
      int max_active_challenges = 0,
      bool enabled = false,
      bool trivia_enabled = false,
      int sdb_effect_value = 0,
      int challenge_max_rounds = 0,
      int challenge_expiration_hours = 0,
      int challenge_nudge_hours = 0,
      int suggestion_expiration_hours = 0) {
    builder.StartTable(10);
    ChallengeConfig.AddSuggestionExpirationHours(builder, suggestion_expiration_hours);
    ChallengeConfig.AddChallengeNudgeHours(builder, challenge_nudge_hours);
    ChallengeConfig.AddChallengeExpirationHours(builder, challenge_expiration_hours);
    ChallengeConfig.AddChallengeMaxRounds(builder, challenge_max_rounds);
    ChallengeConfig.AddSdbEffectValue(builder, sdb_effect_value);
    ChallengeConfig.AddMaxActiveChallenges(builder, max_active_challenges);
    ChallengeConfig.AddTotalWinsCount(builder, total_wins_count);
    ChallengeConfig.AddUid(builder, uidOffset);
    ChallengeConfig.AddTriviaEnabled(builder, trivia_enabled);
    ChallengeConfig.AddEnabled(builder, enabled);
    return ChallengeConfig.EndChallengeConfig(builder);
  }

  public static void StartChallengeConfig(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTotalWinsCount(FlatBufferBuilder builder, int totalWinsCount) { builder.AddInt(1, totalWinsCount, 0); }
  public static void AddMaxActiveChallenges(FlatBufferBuilder builder, int maxActiveChallenges) { builder.AddInt(2, maxActiveChallenges, 0); }
  public static void AddEnabled(FlatBufferBuilder builder, bool enabled) { builder.AddBool(3, enabled, false); }
  public static void AddTriviaEnabled(FlatBufferBuilder builder, bool triviaEnabled) { builder.AddBool(4, triviaEnabled, false); }
  public static void AddSdbEffectValue(FlatBufferBuilder builder, int sdbEffectValue) { builder.AddInt(5, sdbEffectValue, 0); }
  public static void AddChallengeMaxRounds(FlatBufferBuilder builder, int challengeMaxRounds) { builder.AddInt(6, challengeMaxRounds, 0); }
  public static void AddChallengeExpirationHours(FlatBufferBuilder builder, int challengeExpirationHours) { builder.AddInt(7, challengeExpirationHours, 0); }
  public static void AddChallengeNudgeHours(FlatBufferBuilder builder, int challengeNudgeHours) { builder.AddInt(8, challengeNudgeHours, 0); }
  public static void AddSuggestionExpirationHours(FlatBufferBuilder builder, int suggestionExpirationHours) { builder.AddInt(9, suggestionExpirationHours, 0); }
  public static Offset<FBConfig.ChallengeConfig> EndChallengeConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ChallengeConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfChallengeConfig(FlatBufferBuilder builder, Offset<ChallengeConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ChallengeConfig> o1, Offset<ChallengeConfig> o2) =>
        new ChallengeConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ChallengeConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ChallengeConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ChallengeConfig obj_ = new ChallengeConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ChallengeConfigT UnPack() {
    var _o = new ChallengeConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeConfigT _o) {
    _o.Uid = this.Uid;
    _o.TotalWinsCount = this.TotalWinsCount;
    _o.MaxActiveChallenges = this.MaxActiveChallenges;
    _o.Enabled = this.Enabled;
    _o.TriviaEnabled = this.TriviaEnabled;
    _o.SdbEffectValue = this.SdbEffectValue;
    _o.ChallengeMaxRounds = this.ChallengeMaxRounds;
    _o.ChallengeExpirationHours = this.ChallengeExpirationHours;
    _o.ChallengeNudgeHours = this.ChallengeNudgeHours;
    _o.SuggestionExpirationHours = this.SuggestionExpirationHours;
  }
  public static Offset<FBConfig.ChallengeConfig> Pack(FlatBufferBuilder builder, ChallengeConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateChallengeConfig(
      builder,
      _uid,
      _o.TotalWinsCount,
      _o.MaxActiveChallenges,
      _o.Enabled,
      _o.TriviaEnabled,
      _o.SdbEffectValue,
      _o.ChallengeMaxRounds,
      _o.ChallengeExpirationHours,
      _o.ChallengeNudgeHours,
      _o.SuggestionExpirationHours);
  }
}

public class ChallengeConfigT
{
  public string Uid { get; set; }
  public int TotalWinsCount { get; set; }
  public int MaxActiveChallenges { get; set; }
  public bool Enabled { get; set; }
  public bool TriviaEnabled { get; set; }
  public int SdbEffectValue { get; set; }
  public int ChallengeMaxRounds { get; set; }
  public int ChallengeExpirationHours { get; set; }
  public int ChallengeNudgeHours { get; set; }
  public int SuggestionExpirationHours { get; set; }

  public ChallengeConfigT() {
    this.Uid = null;
    this.TotalWinsCount = 0;
    this.MaxActiveChallenges = 0;
    this.Enabled = false;
    this.TriviaEnabled = false;
    this.SdbEffectValue = 0;
    this.ChallengeMaxRounds = 0;
    this.ChallengeExpirationHours = 0;
    this.ChallengeNudgeHours = 0;
    this.SuggestionExpirationHours = 0;
  }
}

public struct ChallengeConfigDict : IFlatbufferConfigDict<ChallengeConfig, ChallengeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeConfigDict GetRootAsChallengeConfigDict(ByteBuffer _bb) { return GetRootAsChallengeConfigDict(_bb, new ChallengeConfigDict()); }
  public static ChallengeConfigDict GetRootAsChallengeConfigDict(ByteBuffer _bb, ChallengeConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ChallengeConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ChallengeConfig?)(new FBConfig.ChallengeConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ChallengeConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ChallengeConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ChallengeConfigDict> CreateChallengeConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ChallengeConfigDict.AddValues(builder, valuesOffset);
    return ChallengeConfigDict.EndChallengeConfigDict(builder);
  }

  public static void StartChallengeConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ChallengeConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ChallengeConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ChallengeConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ChallengeConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ChallengeConfigDict> EndChallengeConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ChallengeConfigDict>(o);
  }
  public static void FinishChallengeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedChallengeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ChallengeConfigDictT UnPack() {
    var _o = new ChallengeConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeConfigDictT _o) {
    _o.Values = new List<FBConfig.ChallengeConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ChallengeConfigDict> Pack(FlatBufferBuilder builder, ChallengeConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ChallengeConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ChallengeConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateChallengeConfigDict(
      builder,
      _values);
  }
}

public class ChallengeConfigDictT
{
  public List<FBConfig.ChallengeConfigT> Values { get; set; }

  public ChallengeConfigDictT() {
    this.Values = null;
  }
  public static ChallengeConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ChallengeConfigDict.GetRootAsChallengeConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ChallengeConfigDict.FinishChallengeConfigDictBuffer(fbb, ChallengeConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
