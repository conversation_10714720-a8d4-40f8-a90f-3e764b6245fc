// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DailyTasksLocalNotifierSettings : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DailyTasksLocalNotifierSettings GetRootAsDailyTasksLocalNotifierSettings(ByteBuffer _bb) { return GetRootAsDailyTasksLocalNotifierSettings(_bb, new DailyTasksLocalNotifierSettings()); }
  public static DailyTasksLocalNotifierSettings GetRootAsDailyTasksLocalNotifierSettings(ByteBuffer _bb, DailyTasksLocalNotifierSettings obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DailyTasksLocalNotifierSettings __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int TimeOffsetInMinutes { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTimeOffsetInMinutes(int time_offset_in_minutes) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, time_offset_in_minutes); return true; } else { return false; } }
  public int StreakToNotify { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateStreakToNotify(int streak_to_notify) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, streak_to_notify); return true; } else { return false; } }
  public string NotifierHeaderLocalizedKey { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNotifierHeaderLocalizedKeyBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetNotifierHeaderLocalizedKeyBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetNotifierHeaderLocalizedKeyArray() { return __p.__vector_as_array<byte>(10); }
  public string NotifierBodyLocalizedKey { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNotifierBodyLocalizedKeyBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetNotifierBodyLocalizedKeyBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetNotifierBodyLocalizedKeyArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.DailyTasksLocalNotifierSettings> CreateDailyTasksLocalNotifierSettings(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int time_offset_in_minutes = 0,
      int streak_to_notify = 0,
      StringOffset notifier_header_localized_keyOffset = default(StringOffset),
      StringOffset notifier_body_localized_keyOffset = default(StringOffset)) {
    builder.StartTable(5);
    DailyTasksLocalNotifierSettings.AddNotifierBodyLocalizedKey(builder, notifier_body_localized_keyOffset);
    DailyTasksLocalNotifierSettings.AddNotifierHeaderLocalizedKey(builder, notifier_header_localized_keyOffset);
    DailyTasksLocalNotifierSettings.AddStreakToNotify(builder, streak_to_notify);
    DailyTasksLocalNotifierSettings.AddTimeOffsetInMinutes(builder, time_offset_in_minutes);
    DailyTasksLocalNotifierSettings.AddUid(builder, uidOffset);
    return DailyTasksLocalNotifierSettings.EndDailyTasksLocalNotifierSettings(builder);
  }

  public static void StartDailyTasksLocalNotifierSettings(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTimeOffsetInMinutes(FlatBufferBuilder builder, int timeOffsetInMinutes) { builder.AddInt(1, timeOffsetInMinutes, 0); }
  public static void AddStreakToNotify(FlatBufferBuilder builder, int streakToNotify) { builder.AddInt(2, streakToNotify, 0); }
  public static void AddNotifierHeaderLocalizedKey(FlatBufferBuilder builder, StringOffset notifierHeaderLocalizedKeyOffset) { builder.AddOffset(3, notifierHeaderLocalizedKeyOffset.Value, 0); }
  public static void AddNotifierBodyLocalizedKey(FlatBufferBuilder builder, StringOffset notifierBodyLocalizedKeyOffset) { builder.AddOffset(4, notifierBodyLocalizedKeyOffset.Value, 0); }
  public static Offset<FBConfig.DailyTasksLocalNotifierSettings> EndDailyTasksLocalNotifierSettings(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DailyTasksLocalNotifierSettings>(o);
  }
  public DailyTasksLocalNotifierSettingsT UnPack() {
    var _o = new DailyTasksLocalNotifierSettingsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DailyTasksLocalNotifierSettingsT _o) {
    _o.Uid = this.Uid;
    _o.TimeOffsetInMinutes = this.TimeOffsetInMinutes;
    _o.StreakToNotify = this.StreakToNotify;
    _o.NotifierHeaderLocalizedKey = this.NotifierHeaderLocalizedKey;
    _o.NotifierBodyLocalizedKey = this.NotifierBodyLocalizedKey;
  }
  public static Offset<FBConfig.DailyTasksLocalNotifierSettings> Pack(FlatBufferBuilder builder, DailyTasksLocalNotifierSettingsT _o) {
    if (_o == null) return default(Offset<FBConfig.DailyTasksLocalNotifierSettings>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _notifier_header_localized_key = _o.NotifierHeaderLocalizedKey == null ? default(StringOffset) : builder.CreateString(_o.NotifierHeaderLocalizedKey);
    var _notifier_body_localized_key = _o.NotifierBodyLocalizedKey == null ? default(StringOffset) : builder.CreateString(_o.NotifierBodyLocalizedKey);
    return CreateDailyTasksLocalNotifierSettings(
      builder,
      _uid,
      _o.TimeOffsetInMinutes,
      _o.StreakToNotify,
      _notifier_header_localized_key,
      _notifier_body_localized_key);
  }
}

public class DailyTasksLocalNotifierSettingsT
{
  public string Uid { get; set; }
  public int TimeOffsetInMinutes { get; set; }
  public int StreakToNotify { get; set; }
  public string NotifierHeaderLocalizedKey { get; set; }
  public string NotifierBodyLocalizedKey { get; set; }

  public DailyTasksLocalNotifierSettingsT() {
    this.Uid = null;
    this.TimeOffsetInMinutes = 0;
    this.StreakToNotify = 0;
    this.NotifierHeaderLocalizedKey = null;
    this.NotifierBodyLocalizedKey = null;
  }
}

public struct DailyTaskSettingsConfig : IFlatbufferConfig<DailyTaskSettingsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DailyTaskSettingsConfig GetRootAsDailyTaskSettingsConfig(ByteBuffer _bb) { return GetRootAsDailyTaskSettingsConfig(_bb, new DailyTaskSettingsConfig()); }
  public static DailyTaskSettingsConfig GetRootAsDailyTaskSettingsConfig(ByteBuffer _bb, DailyTaskSettingsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DailyTaskSettingsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Streak { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateStreak(int streak) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, streak); return true; } else { return false; } }
  public FBConfig.DictStringInt? DayStreakReward(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int DayStreakRewardLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? CompleteStreakReward(int j) { int o = __p.__offset(10); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int CompleteStreakRewardLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ListString? Categories(int j) { int o = __p.__offset(12); return o != 0 ? (FBConfig.ListString?)(new FBConfig.ListString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int CategoriesLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int ActiveTasks { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateActiveTasks(int active_tasks) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, active_tasks); return true; } else { return false; } }
  public int HistoryDays { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateHistoryDays(int history_days) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, history_days); return true; } else { return false; } }
  public FBConfig.DictStringInt? HistoryDaysPerType(int j) { int o = __p.__offset(18); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int HistoryDaysPerTypeLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int LocalNotifierTimeOffsetInMinutes { get { int o = __p.__offset(20); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLocalNotifierTimeOffsetInMinutes(int local_notifier_time_offset_in_minutes) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, local_notifier_time_offset_in_minutes); return true; } else { return false; } }
  public FBConfig.DailyTasksLocalNotifierSettings? LocalNotifierSettings(int j) { int o = __p.__offset(22); return o != 0 ? (FBConfig.DailyTasksLocalNotifierSettings?)(new FBConfig.DailyTasksLocalNotifierSettings()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int LocalNotifierSettingsLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.DailyTaskSettingsConfig> CreateDailyTaskSettingsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int streak = 0,
      VectorOffset day_streak_rewardOffset = default(VectorOffset),
      VectorOffset complete_streak_rewardOffset = default(VectorOffset),
      VectorOffset categoriesOffset = default(VectorOffset),
      int active_tasks = 0,
      int history_days = 0,
      VectorOffset history_days_per_typeOffset = default(VectorOffset),
      int local_notifier_time_offset_in_minutes = 0,
      VectorOffset local_notifier_settingsOffset = default(VectorOffset)) {
    builder.StartTable(10);
    DailyTaskSettingsConfig.AddLocalNotifierSettings(builder, local_notifier_settingsOffset);
    DailyTaskSettingsConfig.AddLocalNotifierTimeOffsetInMinutes(builder, local_notifier_time_offset_in_minutes);
    DailyTaskSettingsConfig.AddHistoryDaysPerType(builder, history_days_per_typeOffset);
    DailyTaskSettingsConfig.AddHistoryDays(builder, history_days);
    DailyTaskSettingsConfig.AddActiveTasks(builder, active_tasks);
    DailyTaskSettingsConfig.AddCategories(builder, categoriesOffset);
    DailyTaskSettingsConfig.AddCompleteStreakReward(builder, complete_streak_rewardOffset);
    DailyTaskSettingsConfig.AddDayStreakReward(builder, day_streak_rewardOffset);
    DailyTaskSettingsConfig.AddStreak(builder, streak);
    DailyTaskSettingsConfig.AddUid(builder, uidOffset);
    return DailyTaskSettingsConfig.EndDailyTaskSettingsConfig(builder);
  }

  public static void StartDailyTaskSettingsConfig(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddStreak(FlatBufferBuilder builder, int streak) { builder.AddInt(1, streak, 0); }
  public static void AddDayStreakReward(FlatBufferBuilder builder, VectorOffset dayStreakRewardOffset) { builder.AddOffset(2, dayStreakRewardOffset.Value, 0); }
  public static VectorOffset CreateDayStreakRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDayStreakRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDayStreakRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDayStreakRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDayStreakRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddCompleteStreakReward(FlatBufferBuilder builder, VectorOffset completeStreakRewardOffset) { builder.AddOffset(3, completeStreakRewardOffset.Value, 0); }
  public static VectorOffset CreateCompleteStreakRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCompleteStreakRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCompleteStreakRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCompleteStreakRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCompleteStreakRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddCategories(FlatBufferBuilder builder, VectorOffset categoriesOffset) { builder.AddOffset(4, categoriesOffset.Value, 0); }
  public static VectorOffset CreateCategoriesVector(FlatBufferBuilder builder, Offset<FBConfig.ListString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCategoriesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ListString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCategoriesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ListString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCategoriesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ListString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCategoriesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddActiveTasks(FlatBufferBuilder builder, int activeTasks) { builder.AddInt(5, activeTasks, 0); }
  public static void AddHistoryDays(FlatBufferBuilder builder, int historyDays) { builder.AddInt(6, historyDays, 0); }
  public static void AddHistoryDaysPerType(FlatBufferBuilder builder, VectorOffset historyDaysPerTypeOffset) { builder.AddOffset(7, historyDaysPerTypeOffset.Value, 0); }
  public static VectorOffset CreateHistoryDaysPerTypeVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateHistoryDaysPerTypeVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHistoryDaysPerTypeVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHistoryDaysPerTypeVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartHistoryDaysPerTypeVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLocalNotifierTimeOffsetInMinutes(FlatBufferBuilder builder, int localNotifierTimeOffsetInMinutes) { builder.AddInt(8, localNotifierTimeOffsetInMinutes, 0); }
  public static void AddLocalNotifierSettings(FlatBufferBuilder builder, VectorOffset localNotifierSettingsOffset) { builder.AddOffset(9, localNotifierSettingsOffset.Value, 0); }
  public static VectorOffset CreateLocalNotifierSettingsVector(FlatBufferBuilder builder, Offset<FBConfig.DailyTasksLocalNotifierSettings>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLocalNotifierSettingsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DailyTasksLocalNotifierSettings>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLocalNotifierSettingsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DailyTasksLocalNotifierSettings>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLocalNotifierSettingsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DailyTasksLocalNotifierSettings>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLocalNotifierSettingsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.DailyTaskSettingsConfig> EndDailyTaskSettingsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.DailyTaskSettingsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfDailyTaskSettingsConfig(FlatBufferBuilder builder, Offset<DailyTaskSettingsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<DailyTaskSettingsConfig> o1, Offset<DailyTaskSettingsConfig> o2) =>
        new DailyTaskSettingsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new DailyTaskSettingsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static DailyTaskSettingsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    DailyTaskSettingsConfig obj_ = new DailyTaskSettingsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public DailyTaskSettingsConfigT UnPack() {
    var _o = new DailyTaskSettingsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DailyTaskSettingsConfigT _o) {
    _o.Uid = this.Uid;
    _o.Streak = this.Streak;
    _o.DayStreakReward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.DayStreakRewardLength; ++_j) {_o.DayStreakReward.Add(this.DayStreakReward(_j).HasValue ? this.DayStreakReward(_j).Value.UnPack() : null);}
    _o.CompleteStreakReward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.CompleteStreakRewardLength; ++_j) {_o.CompleteStreakReward.Add(this.CompleteStreakReward(_j).HasValue ? this.CompleteStreakReward(_j).Value.UnPack() : null);}
    _o.Categories = new List<FBConfig.ListStringT>();
    for (var _j = 0; _j < this.CategoriesLength; ++_j) {_o.Categories.Add(this.Categories(_j).HasValue ? this.Categories(_j).Value.UnPack() : null);}
    _o.ActiveTasks = this.ActiveTasks;
    _o.HistoryDays = this.HistoryDays;
    _o.HistoryDaysPerType = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.HistoryDaysPerTypeLength; ++_j) {_o.HistoryDaysPerType.Add(this.HistoryDaysPerType(_j).HasValue ? this.HistoryDaysPerType(_j).Value.UnPack() : null);}
    _o.LocalNotifierTimeOffsetInMinutes = this.LocalNotifierTimeOffsetInMinutes;
    _o.LocalNotifierSettings = new List<FBConfig.DailyTasksLocalNotifierSettingsT>();
    for (var _j = 0; _j < this.LocalNotifierSettingsLength; ++_j) {_o.LocalNotifierSettings.Add(this.LocalNotifierSettings(_j).HasValue ? this.LocalNotifierSettings(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.DailyTaskSettingsConfig> Pack(FlatBufferBuilder builder, DailyTaskSettingsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.DailyTaskSettingsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _day_streak_reward = default(VectorOffset);
    if (_o.DayStreakReward != null) {
      var __day_streak_reward = new Offset<FBConfig.DictStringInt>[_o.DayStreakReward.Count];
      for (var _j = 0; _j < __day_streak_reward.Length; ++_j) { __day_streak_reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.DayStreakReward[_j]); }
      _day_streak_reward = CreateDayStreakRewardVector(builder, __day_streak_reward);
    }
    var _complete_streak_reward = default(VectorOffset);
    if (_o.CompleteStreakReward != null) {
      var __complete_streak_reward = new Offset<FBConfig.DictStringInt>[_o.CompleteStreakReward.Count];
      for (var _j = 0; _j < __complete_streak_reward.Length; ++_j) { __complete_streak_reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.CompleteStreakReward[_j]); }
      _complete_streak_reward = CreateCompleteStreakRewardVector(builder, __complete_streak_reward);
    }
    var _categories = default(VectorOffset);
    if (_o.Categories != null) {
      var __categories = new Offset<FBConfig.ListString>[_o.Categories.Count];
      for (var _j = 0; _j < __categories.Length; ++_j) { __categories[_j] = FBConfig.ListString.Pack(builder, _o.Categories[_j]); }
      _categories = CreateCategoriesVector(builder, __categories);
    }
    var _history_days_per_type = default(VectorOffset);
    if (_o.HistoryDaysPerType != null) {
      var __history_days_per_type = new Offset<FBConfig.DictStringInt>[_o.HistoryDaysPerType.Count];
      for (var _j = 0; _j < __history_days_per_type.Length; ++_j) { __history_days_per_type[_j] = FBConfig.DictStringInt.Pack(builder, _o.HistoryDaysPerType[_j]); }
      _history_days_per_type = CreateHistoryDaysPerTypeVector(builder, __history_days_per_type);
    }
    var _local_notifier_settings = default(VectorOffset);
    if (_o.LocalNotifierSettings != null) {
      var __local_notifier_settings = new Offset<FBConfig.DailyTasksLocalNotifierSettings>[_o.LocalNotifierSettings.Count];
      for (var _j = 0; _j < __local_notifier_settings.Length; ++_j) { __local_notifier_settings[_j] = FBConfig.DailyTasksLocalNotifierSettings.Pack(builder, _o.LocalNotifierSettings[_j]); }
      _local_notifier_settings = CreateLocalNotifierSettingsVector(builder, __local_notifier_settings);
    }
    return CreateDailyTaskSettingsConfig(
      builder,
      _uid,
      _o.Streak,
      _day_streak_reward,
      _complete_streak_reward,
      _categories,
      _o.ActiveTasks,
      _o.HistoryDays,
      _history_days_per_type,
      _o.LocalNotifierTimeOffsetInMinutes,
      _local_notifier_settings);
  }
}

public class DailyTaskSettingsConfigT
{
  public string Uid { get; set; }
  public int Streak { get; set; }
  public List<FBConfig.DictStringIntT> DayStreakReward { get; set; }
  public List<FBConfig.DictStringIntT> CompleteStreakReward { get; set; }
  public List<FBConfig.ListStringT> Categories { get; set; }
  public int ActiveTasks { get; set; }
  public int HistoryDays { get; set; }
  public List<FBConfig.DictStringIntT> HistoryDaysPerType { get; set; }
  public int LocalNotifierTimeOffsetInMinutes { get; set; }
  public List<FBConfig.DailyTasksLocalNotifierSettingsT> LocalNotifierSettings { get; set; }

  public DailyTaskSettingsConfigT() {
    this.Uid = null;
    this.Streak = 0;
    this.DayStreakReward = null;
    this.CompleteStreakReward = null;
    this.Categories = null;
    this.ActiveTasks = 0;
    this.HistoryDays = 0;
    this.HistoryDaysPerType = null;
    this.LocalNotifierTimeOffsetInMinutes = 0;
    this.LocalNotifierSettings = null;
  }
}

public struct DailyTaskSettingsConfigDict : IFlatbufferConfigDict<DailyTaskSettingsConfig, DailyTaskSettingsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DailyTaskSettingsConfigDict GetRootAsDailyTaskSettingsConfigDict(ByteBuffer _bb) { return GetRootAsDailyTaskSettingsConfigDict(_bb, new DailyTaskSettingsConfigDict()); }
  public static DailyTaskSettingsConfigDict GetRootAsDailyTaskSettingsConfigDict(ByteBuffer _bb, DailyTaskSettingsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DailyTaskSettingsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.DailyTaskSettingsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.DailyTaskSettingsConfig?)(new FBConfig.DailyTaskSettingsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DailyTaskSettingsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.DailyTaskSettingsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.DailyTaskSettingsConfigDict> CreateDailyTaskSettingsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    DailyTaskSettingsConfigDict.AddValues(builder, valuesOffset);
    return DailyTaskSettingsConfigDict.EndDailyTaskSettingsConfigDict(builder);
  }

  public static void StartDailyTaskSettingsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.DailyTaskSettingsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DailyTaskSettingsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DailyTaskSettingsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DailyTaskSettingsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.DailyTaskSettingsConfigDict> EndDailyTaskSettingsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DailyTaskSettingsConfigDict>(o);
  }
  public static void FinishDailyTaskSettingsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.DailyTaskSettingsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedDailyTaskSettingsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.DailyTaskSettingsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public DailyTaskSettingsConfigDictT UnPack() {
    var _o = new DailyTaskSettingsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DailyTaskSettingsConfigDictT _o) {
    _o.Values = new List<FBConfig.DailyTaskSettingsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.DailyTaskSettingsConfigDict> Pack(FlatBufferBuilder builder, DailyTaskSettingsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.DailyTaskSettingsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.DailyTaskSettingsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.DailyTaskSettingsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateDailyTaskSettingsConfigDict(
      builder,
      _values);
  }
}

public class DailyTaskSettingsConfigDictT
{
  public List<FBConfig.DailyTaskSettingsConfigT> Values { get; set; }

  public DailyTaskSettingsConfigDictT() {
    this.Values = null;
  }
  public static DailyTaskSettingsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return DailyTaskSettingsConfigDict.GetRootAsDailyTaskSettingsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    DailyTaskSettingsConfigDict.FinishDailyTaskSettingsConfigDictBuffer(fbb, DailyTaskSettingsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
