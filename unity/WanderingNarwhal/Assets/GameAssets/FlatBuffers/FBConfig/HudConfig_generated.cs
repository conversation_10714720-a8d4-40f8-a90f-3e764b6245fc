// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct HudConfig : IFlatbufferConfig<HudConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HudConfig GetRootAsHudConfig(ByteBuffer _bb) { return GetRootAsHudConfig(_bb, new HudConfig()); }
  public static HudConfig GetRootAsHudConfig(ByteBuffer _bb, HudConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HudConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Side { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSideBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetSideBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetSideArray() { return __p.__vector_as_array<byte>(6); }
  public int Slot { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSlot(int slot) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, slot); return true; } else { return false; } }
  public string PriorityList { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPriorityListBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetPriorityListBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetPriorityListArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.HudConfig> CreateHudConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset sideOffset = default(StringOffset),
      int slot = 0,
      StringOffset priority_listOffset = default(StringOffset)) {
    builder.StartTable(5);
    HudConfig.AddPriorityList(builder, priority_listOffset);
    HudConfig.AddSlot(builder, slot);
    HudConfig.AddSide(builder, sideOffset);
    HudConfig.AddUid(builder, uidOffset);
    return HudConfig.EndHudConfig(builder);
  }

  public static void StartHudConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSide(FlatBufferBuilder builder, StringOffset sideOffset) { builder.AddOffset(1, sideOffset.Value, 0); }
  public static void AddSlot(FlatBufferBuilder builder, int slot) { builder.AddInt(2, slot, 0); }
  public static void AddPriorityList(FlatBufferBuilder builder, StringOffset priorityListOffset) { builder.AddOffset(4, priorityListOffset.Value, 0); }
  public static Offset<FBConfig.HudConfig> EndHudConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.HudConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfHudConfig(FlatBufferBuilder builder, Offset<HudConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<HudConfig> o1, Offset<HudConfig> o2) =>
        new HudConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new HudConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static HudConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    HudConfig obj_ = new HudConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public HudConfigT UnPack() {
    var _o = new HudConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HudConfigT _o) {
    _o.Uid = this.Uid;
    _o.Side = this.Side;
    _o.Slot = this.Slot;
    _o.PriorityList = this.PriorityList;
  }
  public static Offset<FBConfig.HudConfig> Pack(FlatBufferBuilder builder, HudConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.HudConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _side = _o.Side == null ? default(StringOffset) : builder.CreateString(_o.Side);
    var _priority_list = _o.PriorityList == null ? default(StringOffset) : builder.CreateString(_o.PriorityList);
    return CreateHudConfig(
      builder,
      _uid,
      _side,
      _o.Slot,
      _priority_list);
  }
}

public class HudConfigT
{
  public string Uid { get; set; }
  public string Side { get; set; }
  public int Slot { get; set; }
  public string PriorityList { get; set; }

  public HudConfigT() {
    this.Uid = null;
    this.Side = null;
    this.Slot = 0;
    this.PriorityList = null;
  }
}

public struct HudConfigDict : IFlatbufferConfigDict<HudConfig, HudConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HudConfigDict GetRootAsHudConfigDict(ByteBuffer _bb) { return GetRootAsHudConfigDict(_bb, new HudConfigDict()); }
  public static HudConfigDict GetRootAsHudConfigDict(ByteBuffer _bb, HudConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HudConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.HudConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.HudConfig?)(new FBConfig.HudConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.HudConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.HudConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.HudConfigDict> CreateHudConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    HudConfigDict.AddValues(builder, valuesOffset);
    return HudConfigDict.EndHudConfigDict(builder);
  }

  public static void StartHudConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.HudConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.HudConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.HudConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.HudConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.HudConfigDict> EndHudConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.HudConfigDict>(o);
  }
  public static void FinishHudConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HudConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedHudConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HudConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public HudConfigDictT UnPack() {
    var _o = new HudConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HudConfigDictT _o) {
    _o.Values = new List<FBConfig.HudConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.HudConfigDict> Pack(FlatBufferBuilder builder, HudConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.HudConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.HudConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.HudConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateHudConfigDict(
      builder,
      _values);
  }
}

public class HudConfigDictT
{
  public List<FBConfig.HudConfigT> Values { get; set; }

  public HudConfigDictT() {
    this.Values = null;
  }
  public static HudConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return HudConfigDict.GetRootAsHudConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    HudConfigDict.FinishHudConfigDictBuffer(fbb, HudConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
