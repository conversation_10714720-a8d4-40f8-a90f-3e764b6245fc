// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LocalNotificationsConfig : IFlatbufferConfig<LocalNotificationsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LocalNotificationsConfig GetRootAsLocalNotificationsConfig(ByteBuffer _bb) { return GetRootAsLocalNotificationsConfig(_bb, new LocalNotificationsConfig()); }
  public static LocalNotificationsConfig GetRootAsLocalNotificationsConfig(ByteBuffer _bb, LocalNotificationsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LocalNotificationsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Title { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(6); }
  public string Message { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMessageBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetMessageBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetMessageArray() { return __p.__vector_as_array<byte>(8); }
  public string Subtitle { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubtitleBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetSubtitleBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetSubtitleArray() { return __p.__vector_as_array<byte>(10); }
  public string Category { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(12); }
  public string Image { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetImageBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetImageBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetImageArray() { return __p.__vector_as_array<byte>(14); }
  public string Sound { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSoundBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetSoundBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetSoundArray() { return __p.__vector_as_array<byte>(16); }
  public int Priority { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePriority(int priority) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, priority); return true; } else { return false; } }
  public string Action { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetActionBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetActionBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetActionArray() { return __p.__vector_as_array<byte>(20); }
  public string ActionParams { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetActionParamsBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetActionParamsBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetActionParamsArray() { return __p.__vector_as_array<byte>(22); }

  public static Offset<FBConfig.LocalNotificationsConfig> CreateLocalNotificationsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset messageOffset = default(StringOffset),
      StringOffset subtitleOffset = default(StringOffset),
      StringOffset categoryOffset = default(StringOffset),
      StringOffset imageOffset = default(StringOffset),
      StringOffset soundOffset = default(StringOffset),
      int priority = 0,
      StringOffset actionOffset = default(StringOffset),
      StringOffset action_paramsOffset = default(StringOffset)) {
    builder.StartTable(10);
    LocalNotificationsConfig.AddActionParams(builder, action_paramsOffset);
    LocalNotificationsConfig.AddAction(builder, actionOffset);
    LocalNotificationsConfig.AddPriority(builder, priority);
    LocalNotificationsConfig.AddSound(builder, soundOffset);
    LocalNotificationsConfig.AddImage(builder, imageOffset);
    LocalNotificationsConfig.AddCategory(builder, categoryOffset);
    LocalNotificationsConfig.AddSubtitle(builder, subtitleOffset);
    LocalNotificationsConfig.AddMessage(builder, messageOffset);
    LocalNotificationsConfig.AddTitle(builder, titleOffset);
    LocalNotificationsConfig.AddUid(builder, uidOffset);
    return LocalNotificationsConfig.EndLocalNotificationsConfig(builder);
  }

  public static void StartLocalNotificationsConfig(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(1, titleOffset.Value, 0); }
  public static void AddMessage(FlatBufferBuilder builder, StringOffset messageOffset) { builder.AddOffset(2, messageOffset.Value, 0); }
  public static void AddSubtitle(FlatBufferBuilder builder, StringOffset subtitleOffset) { builder.AddOffset(3, subtitleOffset.Value, 0); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(4, categoryOffset.Value, 0); }
  public static void AddImage(FlatBufferBuilder builder, StringOffset imageOffset) { builder.AddOffset(5, imageOffset.Value, 0); }
  public static void AddSound(FlatBufferBuilder builder, StringOffset soundOffset) { builder.AddOffset(6, soundOffset.Value, 0); }
  public static void AddPriority(FlatBufferBuilder builder, int priority) { builder.AddInt(7, priority, 0); }
  public static void AddAction(FlatBufferBuilder builder, StringOffset actionOffset) { builder.AddOffset(8, actionOffset.Value, 0); }
  public static void AddActionParams(FlatBufferBuilder builder, StringOffset actionParamsOffset) { builder.AddOffset(9, actionParamsOffset.Value, 0); }
  public static Offset<FBConfig.LocalNotificationsConfig> EndLocalNotificationsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LocalNotificationsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLocalNotificationsConfig(FlatBufferBuilder builder, Offset<LocalNotificationsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LocalNotificationsConfig> o1, Offset<LocalNotificationsConfig> o2) =>
        new LocalNotificationsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LocalNotificationsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LocalNotificationsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LocalNotificationsConfig obj_ = new LocalNotificationsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LocalNotificationsConfigT UnPack() {
    var _o = new LocalNotificationsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LocalNotificationsConfigT _o) {
    _o.Uid = this.Uid;
    _o.Title = this.Title;
    _o.Message = this.Message;
    _o.Subtitle = this.Subtitle;
    _o.Category = this.Category;
    _o.Image = this.Image;
    _o.Sound = this.Sound;
    _o.Priority = this.Priority;
    _o.Action = this.Action;
    _o.ActionParams = this.ActionParams;
  }
  public static Offset<FBConfig.LocalNotificationsConfig> Pack(FlatBufferBuilder builder, LocalNotificationsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LocalNotificationsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _message = _o.Message == null ? default(StringOffset) : builder.CreateString(_o.Message);
    var _subtitle = _o.Subtitle == null ? default(StringOffset) : builder.CreateString(_o.Subtitle);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    var _image = _o.Image == null ? default(StringOffset) : builder.CreateString(_o.Image);
    var _sound = _o.Sound == null ? default(StringOffset) : builder.CreateString(_o.Sound);
    var _action = _o.Action == null ? default(StringOffset) : builder.CreateString(_o.Action);
    var _action_params = _o.ActionParams == null ? default(StringOffset) : builder.CreateString(_o.ActionParams);
    return CreateLocalNotificationsConfig(
      builder,
      _uid,
      _title,
      _message,
      _subtitle,
      _category,
      _image,
      _sound,
      _o.Priority,
      _action,
      _action_params);
  }
}

public class LocalNotificationsConfigT
{
  public string Uid { get; set; }
  public string Title { get; set; }
  public string Message { get; set; }
  public string Subtitle { get; set; }
  public string Category { get; set; }
  public string Image { get; set; }
  public string Sound { get; set; }
  public int Priority { get; set; }
  public string Action { get; set; }
  public string ActionParams { get; set; }

  public LocalNotificationsConfigT() {
    this.Uid = null;
    this.Title = null;
    this.Message = null;
    this.Subtitle = null;
    this.Category = null;
    this.Image = null;
    this.Sound = null;
    this.Priority = 0;
    this.Action = null;
    this.ActionParams = null;
  }
}

public struct LocalNotificationsConfigDict : IFlatbufferConfigDict<LocalNotificationsConfig, LocalNotificationsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LocalNotificationsConfigDict GetRootAsLocalNotificationsConfigDict(ByteBuffer _bb) { return GetRootAsLocalNotificationsConfigDict(_bb, new LocalNotificationsConfigDict()); }
  public static LocalNotificationsConfigDict GetRootAsLocalNotificationsConfigDict(ByteBuffer _bb, LocalNotificationsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LocalNotificationsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LocalNotificationsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LocalNotificationsConfig?)(new FBConfig.LocalNotificationsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LocalNotificationsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LocalNotificationsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LocalNotificationsConfigDict> CreateLocalNotificationsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LocalNotificationsConfigDict.AddValues(builder, valuesOffset);
    return LocalNotificationsConfigDict.EndLocalNotificationsConfigDict(builder);
  }

  public static void StartLocalNotificationsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LocalNotificationsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LocalNotificationsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LocalNotificationsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LocalNotificationsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LocalNotificationsConfigDict> EndLocalNotificationsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LocalNotificationsConfigDict>(o);
  }
  public static void FinishLocalNotificationsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LocalNotificationsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLocalNotificationsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LocalNotificationsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LocalNotificationsConfigDictT UnPack() {
    var _o = new LocalNotificationsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LocalNotificationsConfigDictT _o) {
    _o.Values = new List<FBConfig.LocalNotificationsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LocalNotificationsConfigDict> Pack(FlatBufferBuilder builder, LocalNotificationsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LocalNotificationsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LocalNotificationsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LocalNotificationsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLocalNotificationsConfigDict(
      builder,
      _values);
  }
}

public class LocalNotificationsConfigDictT
{
  public List<FBConfig.LocalNotificationsConfigT> Values { get; set; }

  public LocalNotificationsConfigDictT() {
    this.Values = null;
  }
  public static LocalNotificationsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LocalNotificationsConfigDict.GetRootAsLocalNotificationsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LocalNotificationsConfigDict.FinishLocalNotificationsConfigDictBuffer(fbb, LocalNotificationsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
