// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ThemeConfig : IFlatbufferConfig<ThemeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ThemeConfig GetRootAsThemeConfig(ByteBuffer _bb) { return GetRootAsThemeConfig(_bb, new ThemeConfig()); }
  public static ThemeConfig GetRootAsThemeConfig(ByteBuffer _bb, ThemeConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ThemeConfig __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string SchedulingType { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSchedulingTypeBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetSchedulingTypeBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetSchedulingTypeArray() { return __p.__vector_as_array<byte>(6); }
  public FBConfig.DayMonthYear? AbsoluteStartTime { get { int o = __p.__offset(8); return o != 0 ? (FBConfig.DayMonthYear?)(new FBConfig.DayMonthYear()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int DurationInDays { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDurationInDays(int duration_in_days) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, duration_in_days); return true; } else { return false; } }
  public string WeekdaysSchedule { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetWeekdaysScheduleBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetWeekdaysScheduleBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetWeekdaysScheduleArray() { return __p.__vector_as_array<byte>(12); }
  public int RelativeSortIndex { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRelativeSortIndex(int relative_sort_index) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, relative_sort_index); return true; } else { return false; } }
  public int DownloadWindowInDays { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDownloadWindowInDays(int download_window_in_days) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, download_window_in_days); return true; } else { return false; } }

  public static Offset<FBConfig.ThemeConfig> CreateThemeConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset scheduling_typeOffset = default(StringOffset),
      Offset<FBConfig.DayMonthYear> absolute_start_timeOffset = default(Offset<FBConfig.DayMonthYear>),
      int duration_in_days = 0,
      StringOffset weekdays_scheduleOffset = default(StringOffset),
      int relative_sort_index = 0,
      int download_window_in_days = 0) {
    builder.StartTable(7);
    ThemeConfig.AddDownloadWindowInDays(builder, download_window_in_days);
    ThemeConfig.AddRelativeSortIndex(builder, relative_sort_index);
    ThemeConfig.AddWeekdaysSchedule(builder, weekdays_scheduleOffset);
    ThemeConfig.AddDurationInDays(builder, duration_in_days);
    ThemeConfig.AddAbsoluteStartTime(builder, absolute_start_timeOffset);
    ThemeConfig.AddSchedulingType(builder, scheduling_typeOffset);
    ThemeConfig.AddUid(builder, uidOffset);
    return ThemeConfig.EndThemeConfig(builder);
  }

  public static void StartThemeConfig(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSchedulingType(FlatBufferBuilder builder, StringOffset schedulingTypeOffset) { builder.AddOffset(1, schedulingTypeOffset.Value, 0); }
  public static void AddAbsoluteStartTime(FlatBufferBuilder builder, Offset<FBConfig.DayMonthYear> absoluteStartTimeOffset) { builder.AddOffset(2, absoluteStartTimeOffset.Value, 0); }
  public static void AddDurationInDays(FlatBufferBuilder builder, int durationInDays) { builder.AddInt(3, durationInDays, 0); }
  public static void AddWeekdaysSchedule(FlatBufferBuilder builder, StringOffset weekdaysScheduleOffset) { builder.AddOffset(4, weekdaysScheduleOffset.Value, 0); }
  public static void AddRelativeSortIndex(FlatBufferBuilder builder, int relativeSortIndex) { builder.AddInt(5, relativeSortIndex, 0); }
  public static void AddDownloadWindowInDays(FlatBufferBuilder builder, int downloadWindowInDays) { builder.AddInt(6, downloadWindowInDays, 0); }
  public static Offset<FBConfig.ThemeConfig> EndThemeConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ThemeConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfThemeConfig(FlatBufferBuilder builder, Offset<ThemeConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ThemeConfig> o1, Offset<ThemeConfig> o2) =>
        new ThemeConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ThemeConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ThemeConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ThemeConfig obj_ = new ThemeConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ThemeConfigT UnPack() {
    var _o = new ThemeConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ThemeConfigT _o) {
    _o.Uid = this.Uid;
    _o.SchedulingType = this.SchedulingType;
    _o.AbsoluteStartTime = this.AbsoluteStartTime.HasValue ? this.AbsoluteStartTime.Value.UnPack() : null;
    _o.DurationInDays = this.DurationInDays;
    _o.WeekdaysSchedule = this.WeekdaysSchedule;
    _o.RelativeSortIndex = this.RelativeSortIndex;
    _o.DownloadWindowInDays = this.DownloadWindowInDays;
  }
  public static Offset<FBConfig.ThemeConfig> Pack(FlatBufferBuilder builder, ThemeConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ThemeConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _scheduling_type = _o.SchedulingType == null ? default(StringOffset) : builder.CreateString(_o.SchedulingType);
    var _absolute_start_time = _o.AbsoluteStartTime == null ? default(Offset<FBConfig.DayMonthYear>) : FBConfig.DayMonthYear.Pack(builder, _o.AbsoluteStartTime);
    var _weekdays_schedule = _o.WeekdaysSchedule == null ? default(StringOffset) : builder.CreateString(_o.WeekdaysSchedule);
    return CreateThemeConfig(
      builder,
      _uid,
      _scheduling_type,
      _absolute_start_time,
      _o.DurationInDays,
      _weekdays_schedule,
      _o.RelativeSortIndex,
      _o.DownloadWindowInDays);
  }
}

public class ThemeConfigT
{
  public string Uid { get; set; }
  public string SchedulingType { get; set; }
  public FBConfig.DayMonthYearT AbsoluteStartTime { get; set; }
  public int DurationInDays { get; set; }
  public string WeekdaysSchedule { get; set; }
  public int RelativeSortIndex { get; set; }
  public int DownloadWindowInDays { get; set; }

  public ThemeConfigT() {
    this.Uid = null;
    this.SchedulingType = null;
    this.AbsoluteStartTime = null;
    this.DurationInDays = 0;
    this.WeekdaysSchedule = null;
    this.RelativeSortIndex = 0;
    this.DownloadWindowInDays = 0;
  }
}

public struct ThemeConfigDict : IFlatbufferConfigDict<ThemeConfig, ThemeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ThemeConfigDict GetRootAsThemeConfigDict(ByteBuffer _bb) { return GetRootAsThemeConfigDict(_bb, new ThemeConfigDict()); }
  public static ThemeConfigDict GetRootAsThemeConfigDict(ByteBuffer _bb, ThemeConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ThemeConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ThemeConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ThemeConfig?)(new FBConfig.ThemeConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ThemeConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ThemeConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ThemeConfigDict> CreateThemeConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ThemeConfigDict.AddValues(builder, valuesOffset);
    return ThemeConfigDict.EndThemeConfigDict(builder);
  }

  public static void StartThemeConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ThemeConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ThemeConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ThemeConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ThemeConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ThemeConfigDict> EndThemeConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ThemeConfigDict>(o);
  }
  public static void FinishThemeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ThemeConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedThemeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ThemeConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ThemeConfigDictT UnPack() {
    var _o = new ThemeConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ThemeConfigDictT _o) {
    _o.Values = new List<FBConfig.ThemeConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ThemeConfigDict> Pack(FlatBufferBuilder builder, ThemeConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ThemeConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ThemeConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ThemeConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateThemeConfigDict(
      builder,
      _values);
  }
}

public class ThemeConfigDictT
{
  public List<FBConfig.ThemeConfigT> Values { get; set; }

  public ThemeConfigDictT() {
    this.Values = null;
  }
  public static ThemeConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ThemeConfigDict.GetRootAsThemeConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ThemeConfigDict.FinishThemeConfigDictBuffer(fbb, ThemeConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
