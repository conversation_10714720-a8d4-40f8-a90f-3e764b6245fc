// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct FakeUsersConfig : IFlatbufferConfig<FakeUsersConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static FakeUsersConfig GetRootAsFakeUsersConfig(ByteBuffer _bb) { return GetRootAsFakeUsersConfig(_bb, new FakeUsersConfig()); }
  public static FakeUsersConfig GetRootAsFakeUsersConfig(ByteBuffer _bb, FakeUsersConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public FakeUsersConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string NameLoc { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameLocBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameLocBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameLocArray() { return __p.__vector_as_array<byte>(6); }
  public string AvatarUrl { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAvatarUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetAvatarUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetAvatarUrlArray() { return __p.__vector_as_array<byte>(8); }
  public string LastUnlockedLocationId { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLastUnlockedLocationIdBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetLastUnlockedLocationIdBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetLastUnlockedLocationIdArray() { return __p.__vector_as_array<byte>(10); }
  public string HighestPassedLevelId { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHighestPassedLevelIdBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetHighestPassedLevelIdBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetHighestPassedLevelIdArray() { return __p.__vector_as_array<byte>(12); }
  public int SortPriority { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSortPriority(int sort_priority) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, sort_priority); return true; } else { return false; } }

  public static Offset<FBConfig.FakeUsersConfig> CreateFakeUsersConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset name_locOffset = default(StringOffset),
      StringOffset avatar_urlOffset = default(StringOffset),
      StringOffset last_unlocked_location_idOffset = default(StringOffset),
      StringOffset highest_passed_level_idOffset = default(StringOffset),
      int sort_priority = 0) {
    builder.StartTable(6);
    FakeUsersConfig.AddSortPriority(builder, sort_priority);
    FakeUsersConfig.AddHighestPassedLevelId(builder, highest_passed_level_idOffset);
    FakeUsersConfig.AddLastUnlockedLocationId(builder, last_unlocked_location_idOffset);
    FakeUsersConfig.AddAvatarUrl(builder, avatar_urlOffset);
    FakeUsersConfig.AddNameLoc(builder, name_locOffset);
    FakeUsersConfig.AddUid(builder, uidOffset);
    return FakeUsersConfig.EndFakeUsersConfig(builder);
  }

  public static void StartFakeUsersConfig(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddNameLoc(FlatBufferBuilder builder, StringOffset nameLocOffset) { builder.AddOffset(1, nameLocOffset.Value, 0); }
  public static void AddAvatarUrl(FlatBufferBuilder builder, StringOffset avatarUrlOffset) { builder.AddOffset(2, avatarUrlOffset.Value, 0); }
  public static void AddLastUnlockedLocationId(FlatBufferBuilder builder, StringOffset lastUnlockedLocationIdOffset) { builder.AddOffset(3, lastUnlockedLocationIdOffset.Value, 0); }
  public static void AddHighestPassedLevelId(FlatBufferBuilder builder, StringOffset highestPassedLevelIdOffset) { builder.AddOffset(4, highestPassedLevelIdOffset.Value, 0); }
  public static void AddSortPriority(FlatBufferBuilder builder, int sortPriority) { builder.AddInt(5, sortPriority, 0); }
  public static Offset<FBConfig.FakeUsersConfig> EndFakeUsersConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.FakeUsersConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfFakeUsersConfig(FlatBufferBuilder builder, Offset<FakeUsersConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<FakeUsersConfig> o1, Offset<FakeUsersConfig> o2) =>
        new FakeUsersConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new FakeUsersConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static FakeUsersConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    FakeUsersConfig obj_ = new FakeUsersConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public FakeUsersConfigT UnPack() {
    var _o = new FakeUsersConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(FakeUsersConfigT _o) {
    _o.Uid = this.Uid;
    _o.NameLoc = this.NameLoc;
    _o.AvatarUrl = this.AvatarUrl;
    _o.LastUnlockedLocationId = this.LastUnlockedLocationId;
    _o.HighestPassedLevelId = this.HighestPassedLevelId;
    _o.SortPriority = this.SortPriority;
  }
  public static Offset<FBConfig.FakeUsersConfig> Pack(FlatBufferBuilder builder, FakeUsersConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.FakeUsersConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name_loc = _o.NameLoc == null ? default(StringOffset) : builder.CreateString(_o.NameLoc);
    var _avatar_url = _o.AvatarUrl == null ? default(StringOffset) : builder.CreateString(_o.AvatarUrl);
    var _last_unlocked_location_id = _o.LastUnlockedLocationId == null ? default(StringOffset) : builder.CreateString(_o.LastUnlockedLocationId);
    var _highest_passed_level_id = _o.HighestPassedLevelId == null ? default(StringOffset) : builder.CreateString(_o.HighestPassedLevelId);
    return CreateFakeUsersConfig(
      builder,
      _uid,
      _name_loc,
      _avatar_url,
      _last_unlocked_location_id,
      _highest_passed_level_id,
      _o.SortPriority);
  }
}

public class FakeUsersConfigT
{
  public string Uid { get; set; }
  public string NameLoc { get; set; }
  public string AvatarUrl { get; set; }
  public string LastUnlockedLocationId { get; set; }
  public string HighestPassedLevelId { get; set; }
  public int SortPriority { get; set; }

  public FakeUsersConfigT() {
    this.Uid = null;
    this.NameLoc = null;
    this.AvatarUrl = null;
    this.LastUnlockedLocationId = null;
    this.HighestPassedLevelId = null;
    this.SortPriority = 0;
  }
}

public struct FakeUsersConfigDict : IFlatbufferConfigDict<FakeUsersConfig, FakeUsersConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static FakeUsersConfigDict GetRootAsFakeUsersConfigDict(ByteBuffer _bb) { return GetRootAsFakeUsersConfigDict(_bb, new FakeUsersConfigDict()); }
  public static FakeUsersConfigDict GetRootAsFakeUsersConfigDict(ByteBuffer _bb, FakeUsersConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public FakeUsersConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.FakeUsersConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.FakeUsersConfig?)(new FBConfig.FakeUsersConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.FakeUsersConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.FakeUsersConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.FakeUsersConfigDict> CreateFakeUsersConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    FakeUsersConfigDict.AddValues(builder, valuesOffset);
    return FakeUsersConfigDict.EndFakeUsersConfigDict(builder);
  }

  public static void StartFakeUsersConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.FakeUsersConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.FakeUsersConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.FakeUsersConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.FakeUsersConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.FakeUsersConfigDict> EndFakeUsersConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.FakeUsersConfigDict>(o);
  }
  public static void FinishFakeUsersConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.FakeUsersConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedFakeUsersConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.FakeUsersConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public FakeUsersConfigDictT UnPack() {
    var _o = new FakeUsersConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(FakeUsersConfigDictT _o) {
    _o.Values = new List<FBConfig.FakeUsersConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.FakeUsersConfigDict> Pack(FlatBufferBuilder builder, FakeUsersConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.FakeUsersConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.FakeUsersConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.FakeUsersConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateFakeUsersConfigDict(
      builder,
      _values);
  }
}

public class FakeUsersConfigDictT
{
  public List<FBConfig.FakeUsersConfigT> Values { get; set; }

  public FakeUsersConfigDictT() {
    this.Values = null;
  }
  public static FakeUsersConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return FakeUsersConfigDict.GetRootAsFakeUsersConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    FakeUsersConfigDict.FinishFakeUsersConfigDictBuffer(fbb, FakeUsersConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
