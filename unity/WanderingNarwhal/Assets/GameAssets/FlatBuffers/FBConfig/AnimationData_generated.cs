// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct AnimationData : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AnimationData GetRootAsAnimationData(ByteBuffer _bb) { return GetRootAsAnimationData(_bb, new AnimationData()); }
  public static AnimationData GetRootAsAnimationData(ByteBuffer _bb, AnimationData obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AnimationData __assign(int _i, Byte<PERSON>uff<PERSON> _bb) { __init(_i, _bb); return this; }

  public string Skin { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSkinBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetSkinBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetSkinArray() { return __p.__vector_as_array<byte>(4); }
  public string StartAnimation { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetStartAnimationBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetStartAnimationBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetStartAnimationArray() { return __p.__vector_as_array<byte>(6); }
  public bool StartingLoop { get { int o = __p.__offset(8); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateStartingLoop(bool starting_loop) { int o = __p.__offset(8); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(starting_loop ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.AnimationData> CreateAnimationData(FlatBufferBuilder builder,
      StringOffset skinOffset = default(StringOffset),
      StringOffset start_animationOffset = default(StringOffset),
      bool starting_loop = false) {
    builder.StartTable(3);
    AnimationData.AddStartAnimation(builder, start_animationOffset);
    AnimationData.AddSkin(builder, skinOffset);
    AnimationData.AddStartingLoop(builder, starting_loop);
    return AnimationData.EndAnimationData(builder);
  }

  public static void StartAnimationData(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddSkin(FlatBufferBuilder builder, StringOffset skinOffset) { builder.AddOffset(0, skinOffset.Value, 0); }
  public static void AddStartAnimation(FlatBufferBuilder builder, StringOffset startAnimationOffset) { builder.AddOffset(1, startAnimationOffset.Value, 0); }
  public static void AddStartingLoop(FlatBufferBuilder builder, bool startingLoop) { builder.AddBool(2, startingLoop, false); }
  public static Offset<FBConfig.AnimationData> EndAnimationData(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AnimationData>(o);
  }
  public AnimationDataT UnPack() {
    var _o = new AnimationDataT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AnimationDataT _o) {
    _o.Skin = this.Skin;
    _o.StartAnimation = this.StartAnimation;
    _o.StartingLoop = this.StartingLoop;
  }
  public static Offset<FBConfig.AnimationData> Pack(FlatBufferBuilder builder, AnimationDataT _o) {
    if (_o == null) return default(Offset<FBConfig.AnimationData>);
    var _skin = _o.Skin == null ? default(StringOffset) : builder.CreateString(_o.Skin);
    var _start_animation = _o.StartAnimation == null ? default(StringOffset) : builder.CreateString(_o.StartAnimation);
    return CreateAnimationData(
      builder,
      _skin,
      _start_animation,
      _o.StartingLoop);
  }
}

public class AnimationDataT
{
  public string Skin { get; set; }
  public string StartAnimation { get; set; }
  public bool StartingLoop { get; set; }

  public AnimationDataT() {
    this.Skin = null;
    this.StartAnimation = null;
    this.StartingLoop = false;
  }
}


}
