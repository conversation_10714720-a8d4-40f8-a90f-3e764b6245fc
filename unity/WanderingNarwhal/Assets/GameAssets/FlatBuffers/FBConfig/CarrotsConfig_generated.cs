// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct CarrotConfig : IFlatbufferConfig<CarrotConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CarrotConfig GetRootAsCarrotConfig(ByteBuffer _bb) { return GetRootAsCarrotConfig(_bb, new CarrotConfig()); }
  public static CarrotConfig GetRootAsCarrotConfig(ByteBuffer _bb, CarrotConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CarrotConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string NameTextId { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameTextIdBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameTextIdBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameTextIdArray() { return __p.__vector_as_array<byte>(6); }
  public string LevelUid { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLevelUidBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetLevelUidBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetLevelUidArray() { return __p.__vector_as_array<byte>(8); }
  public string Prefab { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPrefabBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetPrefabBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetPrefabArray() { return __p.__vector_as_array<byte>(10); }

  public static Offset<FBConfig.CarrotConfig> CreateCarrotConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset name_text_idOffset = default(StringOffset),
      StringOffset level_uidOffset = default(StringOffset),
      StringOffset prefabOffset = default(StringOffset)) {
    builder.StartTable(4);
    CarrotConfig.AddPrefab(builder, prefabOffset);
    CarrotConfig.AddLevelUid(builder, level_uidOffset);
    CarrotConfig.AddNameTextId(builder, name_text_idOffset);
    CarrotConfig.AddUid(builder, uidOffset);
    return CarrotConfig.EndCarrotConfig(builder);
  }

  public static void StartCarrotConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddNameTextId(FlatBufferBuilder builder, StringOffset nameTextIdOffset) { builder.AddOffset(1, nameTextIdOffset.Value, 0); }
  public static void AddLevelUid(FlatBufferBuilder builder, StringOffset levelUidOffset) { builder.AddOffset(2, levelUidOffset.Value, 0); }
  public static void AddPrefab(FlatBufferBuilder builder, StringOffset prefabOffset) { builder.AddOffset(3, prefabOffset.Value, 0); }
  public static Offset<FBConfig.CarrotConfig> EndCarrotConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.CarrotConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfCarrotConfig(FlatBufferBuilder builder, Offset<CarrotConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<CarrotConfig> o1, Offset<CarrotConfig> o2) =>
        new CarrotConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new CarrotConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static CarrotConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    CarrotConfig obj_ = new CarrotConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public CarrotConfigT UnPack() {
    var _o = new CarrotConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CarrotConfigT _o) {
    _o.Uid = this.Uid;
    _o.NameTextId = this.NameTextId;
    _o.LevelUid = this.LevelUid;
    _o.Prefab = this.Prefab;
  }
  public static Offset<FBConfig.CarrotConfig> Pack(FlatBufferBuilder builder, CarrotConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.CarrotConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name_text_id = _o.NameTextId == null ? default(StringOffset) : builder.CreateString(_o.NameTextId);
    var _level_uid = _o.LevelUid == null ? default(StringOffset) : builder.CreateString(_o.LevelUid);
    var _prefab = _o.Prefab == null ? default(StringOffset) : builder.CreateString(_o.Prefab);
    return CreateCarrotConfig(
      builder,
      _uid,
      _name_text_id,
      _level_uid,
      _prefab);
  }
}

public class CarrotConfigT
{
  public string Uid { get; set; }
  public string NameTextId { get; set; }
  public string LevelUid { get; set; }
  public string Prefab { get; set; }

  public CarrotConfigT() {
    this.Uid = null;
    this.NameTextId = null;
    this.LevelUid = null;
    this.Prefab = null;
  }
}

public struct CarrotsConfig : IFlatbufferConfig<CarrotsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CarrotsConfig GetRootAsCarrotsConfig(ByteBuffer _bb) { return GetRootAsCarrotsConfig(_bb, new CarrotsConfig()); }
  public static CarrotsConfig GetRootAsCarrotsConfig(ByteBuffer _bb, CarrotsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CarrotsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.CarrotConfig? Carrots(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.CarrotConfig?)(new FBConfig.CarrotConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int CarrotsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.CarrotConfig? CarrotsByKey(string key) { int o = __p.__offset(6); return o != 0 ? FBConfig.CarrotConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.CarrotsConfig> CreateCarrotsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset carrotsOffset = default(VectorOffset)) {
    builder.StartTable(2);
    CarrotsConfig.AddCarrots(builder, carrotsOffset);
    CarrotsConfig.AddUid(builder, uidOffset);
    return CarrotsConfig.EndCarrotsConfig(builder);
  }

  public static void StartCarrotsConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCarrots(FlatBufferBuilder builder, VectorOffset carrotsOffset) { builder.AddOffset(1, carrotsOffset.Value, 0); }
  public static VectorOffset CreateCarrotsVector(FlatBufferBuilder builder, Offset<FBConfig.CarrotConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCarrotsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.CarrotConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCarrotsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.CarrotConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCarrotsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.CarrotConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCarrotsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.CarrotsConfig> EndCarrotsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.CarrotsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfCarrotsConfig(FlatBufferBuilder builder, Offset<CarrotsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<CarrotsConfig> o1, Offset<CarrotsConfig> o2) =>
        new CarrotsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new CarrotsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static CarrotsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    CarrotsConfig obj_ = new CarrotsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public CarrotsConfigT UnPack() {
    var _o = new CarrotsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CarrotsConfigT _o) {
    _o.Uid = this.Uid;
    _o.Carrots = new List<FBConfig.CarrotConfigT>();
    for (var _j = 0; _j < this.CarrotsLength; ++_j) {_o.Carrots.Add(this.Carrots(_j).HasValue ? this.Carrots(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.CarrotsConfig> Pack(FlatBufferBuilder builder, CarrotsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.CarrotsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _carrots = default(VectorOffset);
    if (_o.Carrots != null) {
      var __carrots = new Offset<FBConfig.CarrotConfig>[_o.Carrots.Count];
      for (var _j = 0; _j < __carrots.Length; ++_j) { __carrots[_j] = FBConfig.CarrotConfig.Pack(builder, _o.Carrots[_j]); }
      _carrots = CreateCarrotsVector(builder, __carrots);
    }
    return CreateCarrotsConfig(
      builder,
      _uid,
      _carrots);
  }
}

public class CarrotsConfigT
{
  public string Uid { get; set; }
  public List<FBConfig.CarrotConfigT> Carrots { get; set; }

  public CarrotsConfigT() {
    this.Uid = null;
    this.Carrots = null;
  }
}

public struct CarrotsConfigDict : IFlatbufferConfigDict<CarrotsConfig, CarrotsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CarrotsConfigDict GetRootAsCarrotsConfigDict(ByteBuffer _bb) { return GetRootAsCarrotsConfigDict(_bb, new CarrotsConfigDict()); }
  public static CarrotsConfigDict GetRootAsCarrotsConfigDict(ByteBuffer _bb, CarrotsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CarrotsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.CarrotsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.CarrotsConfig?)(new FBConfig.CarrotsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.CarrotsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.CarrotsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.CarrotsConfigDict> CreateCarrotsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    CarrotsConfigDict.AddValues(builder, valuesOffset);
    return CarrotsConfigDict.EndCarrotsConfigDict(builder);
  }

  public static void StartCarrotsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.CarrotsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.CarrotsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.CarrotsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.CarrotsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.CarrotsConfigDict> EndCarrotsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CarrotsConfigDict>(o);
  }
  public static void FinishCarrotsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CarrotsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedCarrotsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CarrotsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public CarrotsConfigDictT UnPack() {
    var _o = new CarrotsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CarrotsConfigDictT _o) {
    _o.Values = new List<FBConfig.CarrotsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.CarrotsConfigDict> Pack(FlatBufferBuilder builder, CarrotsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.CarrotsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.CarrotsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.CarrotsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateCarrotsConfigDict(
      builder,
      _values);
  }
}

public class CarrotsConfigDictT
{
  public List<FBConfig.CarrotsConfigT> Values { get; set; }

  public CarrotsConfigDictT() {
    this.Values = null;
  }
  public static CarrotsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return CarrotsConfigDict.GetRootAsCarrotsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    CarrotsConfigDict.FinishCarrotsConfigDictBuffer(fbb, CarrotsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
