// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DailyTasksConfig : IFlatbufferConfig<DailyTasksConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DailyTasksConfig GetRootAsDailyTasksConfig(ByteBuffer _bb) { return GetRootAsDailyTasksConfig(_bb, new DailyTasksConfig()); }
  public static DailyTasksConfig GetRootAsDailyTasksConfig(ByteBuffer _bb, DailyTasksConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DailyTasksConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string Category { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(8); }
  public string Icon { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetIconBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetIconArray() { return __p.__vector_as_array<byte>(10); }
  public int Amount { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateAmount(int amount) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, amount); return true; } else { return false; } }
  public string Type { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTypeBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetTypeBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetTypeArray() { return __p.__vector_as_array<byte>(14); }
  public FBConfig.DictStringInt? Reward(int j) { int o = __p.__offset(16); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardLength { get { int o = __p.__offset(16); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int Day { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDay(int day) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, day); return true; } else { return false; } }

  public static Offset<FBConfig.DailyTasksConfig> CreateDailyTasksConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset categoryOffset = default(StringOffset),
      StringOffset iconOffset = default(StringOffset),
      int amount = 0,
      StringOffset typeOffset = default(StringOffset),
      VectorOffset rewardOffset = default(VectorOffset),
      int day = 0) {
    builder.StartTable(8);
    DailyTasksConfig.AddDay(builder, day);
    DailyTasksConfig.AddReward(builder, rewardOffset);
    DailyTasksConfig.AddType(builder, typeOffset);
    DailyTasksConfig.AddAmount(builder, amount);
    DailyTasksConfig.AddIcon(builder, iconOffset);
    DailyTasksConfig.AddCategory(builder, categoryOffset);
    DailyTasksConfig.AddName(builder, nameOffset);
    DailyTasksConfig.AddUid(builder, uidOffset);
    return DailyTasksConfig.EndDailyTasksConfig(builder);
  }

  public static void StartDailyTasksConfig(FlatBufferBuilder builder) { builder.StartTable(8); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(2, categoryOffset.Value, 0); }
  public static void AddIcon(FlatBufferBuilder builder, StringOffset iconOffset) { builder.AddOffset(3, iconOffset.Value, 0); }
  public static void AddAmount(FlatBufferBuilder builder, int amount) { builder.AddInt(4, amount, 0); }
  public static void AddType(FlatBufferBuilder builder, StringOffset typeOffset) { builder.AddOffset(5, typeOffset.Value, 0); }
  public static void AddReward(FlatBufferBuilder builder, VectorOffset rewardOffset) { builder.AddOffset(6, rewardOffset.Value, 0); }
  public static VectorOffset CreateRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddDay(FlatBufferBuilder builder, int day) { builder.AddInt(7, day, 0); }
  public static Offset<FBConfig.DailyTasksConfig> EndDailyTasksConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.DailyTasksConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfDailyTasksConfig(FlatBufferBuilder builder, Offset<DailyTasksConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<DailyTasksConfig> o1, Offset<DailyTasksConfig> o2) =>
        new DailyTasksConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new DailyTasksConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static DailyTasksConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    DailyTasksConfig obj_ = new DailyTasksConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public DailyTasksConfigT UnPack() {
    var _o = new DailyTasksConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DailyTasksConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Category = this.Category;
    _o.Icon = this.Icon;
    _o.Amount = this.Amount;
    _o.Type = this.Type;
    _o.Reward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardLength; ++_j) {_o.Reward.Add(this.Reward(_j).HasValue ? this.Reward(_j).Value.UnPack() : null);}
    _o.Day = this.Day;
  }
  public static Offset<FBConfig.DailyTasksConfig> Pack(FlatBufferBuilder builder, DailyTasksConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.DailyTasksConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    var _icon = _o.Icon == null ? default(StringOffset) : builder.CreateString(_o.Icon);
    var _type = _o.Type == null ? default(StringOffset) : builder.CreateString(_o.Type);
    var _reward = default(VectorOffset);
    if (_o.Reward != null) {
      var __reward = new Offset<FBConfig.DictStringInt>[_o.Reward.Count];
      for (var _j = 0; _j < __reward.Length; ++_j) { __reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.Reward[_j]); }
      _reward = CreateRewardVector(builder, __reward);
    }
    return CreateDailyTasksConfig(
      builder,
      _uid,
      _name,
      _category,
      _icon,
      _o.Amount,
      _type,
      _reward,
      _o.Day);
  }
}

public class DailyTasksConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string Category { get; set; }
  public string Icon { get; set; }
  public int Amount { get; set; }
  public string Type { get; set; }
  public List<FBConfig.DictStringIntT> Reward { get; set; }
  public int Day { get; set; }

  public DailyTasksConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Category = null;
    this.Icon = null;
    this.Amount = 0;
    this.Type = null;
    this.Reward = null;
    this.Day = 0;
  }
}

public struct DailyTasksConfigDict : IFlatbufferConfigDict<DailyTasksConfig, DailyTasksConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DailyTasksConfigDict GetRootAsDailyTasksConfigDict(ByteBuffer _bb) { return GetRootAsDailyTasksConfigDict(_bb, new DailyTasksConfigDict()); }
  public static DailyTasksConfigDict GetRootAsDailyTasksConfigDict(ByteBuffer _bb, DailyTasksConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DailyTasksConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.DailyTasksConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.DailyTasksConfig?)(new FBConfig.DailyTasksConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DailyTasksConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.DailyTasksConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.DailyTasksConfigDict> CreateDailyTasksConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    DailyTasksConfigDict.AddValues(builder, valuesOffset);
    return DailyTasksConfigDict.EndDailyTasksConfigDict(builder);
  }

  public static void StartDailyTasksConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.DailyTasksConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DailyTasksConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DailyTasksConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DailyTasksConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.DailyTasksConfigDict> EndDailyTasksConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DailyTasksConfigDict>(o);
  }
  public static void FinishDailyTasksConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.DailyTasksConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedDailyTasksConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.DailyTasksConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public DailyTasksConfigDictT UnPack() {
    var _o = new DailyTasksConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DailyTasksConfigDictT _o) {
    _o.Values = new List<FBConfig.DailyTasksConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.DailyTasksConfigDict> Pack(FlatBufferBuilder builder, DailyTasksConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.DailyTasksConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.DailyTasksConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.DailyTasksConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateDailyTasksConfigDict(
      builder,
      _values);
  }
}

public class DailyTasksConfigDictT
{
  public List<FBConfig.DailyTasksConfigT> Values { get; set; }

  public DailyTasksConfigDictT() {
    this.Values = null;
  }
  public static DailyTasksConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return DailyTasksConfigDict.GetRootAsDailyTasksConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    DailyTasksConfigDict.FinishDailyTasksConfigDictBuffer(fbb, DailyTasksConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
