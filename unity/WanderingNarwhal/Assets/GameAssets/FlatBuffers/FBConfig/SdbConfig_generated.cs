// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SdbConfig : IFlatbufferConfig<SdbConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SdbConfig GetRootAsSdbConfig(ByteBuffer _bb) { return GetRootAsSdbConfig(_bb, new SdbConfig()); }
  public static SdbConfig GetRootAsSdbConfig(ByteBuffer _bb, SdbConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public SdbConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public bool Enabled { get { int o = __p.__offset(6); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateEnabled(bool enabled) { int o = __p.__offset(6); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(enabled ? 1 : 0)); return true; } else { return false; } }
  public int TotalWinsCount { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTotalWinsCount(int total_wins_count) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, total_wins_count); return true; } else { return false; } }
  public int SdbEffectValue { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSdbEffectValue(int sdb_effect_value) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, sdb_effect_value); return true; } else { return false; } }

  public static Offset<FBConfig.SdbConfig> CreateSdbConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      bool enabled = false,
      int total_wins_count = 0,
      int sdb_effect_value = 0) {
    builder.StartTable(4);
    SdbConfig.AddSdbEffectValue(builder, sdb_effect_value);
    SdbConfig.AddTotalWinsCount(builder, total_wins_count);
    SdbConfig.AddUid(builder, uidOffset);
    SdbConfig.AddEnabled(builder, enabled);
    return SdbConfig.EndSdbConfig(builder);
  }

  public static void StartSdbConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddEnabled(FlatBufferBuilder builder, bool enabled) { builder.AddBool(1, enabled, false); }
  public static void AddTotalWinsCount(FlatBufferBuilder builder, int totalWinsCount) { builder.AddInt(2, totalWinsCount, 0); }
  public static void AddSdbEffectValue(FlatBufferBuilder builder, int sdbEffectValue) { builder.AddInt(3, sdbEffectValue, 0); }
  public static Offset<FBConfig.SdbConfig> EndSdbConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SdbConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSdbConfig(FlatBufferBuilder builder, Offset<SdbConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SdbConfig> o1, Offset<SdbConfig> o2) =>
        new SdbConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SdbConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SdbConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SdbConfig obj_ = new SdbConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SdbConfigT UnPack() {
    var _o = new SdbConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SdbConfigT _o) {
    _o.Uid = this.Uid;
    _o.Enabled = this.Enabled;
    _o.TotalWinsCount = this.TotalWinsCount;
    _o.SdbEffectValue = this.SdbEffectValue;
  }
  public static Offset<FBConfig.SdbConfig> Pack(FlatBufferBuilder builder, SdbConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SdbConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateSdbConfig(
      builder,
      _uid,
      _o.Enabled,
      _o.TotalWinsCount,
      _o.SdbEffectValue);
  }
}

public class SdbConfigT
{
  public string Uid { get; set; }
  public bool Enabled { get; set; }
  public int TotalWinsCount { get; set; }
  public int SdbEffectValue { get; set; }

  public SdbConfigT() {
    this.Uid = null;
    this.Enabled = false;
    this.TotalWinsCount = 0;
    this.SdbEffectValue = 0;
  }
}

public struct SdbConfigDict : IFlatbufferConfigDict<SdbConfig, SdbConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SdbConfigDict GetRootAsSdbConfigDict(ByteBuffer _bb) { return GetRootAsSdbConfigDict(_bb, new SdbConfigDict()); }
  public static SdbConfigDict GetRootAsSdbConfigDict(ByteBuffer _bb, SdbConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SdbConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SdbConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SdbConfig?)(new FBConfig.SdbConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SdbConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SdbConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SdbConfigDict> CreateSdbConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SdbConfigDict.AddValues(builder, valuesOffset);
    return SdbConfigDict.EndSdbConfigDict(builder);
  }

  public static void StartSdbConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SdbConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SdbConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SdbConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SdbConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SdbConfigDict> EndSdbConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SdbConfigDict>(o);
  }
  public static void FinishSdbConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SdbConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSdbConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SdbConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SdbConfigDictT UnPack() {
    var _o = new SdbConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SdbConfigDictT _o) {
    _o.Values = new List<FBConfig.SdbConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SdbConfigDict> Pack(FlatBufferBuilder builder, SdbConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SdbConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SdbConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SdbConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSdbConfigDict(
      builder,
      _values);
  }
}

public class SdbConfigDictT
{
  public List<FBConfig.SdbConfigT> Values { get; set; }

  public SdbConfigDictT() {
    this.Values = null;
  }
  public static SdbConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SdbConfigDict.GetRootAsSdbConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SdbConfigDict.FinishSdbConfigDictBuffer(fbb, SdbConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
