// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct POITextConfig : IFlatbufferConfig<POITextConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POITextConfig GetRootAsPOITextConfig(ByteBuffer _bb) { return GetRootAsPOITextConfig(_bb, new POITextConfig()); }
  public static POITextConfig GetRootAsPOITextConfig(ByteBuffer _bb, POITextConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POITextConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Citation { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCitationBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetCitationBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetCitationArray() { return __p.__vector_as_array<byte>(6); }
  public string Author { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAuthorBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetAuthorBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetAuthorArray() { return __p.__vector_as_array<byte>(8); }

  public static Offset<FBConfig.POITextConfig> CreatePOITextConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset citationOffset = default(StringOffset),
      StringOffset authorOffset = default(StringOffset)) {
    builder.StartTable(3);
    POITextConfig.AddAuthor(builder, authorOffset);
    POITextConfig.AddCitation(builder, citationOffset);
    POITextConfig.AddUid(builder, uidOffset);
    return POITextConfig.EndPOITextConfig(builder);
  }

  public static void StartPOITextConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCitation(FlatBufferBuilder builder, StringOffset citationOffset) { builder.AddOffset(1, citationOffset.Value, 0); }
  public static void AddAuthor(FlatBufferBuilder builder, StringOffset authorOffset) { builder.AddOffset(2, authorOffset.Value, 0); }
  public static Offset<FBConfig.POITextConfig> EndPOITextConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.POITextConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfPOITextConfig(FlatBufferBuilder builder, Offset<POITextConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<POITextConfig> o1, Offset<POITextConfig> o2) =>
        new POITextConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new POITextConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static POITextConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    POITextConfig obj_ = new POITextConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public POITextConfigT UnPack() {
    var _o = new POITextConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POITextConfigT _o) {
    _o.Uid = this.Uid;
    _o.Citation = this.Citation;
    _o.Author = this.Author;
  }
  public static Offset<FBConfig.POITextConfig> Pack(FlatBufferBuilder builder, POITextConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.POITextConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _citation = _o.Citation == null ? default(StringOffset) : builder.CreateString(_o.Citation);
    var _author = _o.Author == null ? default(StringOffset) : builder.CreateString(_o.Author);
    return CreatePOITextConfig(
      builder,
      _uid,
      _citation,
      _author);
  }
}

public class POITextConfigT
{
  public string Uid { get; set; }
  public string Citation { get; set; }
  public string Author { get; set; }

  public POITextConfigT() {
    this.Uid = null;
    this.Citation = null;
    this.Author = null;
  }
}

public struct POITextConfigDict : IFlatbufferConfigDict<POITextConfig, POITextConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POITextConfigDict GetRootAsPOITextConfigDict(ByteBuffer _bb) { return GetRootAsPOITextConfigDict(_bb, new POITextConfigDict()); }
  public static POITextConfigDict GetRootAsPOITextConfigDict(ByteBuffer _bb, POITextConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POITextConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.POITextConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.POITextConfig?)(new FBConfig.POITextConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.POITextConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.POITextConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.POITextConfigDict> CreatePOITextConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    POITextConfigDict.AddValues(builder, valuesOffset);
    return POITextConfigDict.EndPOITextConfigDict(builder);
  }

  public static void StartPOITextConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.POITextConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.POITextConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.POITextConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.POITextConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.POITextConfigDict> EndPOITextConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.POITextConfigDict>(o);
  }
  public static void FinishPOITextConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POITextConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedPOITextConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POITextConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public POITextConfigDictT UnPack() {
    var _o = new POITextConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POITextConfigDictT _o) {
    _o.Values = new List<FBConfig.POITextConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.POITextConfigDict> Pack(FlatBufferBuilder builder, POITextConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.POITextConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.POITextConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.POITextConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreatePOITextConfigDict(
      builder,
      _values);
  }
}

public class POITextConfigDictT
{
  public List<FBConfig.POITextConfigT> Values { get; set; }

  public POITextConfigDictT() {
    this.Values = null;
  }
  public static POITextConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return POITextConfigDict.GetRootAsPOITextConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    POITextConfigDict.FinishPOITextConfigDictBuffer(fbb, POITextConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
