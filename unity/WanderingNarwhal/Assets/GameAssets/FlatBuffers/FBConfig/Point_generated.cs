// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct Point : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Point GetRootAsPoint(ByteBuffer _bb) { return GetRootAsPoint(_bb, new Point()); }
  public static Point GetRootAsPoint(ByteBuffer _bb, Point obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Point __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float X { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateX(float x) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, x); return true; } else { return false; } }
  public float Y { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateY(float y) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, y); return true; } else { return false; } }

  public static Offset<FBConfig.Point> CreatePoint(FlatBufferBuilder builder,
      float x = 0.0f,
      float y = 0.0f) {
    builder.StartTable(2);
    Point.AddY(builder, y);
    Point.AddX(builder, x);
    return Point.EndPoint(builder);
  }

  public static void StartPoint(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddX(FlatBufferBuilder builder, float x) { builder.AddFloat(0, x, 0.0f); }
  public static void AddY(FlatBufferBuilder builder, float y) { builder.AddFloat(1, y, 0.0f); }
  public static Offset<FBConfig.Point> EndPoint(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.Point>(o);
  }
  public PointT UnPack() {
    var _o = new PointT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(PointT _o) {
    _o.X = this.X;
    _o.Y = this.Y;
  }
  public static Offset<FBConfig.Point> Pack(FlatBufferBuilder builder, PointT _o) {
    if (_o == null) return default(Offset<FBConfig.Point>);
    return CreatePoint(
      builder,
      _o.X,
      _o.Y);
  }
}

public class PointT
{
  public float X { get; set; }
  public float Y { get; set; }

  public PointT() {
    this.X = 0.0f;
    this.Y = 0.0f;
  }
}

public struct Point3 : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Point3 GetRootAsPoint3(ByteBuffer _bb) { return GetRootAsPoint3(_bb, new Point3()); }
  public static Point3 GetRootAsPoint3(ByteBuffer _bb, Point3 obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Point3 __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float X { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateX(float x) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, x); return true; } else { return false; } }
  public float Y { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateY(float y) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, y); return true; } else { return false; } }
  public float Z { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateZ(float z) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, z); return true; } else { return false; } }

  public static Offset<FBConfig.Point3> CreatePoint3(FlatBufferBuilder builder,
      float x = 0.0f,
      float y = 0.0f,
      float z = 0.0f) {
    builder.StartTable(3);
    Point3.AddZ(builder, z);
    Point3.AddY(builder, y);
    Point3.AddX(builder, x);
    return Point3.EndPoint3(builder);
  }

  public static void StartPoint3(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddX(FlatBufferBuilder builder, float x) { builder.AddFloat(0, x, 0.0f); }
  public static void AddY(FlatBufferBuilder builder, float y) { builder.AddFloat(1, y, 0.0f); }
  public static void AddZ(FlatBufferBuilder builder, float z) { builder.AddFloat(2, z, 0.0f); }
  public static Offset<FBConfig.Point3> EndPoint3(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.Point3>(o);
  }
  public Point3T UnPack() {
    var _o = new Point3T();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(Point3T _o) {
    _o.X = this.X;
    _o.Y = this.Y;
    _o.Z = this.Z;
  }
  public static Offset<FBConfig.Point3> Pack(FlatBufferBuilder builder, Point3T _o) {
    if (_o == null) return default(Offset<FBConfig.Point3>);
    return CreatePoint3(
      builder,
      _o.X,
      _o.Y,
      _o.Z);
  }
}

public class Point3T
{
  public float X { get; set; }
  public float Y { get; set; }
  public float Z { get; set; }

  public Point3T() {
    this.X = 0.0f;
    this.Y = 0.0f;
    this.Z = 0.0f;
  }
}


}
