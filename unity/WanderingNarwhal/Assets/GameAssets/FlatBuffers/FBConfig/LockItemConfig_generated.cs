// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LockItemConfig : IFlatbufferConfig<LockItemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LockItemConfig GetRootAsLockItemConfig(ByteBuffer _bb) { return GetRootAsLockItemConfig(_bb, new LockItemConfig()); }
  public static LockItemConfig GetRootAsLockItemConfig(ByteBuffer _bb, LockItemConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, By<PERSON><PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public LockItemConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string ItemType { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetItemTypeBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetItemTypeBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetItemTypeArray() { return __p.__vector_as_array<byte>(6); }
  public string ConditionType { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetConditionTypeBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetConditionTypeBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetConditionTypeArray() { return __p.__vector_as_array<byte>(8); }
  public string Args(int j) { int o = __p.__offset(10); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int ArgsLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.LockItemConfig> CreateLockItemConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset item_typeOffset = default(StringOffset),
      StringOffset condition_typeOffset = default(StringOffset),
      VectorOffset argsOffset = default(VectorOffset)) {
    builder.StartTable(4);
    LockItemConfig.AddArgs(builder, argsOffset);
    LockItemConfig.AddConditionType(builder, condition_typeOffset);
    LockItemConfig.AddItemType(builder, item_typeOffset);
    LockItemConfig.AddUid(builder, uidOffset);
    return LockItemConfig.EndLockItemConfig(builder);
  }

  public static void StartLockItemConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddItemType(FlatBufferBuilder builder, StringOffset itemTypeOffset) { builder.AddOffset(1, itemTypeOffset.Value, 0); }
  public static void AddConditionType(FlatBufferBuilder builder, StringOffset conditionTypeOffset) { builder.AddOffset(2, conditionTypeOffset.Value, 0); }
  public static void AddArgs(FlatBufferBuilder builder, VectorOffset argsOffset) { builder.AddOffset(3, argsOffset.Value, 0); }
  public static VectorOffset CreateArgsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateArgsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateArgsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateArgsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartArgsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LockItemConfig> EndLockItemConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LockItemConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLockItemConfig(FlatBufferBuilder builder, Offset<LockItemConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LockItemConfig> o1, Offset<LockItemConfig> o2) =>
        new LockItemConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LockItemConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LockItemConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LockItemConfig obj_ = new LockItemConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LockItemConfigT UnPack() {
    var _o = new LockItemConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LockItemConfigT _o) {
    _o.Uid = this.Uid;
    _o.ItemType = this.ItemType;
    _o.ConditionType = this.ConditionType;
    _o.Args = new List<string>();
    for (var _j = 0; _j < this.ArgsLength; ++_j) {_o.Args.Add(this.Args(_j));}
  }
  public static Offset<FBConfig.LockItemConfig> Pack(FlatBufferBuilder builder, LockItemConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LockItemConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _item_type = _o.ItemType == null ? default(StringOffset) : builder.CreateString(_o.ItemType);
    var _condition_type = _o.ConditionType == null ? default(StringOffset) : builder.CreateString(_o.ConditionType);
    var _args = default(VectorOffset);
    if (_o.Args != null) {
      var __args = new StringOffset[_o.Args.Count];
      for (var _j = 0; _j < __args.Length; ++_j) { __args[_j] = builder.CreateString(_o.Args[_j]); }
      _args = CreateArgsVector(builder, __args);
    }
    return CreateLockItemConfig(
      builder,
      _uid,
      _item_type,
      _condition_type,
      _args);
  }
}

public class LockItemConfigT
{
  public string Uid { get; set; }
  public string ItemType { get; set; }
  public string ConditionType { get; set; }
  public List<string> Args { get; set; }

  public LockItemConfigT() {
    this.Uid = null;
    this.ItemType = null;
    this.ConditionType = null;
    this.Args = null;
  }
}

public struct LockItemConfigDict : IFlatbufferConfigDict<LockItemConfig, LockItemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LockItemConfigDict GetRootAsLockItemConfigDict(ByteBuffer _bb) { return GetRootAsLockItemConfigDict(_bb, new LockItemConfigDict()); }
  public static LockItemConfigDict GetRootAsLockItemConfigDict(ByteBuffer _bb, LockItemConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LockItemConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LockItemConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LockItemConfig?)(new FBConfig.LockItemConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LockItemConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LockItemConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LockItemConfigDict> CreateLockItemConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LockItemConfigDict.AddValues(builder, valuesOffset);
    return LockItemConfigDict.EndLockItemConfigDict(builder);
  }

  public static void StartLockItemConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LockItemConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LockItemConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LockItemConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LockItemConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LockItemConfigDict> EndLockItemConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LockItemConfigDict>(o);
  }
  public static void FinishLockItemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LockItemConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLockItemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LockItemConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LockItemConfigDictT UnPack() {
    var _o = new LockItemConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LockItemConfigDictT _o) {
    _o.Values = new List<FBConfig.LockItemConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LockItemConfigDict> Pack(FlatBufferBuilder builder, LockItemConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LockItemConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LockItemConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LockItemConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLockItemConfigDict(
      builder,
      _values);
  }
}

public class LockItemConfigDictT
{
  public List<FBConfig.LockItemConfigT> Values { get; set; }

  public LockItemConfigDictT() {
    this.Values = null;
  }
  public static LockItemConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LockItemConfigDict.GetRootAsLockItemConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LockItemConfigDict.FinishLockItemConfigDictBuffer(fbb, LockItemConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
