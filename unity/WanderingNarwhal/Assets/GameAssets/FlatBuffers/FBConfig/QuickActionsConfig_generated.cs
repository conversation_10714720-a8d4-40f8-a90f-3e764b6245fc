// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct QuickActionsConfig : IFlatbufferConfig<QuickActionsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static QuickActionsConfig GetRootAsQuickActionsConfig(ByteBuffer _bb) { return GetRootAsQuickActionsConfig(_bb, new QuickActionsConfig()); }
  public static QuickActionsConfig GetRootAsQuickActionsConfig(ByteBuffer _bb, QuickActionsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public QuickActionsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Title { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(6); }
  public string SubTitle { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubTitleBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetSubTitleBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetSubTitleArray() { return __p.__vector_as_array<byte>(8); }
  public int IconType { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateIconType(int icon_type) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, icon_type); return true; } else { return false; } }
  public string UserInfo { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUserInfoBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetUserInfoBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetUserInfoArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.QuickActionsConfig> CreateQuickActionsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset sub_titleOffset = default(StringOffset),
      int icon_type = 0,
      StringOffset user_infoOffset = default(StringOffset)) {
    builder.StartTable(5);
    QuickActionsConfig.AddUserInfo(builder, user_infoOffset);
    QuickActionsConfig.AddIconType(builder, icon_type);
    QuickActionsConfig.AddSubTitle(builder, sub_titleOffset);
    QuickActionsConfig.AddTitle(builder, titleOffset);
    QuickActionsConfig.AddUid(builder, uidOffset);
    return QuickActionsConfig.EndQuickActionsConfig(builder);
  }

  public static void StartQuickActionsConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(1, titleOffset.Value, 0); }
  public static void AddSubTitle(FlatBufferBuilder builder, StringOffset subTitleOffset) { builder.AddOffset(2, subTitleOffset.Value, 0); }
  public static void AddIconType(FlatBufferBuilder builder, int iconType) { builder.AddInt(3, iconType, 0); }
  public static void AddUserInfo(FlatBufferBuilder builder, StringOffset userInfoOffset) { builder.AddOffset(4, userInfoOffset.Value, 0); }
  public static Offset<FBConfig.QuickActionsConfig> EndQuickActionsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.QuickActionsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfQuickActionsConfig(FlatBufferBuilder builder, Offset<QuickActionsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<QuickActionsConfig> o1, Offset<QuickActionsConfig> o2) =>
        new QuickActionsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new QuickActionsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static QuickActionsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    QuickActionsConfig obj_ = new QuickActionsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public QuickActionsConfigT UnPack() {
    var _o = new QuickActionsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(QuickActionsConfigT _o) {
    _o.Uid = this.Uid;
    _o.Title = this.Title;
    _o.SubTitle = this.SubTitle;
    _o.IconType = this.IconType;
    _o.UserInfo = this.UserInfo;
  }
  public static Offset<FBConfig.QuickActionsConfig> Pack(FlatBufferBuilder builder, QuickActionsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.QuickActionsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _sub_title = _o.SubTitle == null ? default(StringOffset) : builder.CreateString(_o.SubTitle);
    var _user_info = _o.UserInfo == null ? default(StringOffset) : builder.CreateString(_o.UserInfo);
    return CreateQuickActionsConfig(
      builder,
      _uid,
      _title,
      _sub_title,
      _o.IconType,
      _user_info);
  }
}

public class QuickActionsConfigT
{
  public string Uid { get; set; }
  public string Title { get; set; }
  public string SubTitle { get; set; }
  public int IconType { get; set; }
  public string UserInfo { get; set; }

  public QuickActionsConfigT() {
    this.Uid = null;
    this.Title = null;
    this.SubTitle = null;
    this.IconType = 0;
    this.UserInfo = null;
  }
}

public struct QuickActionsConfigDict : IFlatbufferConfigDict<QuickActionsConfig, QuickActionsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static QuickActionsConfigDict GetRootAsQuickActionsConfigDict(ByteBuffer _bb) { return GetRootAsQuickActionsConfigDict(_bb, new QuickActionsConfigDict()); }
  public static QuickActionsConfigDict GetRootAsQuickActionsConfigDict(ByteBuffer _bb, QuickActionsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public QuickActionsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.QuickActionsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.QuickActionsConfig?)(new FBConfig.QuickActionsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.QuickActionsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.QuickActionsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.QuickActionsConfigDict> CreateQuickActionsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    QuickActionsConfigDict.AddValues(builder, valuesOffset);
    return QuickActionsConfigDict.EndQuickActionsConfigDict(builder);
  }

  public static void StartQuickActionsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.QuickActionsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.QuickActionsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.QuickActionsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.QuickActionsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.QuickActionsConfigDict> EndQuickActionsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.QuickActionsConfigDict>(o);
  }
  public static void FinishQuickActionsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.QuickActionsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedQuickActionsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.QuickActionsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public QuickActionsConfigDictT UnPack() {
    var _o = new QuickActionsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(QuickActionsConfigDictT _o) {
    _o.Values = new List<FBConfig.QuickActionsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.QuickActionsConfigDict> Pack(FlatBufferBuilder builder, QuickActionsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.QuickActionsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.QuickActionsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.QuickActionsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateQuickActionsConfigDict(
      builder,
      _values);
  }
}

public class QuickActionsConfigDictT
{
  public List<FBConfig.QuickActionsConfigT> Values { get; set; }

  public QuickActionsConfigDictT() {
    this.Values = null;
  }
  public static QuickActionsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return QuickActionsConfigDict.GetRootAsQuickActionsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    QuickActionsConfigDict.FinishQuickActionsConfigDictBuffer(fbb, QuickActionsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
