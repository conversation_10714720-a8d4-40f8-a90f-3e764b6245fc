// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SweepstakesVideoConfig : IFlatbufferConfig<SweepstakesVideoConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SweepstakesVideoConfig GetRootAsSweepstakesVideoConfig(ByteBuffer _bb) { return GetRootAsSweepstakesVideoConfig(_bb, new SweepstakesVideoConfig()); }
  public static SweepstakesVideoConfig GetRootAsSweepstakesVideoConfig(ByteBuffer _bb, SweepstakesVideoConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SweepstakesVideoConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Title { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(6); }
  public string Url { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetUrlArray() { return __p.__vector_as_array<byte>(8); }
  public string ThumbnailName { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailNameBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetThumbnailNameBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetThumbnailNameArray() { return __p.__vector_as_array<byte>(10); }

  public static Offset<FBConfig.SweepstakesVideoConfig> CreateSweepstakesVideoConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset urlOffset = default(StringOffset),
      StringOffset thumbnail_nameOffset = default(StringOffset)) {
    builder.StartTable(4);
    SweepstakesVideoConfig.AddThumbnailName(builder, thumbnail_nameOffset);
    SweepstakesVideoConfig.AddUrl(builder, urlOffset);
    SweepstakesVideoConfig.AddTitle(builder, titleOffset);
    SweepstakesVideoConfig.AddUid(builder, uidOffset);
    return SweepstakesVideoConfig.EndSweepstakesVideoConfig(builder);
  }

  public static void StartSweepstakesVideoConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(1, titleOffset.Value, 0); }
  public static void AddUrl(FlatBufferBuilder builder, StringOffset urlOffset) { builder.AddOffset(2, urlOffset.Value, 0); }
  public static void AddThumbnailName(FlatBufferBuilder builder, StringOffset thumbnailNameOffset) { builder.AddOffset(3, thumbnailNameOffset.Value, 0); }
  public static Offset<FBConfig.SweepstakesVideoConfig> EndSweepstakesVideoConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SweepstakesVideoConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSweepstakesVideoConfig(FlatBufferBuilder builder, Offset<SweepstakesVideoConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SweepstakesVideoConfig> o1, Offset<SweepstakesVideoConfig> o2) =>
        new SweepstakesVideoConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SweepstakesVideoConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SweepstakesVideoConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SweepstakesVideoConfig obj_ = new SweepstakesVideoConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SweepstakesVideoConfigT UnPack() {
    var _o = new SweepstakesVideoConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SweepstakesVideoConfigT _o) {
    _o.Uid = this.Uid;
    _o.Title = this.Title;
    _o.Url = this.Url;
    _o.ThumbnailName = this.ThumbnailName;
  }
  public static Offset<FBConfig.SweepstakesVideoConfig> Pack(FlatBufferBuilder builder, SweepstakesVideoConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SweepstakesVideoConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _url = _o.Url == null ? default(StringOffset) : builder.CreateString(_o.Url);
    var _thumbnail_name = _o.ThumbnailName == null ? default(StringOffset) : builder.CreateString(_o.ThumbnailName);
    return CreateSweepstakesVideoConfig(
      builder,
      _uid,
      _title,
      _url,
      _thumbnail_name);
  }
}

public class SweepstakesVideoConfigT
{
  public string Uid { get; set; }
  public string Title { get; set; }
  public string Url { get; set; }
  public string ThumbnailName { get; set; }

  public SweepstakesVideoConfigT() {
    this.Uid = null;
    this.Title = null;
    this.Url = null;
    this.ThumbnailName = null;
  }
}

public struct SweepstakesVideoConfigDict : IFlatbufferConfigDict<SweepstakesVideoConfig, SweepstakesVideoConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SweepstakesVideoConfigDict GetRootAsSweepstakesVideoConfigDict(ByteBuffer _bb) { return GetRootAsSweepstakesVideoConfigDict(_bb, new SweepstakesVideoConfigDict()); }
  public static SweepstakesVideoConfigDict GetRootAsSweepstakesVideoConfigDict(ByteBuffer _bb, SweepstakesVideoConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SweepstakesVideoConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SweepstakesVideoConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SweepstakesVideoConfig?)(new FBConfig.SweepstakesVideoConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SweepstakesVideoConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SweepstakesVideoConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SweepstakesVideoConfigDict> CreateSweepstakesVideoConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SweepstakesVideoConfigDict.AddValues(builder, valuesOffset);
    return SweepstakesVideoConfigDict.EndSweepstakesVideoConfigDict(builder);
  }

  public static void StartSweepstakesVideoConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesVideoConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesVideoConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SweepstakesVideoConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SweepstakesVideoConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SweepstakesVideoConfigDict> EndSweepstakesVideoConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SweepstakesVideoConfigDict>(o);
  }
  public static void FinishSweepstakesVideoConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesVideoConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSweepstakesVideoConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesVideoConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SweepstakesVideoConfigDictT UnPack() {
    var _o = new SweepstakesVideoConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SweepstakesVideoConfigDictT _o) {
    _o.Values = new List<FBConfig.SweepstakesVideoConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SweepstakesVideoConfigDict> Pack(FlatBufferBuilder builder, SweepstakesVideoConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SweepstakesVideoConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SweepstakesVideoConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SweepstakesVideoConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSweepstakesVideoConfigDict(
      builder,
      _values);
  }
}

public class SweepstakesVideoConfigDictT
{
  public List<FBConfig.SweepstakesVideoConfigT> Values { get; set; }

  public SweepstakesVideoConfigDictT() {
    this.Values = null;
  }
  public static SweepstakesVideoConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SweepstakesVideoConfigDict.GetRootAsSweepstakesVideoConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SweepstakesVideoConfigDict.FinishSweepstakesVideoConfigDictBuffer(fbb, SweepstakesVideoConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
