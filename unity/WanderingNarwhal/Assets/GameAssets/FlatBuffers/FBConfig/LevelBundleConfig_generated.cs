// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LevelBundleConfig : IFlatbufferConfig<LevelBundleConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelBundleConfig GetRootAsLevelBundleConfig(ByteBuffer _bb) { return GetRootAsLevelBundleConfig(_bb, new LevelBundleConfig()); }
  public static LevelBundleConfig GetRootAsLevelBundleConfig(ByteBuffer _bb, LevelBundleConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelBundleConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string AssetBundles(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int AssetBundlesLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string AssetBundlesRegex(int j) { int o = __p.__offset(8); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int AssetBundlesRegexLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.LevelBundleConfig> CreateLevelBundleConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset asset_bundlesOffset = default(VectorOffset),
      VectorOffset asset_bundles_regexOffset = default(VectorOffset)) {
    builder.StartTable(3);
    LevelBundleConfig.AddAssetBundlesRegex(builder, asset_bundles_regexOffset);
    LevelBundleConfig.AddAssetBundles(builder, asset_bundlesOffset);
    LevelBundleConfig.AddUid(builder, uidOffset);
    return LevelBundleConfig.EndLevelBundleConfig(builder);
  }

  public static void StartLevelBundleConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddAssetBundles(FlatBufferBuilder builder, VectorOffset assetBundlesOffset) { builder.AddOffset(1, assetBundlesOffset.Value, 0); }
  public static VectorOffset CreateAssetBundlesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAssetBundlesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssetBundlesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssetBundlesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAssetBundlesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAssetBundlesRegex(FlatBufferBuilder builder, VectorOffset assetBundlesRegexOffset) { builder.AddOffset(2, assetBundlesRegexOffset.Value, 0); }
  public static VectorOffset CreateAssetBundlesRegexVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAssetBundlesRegexVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssetBundlesRegexVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssetBundlesRegexVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAssetBundlesRegexVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelBundleConfig> EndLevelBundleConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LevelBundleConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLevelBundleConfig(FlatBufferBuilder builder, Offset<LevelBundleConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LevelBundleConfig> o1, Offset<LevelBundleConfig> o2) =>
        new LevelBundleConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LevelBundleConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LevelBundleConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LevelBundleConfig obj_ = new LevelBundleConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LevelBundleConfigT UnPack() {
    var _o = new LevelBundleConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelBundleConfigT _o) {
    _o.Uid = this.Uid;
    _o.AssetBundles = new List<string>();
    for (var _j = 0; _j < this.AssetBundlesLength; ++_j) {_o.AssetBundles.Add(this.AssetBundles(_j));}
    _o.AssetBundlesRegex = new List<string>();
    for (var _j = 0; _j < this.AssetBundlesRegexLength; ++_j) {_o.AssetBundlesRegex.Add(this.AssetBundlesRegex(_j));}
  }
  public static Offset<FBConfig.LevelBundleConfig> Pack(FlatBufferBuilder builder, LevelBundleConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelBundleConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _asset_bundles = default(VectorOffset);
    if (_o.AssetBundles != null) {
      var __asset_bundles = new StringOffset[_o.AssetBundles.Count];
      for (var _j = 0; _j < __asset_bundles.Length; ++_j) { __asset_bundles[_j] = builder.CreateString(_o.AssetBundles[_j]); }
      _asset_bundles = CreateAssetBundlesVector(builder, __asset_bundles);
    }
    var _asset_bundles_regex = default(VectorOffset);
    if (_o.AssetBundlesRegex != null) {
      var __asset_bundles_regex = new StringOffset[_o.AssetBundlesRegex.Count];
      for (var _j = 0; _j < __asset_bundles_regex.Length; ++_j) { __asset_bundles_regex[_j] = builder.CreateString(_o.AssetBundlesRegex[_j]); }
      _asset_bundles_regex = CreateAssetBundlesRegexVector(builder, __asset_bundles_regex);
    }
    return CreateLevelBundleConfig(
      builder,
      _uid,
      _asset_bundles,
      _asset_bundles_regex);
  }
}

public class LevelBundleConfigT
{
  public string Uid { get; set; }
  public List<string> AssetBundles { get; set; }
  public List<string> AssetBundlesRegex { get; set; }

  public LevelBundleConfigT() {
    this.Uid = null;
    this.AssetBundles = null;
    this.AssetBundlesRegex = null;
  }
}

public struct LevelBundleConfigDict : IFlatbufferConfigDict<LevelBundleConfig, LevelBundleConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelBundleConfigDict GetRootAsLevelBundleConfigDict(ByteBuffer _bb) { return GetRootAsLevelBundleConfigDict(_bb, new LevelBundleConfigDict()); }
  public static LevelBundleConfigDict GetRootAsLevelBundleConfigDict(ByteBuffer _bb, LevelBundleConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelBundleConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LevelBundleConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LevelBundleConfig?)(new FBConfig.LevelBundleConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LevelBundleConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LevelBundleConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LevelBundleConfigDict> CreateLevelBundleConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LevelBundleConfigDict.AddValues(builder, valuesOffset);
    return LevelBundleConfigDict.EndLevelBundleConfigDict(builder);
  }

  public static void StartLevelBundleConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LevelBundleConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LevelBundleConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LevelBundleConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LevelBundleConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelBundleConfigDict> EndLevelBundleConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LevelBundleConfigDict>(o);
  }
  public static void FinishLevelBundleConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelBundleConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLevelBundleConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelBundleConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LevelBundleConfigDictT UnPack() {
    var _o = new LevelBundleConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelBundleConfigDictT _o) {
    _o.Values = new List<FBConfig.LevelBundleConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LevelBundleConfigDict> Pack(FlatBufferBuilder builder, LevelBundleConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelBundleConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LevelBundleConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LevelBundleConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLevelBundleConfigDict(
      builder,
      _values);
  }
}

public class LevelBundleConfigDictT
{
  public List<FBConfig.LevelBundleConfigT> Values { get; set; }

  public LevelBundleConfigDictT() {
    this.Values = null;
  }
  public static LevelBundleConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LevelBundleConfigDict.GetRootAsLevelBundleConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LevelBundleConfigDict.FinishLevelBundleConfigDictBuffer(fbb, LevelBundleConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
