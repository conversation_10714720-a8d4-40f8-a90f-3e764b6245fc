// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct BoosterConfig : IFlatbufferConfig<BoosterConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BoosterConfig GetRootAsBoosterConfig(ByteBuffer _bb) { return GetRootAsBoosterConfig(_bb, new BoosterConfig()); }
  public static BoosterConfig GetRootAsBoosterConfig(ByteBuffer _bb, BoosterConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BoosterConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string Thumbnail { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(8); }
  public string Description { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescriptionBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetDescriptionBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetDescriptionArray() { return __p.__vector_as_array<byte>(10); }
  public string EnumName { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetEnumNameBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetEnumNameBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetEnumNameArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.BoosterConfig> CreateBoosterConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset thumbnailOffset = default(StringOffset),
      StringOffset descriptionOffset = default(StringOffset),
      StringOffset enum_nameOffset = default(StringOffset)) {
    builder.StartTable(5);
    BoosterConfig.AddEnumName(builder, enum_nameOffset);
    BoosterConfig.AddDescription(builder, descriptionOffset);
    BoosterConfig.AddThumbnail(builder, thumbnailOffset);
    BoosterConfig.AddName(builder, nameOffset);
    BoosterConfig.AddUid(builder, uidOffset);
    return BoosterConfig.EndBoosterConfig(builder);
  }

  public static void StartBoosterConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(2, thumbnailOffset.Value, 0); }
  public static void AddDescription(FlatBufferBuilder builder, StringOffset descriptionOffset) { builder.AddOffset(3, descriptionOffset.Value, 0); }
  public static void AddEnumName(FlatBufferBuilder builder, StringOffset enumNameOffset) { builder.AddOffset(4, enumNameOffset.Value, 0); }
  public static Offset<FBConfig.BoosterConfig> EndBoosterConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.BoosterConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfBoosterConfig(FlatBufferBuilder builder, Offset<BoosterConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<BoosterConfig> o1, Offset<BoosterConfig> o2) =>
        new BoosterConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new BoosterConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static BoosterConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    BoosterConfig obj_ = new BoosterConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public BoosterConfigT UnPack() {
    var _o = new BoosterConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BoosterConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Thumbnail = this.Thumbnail;
    _o.Description = this.Description;
    _o.EnumName = this.EnumName;
  }
  public static Offset<FBConfig.BoosterConfig> Pack(FlatBufferBuilder builder, BoosterConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.BoosterConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    var _description = _o.Description == null ? default(StringOffset) : builder.CreateString(_o.Description);
    var _enum_name = _o.EnumName == null ? default(StringOffset) : builder.CreateString(_o.EnumName);
    return CreateBoosterConfig(
      builder,
      _uid,
      _name,
      _thumbnail,
      _description,
      _enum_name);
  }
}

public class BoosterConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string Thumbnail { get; set; }
  public string Description { get; set; }
  public string EnumName { get; set; }

  public BoosterConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Thumbnail = null;
    this.Description = null;
    this.EnumName = null;
  }
}

public struct BoosterConfigDict : IFlatbufferConfigDict<BoosterConfig, BoosterConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BoosterConfigDict GetRootAsBoosterConfigDict(ByteBuffer _bb) { return GetRootAsBoosterConfigDict(_bb, new BoosterConfigDict()); }
  public static BoosterConfigDict GetRootAsBoosterConfigDict(ByteBuffer _bb, BoosterConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BoosterConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.BoosterConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.BoosterConfig?)(new FBConfig.BoosterConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.BoosterConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.BoosterConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.BoosterConfigDict> CreateBoosterConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    BoosterConfigDict.AddValues(builder, valuesOffset);
    return BoosterConfigDict.EndBoosterConfigDict(builder);
  }

  public static void StartBoosterConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.BoosterConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.BoosterConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.BoosterConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.BoosterConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.BoosterConfigDict> EndBoosterConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.BoosterConfigDict>(o);
  }
  public static void FinishBoosterConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.BoosterConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedBoosterConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.BoosterConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public BoosterConfigDictT UnPack() {
    var _o = new BoosterConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BoosterConfigDictT _o) {
    _o.Values = new List<FBConfig.BoosterConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.BoosterConfigDict> Pack(FlatBufferBuilder builder, BoosterConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.BoosterConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.BoosterConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.BoosterConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateBoosterConfigDict(
      builder,
      _values);
  }
}

public class BoosterConfigDictT
{
  public List<FBConfig.BoosterConfigT> Values { get; set; }

  public BoosterConfigDictT() {
    this.Values = null;
  }
  public static BoosterConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return BoosterConfigDict.GetRootAsBoosterConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    BoosterConfigDict.FinishBoosterConfigDictBuffer(fbb, BoosterConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
