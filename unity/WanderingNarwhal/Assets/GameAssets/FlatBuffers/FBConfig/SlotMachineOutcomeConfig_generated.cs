// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SlotMachineOutcomeConfig : IFlatbufferConfig<SlotMachineOutcomeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SlotMachineOutcomeConfig GetRootAsSlotMachineOutcomeConfig(ByteBuffer _bb) { return GetRootAsSlotMachineOutcomeConfig(_bb, new SlotMachineOutcomeConfig()); }
  public static SlotMachineOutcomeConfig GetRootAsSlotMachineOutcomeConfig(ByteBuffer _bb, SlotMachineOutcomeConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SlotMachineOutcomeConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Count { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCount(int count) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, count); return true; } else { return false; } }
  public float Weight { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateWeight(float weight) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, weight); return true; } else { return false; } }

  public static Offset<FBConfig.SlotMachineOutcomeConfig> CreateSlotMachineOutcomeConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int count = 0,
      float weight = 0.0f) {
    builder.StartTable(3);
    SlotMachineOutcomeConfig.AddWeight(builder, weight);
    SlotMachineOutcomeConfig.AddCount(builder, count);
    SlotMachineOutcomeConfig.AddUid(builder, uidOffset);
    return SlotMachineOutcomeConfig.EndSlotMachineOutcomeConfig(builder);
  }

  public static void StartSlotMachineOutcomeConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCount(FlatBufferBuilder builder, int count) { builder.AddInt(1, count, 0); }
  public static void AddWeight(FlatBufferBuilder builder, float weight) { builder.AddFloat(2, weight, 0.0f); }
  public static Offset<FBConfig.SlotMachineOutcomeConfig> EndSlotMachineOutcomeConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SlotMachineOutcomeConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSlotMachineOutcomeConfig(FlatBufferBuilder builder, Offset<SlotMachineOutcomeConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SlotMachineOutcomeConfig> o1, Offset<SlotMachineOutcomeConfig> o2) =>
        new SlotMachineOutcomeConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SlotMachineOutcomeConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SlotMachineOutcomeConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SlotMachineOutcomeConfig obj_ = new SlotMachineOutcomeConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SlotMachineOutcomeConfigT UnPack() {
    var _o = new SlotMachineOutcomeConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SlotMachineOutcomeConfigT _o) {
    _o.Uid = this.Uid;
    _o.Count = this.Count;
    _o.Weight = this.Weight;
  }
  public static Offset<FBConfig.SlotMachineOutcomeConfig> Pack(FlatBufferBuilder builder, SlotMachineOutcomeConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SlotMachineOutcomeConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateSlotMachineOutcomeConfig(
      builder,
      _uid,
      _o.Count,
      _o.Weight);
  }
}

public class SlotMachineOutcomeConfigT
{
  public string Uid { get; set; }
  public int Count { get; set; }
  public float Weight { get; set; }

  public SlotMachineOutcomeConfigT() {
    this.Uid = null;
    this.Count = 0;
    this.Weight = 0.0f;
  }
}

public struct SlotMachineOutcomeConfigDict : IFlatbufferConfigDict<SlotMachineOutcomeConfig, SlotMachineOutcomeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SlotMachineOutcomeConfigDict GetRootAsSlotMachineOutcomeConfigDict(ByteBuffer _bb) { return GetRootAsSlotMachineOutcomeConfigDict(_bb, new SlotMachineOutcomeConfigDict()); }
  public static SlotMachineOutcomeConfigDict GetRootAsSlotMachineOutcomeConfigDict(ByteBuffer _bb, SlotMachineOutcomeConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SlotMachineOutcomeConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SlotMachineOutcomeConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SlotMachineOutcomeConfig?)(new FBConfig.SlotMachineOutcomeConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SlotMachineOutcomeConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SlotMachineOutcomeConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SlotMachineOutcomeConfigDict> CreateSlotMachineOutcomeConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SlotMachineOutcomeConfigDict.AddValues(builder, valuesOffset);
    return SlotMachineOutcomeConfigDict.EndSlotMachineOutcomeConfigDict(builder);
  }

  public static void StartSlotMachineOutcomeConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SlotMachineOutcomeConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SlotMachineOutcomeConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SlotMachineOutcomeConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SlotMachineOutcomeConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SlotMachineOutcomeConfigDict> EndSlotMachineOutcomeConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SlotMachineOutcomeConfigDict>(o);
  }
  public static void FinishSlotMachineOutcomeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SlotMachineOutcomeConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSlotMachineOutcomeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SlotMachineOutcomeConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SlotMachineOutcomeConfigDictT UnPack() {
    var _o = new SlotMachineOutcomeConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SlotMachineOutcomeConfigDictT _o) {
    _o.Values = new List<FBConfig.SlotMachineOutcomeConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SlotMachineOutcomeConfigDict> Pack(FlatBufferBuilder builder, SlotMachineOutcomeConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SlotMachineOutcomeConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SlotMachineOutcomeConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SlotMachineOutcomeConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSlotMachineOutcomeConfigDict(
      builder,
      _values);
  }
}

public class SlotMachineOutcomeConfigDictT
{
  public List<FBConfig.SlotMachineOutcomeConfigT> Values { get; set; }

  public SlotMachineOutcomeConfigDictT() {
    this.Values = null;
  }
  public static SlotMachineOutcomeConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SlotMachineOutcomeConfigDict.GetRootAsSlotMachineOutcomeConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SlotMachineOutcomeConfigDict.FinishSlotMachineOutcomeConfigDictBuffer(fbb, SlotMachineOutcomeConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
