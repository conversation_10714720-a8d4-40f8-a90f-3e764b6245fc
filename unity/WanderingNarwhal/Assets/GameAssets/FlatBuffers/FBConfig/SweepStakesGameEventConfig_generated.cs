// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SweepStakesGameEventConfig : IFlatbufferConfig<SweepStakesGameEventConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SweepStakesGameEventConfig GetRootAsSweepStakesGameEventConfig(ByteBuffer _bb) { return GetRootAsSweepStakesGameEventConfig(_bb, new SweepStakesGameEventConfig()); }
  public static SweepStakesGameEventConfig GetRootAsSweepStakesGameEventConfig(ByteBuffer _bb, SweepStakesGameEventConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SweepStakesGameEventConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string WinnersVideoIds(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int WinnersVideoIdsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string TermsUrl { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTermsUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetTermsUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetTermsUrlArray() { return __p.__vector_as_array<byte>(8); }
  public float MainBannersStayDuration { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateMainBannersStayDuration(float main_banners_stay_duration) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, main_banners_stay_duration); return true; } else { return false; } }
  public float MainBannersScrollDuration { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateMainBannersScrollDuration(float main_banners_scroll_duration) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, main_banners_scroll_duration); return true; } else { return false; } }
  public float EligibilityBannersStayDuration { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateEligibilityBannersStayDuration(float eligibility_banners_stay_duration) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, eligibility_banners_stay_duration); return true; } else { return false; } }
  public float EligibilityBannersScrollDuration { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateEligibilityBannersScrollDuration(float eligibility_banners_scroll_duration) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, eligibility_banners_scroll_duration); return true; } else { return false; } }
  public string NoConnectionMessageId { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNoConnectionMessageIdBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetNoConnectionMessageIdBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetNoConnectionMessageIdArray() { return __p.__vector_as_array<byte>(18); }
  public float MainProgressBarStayDuration { get { int o = __p.__offset(20); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateMainProgressBarStayDuration(float main_progress_bar_stay_duration) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, main_progress_bar_stay_duration); return true; } else { return false; } }
  public int TicketAmounts(int j) { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int TicketAmountsLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetTicketAmountsBytes() { return __p.__vector_as_span<int>(22, 4); }
#else
  public ArraySegment<byte>? GetTicketAmountsBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public int[] GetTicketAmountsArray() { return __p.__vector_as_array<int>(22); }
  public bool MutateTicketAmounts(int j, int ticket_amounts) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, ticket_amounts); return true; } else { return false; } }

  public static Offset<FBConfig.SweepStakesGameEventConfig> CreateSweepStakesGameEventConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset winners_video_idsOffset = default(VectorOffset),
      StringOffset terms_urlOffset = default(StringOffset),
      float main_banners_stay_duration = 0.0f,
      float main_banners_scroll_duration = 0.0f,
      float eligibility_banners_stay_duration = 0.0f,
      float eligibility_banners_scroll_duration = 0.0f,
      StringOffset no_connection_message_idOffset = default(StringOffset),
      float main_progress_bar_stay_duration = 0.0f,
      VectorOffset ticket_amountsOffset = default(VectorOffset)) {
    builder.StartTable(10);
    SweepStakesGameEventConfig.AddTicketAmounts(builder, ticket_amountsOffset);
    SweepStakesGameEventConfig.AddMainProgressBarStayDuration(builder, main_progress_bar_stay_duration);
    SweepStakesGameEventConfig.AddNoConnectionMessageId(builder, no_connection_message_idOffset);
    SweepStakesGameEventConfig.AddEligibilityBannersScrollDuration(builder, eligibility_banners_scroll_duration);
    SweepStakesGameEventConfig.AddEligibilityBannersStayDuration(builder, eligibility_banners_stay_duration);
    SweepStakesGameEventConfig.AddMainBannersScrollDuration(builder, main_banners_scroll_duration);
    SweepStakesGameEventConfig.AddMainBannersStayDuration(builder, main_banners_stay_duration);
    SweepStakesGameEventConfig.AddTermsUrl(builder, terms_urlOffset);
    SweepStakesGameEventConfig.AddWinnersVideoIds(builder, winners_video_idsOffset);
    SweepStakesGameEventConfig.AddUid(builder, uidOffset);
    return SweepStakesGameEventConfig.EndSweepStakesGameEventConfig(builder);
  }

  public static void StartSweepStakesGameEventConfig(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddWinnersVideoIds(FlatBufferBuilder builder, VectorOffset winnersVideoIdsOffset) { builder.AddOffset(1, winnersVideoIdsOffset.Value, 0); }
  public static VectorOffset CreateWinnersVideoIdsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateWinnersVideoIdsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWinnersVideoIdsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWinnersVideoIdsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartWinnersVideoIdsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTermsUrl(FlatBufferBuilder builder, StringOffset termsUrlOffset) { builder.AddOffset(2, termsUrlOffset.Value, 0); }
  public static void AddMainBannersStayDuration(FlatBufferBuilder builder, float mainBannersStayDuration) { builder.AddFloat(3, mainBannersStayDuration, 0.0f); }
  public static void AddMainBannersScrollDuration(FlatBufferBuilder builder, float mainBannersScrollDuration) { builder.AddFloat(4, mainBannersScrollDuration, 0.0f); }
  public static void AddEligibilityBannersStayDuration(FlatBufferBuilder builder, float eligibilityBannersStayDuration) { builder.AddFloat(5, eligibilityBannersStayDuration, 0.0f); }
  public static void AddEligibilityBannersScrollDuration(FlatBufferBuilder builder, float eligibilityBannersScrollDuration) { builder.AddFloat(6, eligibilityBannersScrollDuration, 0.0f); }
  public static void AddNoConnectionMessageId(FlatBufferBuilder builder, StringOffset noConnectionMessageIdOffset) { builder.AddOffset(7, noConnectionMessageIdOffset.Value, 0); }
  public static void AddMainProgressBarStayDuration(FlatBufferBuilder builder, float mainProgressBarStayDuration) { builder.AddFloat(8, mainProgressBarStayDuration, 0.0f); }
  public static void AddTicketAmounts(FlatBufferBuilder builder, VectorOffset ticketAmountsOffset) { builder.AddOffset(9, ticketAmountsOffset.Value, 0); }
  public static VectorOffset CreateTicketAmountsVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateTicketAmountsVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTicketAmountsVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTicketAmountsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTicketAmountsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SweepStakesGameEventConfig> EndSweepStakesGameEventConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SweepStakesGameEventConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSweepStakesGameEventConfig(FlatBufferBuilder builder, Offset<SweepStakesGameEventConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SweepStakesGameEventConfig> o1, Offset<SweepStakesGameEventConfig> o2) =>
        new SweepStakesGameEventConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SweepStakesGameEventConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SweepStakesGameEventConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SweepStakesGameEventConfig obj_ = new SweepStakesGameEventConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SweepStakesGameEventConfigT UnPack() {
    var _o = new SweepStakesGameEventConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SweepStakesGameEventConfigT _o) {
    _o.Uid = this.Uid;
    _o.WinnersVideoIds = new List<string>();
    for (var _j = 0; _j < this.WinnersVideoIdsLength; ++_j) {_o.WinnersVideoIds.Add(this.WinnersVideoIds(_j));}
    _o.TermsUrl = this.TermsUrl;
    _o.MainBannersStayDuration = this.MainBannersStayDuration;
    _o.MainBannersScrollDuration = this.MainBannersScrollDuration;
    _o.EligibilityBannersStayDuration = this.EligibilityBannersStayDuration;
    _o.EligibilityBannersScrollDuration = this.EligibilityBannersScrollDuration;
    _o.NoConnectionMessageId = this.NoConnectionMessageId;
    _o.MainProgressBarStayDuration = this.MainProgressBarStayDuration;
    _o.TicketAmounts = new List<int>();
    for (var _j = 0; _j < this.TicketAmountsLength; ++_j) {_o.TicketAmounts.Add(this.TicketAmounts(_j));}
  }
  public static Offset<FBConfig.SweepStakesGameEventConfig> Pack(FlatBufferBuilder builder, SweepStakesGameEventConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SweepStakesGameEventConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _winners_video_ids = default(VectorOffset);
    if (_o.WinnersVideoIds != null) {
      var __winners_video_ids = new StringOffset[_o.WinnersVideoIds.Count];
      for (var _j = 0; _j < __winners_video_ids.Length; ++_j) { __winners_video_ids[_j] = builder.CreateString(_o.WinnersVideoIds[_j]); }
      _winners_video_ids = CreateWinnersVideoIdsVector(builder, __winners_video_ids);
    }
    var _terms_url = _o.TermsUrl == null ? default(StringOffset) : builder.CreateString(_o.TermsUrl);
    var _no_connection_message_id = _o.NoConnectionMessageId == null ? default(StringOffset) : builder.CreateString(_o.NoConnectionMessageId);
    var _ticket_amounts = default(VectorOffset);
    if (_o.TicketAmounts != null) {
      var __ticket_amounts = _o.TicketAmounts.ToArray();
      _ticket_amounts = CreateTicketAmountsVector(builder, __ticket_amounts);
    }
    return CreateSweepStakesGameEventConfig(
      builder,
      _uid,
      _winners_video_ids,
      _terms_url,
      _o.MainBannersStayDuration,
      _o.MainBannersScrollDuration,
      _o.EligibilityBannersStayDuration,
      _o.EligibilityBannersScrollDuration,
      _no_connection_message_id,
      _o.MainProgressBarStayDuration,
      _ticket_amounts);
  }
}

public class SweepStakesGameEventConfigT
{
  public string Uid { get; set; }
  public List<string> WinnersVideoIds { get; set; }
  public string TermsUrl { get; set; }
  public float MainBannersStayDuration { get; set; }
  public float MainBannersScrollDuration { get; set; }
  public float EligibilityBannersStayDuration { get; set; }
  public float EligibilityBannersScrollDuration { get; set; }
  public string NoConnectionMessageId { get; set; }
  public float MainProgressBarStayDuration { get; set; }
  public List<int> TicketAmounts { get; set; }

  public SweepStakesGameEventConfigT() {
    this.Uid = null;
    this.WinnersVideoIds = null;
    this.TermsUrl = null;
    this.MainBannersStayDuration = 0.0f;
    this.MainBannersScrollDuration = 0.0f;
    this.EligibilityBannersStayDuration = 0.0f;
    this.EligibilityBannersScrollDuration = 0.0f;
    this.NoConnectionMessageId = null;
    this.MainProgressBarStayDuration = 0.0f;
    this.TicketAmounts = null;
  }
}

public struct SweepStakesGameEventConfigDict : IFlatbufferConfigDict<SweepStakesGameEventConfig, SweepStakesGameEventConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SweepStakesGameEventConfigDict GetRootAsSweepStakesGameEventConfigDict(ByteBuffer _bb) { return GetRootAsSweepStakesGameEventConfigDict(_bb, new SweepStakesGameEventConfigDict()); }
  public static SweepStakesGameEventConfigDict GetRootAsSweepStakesGameEventConfigDict(ByteBuffer _bb, SweepStakesGameEventConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SweepStakesGameEventConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SweepStakesGameEventConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SweepStakesGameEventConfig?)(new FBConfig.SweepStakesGameEventConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SweepStakesGameEventConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SweepStakesGameEventConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SweepStakesGameEventConfigDict> CreateSweepStakesGameEventConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SweepStakesGameEventConfigDict.AddValues(builder, valuesOffset);
    return SweepStakesGameEventConfigDict.EndSweepStakesGameEventConfigDict(builder);
  }

  public static void StartSweepStakesGameEventConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SweepStakesGameEventConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SweepStakesGameEventConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SweepStakesGameEventConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SweepStakesGameEventConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SweepStakesGameEventConfigDict> EndSweepStakesGameEventConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SweepStakesGameEventConfigDict>(o);
  }
  public static void FinishSweepStakesGameEventConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SweepStakesGameEventConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSweepStakesGameEventConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SweepStakesGameEventConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SweepStakesGameEventConfigDictT UnPack() {
    var _o = new SweepStakesGameEventConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SweepStakesGameEventConfigDictT _o) {
    _o.Values = new List<FBConfig.SweepStakesGameEventConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SweepStakesGameEventConfigDict> Pack(FlatBufferBuilder builder, SweepStakesGameEventConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SweepStakesGameEventConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SweepStakesGameEventConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SweepStakesGameEventConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSweepStakesGameEventConfigDict(
      builder,
      _values);
  }
}

public class SweepStakesGameEventConfigDictT
{
  public List<FBConfig.SweepStakesGameEventConfigT> Values { get; set; }

  public SweepStakesGameEventConfigDictT() {
    this.Values = null;
  }
  public static SweepStakesGameEventConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SweepStakesGameEventConfigDict.GetRootAsSweepStakesGameEventConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SweepStakesGameEventConfigDict.FinishSweepStakesGameEventConfigDictBuffer(fbb, SweepStakesGameEventConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
