// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SweepstakesDailyLoginConfig : IFlatbufferConfig<SweepstakesDailyLoginConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SweepstakesDailyLoginConfig GetRootAsSweepstakesDailyLoginConfig(ByteBuffer _bb) { return GetRootAsSweepstakesDailyLoginConfig(_bb, new SweepstakesDailyLoginConfig()); }
  public static SweepstakesDailyLoginConfig GetRootAsSweepstakesDailyLoginConfig(ByteBuffer _bb, SweepstakesDailyLoginConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SweepstakesDailyLoginConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Day { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDay(int day) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, day); return true; } else { return false; } }
  public FBConfig.DictStringInt? Reward(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.SweepstakesDailyLoginConfig> CreateSweepstakesDailyLoginConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int day = 0,
      VectorOffset rewardOffset = default(VectorOffset)) {
    builder.StartTable(3);
    SweepstakesDailyLoginConfig.AddReward(builder, rewardOffset);
    SweepstakesDailyLoginConfig.AddDay(builder, day);
    SweepstakesDailyLoginConfig.AddUid(builder, uidOffset);
    return SweepstakesDailyLoginConfig.EndSweepstakesDailyLoginConfig(builder);
  }

  public static void StartSweepstakesDailyLoginConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddDay(FlatBufferBuilder builder, int day) { builder.AddInt(1, day, 0); }
  public static void AddReward(FlatBufferBuilder builder, VectorOffset rewardOffset) { builder.AddOffset(2, rewardOffset.Value, 0); }
  public static VectorOffset CreateRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SweepstakesDailyLoginConfig> EndSweepstakesDailyLoginConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SweepstakesDailyLoginConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSweepstakesDailyLoginConfig(FlatBufferBuilder builder, Offset<SweepstakesDailyLoginConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SweepstakesDailyLoginConfig> o1, Offset<SweepstakesDailyLoginConfig> o2) =>
        new SweepstakesDailyLoginConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SweepstakesDailyLoginConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SweepstakesDailyLoginConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SweepstakesDailyLoginConfig obj_ = new SweepstakesDailyLoginConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SweepstakesDailyLoginConfigT UnPack() {
    var _o = new SweepstakesDailyLoginConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SweepstakesDailyLoginConfigT _o) {
    _o.Uid = this.Uid;
    _o.Day = this.Day;
    _o.Reward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardLength; ++_j) {_o.Reward.Add(this.Reward(_j).HasValue ? this.Reward(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SweepstakesDailyLoginConfig> Pack(FlatBufferBuilder builder, SweepstakesDailyLoginConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SweepstakesDailyLoginConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _reward = default(VectorOffset);
    if (_o.Reward != null) {
      var __reward = new Offset<FBConfig.DictStringInt>[_o.Reward.Count];
      for (var _j = 0; _j < __reward.Length; ++_j) { __reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.Reward[_j]); }
      _reward = CreateRewardVector(builder, __reward);
    }
    return CreateSweepstakesDailyLoginConfig(
      builder,
      _uid,
      _o.Day,
      _reward);
  }
}

public class SweepstakesDailyLoginConfigT
{
  public string Uid { get; set; }
  public int Day { get; set; }
  public List<FBConfig.DictStringIntT> Reward { get; set; }

  public SweepstakesDailyLoginConfigT() {
    this.Uid = null;
    this.Day = 0;
    this.Reward = null;
  }
}

public struct SweepstakesDailyLoginConfigDict : IFlatbufferConfigDict<SweepstakesDailyLoginConfig, SweepstakesDailyLoginConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SweepstakesDailyLoginConfigDict GetRootAsSweepstakesDailyLoginConfigDict(ByteBuffer _bb) { return GetRootAsSweepstakesDailyLoginConfigDict(_bb, new SweepstakesDailyLoginConfigDict()); }
  public static SweepstakesDailyLoginConfigDict GetRootAsSweepstakesDailyLoginConfigDict(ByteBuffer _bb, SweepstakesDailyLoginConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SweepstakesDailyLoginConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SweepstakesDailyLoginConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SweepstakesDailyLoginConfig?)(new FBConfig.SweepstakesDailyLoginConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SweepstakesDailyLoginConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SweepstakesDailyLoginConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SweepstakesDailyLoginConfigDict> CreateSweepstakesDailyLoginConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SweepstakesDailyLoginConfigDict.AddValues(builder, valuesOffset);
    return SweepstakesDailyLoginConfigDict.EndSweepstakesDailyLoginConfigDict(builder);
  }

  public static void StartSweepstakesDailyLoginConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesDailyLoginConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesDailyLoginConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SweepstakesDailyLoginConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SweepstakesDailyLoginConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SweepstakesDailyLoginConfigDict> EndSweepstakesDailyLoginConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SweepstakesDailyLoginConfigDict>(o);
  }
  public static void FinishSweepstakesDailyLoginConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesDailyLoginConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSweepstakesDailyLoginConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SweepstakesDailyLoginConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SweepstakesDailyLoginConfigDictT UnPack() {
    var _o = new SweepstakesDailyLoginConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SweepstakesDailyLoginConfigDictT _o) {
    _o.Values = new List<FBConfig.SweepstakesDailyLoginConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SweepstakesDailyLoginConfigDict> Pack(FlatBufferBuilder builder, SweepstakesDailyLoginConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SweepstakesDailyLoginConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SweepstakesDailyLoginConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SweepstakesDailyLoginConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSweepstakesDailyLoginConfigDict(
      builder,
      _values);
  }
}

public class SweepstakesDailyLoginConfigDictT
{
  public List<FBConfig.SweepstakesDailyLoginConfigT> Values { get; set; }

  public SweepstakesDailyLoginConfigDictT() {
    this.Values = null;
  }
  public static SweepstakesDailyLoginConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SweepstakesDailyLoginConfigDict.GetRootAsSweepstakesDailyLoginConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SweepstakesDailyLoginConfigDict.FinishSweepstakesDailyLoginConfigDictBuffer(fbb, SweepstakesDailyLoginConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
