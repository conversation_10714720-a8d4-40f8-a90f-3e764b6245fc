// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct CollectionCardsConfig : IFlatbufferConfig<CollectionCardsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CollectionCardsConfig GetRootAsCollectionCardsConfig(ByteBuffer _bb) { return GetRootAsCollectionCardsConfig(_bb, new CollectionCardsConfig()); }
  public static CollectionCardsConfig GetRootAsCollectionCardsConfig(ByteBuffer _bb, CollectionCardsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CollectionCardsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string SpriteName { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSpriteNameBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetSpriteNameBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetSpriteNameArray() { return __p.__vector_as_array<byte>(8); }
  public string UniqueInfo { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUniqueInfoBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetUniqueInfoBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetUniqueInfoArray() { return __p.__vector_as_array<byte>(10); }
  public int Rarity { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRarity(int rarity) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, rarity); return true; } else { return false; } }
  public string SetUid { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSetUidBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetSetUidBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetSetUidArray() { return __p.__vector_as_array<byte>(14); }
  public int Order { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }

  public static Offset<FBConfig.CollectionCardsConfig> CreateCollectionCardsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset sprite_nameOffset = default(StringOffset),
      StringOffset unique_infoOffset = default(StringOffset),
      int rarity = 0,
      StringOffset set_uidOffset = default(StringOffset),
      int order = 0) {
    builder.StartTable(7);
    CollectionCardsConfig.AddOrder(builder, order);
    CollectionCardsConfig.AddSetUid(builder, set_uidOffset);
    CollectionCardsConfig.AddRarity(builder, rarity);
    CollectionCardsConfig.AddUniqueInfo(builder, unique_infoOffset);
    CollectionCardsConfig.AddSpriteName(builder, sprite_nameOffset);
    CollectionCardsConfig.AddName(builder, nameOffset);
    CollectionCardsConfig.AddUid(builder, uidOffset);
    return CollectionCardsConfig.EndCollectionCardsConfig(builder);
  }

  public static void StartCollectionCardsConfig(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddSpriteName(FlatBufferBuilder builder, StringOffset spriteNameOffset) { builder.AddOffset(2, spriteNameOffset.Value, 0); }
  public static void AddUniqueInfo(FlatBufferBuilder builder, StringOffset uniqueInfoOffset) { builder.AddOffset(3, uniqueInfoOffset.Value, 0); }
  public static void AddRarity(FlatBufferBuilder builder, int rarity) { builder.AddInt(4, rarity, 0); }
  public static void AddSetUid(FlatBufferBuilder builder, StringOffset setUidOffset) { builder.AddOffset(5, setUidOffset.Value, 0); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(6, order, 0); }
  public static Offset<FBConfig.CollectionCardsConfig> EndCollectionCardsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.CollectionCardsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfCollectionCardsConfig(FlatBufferBuilder builder, Offset<CollectionCardsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<CollectionCardsConfig> o1, Offset<CollectionCardsConfig> o2) =>
        new CollectionCardsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new CollectionCardsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static CollectionCardsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    CollectionCardsConfig obj_ = new CollectionCardsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public CollectionCardsConfigT UnPack() {
    var _o = new CollectionCardsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CollectionCardsConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.SpriteName = this.SpriteName;
    _o.UniqueInfo = this.UniqueInfo;
    _o.Rarity = this.Rarity;
    _o.SetUid = this.SetUid;
    _o.Order = this.Order;
  }
  public static Offset<FBConfig.CollectionCardsConfig> Pack(FlatBufferBuilder builder, CollectionCardsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.CollectionCardsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _sprite_name = _o.SpriteName == null ? default(StringOffset) : builder.CreateString(_o.SpriteName);
    var _unique_info = _o.UniqueInfo == null ? default(StringOffset) : builder.CreateString(_o.UniqueInfo);
    var _set_uid = _o.SetUid == null ? default(StringOffset) : builder.CreateString(_o.SetUid);
    return CreateCollectionCardsConfig(
      builder,
      _uid,
      _name,
      _sprite_name,
      _unique_info,
      _o.Rarity,
      _set_uid,
      _o.Order);
  }
}

public class CollectionCardsConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string SpriteName { get; set; }
  public string UniqueInfo { get; set; }
  public int Rarity { get; set; }
  public string SetUid { get; set; }
  public int Order { get; set; }

  public CollectionCardsConfigT() {
    this.Uid = null;
    this.Name = null;
    this.SpriteName = null;
    this.UniqueInfo = null;
    this.Rarity = 0;
    this.SetUid = null;
    this.Order = 0;
  }
}

public struct CollectionCardsConfigDict : IFlatbufferConfigDict<CollectionCardsConfig, CollectionCardsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CollectionCardsConfigDict GetRootAsCollectionCardsConfigDict(ByteBuffer _bb) { return GetRootAsCollectionCardsConfigDict(_bb, new CollectionCardsConfigDict()); }
  public static CollectionCardsConfigDict GetRootAsCollectionCardsConfigDict(ByteBuffer _bb, CollectionCardsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CollectionCardsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.CollectionCardsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.CollectionCardsConfig?)(new FBConfig.CollectionCardsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.CollectionCardsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.CollectionCardsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.CollectionCardsConfigDict> CreateCollectionCardsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    CollectionCardsConfigDict.AddValues(builder, valuesOffset);
    return CollectionCardsConfigDict.EndCollectionCardsConfigDict(builder);
  }

  public static void StartCollectionCardsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.CollectionCardsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.CollectionCardsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.CollectionCardsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.CollectionCardsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.CollectionCardsConfigDict> EndCollectionCardsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CollectionCardsConfigDict>(o);
  }
  public static void FinishCollectionCardsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CollectionCardsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedCollectionCardsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CollectionCardsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public CollectionCardsConfigDictT UnPack() {
    var _o = new CollectionCardsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CollectionCardsConfigDictT _o) {
    _o.Values = new List<FBConfig.CollectionCardsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.CollectionCardsConfigDict> Pack(FlatBufferBuilder builder, CollectionCardsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.CollectionCardsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.CollectionCardsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.CollectionCardsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateCollectionCardsConfigDict(
      builder,
      _values);
  }
}

public class CollectionCardsConfigDictT
{
  public List<FBConfig.CollectionCardsConfigT> Values { get; set; }

  public CollectionCardsConfigDictT() {
    this.Values = null;
  }
  public static CollectionCardsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return CollectionCardsConfigDict.GetRootAsCollectionCardsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    CollectionCardsConfigDict.FinishCollectionCardsConfigDictBuffer(fbb, CollectionCardsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
