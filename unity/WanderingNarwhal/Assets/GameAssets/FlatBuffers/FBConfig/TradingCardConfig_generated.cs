// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct RewardResource : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static RewardResource GetRootAsRewardResource(ByteBuffer _bb) { return GetRootAsRewardResource(_bb, new RewardResource()); }
  public static RewardResource GetRootAsRewardResource(ByteBuffer _bb, RewardResource obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public RewardResource __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public int Type { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateType(int type) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, type); return true; } else { return false; } }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public long Count { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetLong(o + __p.bb_pos) : (long)0; } }
  public bool MutateCount(long count) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutLong(o + __p.bb_pos, count); return true; } else { return false; } }

  public static Offset<FBConfig.RewardResource> CreateRewardResource(FlatBufferBuilder builder,
      int type = 0,
      StringOffset nameOffset = default(StringOffset),
      long count = 0) {
    builder.StartTable(3);
    RewardResource.AddCount(builder, count);
    RewardResource.AddName(builder, nameOffset);
    RewardResource.AddType(builder, type);
    return RewardResource.EndRewardResource(builder);
  }

  public static void StartRewardResource(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddType(FlatBufferBuilder builder, int type) { builder.AddInt(0, type, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddCount(FlatBufferBuilder builder, long count) { builder.AddLong(2, count, 0); }
  public static Offset<FBConfig.RewardResource> EndRewardResource(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.RewardResource>(o);
  }
  public RewardResourceT UnPack() {
    var _o = new RewardResourceT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(RewardResourceT _o) {
    _o.Type = this.Type;
    _o.Name = this.Name;
    _o.Count = this.Count;
  }
  public static Offset<FBConfig.RewardResource> Pack(FlatBufferBuilder builder, RewardResourceT _o) {
    if (_o == null) return default(Offset<FBConfig.RewardResource>);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    return CreateRewardResource(
      builder,
      _o.Type,
      _name,
      _o.Count);
  }
}

public class RewardResourceT
{
  public int Type { get; set; }
  public string Name { get; set; }
  public long Count { get; set; }

  public RewardResourceT() {
    this.Type = 0;
    this.Name = null;
    this.Count = 0;
  }
}

public struct LevelReward : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelReward GetRootAsLevelReward(ByteBuffer _bb) { return GetRootAsLevelReward(_bb, new LevelReward()); }
  public static LevelReward GetRootAsLevelReward(ByteBuffer _bb, LevelReward obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelReward __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float Regular { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateRegular(float regular) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, regular); return true; } else { return false; } }
  public float Worker { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateWorker(float worker) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, worker); return true; } else { return false; } }
  public FBConfig.RewardResource? Resources(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.RewardResource?)(new FBConfig.RewardResource()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ResourcesLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.LevelReward> CreateLevelReward(FlatBufferBuilder builder,
      float regular = 0.0f,
      float worker = 0.0f,
      VectorOffset resourcesOffset = default(VectorOffset)) {
    builder.StartTable(3);
    LevelReward.AddResources(builder, resourcesOffset);
    LevelReward.AddWorker(builder, worker);
    LevelReward.AddRegular(builder, regular);
    return LevelReward.EndLevelReward(builder);
  }

  public static void StartLevelReward(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddRegular(FlatBufferBuilder builder, float regular) { builder.AddFloat(0, regular, 0.0f); }
  public static void AddWorker(FlatBufferBuilder builder, float worker) { builder.AddFloat(1, worker, 0.0f); }
  public static void AddResources(FlatBufferBuilder builder, VectorOffset resourcesOffset) { builder.AddOffset(2, resourcesOffset.Value, 0); }
  public static VectorOffset CreateResourcesVector(FlatBufferBuilder builder, Offset<FBConfig.RewardResource>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateResourcesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.RewardResource>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateResourcesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.RewardResource>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateResourcesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.RewardResource>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartResourcesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelReward> EndLevelReward(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LevelReward>(o);
  }
  public LevelRewardT UnPack() {
    var _o = new LevelRewardT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelRewardT _o) {
    _o.Regular = this.Regular;
    _o.Worker = this.Worker;
    _o.Resources = new List<FBConfig.RewardResourceT>();
    for (var _j = 0; _j < this.ResourcesLength; ++_j) {_o.Resources.Add(this.Resources(_j).HasValue ? this.Resources(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LevelReward> Pack(FlatBufferBuilder builder, LevelRewardT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelReward>);
    var _resources = default(VectorOffset);
    if (_o.Resources != null) {
      var __resources = new Offset<FBConfig.RewardResource>[_o.Resources.Count];
      for (var _j = 0; _j < __resources.Length; ++_j) { __resources[_j] = FBConfig.RewardResource.Pack(builder, _o.Resources[_j]); }
      _resources = CreateResourcesVector(builder, __resources);
    }
    return CreateLevelReward(
      builder,
      _o.Regular,
      _o.Worker,
      _resources);
  }
}

public class LevelRewardT
{
  public float Regular { get; set; }
  public float Worker { get; set; }
  public List<FBConfig.RewardResourceT> Resources { get; set; }

  public LevelRewardT() {
    this.Regular = 0.0f;
    this.Worker = 0.0f;
    this.Resources = null;
  }
}

public struct TradingCardConfig : IFlatbufferConfig<TradingCardConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TradingCardConfig GetRootAsTradingCardConfig(ByteBuffer _bb) { return GetRootAsTradingCardConfig(_bb, new TradingCardConfig()); }
  public static TradingCardConfig GetRootAsTradingCardConfig(ByteBuffer _bb, TradingCardConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TradingCardConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string LocationUid { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLocationUidBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetLocationUidBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetLocationUidArray() { return __p.__vector_as_array<byte>(6); }
  public string LevelUid { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLevelUidBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetLevelUidBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetLevelUidArray() { return __p.__vector_as_array<byte>(8); }
  public FBConfig.Point? Position { get { int o = __p.__offset(10); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.Point? Size { get { int o = __p.__offset(12); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.Price? PriceFb { get { int o = __p.__offset(14); return o != 0 ? (FBConfig.Price?)(new FBConfig.Price()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.LevelReward? Reward(int j) { int o = __p.__offset(16); return o != 0 ? (FBConfig.LevelReward?)(new FBConfig.LevelReward()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardLength { get { int o = __p.__offset(16); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string Prefab { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPrefabBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetPrefabBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetPrefabArray() { return __p.__vector_as_array<byte>(18); }
  public string AnimationPrefab { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAnimationPrefabBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetAnimationPrefabBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetAnimationPrefabArray() { return __p.__vector_as_array<byte>(20); }
  public FBConfig.AnimationData? CloseAnimation { get { int o = __p.__offset(22); return o != 0 ? (FBConfig.AnimationData?)(new FBConfig.AnimationData()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.AnimationData? OpenAnimation { get { int o = __p.__offset(24); return o != 0 ? (FBConfig.AnimationData?)(new FBConfig.AnimationData()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.AnimationData? AcceptedAnimation { get { int o = __p.__offset(26); return o != 0 ? (FBConfig.AnimationData?)(new FBConfig.AnimationData()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string Icon { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetIconBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetIconArray() { return __p.__vector_as_array<byte>(28); }
  public string Name { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(30); }
  public string HelpingHandsUid { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHelpingHandsUidBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetHelpingHandsUidBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetHelpingHandsUidArray() { return __p.__vector_as_array<byte>(32); }
  public string RelatedLevel { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRelatedLevelBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetRelatedLevelBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetRelatedLevelArray() { return __p.__vector_as_array<byte>(34); }
  public bool HorizontalFlip { get { int o = __p.__offset(36); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateHorizontalFlip(bool horizontal_flip) { int o = __p.__offset(36); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(horizontal_flip ? 1 : 0)); return true; } else { return false; } }
  public string GreetingsLine { get { int o = __p.__offset(38); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGreetingsLineBytes() { return __p.__vector_as_span<byte>(38, 1); }
#else
  public ArraySegment<byte>? GetGreetingsLineBytes() { return __p.__vector_as_arraysegment(38); }
#endif
  public byte[] GetGreetingsLineArray() { return __p.__vector_as_array<byte>(38); }
  public string TradingLine { get { int o = __p.__offset(40); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTradingLineBytes() { return __p.__vector_as_span<byte>(40, 1); }
#else
  public ArraySegment<byte>? GetTradingLineBytes() { return __p.__vector_as_arraysegment(40); }
#endif
  public byte[] GetTradingLineArray() { return __p.__vector_as_array<byte>(40); }
  public string PoiLine { get { int o = __p.__offset(42); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPoiLineBytes() { return __p.__vector_as_span<byte>(42, 1); }
#else
  public ArraySegment<byte>? GetPoiLineBytes() { return __p.__vector_as_arraysegment(42); }
#endif
  public byte[] GetPoiLineArray() { return __p.__vector_as_array<byte>(42); }
  public bool HorizontalFlipvip { get { int o = __p.__offset(44); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateHorizontalFlipvip(bool horizontal_flipvip) { int o = __p.__offset(44); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(horizontal_flipvip ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.TradingCardConfig> CreateTradingCardConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset location_uidOffset = default(StringOffset),
      StringOffset level_uidOffset = default(StringOffset),
      Offset<FBConfig.Point> positionOffset = default(Offset<FBConfig.Point>),
      Offset<FBConfig.Point> sizeOffset = default(Offset<FBConfig.Point>),
      Offset<FBConfig.Price> price_fbOffset = default(Offset<FBConfig.Price>),
      VectorOffset rewardOffset = default(VectorOffset),
      StringOffset prefabOffset = default(StringOffset),
      StringOffset animation_prefabOffset = default(StringOffset),
      Offset<FBConfig.AnimationData> close_animationOffset = default(Offset<FBConfig.AnimationData>),
      Offset<FBConfig.AnimationData> open_animationOffset = default(Offset<FBConfig.AnimationData>),
      Offset<FBConfig.AnimationData> accepted_animationOffset = default(Offset<FBConfig.AnimationData>),
      StringOffset iconOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset helping_hands_uidOffset = default(StringOffset),
      StringOffset related_levelOffset = default(StringOffset),
      bool horizontal_flip = false,
      StringOffset greetings_lineOffset = default(StringOffset),
      StringOffset trading_lineOffset = default(StringOffset),
      StringOffset poi_lineOffset = default(StringOffset),
      bool horizontal_flipvip = false) {
    builder.StartTable(21);
    TradingCardConfig.AddPoiLine(builder, poi_lineOffset);
    TradingCardConfig.AddTradingLine(builder, trading_lineOffset);
    TradingCardConfig.AddGreetingsLine(builder, greetings_lineOffset);
    TradingCardConfig.AddRelatedLevel(builder, related_levelOffset);
    TradingCardConfig.AddHelpingHandsUid(builder, helping_hands_uidOffset);
    TradingCardConfig.AddName(builder, nameOffset);
    TradingCardConfig.AddIcon(builder, iconOffset);
    TradingCardConfig.AddAcceptedAnimation(builder, accepted_animationOffset);
    TradingCardConfig.AddOpenAnimation(builder, open_animationOffset);
    TradingCardConfig.AddCloseAnimation(builder, close_animationOffset);
    TradingCardConfig.AddAnimationPrefab(builder, animation_prefabOffset);
    TradingCardConfig.AddPrefab(builder, prefabOffset);
    TradingCardConfig.AddReward(builder, rewardOffset);
    TradingCardConfig.AddPriceFb(builder, price_fbOffset);
    TradingCardConfig.AddSize(builder, sizeOffset);
    TradingCardConfig.AddPosition(builder, positionOffset);
    TradingCardConfig.AddLevelUid(builder, level_uidOffset);
    TradingCardConfig.AddLocationUid(builder, location_uidOffset);
    TradingCardConfig.AddUid(builder, uidOffset);
    TradingCardConfig.AddHorizontalFlipvip(builder, horizontal_flipvip);
    TradingCardConfig.AddHorizontalFlip(builder, horizontal_flip);
    return TradingCardConfig.EndTradingCardConfig(builder);
  }

  public static void StartTradingCardConfig(FlatBufferBuilder builder) { builder.StartTable(21); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddLocationUid(FlatBufferBuilder builder, StringOffset locationUidOffset) { builder.AddOffset(1, locationUidOffset.Value, 0); }
  public static void AddLevelUid(FlatBufferBuilder builder, StringOffset levelUidOffset) { builder.AddOffset(2, levelUidOffset.Value, 0); }
  public static void AddPosition(FlatBufferBuilder builder, Offset<FBConfig.Point> positionOffset) { builder.AddOffset(3, positionOffset.Value, 0); }
  public static void AddSize(FlatBufferBuilder builder, Offset<FBConfig.Point> sizeOffset) { builder.AddOffset(4, sizeOffset.Value, 0); }
  public static void AddPriceFb(FlatBufferBuilder builder, Offset<FBConfig.Price> priceFbOffset) { builder.AddOffset(5, priceFbOffset.Value, 0); }
  public static void AddReward(FlatBufferBuilder builder, VectorOffset rewardOffset) { builder.AddOffset(6, rewardOffset.Value, 0); }
  public static VectorOffset CreateRewardVector(FlatBufferBuilder builder, Offset<FBConfig.LevelReward>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LevelReward>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LevelReward>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LevelReward>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPrefab(FlatBufferBuilder builder, StringOffset prefabOffset) { builder.AddOffset(7, prefabOffset.Value, 0); }
  public static void AddAnimationPrefab(FlatBufferBuilder builder, StringOffset animationPrefabOffset) { builder.AddOffset(8, animationPrefabOffset.Value, 0); }
  public static void AddCloseAnimation(FlatBufferBuilder builder, Offset<FBConfig.AnimationData> closeAnimationOffset) { builder.AddOffset(9, closeAnimationOffset.Value, 0); }
  public static void AddOpenAnimation(FlatBufferBuilder builder, Offset<FBConfig.AnimationData> openAnimationOffset) { builder.AddOffset(10, openAnimationOffset.Value, 0); }
  public static void AddAcceptedAnimation(FlatBufferBuilder builder, Offset<FBConfig.AnimationData> acceptedAnimationOffset) { builder.AddOffset(11, acceptedAnimationOffset.Value, 0); }
  public static void AddIcon(FlatBufferBuilder builder, StringOffset iconOffset) { builder.AddOffset(12, iconOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(13, nameOffset.Value, 0); }
  public static void AddHelpingHandsUid(FlatBufferBuilder builder, StringOffset helpingHandsUidOffset) { builder.AddOffset(14, helpingHandsUidOffset.Value, 0); }
  public static void AddRelatedLevel(FlatBufferBuilder builder, StringOffset relatedLevelOffset) { builder.AddOffset(15, relatedLevelOffset.Value, 0); }
  public static void AddHorizontalFlip(FlatBufferBuilder builder, bool horizontalFlip) { builder.AddBool(16, horizontalFlip, false); }
  public static void AddGreetingsLine(FlatBufferBuilder builder, StringOffset greetingsLineOffset) { builder.AddOffset(17, greetingsLineOffset.Value, 0); }
  public static void AddTradingLine(FlatBufferBuilder builder, StringOffset tradingLineOffset) { builder.AddOffset(18, tradingLineOffset.Value, 0); }
  public static void AddPoiLine(FlatBufferBuilder builder, StringOffset poiLineOffset) { builder.AddOffset(19, poiLineOffset.Value, 0); }
  public static void AddHorizontalFlipvip(FlatBufferBuilder builder, bool horizontalFlipvip) { builder.AddBool(20, horizontalFlipvip, false); }
  public static Offset<FBConfig.TradingCardConfig> EndTradingCardConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.TradingCardConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfTradingCardConfig(FlatBufferBuilder builder, Offset<TradingCardConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<TradingCardConfig> o1, Offset<TradingCardConfig> o2) =>
        new TradingCardConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new TradingCardConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static TradingCardConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    TradingCardConfig obj_ = new TradingCardConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public TradingCardConfigT UnPack() {
    var _o = new TradingCardConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TradingCardConfigT _o) {
    _o.Uid = this.Uid;
    _o.LocationUid = this.LocationUid;
    _o.LevelUid = this.LevelUid;
    _o.Position = this.Position.HasValue ? this.Position.Value.UnPack() : null;
    _o.Size = this.Size.HasValue ? this.Size.Value.UnPack() : null;
    _o.PriceFb = this.PriceFb.HasValue ? this.PriceFb.Value.UnPack() : null;
    _o.Reward = new List<FBConfig.LevelRewardT>();
    for (var _j = 0; _j < this.RewardLength; ++_j) {_o.Reward.Add(this.Reward(_j).HasValue ? this.Reward(_j).Value.UnPack() : null);}
    _o.Prefab = this.Prefab;
    _o.AnimationPrefab = this.AnimationPrefab;
    _o.CloseAnimation = this.CloseAnimation.HasValue ? this.CloseAnimation.Value.UnPack() : null;
    _o.OpenAnimation = this.OpenAnimation.HasValue ? this.OpenAnimation.Value.UnPack() : null;
    _o.AcceptedAnimation = this.AcceptedAnimation.HasValue ? this.AcceptedAnimation.Value.UnPack() : null;
    _o.Icon = this.Icon;
    _o.Name = this.Name;
    _o.HelpingHandsUid = this.HelpingHandsUid;
    _o.RelatedLevel = this.RelatedLevel;
    _o.HorizontalFlip = this.HorizontalFlip;
    _o.GreetingsLine = this.GreetingsLine;
    _o.TradingLine = this.TradingLine;
    _o.PoiLine = this.PoiLine;
    _o.HorizontalFlipvip = this.HorizontalFlipvip;
  }
  public static Offset<FBConfig.TradingCardConfig> Pack(FlatBufferBuilder builder, TradingCardConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.TradingCardConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _location_uid = _o.LocationUid == null ? default(StringOffset) : builder.CreateString(_o.LocationUid);
    var _level_uid = _o.LevelUid == null ? default(StringOffset) : builder.CreateString(_o.LevelUid);
    var _position = _o.Position == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Position);
    var _size = _o.Size == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Size);
    var _price_fb = _o.PriceFb == null ? default(Offset<FBConfig.Price>) : FBConfig.Price.Pack(builder, _o.PriceFb);
    var _reward = default(VectorOffset);
    if (_o.Reward != null) {
      var __reward = new Offset<FBConfig.LevelReward>[_o.Reward.Count];
      for (var _j = 0; _j < __reward.Length; ++_j) { __reward[_j] = FBConfig.LevelReward.Pack(builder, _o.Reward[_j]); }
      _reward = CreateRewardVector(builder, __reward);
    }
    var _prefab = _o.Prefab == null ? default(StringOffset) : builder.CreateString(_o.Prefab);
    var _animation_prefab = _o.AnimationPrefab == null ? default(StringOffset) : builder.CreateString(_o.AnimationPrefab);
    var _close_animation = _o.CloseAnimation == null ? default(Offset<FBConfig.AnimationData>) : FBConfig.AnimationData.Pack(builder, _o.CloseAnimation);
    var _open_animation = _o.OpenAnimation == null ? default(Offset<FBConfig.AnimationData>) : FBConfig.AnimationData.Pack(builder, _o.OpenAnimation);
    var _accepted_animation = _o.AcceptedAnimation == null ? default(Offset<FBConfig.AnimationData>) : FBConfig.AnimationData.Pack(builder, _o.AcceptedAnimation);
    var _icon = _o.Icon == null ? default(StringOffset) : builder.CreateString(_o.Icon);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _helping_hands_uid = _o.HelpingHandsUid == null ? default(StringOffset) : builder.CreateString(_o.HelpingHandsUid);
    var _related_level = _o.RelatedLevel == null ? default(StringOffset) : builder.CreateString(_o.RelatedLevel);
    var _greetings_line = _o.GreetingsLine == null ? default(StringOffset) : builder.CreateString(_o.GreetingsLine);
    var _trading_line = _o.TradingLine == null ? default(StringOffset) : builder.CreateString(_o.TradingLine);
    var _poi_line = _o.PoiLine == null ? default(StringOffset) : builder.CreateString(_o.PoiLine);
    return CreateTradingCardConfig(
      builder,
      _uid,
      _location_uid,
      _level_uid,
      _position,
      _size,
      _price_fb,
      _reward,
      _prefab,
      _animation_prefab,
      _close_animation,
      _open_animation,
      _accepted_animation,
      _icon,
      _name,
      _helping_hands_uid,
      _related_level,
      _o.HorizontalFlip,
      _greetings_line,
      _trading_line,
      _poi_line,
      _o.HorizontalFlipvip);
  }
}

public class TradingCardConfigT
{
  public string Uid { get; set; }
  public string LocationUid { get; set; }
  public string LevelUid { get; set; }
  public FBConfig.PointT Position { get; set; }
  public FBConfig.PointT Size { get; set; }
  public FBConfig.PriceT PriceFb { get; set; }
  public List<FBConfig.LevelRewardT> Reward { get; set; }
  public string Prefab { get; set; }
  public string AnimationPrefab { get; set; }
  public FBConfig.AnimationDataT CloseAnimation { get; set; }
  public FBConfig.AnimationDataT OpenAnimation { get; set; }
  public FBConfig.AnimationDataT AcceptedAnimation { get; set; }
  public string Icon { get; set; }
  public string Name { get; set; }
  public string HelpingHandsUid { get; set; }
  public string RelatedLevel { get; set; }
  public bool HorizontalFlip { get; set; }
  public string GreetingsLine { get; set; }
  public string TradingLine { get; set; }
  public string PoiLine { get; set; }
  public bool HorizontalFlipvip { get; set; }

  public TradingCardConfigT() {
    this.Uid = null;
    this.LocationUid = null;
    this.LevelUid = null;
    this.Position = null;
    this.Size = null;
    this.PriceFb = null;
    this.Reward = null;
    this.Prefab = null;
    this.AnimationPrefab = null;
    this.CloseAnimation = null;
    this.OpenAnimation = null;
    this.AcceptedAnimation = null;
    this.Icon = null;
    this.Name = null;
    this.HelpingHandsUid = null;
    this.RelatedLevel = null;
    this.HorizontalFlip = false;
    this.GreetingsLine = null;
    this.TradingLine = null;
    this.PoiLine = null;
    this.HorizontalFlipvip = false;
  }
}

public struct TradingCardConfigDict : IFlatbufferConfigDict<TradingCardConfig, TradingCardConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TradingCardConfigDict GetRootAsTradingCardConfigDict(ByteBuffer _bb) { return GetRootAsTradingCardConfigDict(_bb, new TradingCardConfigDict()); }
  public static TradingCardConfigDict GetRootAsTradingCardConfigDict(ByteBuffer _bb, TradingCardConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TradingCardConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.TradingCardConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.TradingCardConfig?)(new FBConfig.TradingCardConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.TradingCardConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.TradingCardConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.TradingCardConfigDict> CreateTradingCardConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    TradingCardConfigDict.AddValues(builder, valuesOffset);
    return TradingCardConfigDict.EndTradingCardConfigDict(builder);
  }

  public static void StartTradingCardConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.TradingCardConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TradingCardConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TradingCardConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TradingCardConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.TradingCardConfigDict> EndTradingCardConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TradingCardConfigDict>(o);
  }
  public static void FinishTradingCardConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TradingCardConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedTradingCardConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TradingCardConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public TradingCardConfigDictT UnPack() {
    var _o = new TradingCardConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TradingCardConfigDictT _o) {
    _o.Values = new List<FBConfig.TradingCardConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.TradingCardConfigDict> Pack(FlatBufferBuilder builder, TradingCardConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.TradingCardConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.TradingCardConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.TradingCardConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateTradingCardConfigDict(
      builder,
      _values);
  }
}

public class TradingCardConfigDictT
{
  public List<FBConfig.TradingCardConfigT> Values { get; set; }

  public TradingCardConfigDictT() {
    this.Values = null;
  }
  public static TradingCardConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return TradingCardConfigDict.GetRootAsTradingCardConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    TradingCardConfigDict.FinishTradingCardConfigDictBuffer(fbb, TradingCardConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
