// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LocalPushNotificationsTimingConfig : IFlatbufferConfig<LocalPushNotificationsTimingConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LocalPushNotificationsTimingConfig GetRootAsLocalPushNotificationsTimingConfig(ByteBuffer _bb) { return GetRootAsLocalPushNotificationsTimingConfig(_bb, new LocalPushNotificationsTimingConfig()); }
  public static LocalPushNotificationsTimingConfig GetRootAsLocalPushNotificationsTimingConfig(ByteBuffer _bb, LocalPushNotificationsTimingConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LocalPushNotificationsTimingConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int MaxNumberPerDate { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxNumberPerDate(int max_number_per_date) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_number_per_date); return true; } else { return false; } }
  public double HoursDropDif { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetDouble(o + __p.bb_pos) : (double)0.0; } }
  public bool MutateHoursDropDif(double hours_drop_dif) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutDouble(o + __p.bb_pos, hours_drop_dif); return true; } else { return false; } }
  public int MaxHour { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxHour(int max_hour) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_hour); return true; } else { return false; } }
  public int MinHour { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMinHour(int min_hour) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, min_hour); return true; } else { return false; } }
  public double NightShiftOffset { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetDouble(o + __p.bb_pos) : (double)0.0; } }
  public bool MutateNightShiftOffset(double night_shift_offset) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutDouble(o + __p.bb_pos, night_shift_offset); return true; } else { return false; } }
  public int SecondsInNotifDay { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSecondsInNotifDay(int seconds_in_notif_day) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, seconds_in_notif_day); return true; } else { return false; } }

  public static Offset<FBConfig.LocalPushNotificationsTimingConfig> CreateLocalPushNotificationsTimingConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int max_number_per_date = 0,
      double hours_drop_dif = 0.0,
      int max_hour = 0,
      int min_hour = 0,
      double night_shift_offset = 0.0,
      int seconds_in_notif_day = 0) {
    builder.StartTable(7);
    LocalPushNotificationsTimingConfig.AddNightShiftOffset(builder, night_shift_offset);
    LocalPushNotificationsTimingConfig.AddHoursDropDif(builder, hours_drop_dif);
    LocalPushNotificationsTimingConfig.AddSecondsInNotifDay(builder, seconds_in_notif_day);
    LocalPushNotificationsTimingConfig.AddMinHour(builder, min_hour);
    LocalPushNotificationsTimingConfig.AddMaxHour(builder, max_hour);
    LocalPushNotificationsTimingConfig.AddMaxNumberPerDate(builder, max_number_per_date);
    LocalPushNotificationsTimingConfig.AddUid(builder, uidOffset);
    return LocalPushNotificationsTimingConfig.EndLocalPushNotificationsTimingConfig(builder);
  }

  public static void StartLocalPushNotificationsTimingConfig(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddMaxNumberPerDate(FlatBufferBuilder builder, int maxNumberPerDate) { builder.AddInt(1, maxNumberPerDate, 0); }
  public static void AddHoursDropDif(FlatBufferBuilder builder, double hoursDropDif) { builder.AddDouble(2, hoursDropDif, 0.0); }
  public static void AddMaxHour(FlatBufferBuilder builder, int maxHour) { builder.AddInt(3, maxHour, 0); }
  public static void AddMinHour(FlatBufferBuilder builder, int minHour) { builder.AddInt(4, minHour, 0); }
  public static void AddNightShiftOffset(FlatBufferBuilder builder, double nightShiftOffset) { builder.AddDouble(5, nightShiftOffset, 0.0); }
  public static void AddSecondsInNotifDay(FlatBufferBuilder builder, int secondsInNotifDay) { builder.AddInt(6, secondsInNotifDay, 0); }
  public static Offset<FBConfig.LocalPushNotificationsTimingConfig> EndLocalPushNotificationsTimingConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LocalPushNotificationsTimingConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLocalPushNotificationsTimingConfig(FlatBufferBuilder builder, Offset<LocalPushNotificationsTimingConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LocalPushNotificationsTimingConfig> o1, Offset<LocalPushNotificationsTimingConfig> o2) =>
        new LocalPushNotificationsTimingConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LocalPushNotificationsTimingConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LocalPushNotificationsTimingConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LocalPushNotificationsTimingConfig obj_ = new LocalPushNotificationsTimingConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LocalPushNotificationsTimingConfigT UnPack() {
    var _o = new LocalPushNotificationsTimingConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LocalPushNotificationsTimingConfigT _o) {
    _o.Uid = this.Uid;
    _o.MaxNumberPerDate = this.MaxNumberPerDate;
    _o.HoursDropDif = this.HoursDropDif;
    _o.MaxHour = this.MaxHour;
    _o.MinHour = this.MinHour;
    _o.NightShiftOffset = this.NightShiftOffset;
    _o.SecondsInNotifDay = this.SecondsInNotifDay;
  }
  public static Offset<FBConfig.LocalPushNotificationsTimingConfig> Pack(FlatBufferBuilder builder, LocalPushNotificationsTimingConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LocalPushNotificationsTimingConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateLocalPushNotificationsTimingConfig(
      builder,
      _uid,
      _o.MaxNumberPerDate,
      _o.HoursDropDif,
      _o.MaxHour,
      _o.MinHour,
      _o.NightShiftOffset,
      _o.SecondsInNotifDay);
  }
}

public class LocalPushNotificationsTimingConfigT
{
  public string Uid { get; set; }
  public int MaxNumberPerDate { get; set; }
  public double HoursDropDif { get; set; }
  public int MaxHour { get; set; }
  public int MinHour { get; set; }
  public double NightShiftOffset { get; set; }
  public int SecondsInNotifDay { get; set; }

  public LocalPushNotificationsTimingConfigT() {
    this.Uid = null;
    this.MaxNumberPerDate = 0;
    this.HoursDropDif = 0.0;
    this.MaxHour = 0;
    this.MinHour = 0;
    this.NightShiftOffset = 0.0;
    this.SecondsInNotifDay = 0;
  }
}

public struct LocalPushNotificationsTimingConfigDict : IFlatbufferConfigDict<LocalPushNotificationsTimingConfig, LocalPushNotificationsTimingConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LocalPushNotificationsTimingConfigDict GetRootAsLocalPushNotificationsTimingConfigDict(ByteBuffer _bb) { return GetRootAsLocalPushNotificationsTimingConfigDict(_bb, new LocalPushNotificationsTimingConfigDict()); }
  public static LocalPushNotificationsTimingConfigDict GetRootAsLocalPushNotificationsTimingConfigDict(ByteBuffer _bb, LocalPushNotificationsTimingConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LocalPushNotificationsTimingConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LocalPushNotificationsTimingConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LocalPushNotificationsTimingConfig?)(new FBConfig.LocalPushNotificationsTimingConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LocalPushNotificationsTimingConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LocalPushNotificationsTimingConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LocalPushNotificationsTimingConfigDict> CreateLocalPushNotificationsTimingConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LocalPushNotificationsTimingConfigDict.AddValues(builder, valuesOffset);
    return LocalPushNotificationsTimingConfigDict.EndLocalPushNotificationsTimingConfigDict(builder);
  }

  public static void StartLocalPushNotificationsTimingConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LocalPushNotificationsTimingConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LocalPushNotificationsTimingConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LocalPushNotificationsTimingConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LocalPushNotificationsTimingConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LocalPushNotificationsTimingConfigDict> EndLocalPushNotificationsTimingConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LocalPushNotificationsTimingConfigDict>(o);
  }
  public static void FinishLocalPushNotificationsTimingConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LocalPushNotificationsTimingConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLocalPushNotificationsTimingConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LocalPushNotificationsTimingConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LocalPushNotificationsTimingConfigDictT UnPack() {
    var _o = new LocalPushNotificationsTimingConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LocalPushNotificationsTimingConfigDictT _o) {
    _o.Values = new List<FBConfig.LocalPushNotificationsTimingConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LocalPushNotificationsTimingConfigDict> Pack(FlatBufferBuilder builder, LocalPushNotificationsTimingConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LocalPushNotificationsTimingConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LocalPushNotificationsTimingConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LocalPushNotificationsTimingConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLocalPushNotificationsTimingConfigDict(
      builder,
      _values);
  }
}

public class LocalPushNotificationsTimingConfigDictT
{
  public List<FBConfig.LocalPushNotificationsTimingConfigT> Values { get; set; }

  public LocalPushNotificationsTimingConfigDictT() {
    this.Values = null;
  }
  public static LocalPushNotificationsTimingConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LocalPushNotificationsTimingConfigDict.GetRootAsLocalPushNotificationsTimingConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LocalPushNotificationsTimingConfigDict.FinishLocalPushNotificationsTimingConfigDictBuffer(fbb, LocalPushNotificationsTimingConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
