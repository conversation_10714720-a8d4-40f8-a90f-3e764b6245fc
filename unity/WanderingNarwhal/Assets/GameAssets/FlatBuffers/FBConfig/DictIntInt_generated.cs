// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DictIntInt : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DictIntInt GetRootAsDictIntInt(ByteBuffer _bb) { return GetRootAsDictIntInt(_bb, new DictIntInt()); }
  public static DictIntInt GetRootAsDictIntInt(ByteBuffer _bb, DictIntInt obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DictIntInt __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public int Key { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateKey(int key) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, key); return true; } else { return false; } }
  public int Value { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateValue(int value) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, value); return true; } else { return false; } }

  public static Offset<FBConfig.DictIntInt> CreateDictIntInt(FlatBufferBuilder builder,
      int key = 0,
      int value = 0) {
    builder.StartTable(2);
    DictIntInt.AddValue(builder, value);
    DictIntInt.AddKey(builder, key);
    return DictIntInt.EndDictIntInt(builder);
  }

  public static void StartDictIntInt(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddKey(FlatBufferBuilder builder, int key) { builder.AddInt(0, key, 0); }
  public static void AddValue(FlatBufferBuilder builder, int value) { builder.AddInt(1, value, 0); }
  public static Offset<FBConfig.DictIntInt> EndDictIntInt(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DictIntInt>(o);
  }
  public DictIntIntT UnPack() {
    var _o = new DictIntIntT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DictIntIntT _o) {
    _o.Key = this.Key;
    _o.Value = this.Value;
  }
  public static Offset<FBConfig.DictIntInt> Pack(FlatBufferBuilder builder, DictIntIntT _o) {
    if (_o == null) return default(Offset<FBConfig.DictIntInt>);
    return CreateDictIntInt(
      builder,
      _o.Key,
      _o.Value);
  }
}

public class DictIntIntT
{
  public int Key { get; set; }
  public int Value { get; set; }

  public DictIntIntT() {
    this.Key = 0;
    this.Value = 0;
  }
}


}
