// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct TutorialConfig : IFlatbufferConfig<TutorialConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TutorialConfig GetRootAsTutorialConfig(ByteBuffer _bb) { return GetRootAsTutorialConfig(_bb, new TutorialConfig()); }
  public static TutorialConfig GetRootAsTutorialConfig(ByteBuffer _bb, TutorialConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public TutorialConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public bool Enabled { get { int o = __p.__offset(8); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateEnabled(bool enabled) { int o = __p.__offset(8); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(enabled ? 1 : 0)); return true; } else { return false; } }
  public int Order { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }
  public string LevelUid { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLevelUidBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetLevelUidBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetLevelUidArray() { return __p.__vector_as_array<byte>(12); }
  public bool ShouldRestart { get { int o = __p.__offset(14); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateShouldRestart(bool should_restart) { int o = __p.__offset(14); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(should_restart ? 1 : 0)); return true; } else { return false; } }
  public string Prefab { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPrefabBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetPrefabBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetPrefabArray() { return __p.__vector_as_array<byte>(16); }
  public string Type { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTypeBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetTypeBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetTypeArray() { return __p.__vector_as_array<byte>(18); }

  public static Offset<FBConfig.TutorialConfig> CreateTutorialConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      bool enabled = false,
      int order = 0,
      StringOffset level_uidOffset = default(StringOffset),
      bool should_restart = false,
      StringOffset prefabOffset = default(StringOffset),
      StringOffset typeOffset = default(StringOffset)) {
    builder.StartTable(8);
    TutorialConfig.AddType(builder, typeOffset);
    TutorialConfig.AddPrefab(builder, prefabOffset);
    TutorialConfig.AddLevelUid(builder, level_uidOffset);
    TutorialConfig.AddOrder(builder, order);
    TutorialConfig.AddName(builder, nameOffset);
    TutorialConfig.AddUid(builder, uidOffset);
    TutorialConfig.AddShouldRestart(builder, should_restart);
    TutorialConfig.AddEnabled(builder, enabled);
    return TutorialConfig.EndTutorialConfig(builder);
  }

  public static void StartTutorialConfig(FlatBufferBuilder builder) { builder.StartTable(8); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddEnabled(FlatBufferBuilder builder, bool enabled) { builder.AddBool(2, enabled, false); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(3, order, 0); }
  public static void AddLevelUid(FlatBufferBuilder builder, StringOffset levelUidOffset) { builder.AddOffset(4, levelUidOffset.Value, 0); }
  public static void AddShouldRestart(FlatBufferBuilder builder, bool shouldRestart) { builder.AddBool(5, shouldRestart, false); }
  public static void AddPrefab(FlatBufferBuilder builder, StringOffset prefabOffset) { builder.AddOffset(6, prefabOffset.Value, 0); }
  public static void AddType(FlatBufferBuilder builder, StringOffset typeOffset) { builder.AddOffset(7, typeOffset.Value, 0); }
  public static Offset<FBConfig.TutorialConfig> EndTutorialConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.TutorialConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfTutorialConfig(FlatBufferBuilder builder, Offset<TutorialConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<TutorialConfig> o1, Offset<TutorialConfig> o2) =>
        new TutorialConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new TutorialConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static TutorialConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    TutorialConfig obj_ = new TutorialConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public TutorialConfigT UnPack() {
    var _o = new TutorialConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TutorialConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Enabled = this.Enabled;
    _o.Order = this.Order;
    _o.LevelUid = this.LevelUid;
    _o.ShouldRestart = this.ShouldRestart;
    _o.Prefab = this.Prefab;
    _o.Type = this.Type;
  }
  public static Offset<FBConfig.TutorialConfig> Pack(FlatBufferBuilder builder, TutorialConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.TutorialConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _level_uid = _o.LevelUid == null ? default(StringOffset) : builder.CreateString(_o.LevelUid);
    var _prefab = _o.Prefab == null ? default(StringOffset) : builder.CreateString(_o.Prefab);
    var _type = _o.Type == null ? default(StringOffset) : builder.CreateString(_o.Type);
    return CreateTutorialConfig(
      builder,
      _uid,
      _name,
      _o.Enabled,
      _o.Order,
      _level_uid,
      _o.ShouldRestart,
      _prefab,
      _type);
  }
}

public class TutorialConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public bool Enabled { get; set; }
  public int Order { get; set; }
  public string LevelUid { get; set; }
  public bool ShouldRestart { get; set; }
  public string Prefab { get; set; }
  public string Type { get; set; }

  public TutorialConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Enabled = false;
    this.Order = 0;
    this.LevelUid = null;
    this.ShouldRestart = false;
    this.Prefab = null;
    this.Type = null;
  }
}

public struct TutorialConfigDict : IFlatbufferConfigDict<TutorialConfig, TutorialConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TutorialConfigDict GetRootAsTutorialConfigDict(ByteBuffer _bb) { return GetRootAsTutorialConfigDict(_bb, new TutorialConfigDict()); }
  public static TutorialConfigDict GetRootAsTutorialConfigDict(ByteBuffer _bb, TutorialConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TutorialConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.TutorialConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.TutorialConfig?)(new FBConfig.TutorialConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.TutorialConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.TutorialConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.TutorialConfigDict> CreateTutorialConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    TutorialConfigDict.AddValues(builder, valuesOffset);
    return TutorialConfigDict.EndTutorialConfigDict(builder);
  }

  public static void StartTutorialConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.TutorialConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TutorialConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TutorialConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TutorialConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.TutorialConfigDict> EndTutorialConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TutorialConfigDict>(o);
  }
  public static void FinishTutorialConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TutorialConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedTutorialConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TutorialConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public TutorialConfigDictT UnPack() {
    var _o = new TutorialConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TutorialConfigDictT _o) {
    _o.Values = new List<FBConfig.TutorialConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.TutorialConfigDict> Pack(FlatBufferBuilder builder, TutorialConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.TutorialConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.TutorialConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.TutorialConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateTutorialConfigDict(
      builder,
      _values);
  }
}

public class TutorialConfigDictT
{
  public List<FBConfig.TutorialConfigT> Values { get; set; }

  public TutorialConfigDictT() {
    this.Values = null;
  }
  public static TutorialConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return TutorialConfigDict.GetRootAsTutorialConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    TutorialConfigDict.FinishTutorialConfigDictBuffer(fbb, TutorialConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
