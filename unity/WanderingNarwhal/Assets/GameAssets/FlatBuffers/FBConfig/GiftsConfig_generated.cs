// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct GiftsConfig : IFlatbufferConfig<GiftsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GiftsConfig GetRootAsGiftsConfig(ByteBuffer _bb) { return GetRootAsGiftsConfig(_bb, new GiftsConfig()); }
  public static GiftsConfig GetRootAsGiftsConfig(ByteBuffer _bb, GiftsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GiftsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int MaxAmountsCooldown { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxAmountsCooldown(int max_amounts_cooldown) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_amounts_cooldown); return true; } else { return false; } }
  public int MaxAskAmount { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxAskAmount(int max_ask_amount) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_ask_amount); return true; } else { return false; } }
  public int MaxSendAmount { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxSendAmount(int max_send_amount) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_send_amount); return true; } else { return false; } }
  public int MaxReceiveAmount { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxReceiveAmount(int max_receive_amount) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_receive_amount); return true; } else { return false; } }
  public int CooldownSendGift { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCooldownSendGift(int cooldown_send_gift) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, cooldown_send_gift); return true; } else { return false; } }
  public int CooldownAskGift { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCooldownAskGift(int cooldown_ask_gift) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, cooldown_ask_gift); return true; } else { return false; } }
  public FBConfig.DictStringInt? SendGiftClickRewardFb(int j) { int o = __p.__offset(18); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int SendGiftClickRewardFbLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? AskGiftClickRewardFb(int j) { int o = __p.__offset(20); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int AskGiftClickRewardFbLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int PoiLikesCountForGift { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePoiLikesCountForGift(int poi_likes_count_for_gift) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, poi_likes_count_for_gift); return true; } else { return false; } }
  public int PoiSharesCountForGift { get { int o = __p.__offset(24); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePoiSharesCountForGift(int poi_shares_count_for_gift) { int o = __p.__offset(24); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, poi_shares_count_for_gift); return true; } else { return false; } }
  public FBConfig.DictStringInt? PoiLikesRewardFb(int j) { int o = __p.__offset(26); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PoiLikesRewardFbLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? PoiSharesRewardFb(int j) { int o = __p.__offset(28); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PoiSharesRewardFbLength { get { int o = __p.__offset(28); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int PoiRewardCooldownSeconds { get { int o = __p.__offset(30); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePoiRewardCooldownSeconds(int poi_reward_cooldown_seconds) { int o = __p.__offset(30); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, poi_reward_cooldown_seconds); return true; } else { return false; } }
  public int GiftsWithFriendsAutoDisplayPredicate { get { int o = __p.__offset(32); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateGiftsWithFriendsAutoDisplayPredicate(int gifts_with_friends_auto_display_predicate) { int o = __p.__offset(32); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, gifts_with_friends_auto_display_predicate); return true; } else { return false; } }
  public bool GiftsWithFriendsIgnoreLastSocialUsage { get { int o = __p.__offset(34); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateGiftsWithFriendsIgnoreLastSocialUsage(bool gifts_with_friends_ignore_last_social_usage) { int o = __p.__offset(34); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(gifts_with_friends_ignore_last_social_usage ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.GiftsConfig> CreateGiftsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int max_amounts_cooldown = 0,
      int max_ask_amount = 0,
      int max_send_amount = 0,
      int max_receive_amount = 0,
      int cooldown_send_gift = 0,
      int cooldown_ask_gift = 0,
      VectorOffset send_gift_click_reward_fbOffset = default(VectorOffset),
      VectorOffset ask_gift_click_reward_fbOffset = default(VectorOffset),
      int poi_likes_count_for_gift = 0,
      int poi_shares_count_for_gift = 0,
      VectorOffset poi_likes_reward_fbOffset = default(VectorOffset),
      VectorOffset poi_shares_reward_fbOffset = default(VectorOffset),
      int poi_reward_cooldown_seconds = 0,
      int gifts_with_friends_auto_display_predicate = 0,
      bool gifts_with_friends_ignore_last_social_usage = false) {
    builder.StartTable(16);
    GiftsConfig.AddGiftsWithFriendsAutoDisplayPredicate(builder, gifts_with_friends_auto_display_predicate);
    GiftsConfig.AddPoiRewardCooldownSeconds(builder, poi_reward_cooldown_seconds);
    GiftsConfig.AddPoiSharesRewardFb(builder, poi_shares_reward_fbOffset);
    GiftsConfig.AddPoiLikesRewardFb(builder, poi_likes_reward_fbOffset);
    GiftsConfig.AddPoiSharesCountForGift(builder, poi_shares_count_for_gift);
    GiftsConfig.AddPoiLikesCountForGift(builder, poi_likes_count_for_gift);
    GiftsConfig.AddAskGiftClickRewardFb(builder, ask_gift_click_reward_fbOffset);
    GiftsConfig.AddSendGiftClickRewardFb(builder, send_gift_click_reward_fbOffset);
    GiftsConfig.AddCooldownAskGift(builder, cooldown_ask_gift);
    GiftsConfig.AddCooldownSendGift(builder, cooldown_send_gift);
    GiftsConfig.AddMaxReceiveAmount(builder, max_receive_amount);
    GiftsConfig.AddMaxSendAmount(builder, max_send_amount);
    GiftsConfig.AddMaxAskAmount(builder, max_ask_amount);
    GiftsConfig.AddMaxAmountsCooldown(builder, max_amounts_cooldown);
    GiftsConfig.AddUid(builder, uidOffset);
    GiftsConfig.AddGiftsWithFriendsIgnoreLastSocialUsage(builder, gifts_with_friends_ignore_last_social_usage);
    return GiftsConfig.EndGiftsConfig(builder);
  }

  public static void StartGiftsConfig(FlatBufferBuilder builder) { builder.StartTable(16); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddMaxAmountsCooldown(FlatBufferBuilder builder, int maxAmountsCooldown) { builder.AddInt(1, maxAmountsCooldown, 0); }
  public static void AddMaxAskAmount(FlatBufferBuilder builder, int maxAskAmount) { builder.AddInt(2, maxAskAmount, 0); }
  public static void AddMaxSendAmount(FlatBufferBuilder builder, int maxSendAmount) { builder.AddInt(3, maxSendAmount, 0); }
  public static void AddMaxReceiveAmount(FlatBufferBuilder builder, int maxReceiveAmount) { builder.AddInt(4, maxReceiveAmount, 0); }
  public static void AddCooldownSendGift(FlatBufferBuilder builder, int cooldownSendGift) { builder.AddInt(5, cooldownSendGift, 0); }
  public static void AddCooldownAskGift(FlatBufferBuilder builder, int cooldownAskGift) { builder.AddInt(6, cooldownAskGift, 0); }
  public static void AddSendGiftClickRewardFb(FlatBufferBuilder builder, VectorOffset sendGiftClickRewardFbOffset) { builder.AddOffset(7, sendGiftClickRewardFbOffset.Value, 0); }
  public static VectorOffset CreateSendGiftClickRewardFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSendGiftClickRewardFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSendGiftClickRewardFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSendGiftClickRewardFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSendGiftClickRewardFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAskGiftClickRewardFb(FlatBufferBuilder builder, VectorOffset askGiftClickRewardFbOffset) { builder.AddOffset(8, askGiftClickRewardFbOffset.Value, 0); }
  public static VectorOffset CreateAskGiftClickRewardFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAskGiftClickRewardFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAskGiftClickRewardFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAskGiftClickRewardFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAskGiftClickRewardFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPoiLikesCountForGift(FlatBufferBuilder builder, int poiLikesCountForGift) { builder.AddInt(9, poiLikesCountForGift, 0); }
  public static void AddPoiSharesCountForGift(FlatBufferBuilder builder, int poiSharesCountForGift) { builder.AddInt(10, poiSharesCountForGift, 0); }
  public static void AddPoiLikesRewardFb(FlatBufferBuilder builder, VectorOffset poiLikesRewardFbOffset) { builder.AddOffset(11, poiLikesRewardFbOffset.Value, 0); }
  public static VectorOffset CreatePoiLikesRewardFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePoiLikesRewardFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePoiLikesRewardFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePoiLikesRewardFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPoiLikesRewardFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPoiSharesRewardFb(FlatBufferBuilder builder, VectorOffset poiSharesRewardFbOffset) { builder.AddOffset(12, poiSharesRewardFbOffset.Value, 0); }
  public static VectorOffset CreatePoiSharesRewardFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePoiSharesRewardFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePoiSharesRewardFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePoiSharesRewardFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPoiSharesRewardFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPoiRewardCooldownSeconds(FlatBufferBuilder builder, int poiRewardCooldownSeconds) { builder.AddInt(13, poiRewardCooldownSeconds, 0); }
  public static void AddGiftsWithFriendsAutoDisplayPredicate(FlatBufferBuilder builder, int giftsWithFriendsAutoDisplayPredicate) { builder.AddInt(14, giftsWithFriendsAutoDisplayPredicate, 0); }
  public static void AddGiftsWithFriendsIgnoreLastSocialUsage(FlatBufferBuilder builder, bool giftsWithFriendsIgnoreLastSocialUsage) { builder.AddBool(15, giftsWithFriendsIgnoreLastSocialUsage, false); }
  public static Offset<FBConfig.GiftsConfig> EndGiftsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.GiftsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfGiftsConfig(FlatBufferBuilder builder, Offset<GiftsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<GiftsConfig> o1, Offset<GiftsConfig> o2) =>
        new GiftsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new GiftsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static GiftsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    GiftsConfig obj_ = new GiftsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public GiftsConfigT UnPack() {
    var _o = new GiftsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GiftsConfigT _o) {
    _o.Uid = this.Uid;
    _o.MaxAmountsCooldown = this.MaxAmountsCooldown;
    _o.MaxAskAmount = this.MaxAskAmount;
    _o.MaxSendAmount = this.MaxSendAmount;
    _o.MaxReceiveAmount = this.MaxReceiveAmount;
    _o.CooldownSendGift = this.CooldownSendGift;
    _o.CooldownAskGift = this.CooldownAskGift;
    _o.SendGiftClickRewardFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.SendGiftClickRewardFbLength; ++_j) {_o.SendGiftClickRewardFb.Add(this.SendGiftClickRewardFb(_j).HasValue ? this.SendGiftClickRewardFb(_j).Value.UnPack() : null);}
    _o.AskGiftClickRewardFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.AskGiftClickRewardFbLength; ++_j) {_o.AskGiftClickRewardFb.Add(this.AskGiftClickRewardFb(_j).HasValue ? this.AskGiftClickRewardFb(_j).Value.UnPack() : null);}
    _o.PoiLikesCountForGift = this.PoiLikesCountForGift;
    _o.PoiSharesCountForGift = this.PoiSharesCountForGift;
    _o.PoiLikesRewardFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.PoiLikesRewardFbLength; ++_j) {_o.PoiLikesRewardFb.Add(this.PoiLikesRewardFb(_j).HasValue ? this.PoiLikesRewardFb(_j).Value.UnPack() : null);}
    _o.PoiSharesRewardFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.PoiSharesRewardFbLength; ++_j) {_o.PoiSharesRewardFb.Add(this.PoiSharesRewardFb(_j).HasValue ? this.PoiSharesRewardFb(_j).Value.UnPack() : null);}
    _o.PoiRewardCooldownSeconds = this.PoiRewardCooldownSeconds;
    _o.GiftsWithFriendsAutoDisplayPredicate = this.GiftsWithFriendsAutoDisplayPredicate;
    _o.GiftsWithFriendsIgnoreLastSocialUsage = this.GiftsWithFriendsIgnoreLastSocialUsage;
  }
  public static Offset<FBConfig.GiftsConfig> Pack(FlatBufferBuilder builder, GiftsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.GiftsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _send_gift_click_reward_fb = default(VectorOffset);
    if (_o.SendGiftClickRewardFb != null) {
      var __send_gift_click_reward_fb = new Offset<FBConfig.DictStringInt>[_o.SendGiftClickRewardFb.Count];
      for (var _j = 0; _j < __send_gift_click_reward_fb.Length; ++_j) { __send_gift_click_reward_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.SendGiftClickRewardFb[_j]); }
      _send_gift_click_reward_fb = CreateSendGiftClickRewardFbVector(builder, __send_gift_click_reward_fb);
    }
    var _ask_gift_click_reward_fb = default(VectorOffset);
    if (_o.AskGiftClickRewardFb != null) {
      var __ask_gift_click_reward_fb = new Offset<FBConfig.DictStringInt>[_o.AskGiftClickRewardFb.Count];
      for (var _j = 0; _j < __ask_gift_click_reward_fb.Length; ++_j) { __ask_gift_click_reward_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.AskGiftClickRewardFb[_j]); }
      _ask_gift_click_reward_fb = CreateAskGiftClickRewardFbVector(builder, __ask_gift_click_reward_fb);
    }
    var _poi_likes_reward_fb = default(VectorOffset);
    if (_o.PoiLikesRewardFb != null) {
      var __poi_likes_reward_fb = new Offset<FBConfig.DictStringInt>[_o.PoiLikesRewardFb.Count];
      for (var _j = 0; _j < __poi_likes_reward_fb.Length; ++_j) { __poi_likes_reward_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.PoiLikesRewardFb[_j]); }
      _poi_likes_reward_fb = CreatePoiLikesRewardFbVector(builder, __poi_likes_reward_fb);
    }
    var _poi_shares_reward_fb = default(VectorOffset);
    if (_o.PoiSharesRewardFb != null) {
      var __poi_shares_reward_fb = new Offset<FBConfig.DictStringInt>[_o.PoiSharesRewardFb.Count];
      for (var _j = 0; _j < __poi_shares_reward_fb.Length; ++_j) { __poi_shares_reward_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.PoiSharesRewardFb[_j]); }
      _poi_shares_reward_fb = CreatePoiSharesRewardFbVector(builder, __poi_shares_reward_fb);
    }
    return CreateGiftsConfig(
      builder,
      _uid,
      _o.MaxAmountsCooldown,
      _o.MaxAskAmount,
      _o.MaxSendAmount,
      _o.MaxReceiveAmount,
      _o.CooldownSendGift,
      _o.CooldownAskGift,
      _send_gift_click_reward_fb,
      _ask_gift_click_reward_fb,
      _o.PoiLikesCountForGift,
      _o.PoiSharesCountForGift,
      _poi_likes_reward_fb,
      _poi_shares_reward_fb,
      _o.PoiRewardCooldownSeconds,
      _o.GiftsWithFriendsAutoDisplayPredicate,
      _o.GiftsWithFriendsIgnoreLastSocialUsage);
  }
}

public class GiftsConfigT
{
  public string Uid { get; set; }
  public int MaxAmountsCooldown { get; set; }
  public int MaxAskAmount { get; set; }
  public int MaxSendAmount { get; set; }
  public int MaxReceiveAmount { get; set; }
  public int CooldownSendGift { get; set; }
  public int CooldownAskGift { get; set; }
  public List<FBConfig.DictStringIntT> SendGiftClickRewardFb { get; set; }
  public List<FBConfig.DictStringIntT> AskGiftClickRewardFb { get; set; }
  public int PoiLikesCountForGift { get; set; }
  public int PoiSharesCountForGift { get; set; }
  public List<FBConfig.DictStringIntT> PoiLikesRewardFb { get; set; }
  public List<FBConfig.DictStringIntT> PoiSharesRewardFb { get; set; }
  public int PoiRewardCooldownSeconds { get; set; }
  public int GiftsWithFriendsAutoDisplayPredicate { get; set; }
  public bool GiftsWithFriendsIgnoreLastSocialUsage { get; set; }

  public GiftsConfigT() {
    this.Uid = null;
    this.MaxAmountsCooldown = 0;
    this.MaxAskAmount = 0;
    this.MaxSendAmount = 0;
    this.MaxReceiveAmount = 0;
    this.CooldownSendGift = 0;
    this.CooldownAskGift = 0;
    this.SendGiftClickRewardFb = null;
    this.AskGiftClickRewardFb = null;
    this.PoiLikesCountForGift = 0;
    this.PoiSharesCountForGift = 0;
    this.PoiLikesRewardFb = null;
    this.PoiSharesRewardFb = null;
    this.PoiRewardCooldownSeconds = 0;
    this.GiftsWithFriendsAutoDisplayPredicate = 0;
    this.GiftsWithFriendsIgnoreLastSocialUsage = false;
  }
}

public struct GiftsConfigDict : IFlatbufferConfigDict<GiftsConfig, GiftsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GiftsConfigDict GetRootAsGiftsConfigDict(ByteBuffer _bb) { return GetRootAsGiftsConfigDict(_bb, new GiftsConfigDict()); }
  public static GiftsConfigDict GetRootAsGiftsConfigDict(ByteBuffer _bb, GiftsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GiftsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.GiftsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.GiftsConfig?)(new FBConfig.GiftsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.GiftsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.GiftsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.GiftsConfigDict> CreateGiftsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    GiftsConfigDict.AddValues(builder, valuesOffset);
    return GiftsConfigDict.EndGiftsConfigDict(builder);
  }

  public static void StartGiftsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.GiftsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.GiftsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.GiftsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.GiftsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.GiftsConfigDict> EndGiftsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GiftsConfigDict>(o);
  }
  public static void FinishGiftsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GiftsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedGiftsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GiftsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public GiftsConfigDictT UnPack() {
    var _o = new GiftsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GiftsConfigDictT _o) {
    _o.Values = new List<FBConfig.GiftsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.GiftsConfigDict> Pack(FlatBufferBuilder builder, GiftsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.GiftsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.GiftsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.GiftsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateGiftsConfigDict(
      builder,
      _values);
  }
}

public class GiftsConfigDictT
{
  public List<FBConfig.GiftsConfigT> Values { get; set; }

  public GiftsConfigDictT() {
    this.Values = null;
  }
  public static GiftsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return GiftsConfigDict.GetRootAsGiftsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    GiftsConfigDict.FinishGiftsConfigDictBuffer(fbb, GiftsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
