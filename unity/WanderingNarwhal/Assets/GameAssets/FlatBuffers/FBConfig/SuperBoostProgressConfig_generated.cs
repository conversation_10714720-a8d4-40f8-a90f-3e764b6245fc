// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SuperBoostProgressConfig : IFlatbufferConfig<SuperBoostProgressConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SuperBoostProgressConfig GetRootAsSuperBoostProgressConfig(ByteBuffer _bb) { return GetRootAsSuperBoostProgressConfig(_bb, new SuperBoostProgressConfig()); }
  public static SuperBoostProgressConfig GetRootAsSuperBoostProgressConfig(ByteBuffer _bb, SuperBoostProgressConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SuperBoostProgressConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public float ProgressAmount { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateProgressAmount(float progress_amount) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, progress_amount); return true; } else { return false; } }
  public string FirstSpeciality { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFirstSpecialityBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetFirstSpecialityBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetFirstSpecialityArray() { return __p.__vector_as_array<byte>(8); }
  public string SecondSpeciality { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSecondSpecialityBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetSecondSpecialityBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetSecondSpecialityArray() { return __p.__vector_as_array<byte>(10); }

  public static Offset<FBConfig.SuperBoostProgressConfig> CreateSuperBoostProgressConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      float progress_amount = 0.0f,
      StringOffset first_specialityOffset = default(StringOffset),
      StringOffset second_specialityOffset = default(StringOffset)) {
    builder.StartTable(4);
    SuperBoostProgressConfig.AddSecondSpeciality(builder, second_specialityOffset);
    SuperBoostProgressConfig.AddFirstSpeciality(builder, first_specialityOffset);
    SuperBoostProgressConfig.AddProgressAmount(builder, progress_amount);
    SuperBoostProgressConfig.AddUid(builder, uidOffset);
    return SuperBoostProgressConfig.EndSuperBoostProgressConfig(builder);
  }

  public static void StartSuperBoostProgressConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddProgressAmount(FlatBufferBuilder builder, float progressAmount) { builder.AddFloat(1, progressAmount, 0.0f); }
  public static void AddFirstSpeciality(FlatBufferBuilder builder, StringOffset firstSpecialityOffset) { builder.AddOffset(2, firstSpecialityOffset.Value, 0); }
  public static void AddSecondSpeciality(FlatBufferBuilder builder, StringOffset secondSpecialityOffset) { builder.AddOffset(3, secondSpecialityOffset.Value, 0); }
  public static Offset<FBConfig.SuperBoostProgressConfig> EndSuperBoostProgressConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SuperBoostProgressConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSuperBoostProgressConfig(FlatBufferBuilder builder, Offset<SuperBoostProgressConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SuperBoostProgressConfig> o1, Offset<SuperBoostProgressConfig> o2) =>
        new SuperBoostProgressConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SuperBoostProgressConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SuperBoostProgressConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SuperBoostProgressConfig obj_ = new SuperBoostProgressConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SuperBoostProgressConfigT UnPack() {
    var _o = new SuperBoostProgressConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SuperBoostProgressConfigT _o) {
    _o.Uid = this.Uid;
    _o.ProgressAmount = this.ProgressAmount;
    _o.FirstSpeciality = this.FirstSpeciality;
    _o.SecondSpeciality = this.SecondSpeciality;
  }
  public static Offset<FBConfig.SuperBoostProgressConfig> Pack(FlatBufferBuilder builder, SuperBoostProgressConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SuperBoostProgressConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _first_speciality = _o.FirstSpeciality == null ? default(StringOffset) : builder.CreateString(_o.FirstSpeciality);
    var _second_speciality = _o.SecondSpeciality == null ? default(StringOffset) : builder.CreateString(_o.SecondSpeciality);
    return CreateSuperBoostProgressConfig(
      builder,
      _uid,
      _o.ProgressAmount,
      _first_speciality,
      _second_speciality);
  }
}

public class SuperBoostProgressConfigT
{
  public string Uid { get; set; }
  public float ProgressAmount { get; set; }
  public string FirstSpeciality { get; set; }
  public string SecondSpeciality { get; set; }

  public SuperBoostProgressConfigT() {
    this.Uid = null;
    this.ProgressAmount = 0.0f;
    this.FirstSpeciality = null;
    this.SecondSpeciality = null;
  }
}

public struct SuperBoostProgressConfigDict : IFlatbufferConfigDict<SuperBoostProgressConfig, SuperBoostProgressConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SuperBoostProgressConfigDict GetRootAsSuperBoostProgressConfigDict(ByteBuffer _bb) { return GetRootAsSuperBoostProgressConfigDict(_bb, new SuperBoostProgressConfigDict()); }
  public static SuperBoostProgressConfigDict GetRootAsSuperBoostProgressConfigDict(ByteBuffer _bb, SuperBoostProgressConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SuperBoostProgressConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SuperBoostProgressConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SuperBoostProgressConfig?)(new FBConfig.SuperBoostProgressConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SuperBoostProgressConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SuperBoostProgressConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SuperBoostProgressConfigDict> CreateSuperBoostProgressConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SuperBoostProgressConfigDict.AddValues(builder, valuesOffset);
    return SuperBoostProgressConfigDict.EndSuperBoostProgressConfigDict(builder);
  }

  public static void StartSuperBoostProgressConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostProgressConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostProgressConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SuperBoostProgressConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SuperBoostProgressConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SuperBoostProgressConfigDict> EndSuperBoostProgressConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SuperBoostProgressConfigDict>(o);
  }
  public static void FinishSuperBoostProgressConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostProgressConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSuperBoostProgressConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostProgressConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SuperBoostProgressConfigDictT UnPack() {
    var _o = new SuperBoostProgressConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SuperBoostProgressConfigDictT _o) {
    _o.Values = new List<FBConfig.SuperBoostProgressConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SuperBoostProgressConfigDict> Pack(FlatBufferBuilder builder, SuperBoostProgressConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SuperBoostProgressConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SuperBoostProgressConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SuperBoostProgressConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSuperBoostProgressConfigDict(
      builder,
      _values);
  }
}

public class SuperBoostProgressConfigDictT
{
  public List<FBConfig.SuperBoostProgressConfigT> Values { get; set; }

  public SuperBoostProgressConfigDictT() {
    this.Values = null;
  }
  public static SuperBoostProgressConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SuperBoostProgressConfigDict.GetRootAsSuperBoostProgressConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SuperBoostProgressConfigDict.FinishSuperBoostProgressConfigDictBuffer(fbb, SuperBoostProgressConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
