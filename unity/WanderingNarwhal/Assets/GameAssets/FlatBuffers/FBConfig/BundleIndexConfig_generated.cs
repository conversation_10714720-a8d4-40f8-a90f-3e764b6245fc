// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct BundleIndexConfig : IFlatbufferConfig<BundleIndexConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BundleIndexConfig GetRootAsBundleIndexConfig(ByteBuffer _bb) { return GetRootAsBundleIndexConfig(_bb, new BundleIndexConfig()); }
  public static BundleIndexConfig GetRootAsBundleIndexConfig(ByteBuffer _bb, BundleIndexConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BundleIndexConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Bundlename { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBundlenameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetBundlenameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetBundlenameArray() { return __p.__vector_as_array<byte>(6); }
  public string BundleType { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBundleTypeBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetBundleTypeBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetBundleTypeArray() { return __p.__vector_as_array<byte>(8); }

  public static Offset<FBConfig.BundleIndexConfig> CreateBundleIndexConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset bundlenameOffset = default(StringOffset),
      StringOffset bundle_typeOffset = default(StringOffset)) {
    builder.StartTable(3);
    BundleIndexConfig.AddBundleType(builder, bundle_typeOffset);
    BundleIndexConfig.AddBundlename(builder, bundlenameOffset);
    BundleIndexConfig.AddUid(builder, uidOffset);
    return BundleIndexConfig.EndBundleIndexConfig(builder);
  }

  public static void StartBundleIndexConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddBundlename(FlatBufferBuilder builder, StringOffset bundlenameOffset) { builder.AddOffset(1, bundlenameOffset.Value, 0); }
  public static void AddBundleType(FlatBufferBuilder builder, StringOffset bundleTypeOffset) { builder.AddOffset(2, bundleTypeOffset.Value, 0); }
  public static Offset<FBConfig.BundleIndexConfig> EndBundleIndexConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.BundleIndexConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfBundleIndexConfig(FlatBufferBuilder builder, Offset<BundleIndexConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<BundleIndexConfig> o1, Offset<BundleIndexConfig> o2) =>
        new BundleIndexConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new BundleIndexConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static BundleIndexConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    BundleIndexConfig obj_ = new BundleIndexConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public BundleIndexConfigT UnPack() {
    var _o = new BundleIndexConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BundleIndexConfigT _o) {
    _o.Uid = this.Uid;
    _o.Bundlename = this.Bundlename;
    _o.BundleType = this.BundleType;
  }
  public static Offset<FBConfig.BundleIndexConfig> Pack(FlatBufferBuilder builder, BundleIndexConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.BundleIndexConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _bundlename = _o.Bundlename == null ? default(StringOffset) : builder.CreateString(_o.Bundlename);
    var _bundle_type = _o.BundleType == null ? default(StringOffset) : builder.CreateString(_o.BundleType);
    return CreateBundleIndexConfig(
      builder,
      _uid,
      _bundlename,
      _bundle_type);
  }
}

public class BundleIndexConfigT
{
  public string Uid { get; set; }
  public string Bundlename { get; set; }
  public string BundleType { get; set; }

  public BundleIndexConfigT() {
    this.Uid = null;
    this.Bundlename = null;
    this.BundleType = null;
  }
}

public struct BundleIndexConfigDict : IFlatbufferConfigDict<BundleIndexConfig, BundleIndexConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BundleIndexConfigDict GetRootAsBundleIndexConfigDict(ByteBuffer _bb) { return GetRootAsBundleIndexConfigDict(_bb, new BundleIndexConfigDict()); }
  public static BundleIndexConfigDict GetRootAsBundleIndexConfigDict(ByteBuffer _bb, BundleIndexConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BundleIndexConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.BundleIndexConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.BundleIndexConfig?)(new FBConfig.BundleIndexConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.BundleIndexConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.BundleIndexConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.BundleIndexConfigDict> CreateBundleIndexConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    BundleIndexConfigDict.AddValues(builder, valuesOffset);
    return BundleIndexConfigDict.EndBundleIndexConfigDict(builder);
  }

  public static void StartBundleIndexConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.BundleIndexConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.BundleIndexConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.BundleIndexConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.BundleIndexConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.BundleIndexConfigDict> EndBundleIndexConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.BundleIndexConfigDict>(o);
  }
  public static void FinishBundleIndexConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.BundleIndexConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedBundleIndexConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.BundleIndexConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public BundleIndexConfigDictT UnPack() {
    var _o = new BundleIndexConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BundleIndexConfigDictT _o) {
    _o.Values = new List<FBConfig.BundleIndexConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.BundleIndexConfigDict> Pack(FlatBufferBuilder builder, BundleIndexConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.BundleIndexConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.BundleIndexConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.BundleIndexConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateBundleIndexConfigDict(
      builder,
      _values);
  }
}

public class BundleIndexConfigDictT
{
  public List<FBConfig.BundleIndexConfigT> Values { get; set; }

  public BundleIndexConfigDictT() {
    this.Values = null;
  }
  public static BundleIndexConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return BundleIndexConfigDict.GetRootAsBundleIndexConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    BundleIndexConfigDict.FinishBundleIndexConfigDictBuffer(fbb, BundleIndexConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
