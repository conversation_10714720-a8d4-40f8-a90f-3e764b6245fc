// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct WeeklyLeaderboardConfig : IFlatbufferConfig<WeeklyLeaderboardConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static WeeklyLeaderboardConfig GetRootAsWeeklyLeaderboardConfig(ByteBuffer _bb) { return GetRootAsWeeklyLeaderboardConfig(_bb, new WeeklyLeaderboardConfig()); }
  public static WeeklyLeaderboardConfig GetRootAsWeeklyLeaderboardConfig(ByteBuffer _bb, WeeklyLeaderboardConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public WeeklyLeaderboardConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public bool DebugMode { get { int o = __p.__offset(6); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateDebugMode(bool debug_mode) { int o = __p.__offset(6); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(debug_mode ? 1 : 0)); return true; } else { return false; } }
  public int DebugLocalTimeOffsetInSeconds { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDebugLocalTimeOffsetInSeconds(int debug_local_time_offset_in_seconds) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, debug_local_time_offset_in_seconds); return true; } else { return false; } }
  public bool DoubleScoreStreakEnabled { get { int o = __p.__offset(10); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateDoubleScoreStreakEnabled(bool double_score_streak_enabled) { int o = __p.__offset(10); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(double_score_streak_enabled ? 1 : 0)); return true; } else { return false; } }
  public string RequiredLevelUid { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRequiredLevelUidBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetRequiredLevelUidBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetRequiredLevelUidArray() { return __p.__vector_as_array<byte>(12); }
  public FBConfig.DictIntInt? ScoreMultiplier(int j) { int o = __p.__offset(14); return o != 0 ? (FBConfig.DictIntInt?)(new FBConfig.DictIntInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ScoreMultiplierLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.WeeklyLeaderboardConfig> CreateWeeklyLeaderboardConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      bool debug_mode = false,
      int debug_local_time_offset_in_seconds = 0,
      bool double_score_streak_enabled = false,
      StringOffset required_level_uidOffset = default(StringOffset),
      VectorOffset score_multiplierOffset = default(VectorOffset)) {
    builder.StartTable(6);
    WeeklyLeaderboardConfig.AddScoreMultiplier(builder, score_multiplierOffset);
    WeeklyLeaderboardConfig.AddRequiredLevelUid(builder, required_level_uidOffset);
    WeeklyLeaderboardConfig.AddDebugLocalTimeOffsetInSeconds(builder, debug_local_time_offset_in_seconds);
    WeeklyLeaderboardConfig.AddUid(builder, uidOffset);
    WeeklyLeaderboardConfig.AddDoubleScoreStreakEnabled(builder, double_score_streak_enabled);
    WeeklyLeaderboardConfig.AddDebugMode(builder, debug_mode);
    return WeeklyLeaderboardConfig.EndWeeklyLeaderboardConfig(builder);
  }

  public static void StartWeeklyLeaderboardConfig(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddDebugMode(FlatBufferBuilder builder, bool debugMode) { builder.AddBool(1, debugMode, false); }
  public static void AddDebugLocalTimeOffsetInSeconds(FlatBufferBuilder builder, int debugLocalTimeOffsetInSeconds) { builder.AddInt(2, debugLocalTimeOffsetInSeconds, 0); }
  public static void AddDoubleScoreStreakEnabled(FlatBufferBuilder builder, bool doubleScoreStreakEnabled) { builder.AddBool(3, doubleScoreStreakEnabled, false); }
  public static void AddRequiredLevelUid(FlatBufferBuilder builder, StringOffset requiredLevelUidOffset) { builder.AddOffset(4, requiredLevelUidOffset.Value, 0); }
  public static void AddScoreMultiplier(FlatBufferBuilder builder, VectorOffset scoreMultiplierOffset) { builder.AddOffset(5, scoreMultiplierOffset.Value, 0); }
  public static VectorOffset CreateScoreMultiplierVector(FlatBufferBuilder builder, Offset<FBConfig.DictIntInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateScoreMultiplierVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictIntInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreMultiplierVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictIntInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreMultiplierVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictIntInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartScoreMultiplierVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.WeeklyLeaderboardConfig> EndWeeklyLeaderboardConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.WeeklyLeaderboardConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfWeeklyLeaderboardConfig(FlatBufferBuilder builder, Offset<WeeklyLeaderboardConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<WeeklyLeaderboardConfig> o1, Offset<WeeklyLeaderboardConfig> o2) =>
        new WeeklyLeaderboardConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new WeeklyLeaderboardConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static WeeklyLeaderboardConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    WeeklyLeaderboardConfig obj_ = new WeeklyLeaderboardConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public WeeklyLeaderboardConfigT UnPack() {
    var _o = new WeeklyLeaderboardConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(WeeklyLeaderboardConfigT _o) {
    _o.Uid = this.Uid;
    _o.DebugMode = this.DebugMode;
    _o.DebugLocalTimeOffsetInSeconds = this.DebugLocalTimeOffsetInSeconds;
    _o.DoubleScoreStreakEnabled = this.DoubleScoreStreakEnabled;
    _o.RequiredLevelUid = this.RequiredLevelUid;
    _o.ScoreMultiplier = new List<FBConfig.DictIntIntT>();
    for (var _j = 0; _j < this.ScoreMultiplierLength; ++_j) {_o.ScoreMultiplier.Add(this.ScoreMultiplier(_j).HasValue ? this.ScoreMultiplier(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.WeeklyLeaderboardConfig> Pack(FlatBufferBuilder builder, WeeklyLeaderboardConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.WeeklyLeaderboardConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _required_level_uid = _o.RequiredLevelUid == null ? default(StringOffset) : builder.CreateString(_o.RequiredLevelUid);
    var _score_multiplier = default(VectorOffset);
    if (_o.ScoreMultiplier != null) {
      var __score_multiplier = new Offset<FBConfig.DictIntInt>[_o.ScoreMultiplier.Count];
      for (var _j = 0; _j < __score_multiplier.Length; ++_j) { __score_multiplier[_j] = FBConfig.DictIntInt.Pack(builder, _o.ScoreMultiplier[_j]); }
      _score_multiplier = CreateScoreMultiplierVector(builder, __score_multiplier);
    }
    return CreateWeeklyLeaderboardConfig(
      builder,
      _uid,
      _o.DebugMode,
      _o.DebugLocalTimeOffsetInSeconds,
      _o.DoubleScoreStreakEnabled,
      _required_level_uid,
      _score_multiplier);
  }
}

public class WeeklyLeaderboardConfigT
{
  public string Uid { get; set; }
  public bool DebugMode { get; set; }
  public int DebugLocalTimeOffsetInSeconds { get; set; }
  public bool DoubleScoreStreakEnabled { get; set; }
  public string RequiredLevelUid { get; set; }
  public List<FBConfig.DictIntIntT> ScoreMultiplier { get; set; }

  public WeeklyLeaderboardConfigT() {
    this.Uid = null;
    this.DebugMode = false;
    this.DebugLocalTimeOffsetInSeconds = 0;
    this.DoubleScoreStreakEnabled = false;
    this.RequiredLevelUid = null;
    this.ScoreMultiplier = null;
  }
}

public struct WeeklyLeaderboardConfigDict : IFlatbufferConfigDict<WeeklyLeaderboardConfig, WeeklyLeaderboardConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static WeeklyLeaderboardConfigDict GetRootAsWeeklyLeaderboardConfigDict(ByteBuffer _bb) { return GetRootAsWeeklyLeaderboardConfigDict(_bb, new WeeklyLeaderboardConfigDict()); }
  public static WeeklyLeaderboardConfigDict GetRootAsWeeklyLeaderboardConfigDict(ByteBuffer _bb, WeeklyLeaderboardConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public WeeklyLeaderboardConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.WeeklyLeaderboardConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.WeeklyLeaderboardConfig?)(new FBConfig.WeeklyLeaderboardConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.WeeklyLeaderboardConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.WeeklyLeaderboardConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.WeeklyLeaderboardConfigDict> CreateWeeklyLeaderboardConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    WeeklyLeaderboardConfigDict.AddValues(builder, valuesOffset);
    return WeeklyLeaderboardConfigDict.EndWeeklyLeaderboardConfigDict(builder);
  }

  public static void StartWeeklyLeaderboardConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.WeeklyLeaderboardConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.WeeklyLeaderboardConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.WeeklyLeaderboardConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.WeeklyLeaderboardConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.WeeklyLeaderboardConfigDict> EndWeeklyLeaderboardConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.WeeklyLeaderboardConfigDict>(o);
  }
  public static void FinishWeeklyLeaderboardConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.WeeklyLeaderboardConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedWeeklyLeaderboardConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.WeeklyLeaderboardConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public WeeklyLeaderboardConfigDictT UnPack() {
    var _o = new WeeklyLeaderboardConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(WeeklyLeaderboardConfigDictT _o) {
    _o.Values = new List<FBConfig.WeeklyLeaderboardConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.WeeklyLeaderboardConfigDict> Pack(FlatBufferBuilder builder, WeeklyLeaderboardConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.WeeklyLeaderboardConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.WeeklyLeaderboardConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.WeeklyLeaderboardConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateWeeklyLeaderboardConfigDict(
      builder,
      _values);
  }
}

public class WeeklyLeaderboardConfigDictT
{
  public List<FBConfig.WeeklyLeaderboardConfigT> Values { get; set; }

  public WeeklyLeaderboardConfigDictT() {
    this.Values = null;
  }
  public static WeeklyLeaderboardConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return WeeklyLeaderboardConfigDict.GetRootAsWeeklyLeaderboardConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    WeeklyLeaderboardConfigDict.FinishWeeklyLeaderboardConfigDictBuffer(fbb, WeeklyLeaderboardConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
