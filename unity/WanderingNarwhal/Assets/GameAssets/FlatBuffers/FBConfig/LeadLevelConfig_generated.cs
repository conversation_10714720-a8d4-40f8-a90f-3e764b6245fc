// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LeadLevelConfig : IFlatbufferConfig<LeadLevelConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LeadLevelConfig GetRootAsLeadLevelConfig(ByteBuffer _bb) { return GetRootAsLeadLevelConfig(_bb, new LeadLevelConfig()); }
  public static LeadLevelConfig GetRootAsLeadLevelConfig(ByteBuffer _bb, LeadLevelConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LeadLevelConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string DropItemSprite { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDropItemSpriteBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetDropItemSpriteBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetDropItemSpriteArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.LeadLevelConfig> CreateLeadLevelConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset drop_item_spriteOffset = default(StringOffset)) {
    builder.StartTable(2);
    LeadLevelConfig.AddDropItemSprite(builder, drop_item_spriteOffset);
    LeadLevelConfig.AddUid(builder, uidOffset);
    return LeadLevelConfig.EndLeadLevelConfig(builder);
  }

  public static void StartLeadLevelConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddDropItemSprite(FlatBufferBuilder builder, StringOffset dropItemSpriteOffset) { builder.AddOffset(1, dropItemSpriteOffset.Value, 0); }
  public static Offset<FBConfig.LeadLevelConfig> EndLeadLevelConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LeadLevelConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLeadLevelConfig(FlatBufferBuilder builder, Offset<LeadLevelConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LeadLevelConfig> o1, Offset<LeadLevelConfig> o2) =>
        new LeadLevelConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LeadLevelConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LeadLevelConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LeadLevelConfig obj_ = new LeadLevelConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LeadLevelConfigT UnPack() {
    var _o = new LeadLevelConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LeadLevelConfigT _o) {
    _o.Uid = this.Uid;
    _o.DropItemSprite = this.DropItemSprite;
  }
  public static Offset<FBConfig.LeadLevelConfig> Pack(FlatBufferBuilder builder, LeadLevelConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LeadLevelConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _drop_item_sprite = _o.DropItemSprite == null ? default(StringOffset) : builder.CreateString(_o.DropItemSprite);
    return CreateLeadLevelConfig(
      builder,
      _uid,
      _drop_item_sprite);
  }
}

public class LeadLevelConfigT
{
  public string Uid { get; set; }
  public string DropItemSprite { get; set; }

  public LeadLevelConfigT() {
    this.Uid = null;
    this.DropItemSprite = null;
  }
}

public struct LeadLevelConfigDict : IFlatbufferConfigDict<LeadLevelConfig, LeadLevelConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LeadLevelConfigDict GetRootAsLeadLevelConfigDict(ByteBuffer _bb) { return GetRootAsLeadLevelConfigDict(_bb, new LeadLevelConfigDict()); }
  public static LeadLevelConfigDict GetRootAsLeadLevelConfigDict(ByteBuffer _bb, LeadLevelConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LeadLevelConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LeadLevelConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LeadLevelConfig?)(new FBConfig.LeadLevelConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LeadLevelConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LeadLevelConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LeadLevelConfigDict> CreateLeadLevelConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LeadLevelConfigDict.AddValues(builder, valuesOffset);
    return LeadLevelConfigDict.EndLeadLevelConfigDict(builder);
  }

  public static void StartLeadLevelConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LeadLevelConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LeadLevelConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LeadLevelConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LeadLevelConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LeadLevelConfigDict> EndLeadLevelConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LeadLevelConfigDict>(o);
  }
  public static void FinishLeadLevelConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LeadLevelConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLeadLevelConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LeadLevelConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LeadLevelConfigDictT UnPack() {
    var _o = new LeadLevelConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LeadLevelConfigDictT _o) {
    _o.Values = new List<FBConfig.LeadLevelConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LeadLevelConfigDict> Pack(FlatBufferBuilder builder, LeadLevelConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LeadLevelConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LeadLevelConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LeadLevelConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLeadLevelConfigDict(
      builder,
      _values);
  }
}

public class LeadLevelConfigDictT
{
  public List<FBConfig.LeadLevelConfigT> Values { get; set; }

  public LeadLevelConfigDictT() {
    this.Values = null;
  }
  public static LeadLevelConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LeadLevelConfigDict.GetRootAsLeadLevelConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LeadLevelConfigDict.FinishLeadLevelConfigDictBuffer(fbb, LeadLevelConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
