// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ThemeMetaConfig : IFlatbufferConfig<ThemeMetaConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ThemeMetaConfig GetRootAsThemeMetaConfig(ByteBuffer _bb) { return GetRootAsThemeMetaConfig(_bb, new ThemeMetaConfig()); }
  public static ThemeMetaConfig GetRootAsThemeMetaConfig(ByteBuffer _bb, ThemeMetaConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ThemeMetaConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.DayMonthYear? RelativeTimelineStart { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.DayMonthYear?)(new FBConfig.DayMonthYear()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int UtcHourToStart { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateUtcHourToStart(int utc_hour_to_start) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, utc_hour_to_start); return true; } else { return false; } }
  public int UtcMinuteToStart { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateUtcMinuteToStart(int utc_minute_to_start) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, utc_minute_to_start); return true; } else { return false; } }

  public static Offset<FBConfig.ThemeMetaConfig> CreateThemeMetaConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      Offset<FBConfig.DayMonthYear> relative_timeline_startOffset = default(Offset<FBConfig.DayMonthYear>),
      int utc_hour_to_start = 0,
      int utc_minute_to_start = 0) {
    builder.StartTable(4);
    ThemeMetaConfig.AddUtcMinuteToStart(builder, utc_minute_to_start);
    ThemeMetaConfig.AddUtcHourToStart(builder, utc_hour_to_start);
    ThemeMetaConfig.AddRelativeTimelineStart(builder, relative_timeline_startOffset);
    ThemeMetaConfig.AddUid(builder, uidOffset);
    return ThemeMetaConfig.EndThemeMetaConfig(builder);
  }

  public static void StartThemeMetaConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddRelativeTimelineStart(FlatBufferBuilder builder, Offset<FBConfig.DayMonthYear> relativeTimelineStartOffset) { builder.AddOffset(1, relativeTimelineStartOffset.Value, 0); }
  public static void AddUtcHourToStart(FlatBufferBuilder builder, int utcHourToStart) { builder.AddInt(2, utcHourToStart, 0); }
  public static void AddUtcMinuteToStart(FlatBufferBuilder builder, int utcMinuteToStart) { builder.AddInt(3, utcMinuteToStart, 0); }
  public static Offset<FBConfig.ThemeMetaConfig> EndThemeMetaConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ThemeMetaConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfThemeMetaConfig(FlatBufferBuilder builder, Offset<ThemeMetaConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ThemeMetaConfig> o1, Offset<ThemeMetaConfig> o2) =>
        new ThemeMetaConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ThemeMetaConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ThemeMetaConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ThemeMetaConfig obj_ = new ThemeMetaConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ThemeMetaConfigT UnPack() {
    var _o = new ThemeMetaConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ThemeMetaConfigT _o) {
    _o.Uid = this.Uid;
    _o.RelativeTimelineStart = this.RelativeTimelineStart.HasValue ? this.RelativeTimelineStart.Value.UnPack() : null;
    _o.UtcHourToStart = this.UtcHourToStart;
    _o.UtcMinuteToStart = this.UtcMinuteToStart;
  }
  public static Offset<FBConfig.ThemeMetaConfig> Pack(FlatBufferBuilder builder, ThemeMetaConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ThemeMetaConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _relative_timeline_start = _o.RelativeTimelineStart == null ? default(Offset<FBConfig.DayMonthYear>) : FBConfig.DayMonthYear.Pack(builder, _o.RelativeTimelineStart);
    return CreateThemeMetaConfig(
      builder,
      _uid,
      _relative_timeline_start,
      _o.UtcHourToStart,
      _o.UtcMinuteToStart);
  }
}

public class ThemeMetaConfigT
{
  public string Uid { get; set; }
  public FBConfig.DayMonthYearT RelativeTimelineStart { get; set; }
  public int UtcHourToStart { get; set; }
  public int UtcMinuteToStart { get; set; }

  public ThemeMetaConfigT() {
    this.Uid = null;
    this.RelativeTimelineStart = null;
    this.UtcHourToStart = 0;
    this.UtcMinuteToStart = 0;
  }
}

public struct ThemeMetaConfigDict : IFlatbufferConfigDict<ThemeMetaConfig, ThemeMetaConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ThemeMetaConfigDict GetRootAsThemeMetaConfigDict(ByteBuffer _bb) { return GetRootAsThemeMetaConfigDict(_bb, new ThemeMetaConfigDict()); }
  public static ThemeMetaConfigDict GetRootAsThemeMetaConfigDict(ByteBuffer _bb, ThemeMetaConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ThemeMetaConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ThemeMetaConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ThemeMetaConfig?)(new FBConfig.ThemeMetaConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ThemeMetaConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ThemeMetaConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ThemeMetaConfigDict> CreateThemeMetaConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ThemeMetaConfigDict.AddValues(builder, valuesOffset);
    return ThemeMetaConfigDict.EndThemeMetaConfigDict(builder);
  }

  public static void StartThemeMetaConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ThemeMetaConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ThemeMetaConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ThemeMetaConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ThemeMetaConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ThemeMetaConfigDict> EndThemeMetaConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ThemeMetaConfigDict>(o);
  }
  public static void FinishThemeMetaConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ThemeMetaConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedThemeMetaConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ThemeMetaConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ThemeMetaConfigDictT UnPack() {
    var _o = new ThemeMetaConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ThemeMetaConfigDictT _o) {
    _o.Values = new List<FBConfig.ThemeMetaConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ThemeMetaConfigDict> Pack(FlatBufferBuilder builder, ThemeMetaConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ThemeMetaConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ThemeMetaConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ThemeMetaConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateThemeMetaConfigDict(
      builder,
      _values);
  }
}

public class ThemeMetaConfigDictT
{
  public List<FBConfig.ThemeMetaConfigT> Values { get; set; }

  public ThemeMetaConfigDictT() {
    this.Values = null;
  }
  public static ThemeMetaConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ThemeMetaConfigDict.GetRootAsThemeMetaConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ThemeMetaConfigDict.FinishThemeMetaConfigDictBuffer(fbb, ThemeMetaConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
