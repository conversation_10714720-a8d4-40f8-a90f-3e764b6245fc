// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct IAPStoreItemConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreItemConfig GetRootAsIAPStoreItemConfig(ByteBuffer _bb) { return GetRootAsIAPStoreItemConfig(_bb, new IAPStoreItemConfig()); }
  public static IAPStoreItemConfig GetRootAsIAPStoreItemConfig(ByteBuffer _bb, IAPStoreItemConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreItemConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int Type { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateType(int type) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, type); return true; } else { return false; } }
  public string ItemUid { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetItemUidBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetItemUidBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetItemUidArray() { return __p.__vector_as_array<byte>(6); }
  public string Prefab { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPrefabBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetPrefabBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetPrefabArray() { return __p.__vector_as_array<byte>(8); }
  public string Predicate { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPredicateBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetPredicateBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetPredicateArray() { return __p.__vector_as_array<byte>(10); }
  public string BadgePrefab { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBadgePrefabBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetBadgePrefabBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetBadgePrefabArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.IAPStoreItemConfig> CreateIAPStoreItemConfig(FlatBufferBuilder builder,
      int type = 0,
      StringOffset item_uidOffset = default(StringOffset),
      StringOffset prefabOffset = default(StringOffset),
      StringOffset predicateOffset = default(StringOffset),
      StringOffset badge_prefabOffset = default(StringOffset)) {
    builder.StartTable(5);
    IAPStoreItemConfig.AddBadgePrefab(builder, badge_prefabOffset);
    IAPStoreItemConfig.AddPredicate(builder, predicateOffset);
    IAPStoreItemConfig.AddPrefab(builder, prefabOffset);
    IAPStoreItemConfig.AddItemUid(builder, item_uidOffset);
    IAPStoreItemConfig.AddType(builder, type);
    return IAPStoreItemConfig.EndIAPStoreItemConfig(builder);
  }

  public static void StartIAPStoreItemConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddType(FlatBufferBuilder builder, int type) { builder.AddInt(0, type, 0); }
  public static void AddItemUid(FlatBufferBuilder builder, StringOffset itemUidOffset) { builder.AddOffset(1, itemUidOffset.Value, 0); }
  public static void AddPrefab(FlatBufferBuilder builder, StringOffset prefabOffset) { builder.AddOffset(2, prefabOffset.Value, 0); }
  public static void AddPredicate(FlatBufferBuilder builder, StringOffset predicateOffset) { builder.AddOffset(3, predicateOffset.Value, 0); }
  public static void AddBadgePrefab(FlatBufferBuilder builder, StringOffset badgePrefabOffset) { builder.AddOffset(4, badgePrefabOffset.Value, 0); }
  public static Offset<FBConfig.IAPStoreItemConfig> EndIAPStoreItemConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPStoreItemConfig>(o);
  }
  public IAPStoreItemConfigT UnPack() {
    var _o = new IAPStoreItemConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreItemConfigT _o) {
    _o.Type = this.Type;
    _o.ItemUid = this.ItemUid;
    _o.Prefab = this.Prefab;
    _o.Predicate = this.Predicate;
    _o.BadgePrefab = this.BadgePrefab;
  }
  public static Offset<FBConfig.IAPStoreItemConfig> Pack(FlatBufferBuilder builder, IAPStoreItemConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreItemConfig>);
    var _item_uid = _o.ItemUid == null ? default(StringOffset) : builder.CreateString(_o.ItemUid);
    var _prefab = _o.Prefab == null ? default(StringOffset) : builder.CreateString(_o.Prefab);
    var _predicate = _o.Predicate == null ? default(StringOffset) : builder.CreateString(_o.Predicate);
    var _badge_prefab = _o.BadgePrefab == null ? default(StringOffset) : builder.CreateString(_o.BadgePrefab);
    return CreateIAPStoreItemConfig(
      builder,
      _o.Type,
      _item_uid,
      _prefab,
      _predicate,
      _badge_prefab);
  }
}

public class IAPStoreItemConfigT
{
  public int Type { get; set; }
  public string ItemUid { get; set; }
  public string Prefab { get; set; }
  public string Predicate { get; set; }
  public string BadgePrefab { get; set; }

  public IAPStoreItemConfigT() {
    this.Type = 0;
    this.ItemUid = null;
    this.Prefab = null;
    this.Predicate = null;
    this.BadgePrefab = null;
  }
}

public struct IAPStoreCategoryConfig : IFlatbufferConfig<IAPStoreCategoryConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreCategoryConfig GetRootAsIAPStoreCategoryConfig(ByteBuffer _bb) { return GetRootAsIAPStoreCategoryConfig(_bb, new IAPStoreCategoryConfig()); }
  public static IAPStoreCategoryConfig GetRootAsIAPStoreCategoryConfig(ByteBuffer _bb, IAPStoreCategoryConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreCategoryConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Title { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(6); }
  public string TabName { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTabNameBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetTabNameBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetTabNameArray() { return __p.__vector_as_array<byte>(8); }
  public FBConfig.IAPStoreItemConfig? Items(int j) { int o = __p.__offset(10); return o != 0 ? (FBConfig.IAPStoreItemConfig?)(new FBConfig.IAPStoreItemConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ItemsLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int Order { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }
  public string Icon { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetIconBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetIconArray() { return __p.__vector_as_array<byte>(14); }
  public bool Visible { get { int o = __p.__offset(16); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateVisible(bool visible) { int o = __p.__offset(16); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(visible ? 1 : 0)); return true; } else { return false; } }
  public string PromotionUid { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPromotionUidBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetPromotionUidBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetPromotionUidArray() { return __p.__vector_as_array<byte>(18); }
  public string Predicate { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPredicateBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetPredicateBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetPredicateArray() { return __p.__vector_as_array<byte>(20); }
  public int NumColumns { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNumColumns(int num_columns) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, num_columns); return true; } else { return false; } }

  public static Offset<FBConfig.IAPStoreCategoryConfig> CreateIAPStoreCategoryConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset tab_nameOffset = default(StringOffset),
      VectorOffset itemsOffset = default(VectorOffset),
      int order = 0,
      StringOffset iconOffset = default(StringOffset),
      bool visible = false,
      StringOffset promotion_uidOffset = default(StringOffset),
      StringOffset predicateOffset = default(StringOffset),
      int num_columns = 0) {
    builder.StartTable(10);
    IAPStoreCategoryConfig.AddNumColumns(builder, num_columns);
    IAPStoreCategoryConfig.AddPredicate(builder, predicateOffset);
    IAPStoreCategoryConfig.AddPromotionUid(builder, promotion_uidOffset);
    IAPStoreCategoryConfig.AddIcon(builder, iconOffset);
    IAPStoreCategoryConfig.AddOrder(builder, order);
    IAPStoreCategoryConfig.AddItems(builder, itemsOffset);
    IAPStoreCategoryConfig.AddTabName(builder, tab_nameOffset);
    IAPStoreCategoryConfig.AddTitle(builder, titleOffset);
    IAPStoreCategoryConfig.AddUid(builder, uidOffset);
    IAPStoreCategoryConfig.AddVisible(builder, visible);
    return IAPStoreCategoryConfig.EndIAPStoreCategoryConfig(builder);
  }

  public static void StartIAPStoreCategoryConfig(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(1, titleOffset.Value, 0); }
  public static void AddTabName(FlatBufferBuilder builder, StringOffset tabNameOffset) { builder.AddOffset(2, tabNameOffset.Value, 0); }
  public static void AddItems(FlatBufferBuilder builder, VectorOffset itemsOffset) { builder.AddOffset(3, itemsOffset.Value, 0); }
  public static VectorOffset CreateItemsVector(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreItemConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateItemsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreItemConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateItemsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.IAPStoreItemConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateItemsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.IAPStoreItemConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartItemsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(4, order, 0); }
  public static void AddIcon(FlatBufferBuilder builder, StringOffset iconOffset) { builder.AddOffset(5, iconOffset.Value, 0); }
  public static void AddVisible(FlatBufferBuilder builder, bool visible) { builder.AddBool(6, visible, false); }
  public static void AddPromotionUid(FlatBufferBuilder builder, StringOffset promotionUidOffset) { builder.AddOffset(7, promotionUidOffset.Value, 0); }
  public static void AddPredicate(FlatBufferBuilder builder, StringOffset predicateOffset) { builder.AddOffset(8, predicateOffset.Value, 0); }
  public static void AddNumColumns(FlatBufferBuilder builder, int numColumns) { builder.AddInt(9, numColumns, 0); }
  public static Offset<FBConfig.IAPStoreCategoryConfig> EndIAPStoreCategoryConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.IAPStoreCategoryConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfIAPStoreCategoryConfig(FlatBufferBuilder builder, Offset<IAPStoreCategoryConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<IAPStoreCategoryConfig> o1, Offset<IAPStoreCategoryConfig> o2) =>
        new IAPStoreCategoryConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new IAPStoreCategoryConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static IAPStoreCategoryConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    IAPStoreCategoryConfig obj_ = new IAPStoreCategoryConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public IAPStoreCategoryConfigT UnPack() {
    var _o = new IAPStoreCategoryConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreCategoryConfigT _o) {
    _o.Uid = this.Uid;
    _o.Title = this.Title;
    _o.TabName = this.TabName;
    _o.Items = new List<FBConfig.IAPStoreItemConfigT>();
    for (var _j = 0; _j < this.ItemsLength; ++_j) {_o.Items.Add(this.Items(_j).HasValue ? this.Items(_j).Value.UnPack() : null);}
    _o.Order = this.Order;
    _o.Icon = this.Icon;
    _o.Visible = this.Visible;
    _o.PromotionUid = this.PromotionUid;
    _o.Predicate = this.Predicate;
    _o.NumColumns = this.NumColumns;
  }
  public static Offset<FBConfig.IAPStoreCategoryConfig> Pack(FlatBufferBuilder builder, IAPStoreCategoryConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreCategoryConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _tab_name = _o.TabName == null ? default(StringOffset) : builder.CreateString(_o.TabName);
    var _items = default(VectorOffset);
    if (_o.Items != null) {
      var __items = new Offset<FBConfig.IAPStoreItemConfig>[_o.Items.Count];
      for (var _j = 0; _j < __items.Length; ++_j) { __items[_j] = FBConfig.IAPStoreItemConfig.Pack(builder, _o.Items[_j]); }
      _items = CreateItemsVector(builder, __items);
    }
    var _icon = _o.Icon == null ? default(StringOffset) : builder.CreateString(_o.Icon);
    var _promotion_uid = _o.PromotionUid == null ? default(StringOffset) : builder.CreateString(_o.PromotionUid);
    var _predicate = _o.Predicate == null ? default(StringOffset) : builder.CreateString(_o.Predicate);
    return CreateIAPStoreCategoryConfig(
      builder,
      _uid,
      _title,
      _tab_name,
      _items,
      _o.Order,
      _icon,
      _o.Visible,
      _promotion_uid,
      _predicate,
      _o.NumColumns);
  }
}

public class IAPStoreCategoryConfigT
{
  public string Uid { get; set; }
  public string Title { get; set; }
  public string TabName { get; set; }
  public List<FBConfig.IAPStoreItemConfigT> Items { get; set; }
  public int Order { get; set; }
  public string Icon { get; set; }
  public bool Visible { get; set; }
  public string PromotionUid { get; set; }
  public string Predicate { get; set; }
  public int NumColumns { get; set; }

  public IAPStoreCategoryConfigT() {
    this.Uid = null;
    this.Title = null;
    this.TabName = null;
    this.Items = null;
    this.Order = 0;
    this.Icon = null;
    this.Visible = false;
    this.PromotionUid = null;
    this.Predicate = null;
    this.NumColumns = 0;
  }
}

public struct IAPStoreCategoryConfigDict : IFlatbufferConfigDict<IAPStoreCategoryConfig, IAPStoreCategoryConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreCategoryConfigDict GetRootAsIAPStoreCategoryConfigDict(ByteBuffer _bb) { return GetRootAsIAPStoreCategoryConfigDict(_bb, new IAPStoreCategoryConfigDict()); }
  public static IAPStoreCategoryConfigDict GetRootAsIAPStoreCategoryConfigDict(ByteBuffer _bb, IAPStoreCategoryConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreCategoryConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.IAPStoreCategoryConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.IAPStoreCategoryConfig?)(new FBConfig.IAPStoreCategoryConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.IAPStoreCategoryConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.IAPStoreCategoryConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.IAPStoreCategoryConfigDict> CreateIAPStoreCategoryConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    IAPStoreCategoryConfigDict.AddValues(builder, valuesOffset);
    return IAPStoreCategoryConfigDict.EndIAPStoreCategoryConfigDict(builder);
  }

  public static void StartIAPStoreCategoryConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreCategoryConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreCategoryConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.IAPStoreCategoryConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.IAPStoreCategoryConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.IAPStoreCategoryConfigDict> EndIAPStoreCategoryConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPStoreCategoryConfigDict>(o);
  }
  public static void FinishIAPStoreCategoryConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreCategoryConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedIAPStoreCategoryConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreCategoryConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public IAPStoreCategoryConfigDictT UnPack() {
    var _o = new IAPStoreCategoryConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreCategoryConfigDictT _o) {
    _o.Values = new List<FBConfig.IAPStoreCategoryConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.IAPStoreCategoryConfigDict> Pack(FlatBufferBuilder builder, IAPStoreCategoryConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreCategoryConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.IAPStoreCategoryConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.IAPStoreCategoryConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateIAPStoreCategoryConfigDict(
      builder,
      _values);
  }
}

public class IAPStoreCategoryConfigDictT
{
  public List<FBConfig.IAPStoreCategoryConfigT> Values { get; set; }

  public IAPStoreCategoryConfigDictT() {
    this.Values = null;
  }
  public static IAPStoreCategoryConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return IAPStoreCategoryConfigDict.GetRootAsIAPStoreCategoryConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    IAPStoreCategoryConfigDict.FinishIAPStoreCategoryConfigDictBuffer(fbb, IAPStoreCategoryConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
