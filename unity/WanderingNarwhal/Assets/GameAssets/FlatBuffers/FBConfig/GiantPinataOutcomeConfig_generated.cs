// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct GiantPinataOutcomeConfig : IFlatbufferConfig<GiantPinataOutcomeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GiantPinataOutcomeConfig GetRootAsGiantPinataOutcomeConfig(ByteBuffer _bb) { return GetRootAsGiantPinataOutcomeConfig(_bb, new GiantPinataOutcomeConfig()); }
  public static GiantPinataOutcomeConfig GetRootAsGiantPinataOutcomeConfig(ByteBuffer _bb, GiantPinataOutcomeConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GiantPinataOutcomeConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Count { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCount(int count) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, count); return true; } else { return false; } }
  public FBConfig.DictStringFloat? Distribution(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringFloat?)(new FBConfig.DictStringFloat()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int DistributionLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public bool AllowSameType { get { int o = __p.__offset(10); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateAllowSameType(bool allow_same_type) { int o = __p.__offset(10); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(allow_same_type ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.GiantPinataOutcomeConfig> CreateGiantPinataOutcomeConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int count = 0,
      VectorOffset distributionOffset = default(VectorOffset),
      bool allow_same_type = false) {
    builder.StartTable(4);
    GiantPinataOutcomeConfig.AddDistribution(builder, distributionOffset);
    GiantPinataOutcomeConfig.AddCount(builder, count);
    GiantPinataOutcomeConfig.AddUid(builder, uidOffset);
    GiantPinataOutcomeConfig.AddAllowSameType(builder, allow_same_type);
    return GiantPinataOutcomeConfig.EndGiantPinataOutcomeConfig(builder);
  }

  public static void StartGiantPinataOutcomeConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCount(FlatBufferBuilder builder, int count) { builder.AddInt(1, count, 0); }
  public static void AddDistribution(FlatBufferBuilder builder, VectorOffset distributionOffset) { builder.AddOffset(2, distributionOffset.Value, 0); }
  public static VectorOffset CreateDistributionVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringFloat>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDistributionVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringFloat>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDistributionVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringFloat>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDistributionVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringFloat>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDistributionVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAllowSameType(FlatBufferBuilder builder, bool allowSameType) { builder.AddBool(3, allowSameType, false); }
  public static Offset<FBConfig.GiantPinataOutcomeConfig> EndGiantPinataOutcomeConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.GiantPinataOutcomeConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfGiantPinataOutcomeConfig(FlatBufferBuilder builder, Offset<GiantPinataOutcomeConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<GiantPinataOutcomeConfig> o1, Offset<GiantPinataOutcomeConfig> o2) =>
        new GiantPinataOutcomeConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new GiantPinataOutcomeConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static GiantPinataOutcomeConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    GiantPinataOutcomeConfig obj_ = new GiantPinataOutcomeConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public GiantPinataOutcomeConfigT UnPack() {
    var _o = new GiantPinataOutcomeConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GiantPinataOutcomeConfigT _o) {
    _o.Uid = this.Uid;
    _o.Count = this.Count;
    _o.Distribution = new List<FBConfig.DictStringFloatT>();
    for (var _j = 0; _j < this.DistributionLength; ++_j) {_o.Distribution.Add(this.Distribution(_j).HasValue ? this.Distribution(_j).Value.UnPack() : null);}
    _o.AllowSameType = this.AllowSameType;
  }
  public static Offset<FBConfig.GiantPinataOutcomeConfig> Pack(FlatBufferBuilder builder, GiantPinataOutcomeConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.GiantPinataOutcomeConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _distribution = default(VectorOffset);
    if (_o.Distribution != null) {
      var __distribution = new Offset<FBConfig.DictStringFloat>[_o.Distribution.Count];
      for (var _j = 0; _j < __distribution.Length; ++_j) { __distribution[_j] = FBConfig.DictStringFloat.Pack(builder, _o.Distribution[_j]); }
      _distribution = CreateDistributionVector(builder, __distribution);
    }
    return CreateGiantPinataOutcomeConfig(
      builder,
      _uid,
      _o.Count,
      _distribution,
      _o.AllowSameType);
  }
}

public class GiantPinataOutcomeConfigT
{
  public string Uid { get; set; }
  public int Count { get; set; }
  public List<FBConfig.DictStringFloatT> Distribution { get; set; }
  public bool AllowSameType { get; set; }

  public GiantPinataOutcomeConfigT() {
    this.Uid = null;
    this.Count = 0;
    this.Distribution = null;
    this.AllowSameType = false;
  }
}

public struct GiantPinataOutcomeConfigDict : IFlatbufferConfigDict<GiantPinataOutcomeConfig, GiantPinataOutcomeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GiantPinataOutcomeConfigDict GetRootAsGiantPinataOutcomeConfigDict(ByteBuffer _bb) { return GetRootAsGiantPinataOutcomeConfigDict(_bb, new GiantPinataOutcomeConfigDict()); }
  public static GiantPinataOutcomeConfigDict GetRootAsGiantPinataOutcomeConfigDict(ByteBuffer _bb, GiantPinataOutcomeConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GiantPinataOutcomeConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.GiantPinataOutcomeConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.GiantPinataOutcomeConfig?)(new FBConfig.GiantPinataOutcomeConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.GiantPinataOutcomeConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.GiantPinataOutcomeConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.GiantPinataOutcomeConfigDict> CreateGiantPinataOutcomeConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    GiantPinataOutcomeConfigDict.AddValues(builder, valuesOffset);
    return GiantPinataOutcomeConfigDict.EndGiantPinataOutcomeConfigDict(builder);
  }

  public static void StartGiantPinataOutcomeConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.GiantPinataOutcomeConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.GiantPinataOutcomeConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.GiantPinataOutcomeConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.GiantPinataOutcomeConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.GiantPinataOutcomeConfigDict> EndGiantPinataOutcomeConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GiantPinataOutcomeConfigDict>(o);
  }
  public static void FinishGiantPinataOutcomeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GiantPinataOutcomeConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedGiantPinataOutcomeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GiantPinataOutcomeConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public GiantPinataOutcomeConfigDictT UnPack() {
    var _o = new GiantPinataOutcomeConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GiantPinataOutcomeConfigDictT _o) {
    _o.Values = new List<FBConfig.GiantPinataOutcomeConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.GiantPinataOutcomeConfigDict> Pack(FlatBufferBuilder builder, GiantPinataOutcomeConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.GiantPinataOutcomeConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.GiantPinataOutcomeConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.GiantPinataOutcomeConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateGiantPinataOutcomeConfigDict(
      builder,
      _values);
  }
}

public class GiantPinataOutcomeConfigDictT
{
  public List<FBConfig.GiantPinataOutcomeConfigT> Values { get; set; }

  public GiantPinataOutcomeConfigDictT() {
    this.Values = null;
  }
  public static GiantPinataOutcomeConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return GiantPinataOutcomeConfigDict.GetRootAsGiantPinataOutcomeConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    GiantPinataOutcomeConfigDict.FinishGiantPinataOutcomeConfigDictBuffer(fbb, GiantPinataOutcomeConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
