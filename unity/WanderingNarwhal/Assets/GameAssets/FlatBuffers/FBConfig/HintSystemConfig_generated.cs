// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct HintSystemConfig : IFlatbufferConfig<HintSystemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HintSystemConfig GetRootAsHintSystemConfig(ByteBuffer _bb) { return GetRootAsHintSystemConfig(_bb, new HintSystemConfig()); }
  public static HintSystemConfig GetRootAsHintSystemConfig(ByteBuffer _bb, HintSystemConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HintSystemConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public float InitialHintTime { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateInitialHintTime(float initial_hint_time) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, initial_hint_time); return true; } else { return false; } }
  public float HintTime { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateHintTime(float hint_time) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, hint_time); return true; } else { return false; } }
  public FBConfig.DictStringInt? PowerUpHintTimesFb(int j) { int o = __p.__offset(10); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PowerUpHintTimesFbLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? PowerUpHintMovesLeftFb(int j) { int o = __p.__offset(12); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PowerUpHintMovesLeftFbLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.HintSystemConfig> CreateHintSystemConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      float initial_hint_time = 0.0f,
      float hint_time = 0.0f,
      VectorOffset power_up_hint_times_fbOffset = default(VectorOffset),
      VectorOffset power_up_hint_moves_left_fbOffset = default(VectorOffset)) {
    builder.StartTable(5);
    HintSystemConfig.AddPowerUpHintMovesLeftFb(builder, power_up_hint_moves_left_fbOffset);
    HintSystemConfig.AddPowerUpHintTimesFb(builder, power_up_hint_times_fbOffset);
    HintSystemConfig.AddHintTime(builder, hint_time);
    HintSystemConfig.AddInitialHintTime(builder, initial_hint_time);
    HintSystemConfig.AddUid(builder, uidOffset);
    return HintSystemConfig.EndHintSystemConfig(builder);
  }

  public static void StartHintSystemConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddInitialHintTime(FlatBufferBuilder builder, float initialHintTime) { builder.AddFloat(1, initialHintTime, 0.0f); }
  public static void AddHintTime(FlatBufferBuilder builder, float hintTime) { builder.AddFloat(2, hintTime, 0.0f); }
  public static void AddPowerUpHintTimesFb(FlatBufferBuilder builder, VectorOffset powerUpHintTimesFbOffset) { builder.AddOffset(3, powerUpHintTimesFbOffset.Value, 0); }
  public static VectorOffset CreatePowerUpHintTimesFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePowerUpHintTimesFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePowerUpHintTimesFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePowerUpHintTimesFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPowerUpHintTimesFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPowerUpHintMovesLeftFb(FlatBufferBuilder builder, VectorOffset powerUpHintMovesLeftFbOffset) { builder.AddOffset(4, powerUpHintMovesLeftFbOffset.Value, 0); }
  public static VectorOffset CreatePowerUpHintMovesLeftFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePowerUpHintMovesLeftFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePowerUpHintMovesLeftFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePowerUpHintMovesLeftFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPowerUpHintMovesLeftFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.HintSystemConfig> EndHintSystemConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.HintSystemConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfHintSystemConfig(FlatBufferBuilder builder, Offset<HintSystemConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<HintSystemConfig> o1, Offset<HintSystemConfig> o2) =>
        new HintSystemConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new HintSystemConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static HintSystemConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    HintSystemConfig obj_ = new HintSystemConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public HintSystemConfigT UnPack() {
    var _o = new HintSystemConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HintSystemConfigT _o) {
    _o.Uid = this.Uid;
    _o.InitialHintTime = this.InitialHintTime;
    _o.HintTime = this.HintTime;
    _o.PowerUpHintTimesFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.PowerUpHintTimesFbLength; ++_j) {_o.PowerUpHintTimesFb.Add(this.PowerUpHintTimesFb(_j).HasValue ? this.PowerUpHintTimesFb(_j).Value.UnPack() : null);}
    _o.PowerUpHintMovesLeftFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.PowerUpHintMovesLeftFbLength; ++_j) {_o.PowerUpHintMovesLeftFb.Add(this.PowerUpHintMovesLeftFb(_j).HasValue ? this.PowerUpHintMovesLeftFb(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.HintSystemConfig> Pack(FlatBufferBuilder builder, HintSystemConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.HintSystemConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _power_up_hint_times_fb = default(VectorOffset);
    if (_o.PowerUpHintTimesFb != null) {
      var __power_up_hint_times_fb = new Offset<FBConfig.DictStringInt>[_o.PowerUpHintTimesFb.Count];
      for (var _j = 0; _j < __power_up_hint_times_fb.Length; ++_j) { __power_up_hint_times_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.PowerUpHintTimesFb[_j]); }
      _power_up_hint_times_fb = CreatePowerUpHintTimesFbVector(builder, __power_up_hint_times_fb);
    }
    var _power_up_hint_moves_left_fb = default(VectorOffset);
    if (_o.PowerUpHintMovesLeftFb != null) {
      var __power_up_hint_moves_left_fb = new Offset<FBConfig.DictStringInt>[_o.PowerUpHintMovesLeftFb.Count];
      for (var _j = 0; _j < __power_up_hint_moves_left_fb.Length; ++_j) { __power_up_hint_moves_left_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.PowerUpHintMovesLeftFb[_j]); }
      _power_up_hint_moves_left_fb = CreatePowerUpHintMovesLeftFbVector(builder, __power_up_hint_moves_left_fb);
    }
    return CreateHintSystemConfig(
      builder,
      _uid,
      _o.InitialHintTime,
      _o.HintTime,
      _power_up_hint_times_fb,
      _power_up_hint_moves_left_fb);
  }
}

public class HintSystemConfigT
{
  public string Uid { get; set; }
  public float InitialHintTime { get; set; }
  public float HintTime { get; set; }
  public List<FBConfig.DictStringIntT> PowerUpHintTimesFb { get; set; }
  public List<FBConfig.DictStringIntT> PowerUpHintMovesLeftFb { get; set; }

  public HintSystemConfigT() {
    this.Uid = null;
    this.InitialHintTime = 0.0f;
    this.HintTime = 0.0f;
    this.PowerUpHintTimesFb = null;
    this.PowerUpHintMovesLeftFb = null;
  }
}

public struct HintSystemConfigDict : IFlatbufferConfigDict<HintSystemConfig, HintSystemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HintSystemConfigDict GetRootAsHintSystemConfigDict(ByteBuffer _bb) { return GetRootAsHintSystemConfigDict(_bb, new HintSystemConfigDict()); }
  public static HintSystemConfigDict GetRootAsHintSystemConfigDict(ByteBuffer _bb, HintSystemConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HintSystemConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.HintSystemConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.HintSystemConfig?)(new FBConfig.HintSystemConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.HintSystemConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.HintSystemConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.HintSystemConfigDict> CreateHintSystemConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    HintSystemConfigDict.AddValues(builder, valuesOffset);
    return HintSystemConfigDict.EndHintSystemConfigDict(builder);
  }

  public static void StartHintSystemConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.HintSystemConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.HintSystemConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.HintSystemConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.HintSystemConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.HintSystemConfigDict> EndHintSystemConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.HintSystemConfigDict>(o);
  }
  public static void FinishHintSystemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HintSystemConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedHintSystemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.HintSystemConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public HintSystemConfigDictT UnPack() {
    var _o = new HintSystemConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HintSystemConfigDictT _o) {
    _o.Values = new List<FBConfig.HintSystemConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.HintSystemConfigDict> Pack(FlatBufferBuilder builder, HintSystemConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.HintSystemConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.HintSystemConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.HintSystemConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateHintSystemConfigDict(
      builder,
      _values);
  }
}

public class HintSystemConfigDictT
{
  public List<FBConfig.HintSystemConfigT> Values { get; set; }

  public HintSystemConfigDictT() {
    this.Values = null;
  }
  public static HintSystemConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return HintSystemConfigDict.GetRootAsHintSystemConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    HintSystemConfigDict.FinishHintSystemConfigDictBuffer(fbb, HintSystemConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
