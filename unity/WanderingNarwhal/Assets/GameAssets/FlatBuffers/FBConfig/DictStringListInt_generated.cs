// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DictStringListInt : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DictStringListInt GetRootAsDictStringListInt(ByteBuffer _bb) { return GetRootAsDictStringListInt(_bb, new DictStringListInt()); }
  public static DictStringListInt GetRootAsDictStringListInt(ByteBuffer _bb, DictStringListInt obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DictStringListInt __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Key { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetKeyBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetKeyBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetKeyArray() { return __p.__vector_as_array<byte>(4); }
  public int Value(int j) { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int ValueLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetValueBytes() { return __p.__vector_as_span<int>(6, 4); }
#else
  public ArraySegment<byte>? GetValueBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public int[] GetValueArray() { return __p.__vector_as_array<int>(6); }
  public bool MutateValue(int j, int value) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, value); return true; } else { return false; } }

  public static Offset<FBConfig.DictStringListInt> CreateDictStringListInt(FlatBufferBuilder builder,
      StringOffset keyOffset = default(StringOffset),
      VectorOffset valueOffset = default(VectorOffset)) {
    builder.StartTable(2);
    DictStringListInt.AddValue(builder, valueOffset);
    DictStringListInt.AddKey(builder, keyOffset);
    return DictStringListInt.EndDictStringListInt(builder);
  }

  public static void StartDictStringListInt(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddKey(FlatBufferBuilder builder, StringOffset keyOffset) { builder.AddOffset(0, keyOffset.Value, 0); }
  public static void AddValue(FlatBufferBuilder builder, VectorOffset valueOffset) { builder.AddOffset(1, valueOffset.Value, 0); }
  public static VectorOffset CreateValueVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateValueVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValueVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValueVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValueVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.DictStringListInt> EndDictStringListInt(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DictStringListInt>(o);
  }
  public DictStringListIntT UnPack() {
    var _o = new DictStringListIntT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DictStringListIntT _o) {
    _o.Key = this.Key;
    _o.Value = new List<int>();
    for (var _j = 0; _j < this.ValueLength; ++_j) {_o.Value.Add(this.Value(_j));}
  }
  public static Offset<FBConfig.DictStringListInt> Pack(FlatBufferBuilder builder, DictStringListIntT _o) {
    if (_o == null) return default(Offset<FBConfig.DictStringListInt>);
    var _key = _o.Key == null ? default(StringOffset) : builder.CreateString(_o.Key);
    var _value = default(VectorOffset);
    if (_o.Value != null) {
      var __value = _o.Value.ToArray();
      _value = CreateValueVector(builder, __value);
    }
    return CreateDictStringListInt(
      builder,
      _key,
      _value);
  }
}

public class DictStringListIntT
{
  public string Key { get; set; }
  public List<int> Value { get; set; }

  public DictStringListIntT() {
    this.Key = null;
    this.Value = null;
  }
}


}
