// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct Currency : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Currency GetRootAsCurrency(ByteBuffer _bb) { return GetRootAsCurrency(_bb, new Currency()); }
  public static Currency GetRootAsCurrency(ByteBuffer _bb, Currency obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Currency __assign(int _i, <PERSON><PERSON><PERSON><PERSON><PERSON> _bb) { __init(_i, _bb); return this; }

  public string Key { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetKeyBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetKeyBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetKeyArray() { return __p.__vector_as_array<byte>(4); }
  public long Value { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetLong(o + __p.bb_pos) : (long)0; } }
  public bool MutateValue(long value) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutLong(o + __p.bb_pos, value); return true; } else { return false; } }

  public static Offset<FBConfig.Currency> CreateCurrency(FlatBufferBuilder builder,
      StringOffset keyOffset = default(StringOffset),
      long value = 0) {
    builder.StartTable(2);
    Currency.AddValue(builder, value);
    Currency.AddKey(builder, keyOffset);
    return Currency.EndCurrency(builder);
  }

  public static void StartCurrency(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddKey(FlatBufferBuilder builder, StringOffset keyOffset) { builder.AddOffset(0, keyOffset.Value, 0); }
  public static void AddValue(FlatBufferBuilder builder, long value) { builder.AddLong(1, value, 0); }
  public static Offset<FBConfig.Currency> EndCurrency(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // key
    return new Offset<FBConfig.Currency>(o);
  }

  public static VectorOffset CreateSortedVectorOfCurrency(FlatBufferBuilder builder, Offset<Currency>[] offsets) {
    Array.Sort(offsets,
      (Offset<Currency> o1, Offset<Currency> o2) =>
        new Currency().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Key.CompareTo(new Currency().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Key));
    return builder.CreateVectorOfTables(offsets);
  }

  public static Currency? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    Currency obj_ = new Currency();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Key.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public CurrencyT UnPack() {
    var _o = new CurrencyT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CurrencyT _o) {
    _o.Key = this.Key;
    _o.Value = this.Value;
  }
  public static Offset<FBConfig.Currency> Pack(FlatBufferBuilder builder, CurrencyT _o) {
    if (_o == null) return default(Offset<FBConfig.Currency>);
    var _key = _o.Key == null ? default(StringOffset) : builder.CreateString(_o.Key);
    return CreateCurrency(
      builder,
      _key,
      _o.Value);
  }
}

public class CurrencyT
{
  public string Key { get; set; }
  public long Value { get; set; }

  public CurrencyT() {
    this.Key = null;
    this.Value = 0;
  }
}

public struct Price : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Price GetRootAsPrice(ByteBuffer _bb) { return GetRootAsPrice(_bb, new Price()); }
  public static Price GetRootAsPrice(ByteBuffer _bb, Price obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Price __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.Currency? Currencies(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.Currency?)(new FBConfig.Currency()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int CurrenciesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Currency? CurrenciesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.Currency.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.Price> CreatePrice(FlatBufferBuilder builder,
      VectorOffset currenciesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    Price.AddCurrencies(builder, currenciesOffset);
    return Price.EndPrice(builder);
  }

  public static void StartPrice(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddCurrencies(FlatBufferBuilder builder, VectorOffset currenciesOffset) { builder.AddOffset(0, currenciesOffset.Value, 0); }
  public static VectorOffset CreateCurrenciesVector(FlatBufferBuilder builder, Offset<FBConfig.Currency>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCurrenciesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.Currency>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCurrenciesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.Currency>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCurrenciesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.Currency>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCurrenciesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.Price> EndPrice(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.Price>(o);
  }
  public PriceT UnPack() {
    var _o = new PriceT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(PriceT _o) {
    _o.Currencies = new List<FBConfig.CurrencyT>();
    for (var _j = 0; _j < this.CurrenciesLength; ++_j) {_o.Currencies.Add(this.Currencies(_j).HasValue ? this.Currencies(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.Price> Pack(FlatBufferBuilder builder, PriceT _o) {
    if (_o == null) return default(Offset<FBConfig.Price>);
    var _currencies = default(VectorOffset);
    if (_o.Currencies != null) {
      var __currencies = new Offset<FBConfig.Currency>[_o.Currencies.Count];
      for (var _j = 0; _j < __currencies.Length; ++_j) { __currencies[_j] = FBConfig.Currency.Pack(builder, _o.Currencies[_j]); }
      _currencies = CreateCurrenciesVector(builder, __currencies);
    }
    return CreatePrice(
      builder,
      _currencies);
  }
}

public class PriceT
{
  public List<FBConfig.CurrencyT> Currencies { get; set; }

  public PriceT() {
    this.Currencies = null;
  }
}


}
