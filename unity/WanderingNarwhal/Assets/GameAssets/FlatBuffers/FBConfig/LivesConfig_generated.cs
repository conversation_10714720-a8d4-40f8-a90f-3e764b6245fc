// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LivesConfig : IFlatbufferConfig<LivesConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LivesConfig GetRootAsLivesConfig(ByteBuffer _bb) { return GetRootAsLivesConfig(_bb, new LivesConfig()); }
  public static LivesConfig GetRootAsLivesConfig(ByteBuffer _bb, LivesConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LivesConfig __assign(int _i, Byte<PERSON><PERSON>er _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int MaxLives { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxLives(int max_lives) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_lives); return true; } else { return false; } }
  public int LifeRefillDurationInSeconds { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLifeRefillDurationInSeconds(int life_refill_duration_in_seconds) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, life_refill_duration_in_seconds); return true; } else { return false; } }
  public int LifeLifterPackExtraLives { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLifeLifterPackExtraLives(int life_lifter_pack_extra_lives) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, life_lifter_pack_extra_lives); return true; } else { return false; } }

  public static Offset<FBConfig.LivesConfig> CreateLivesConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int max_lives = 0,
      int life_refill_duration_in_seconds = 0,
      int life_lifter_pack_extra_lives = 0) {
    builder.StartTable(4);
    LivesConfig.AddLifeLifterPackExtraLives(builder, life_lifter_pack_extra_lives);
    LivesConfig.AddLifeRefillDurationInSeconds(builder, life_refill_duration_in_seconds);
    LivesConfig.AddMaxLives(builder, max_lives);
    LivesConfig.AddUid(builder, uidOffset);
    return LivesConfig.EndLivesConfig(builder);
  }

  public static void StartLivesConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddMaxLives(FlatBufferBuilder builder, int maxLives) { builder.AddInt(1, maxLives, 0); }
  public static void AddLifeRefillDurationInSeconds(FlatBufferBuilder builder, int lifeRefillDurationInSeconds) { builder.AddInt(2, lifeRefillDurationInSeconds, 0); }
  public static void AddLifeLifterPackExtraLives(FlatBufferBuilder builder, int lifeLifterPackExtraLives) { builder.AddInt(3, lifeLifterPackExtraLives, 0); }
  public static Offset<FBConfig.LivesConfig> EndLivesConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LivesConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLivesConfig(FlatBufferBuilder builder, Offset<LivesConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LivesConfig> o1, Offset<LivesConfig> o2) =>
        new LivesConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LivesConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LivesConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LivesConfig obj_ = new LivesConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LivesConfigT UnPack() {
    var _o = new LivesConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LivesConfigT _o) {
    _o.Uid = this.Uid;
    _o.MaxLives = this.MaxLives;
    _o.LifeRefillDurationInSeconds = this.LifeRefillDurationInSeconds;
    _o.LifeLifterPackExtraLives = this.LifeLifterPackExtraLives;
  }
  public static Offset<FBConfig.LivesConfig> Pack(FlatBufferBuilder builder, LivesConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LivesConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateLivesConfig(
      builder,
      _uid,
      _o.MaxLives,
      _o.LifeRefillDurationInSeconds,
      _o.LifeLifterPackExtraLives);
  }
}

public class LivesConfigT
{
  public string Uid { get; set; }
  public int MaxLives { get; set; }
  public int LifeRefillDurationInSeconds { get; set; }
  public int LifeLifterPackExtraLives { get; set; }

  public LivesConfigT() {
    this.Uid = null;
    this.MaxLives = 0;
    this.LifeRefillDurationInSeconds = 0;
    this.LifeLifterPackExtraLives = 0;
  }
}

public struct LivesConfigDict : IFlatbufferConfigDict<LivesConfig, LivesConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LivesConfigDict GetRootAsLivesConfigDict(ByteBuffer _bb) { return GetRootAsLivesConfigDict(_bb, new LivesConfigDict()); }
  public static LivesConfigDict GetRootAsLivesConfigDict(ByteBuffer _bb, LivesConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LivesConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LivesConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LivesConfig?)(new FBConfig.LivesConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LivesConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LivesConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LivesConfigDict> CreateLivesConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LivesConfigDict.AddValues(builder, valuesOffset);
    return LivesConfigDict.EndLivesConfigDict(builder);
  }

  public static void StartLivesConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LivesConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LivesConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LivesConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LivesConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LivesConfigDict> EndLivesConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LivesConfigDict>(o);
  }
  public static void FinishLivesConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LivesConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLivesConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LivesConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LivesConfigDictT UnPack() {
    var _o = new LivesConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LivesConfigDictT _o) {
    _o.Values = new List<FBConfig.LivesConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LivesConfigDict> Pack(FlatBufferBuilder builder, LivesConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LivesConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LivesConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LivesConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLivesConfigDict(
      builder,
      _values);
  }
}

public class LivesConfigDictT
{
  public List<FBConfig.LivesConfigT> Values { get; set; }

  public LivesConfigDictT() {
    this.Values = null;
  }
  public static LivesConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LivesConfigDict.GetRootAsLivesConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LivesConfigDict.FinishLivesConfigDictBuffer(fbb, LivesConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
