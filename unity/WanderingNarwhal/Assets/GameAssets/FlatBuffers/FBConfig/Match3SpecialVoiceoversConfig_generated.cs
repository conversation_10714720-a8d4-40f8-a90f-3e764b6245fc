// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct Match3SpecialVoiceoversConfig : IFlatbufferConfig<Match3SpecialVoiceoversConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Match3SpecialVoiceoversConfig GetRootAsMatch3SpecialVoiceoversConfig(ByteBuffer _bb) { return GetRootAsMatch3SpecialVoiceoversConfig(_bb, new Match3SpecialVoiceoversConfig()); }
  public static Match3SpecialVoiceoversConfig GetRootAsMatch3SpecialVoiceoversConfig(ByteBuffer _bb, Match3SpecialVoiceoversConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Match3SpecialVoiceoversConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string SoundIds(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int SoundIdsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string TextIds(int j) { int o = __p.__offset(8); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int TextIdsLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public float ChanceToPlay { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateChanceToPlay(float chance_to_play) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, chance_to_play); return true; } else { return false; } }

  public static Offset<FBConfig.Match3SpecialVoiceoversConfig> CreateMatch3SpecialVoiceoversConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset sound_idsOffset = default(VectorOffset),
      VectorOffset text_idsOffset = default(VectorOffset),
      float chance_to_play = 0.0f) {
    builder.StartTable(4);
    Match3SpecialVoiceoversConfig.AddChanceToPlay(builder, chance_to_play);
    Match3SpecialVoiceoversConfig.AddTextIds(builder, text_idsOffset);
    Match3SpecialVoiceoversConfig.AddSoundIds(builder, sound_idsOffset);
    Match3SpecialVoiceoversConfig.AddUid(builder, uidOffset);
    return Match3SpecialVoiceoversConfig.EndMatch3SpecialVoiceoversConfig(builder);
  }

  public static void StartMatch3SpecialVoiceoversConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSoundIds(FlatBufferBuilder builder, VectorOffset soundIdsOffset) { builder.AddOffset(1, soundIdsOffset.Value, 0); }
  public static VectorOffset CreateSoundIdsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSoundIdsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSoundIdsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSoundIdsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSoundIdsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTextIds(FlatBufferBuilder builder, VectorOffset textIdsOffset) { builder.AddOffset(2, textIdsOffset.Value, 0); }
  public static VectorOffset CreateTextIdsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTextIdsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTextIdsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTextIdsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTextIdsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddChanceToPlay(FlatBufferBuilder builder, float chanceToPlay) { builder.AddFloat(3, chanceToPlay, 0.0f); }
  public static Offset<FBConfig.Match3SpecialVoiceoversConfig> EndMatch3SpecialVoiceoversConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.Match3SpecialVoiceoversConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfMatch3SpecialVoiceoversConfig(FlatBufferBuilder builder, Offset<Match3SpecialVoiceoversConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<Match3SpecialVoiceoversConfig> o1, Offset<Match3SpecialVoiceoversConfig> o2) =>
        new Match3SpecialVoiceoversConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new Match3SpecialVoiceoversConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static Match3SpecialVoiceoversConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    Match3SpecialVoiceoversConfig obj_ = new Match3SpecialVoiceoversConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public Match3SpecialVoiceoversConfigT UnPack() {
    var _o = new Match3SpecialVoiceoversConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(Match3SpecialVoiceoversConfigT _o) {
    _o.Uid = this.Uid;
    _o.SoundIds = new List<string>();
    for (var _j = 0; _j < this.SoundIdsLength; ++_j) {_o.SoundIds.Add(this.SoundIds(_j));}
    _o.TextIds = new List<string>();
    for (var _j = 0; _j < this.TextIdsLength; ++_j) {_o.TextIds.Add(this.TextIds(_j));}
    _o.ChanceToPlay = this.ChanceToPlay;
  }
  public static Offset<FBConfig.Match3SpecialVoiceoversConfig> Pack(FlatBufferBuilder builder, Match3SpecialVoiceoversConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.Match3SpecialVoiceoversConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _sound_ids = default(VectorOffset);
    if (_o.SoundIds != null) {
      var __sound_ids = new StringOffset[_o.SoundIds.Count];
      for (var _j = 0; _j < __sound_ids.Length; ++_j) { __sound_ids[_j] = builder.CreateString(_o.SoundIds[_j]); }
      _sound_ids = CreateSoundIdsVector(builder, __sound_ids);
    }
    var _text_ids = default(VectorOffset);
    if (_o.TextIds != null) {
      var __text_ids = new StringOffset[_o.TextIds.Count];
      for (var _j = 0; _j < __text_ids.Length; ++_j) { __text_ids[_j] = builder.CreateString(_o.TextIds[_j]); }
      _text_ids = CreateTextIdsVector(builder, __text_ids);
    }
    return CreateMatch3SpecialVoiceoversConfig(
      builder,
      _uid,
      _sound_ids,
      _text_ids,
      _o.ChanceToPlay);
  }
}

public class Match3SpecialVoiceoversConfigT
{
  public string Uid { get; set; }
  public List<string> SoundIds { get; set; }
  public List<string> TextIds { get; set; }
  public float ChanceToPlay { get; set; }

  public Match3SpecialVoiceoversConfigT() {
    this.Uid = null;
    this.SoundIds = null;
    this.TextIds = null;
    this.ChanceToPlay = 0.0f;
  }
}

public struct Match3SpecialVoiceoversConfigDict : IFlatbufferConfigDict<Match3SpecialVoiceoversConfig, Match3SpecialVoiceoversConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Match3SpecialVoiceoversConfigDict GetRootAsMatch3SpecialVoiceoversConfigDict(ByteBuffer _bb) { return GetRootAsMatch3SpecialVoiceoversConfigDict(_bb, new Match3SpecialVoiceoversConfigDict()); }
  public static Match3SpecialVoiceoversConfigDict GetRootAsMatch3SpecialVoiceoversConfigDict(ByteBuffer _bb, Match3SpecialVoiceoversConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Match3SpecialVoiceoversConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.Match3SpecialVoiceoversConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.Match3SpecialVoiceoversConfig?)(new FBConfig.Match3SpecialVoiceoversConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Match3SpecialVoiceoversConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.Match3SpecialVoiceoversConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.Match3SpecialVoiceoversConfigDict> CreateMatch3SpecialVoiceoversConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    Match3SpecialVoiceoversConfigDict.AddValues(builder, valuesOffset);
    return Match3SpecialVoiceoversConfigDict.EndMatch3SpecialVoiceoversConfigDict(builder);
  }

  public static void StartMatch3SpecialVoiceoversConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.Match3SpecialVoiceoversConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.Match3SpecialVoiceoversConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.Match3SpecialVoiceoversConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.Match3SpecialVoiceoversConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.Match3SpecialVoiceoversConfigDict> EndMatch3SpecialVoiceoversConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.Match3SpecialVoiceoversConfigDict>(o);
  }
  public static void FinishMatch3SpecialVoiceoversConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.Match3SpecialVoiceoversConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedMatch3SpecialVoiceoversConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.Match3SpecialVoiceoversConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public Match3SpecialVoiceoversConfigDictT UnPack() {
    var _o = new Match3SpecialVoiceoversConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(Match3SpecialVoiceoversConfigDictT _o) {
    _o.Values = new List<FBConfig.Match3SpecialVoiceoversConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.Match3SpecialVoiceoversConfigDict> Pack(FlatBufferBuilder builder, Match3SpecialVoiceoversConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.Match3SpecialVoiceoversConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.Match3SpecialVoiceoversConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.Match3SpecialVoiceoversConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateMatch3SpecialVoiceoversConfigDict(
      builder,
      _values);
  }
}

public class Match3SpecialVoiceoversConfigDictT
{
  public List<FBConfig.Match3SpecialVoiceoversConfigT> Values { get; set; }

  public Match3SpecialVoiceoversConfigDictT() {
    this.Values = null;
  }
  public static Match3SpecialVoiceoversConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return Match3SpecialVoiceoversConfigDict.GetRootAsMatch3SpecialVoiceoversConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    Match3SpecialVoiceoversConfigDict.FinishMatch3SpecialVoiceoversConfigDictBuffer(fbb, Match3SpecialVoiceoversConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
