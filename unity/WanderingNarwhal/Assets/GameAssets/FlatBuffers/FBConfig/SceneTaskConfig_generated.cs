// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SceneTaskConfig : IFlatbufferConfig<SceneTaskConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SceneTaskConfig GetRootAsSceneTaskConfig(ByteBuffer _bb) { return GetRootAsSceneTaskConfig(_bb, new SceneTaskConfig()); }
  public static SceneTaskConfig GetRootAsSceneTaskConfig(ByteBuffer _bb, SceneTaskConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, By<PERSON><PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public SceneTaskConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public int StarCost { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateStarCost(int star_cost) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, star_cost); return true; } else { return false; } }
  public FBConfig.ListString? Dependencies(int j) { int o = __p.__offset(10); return o != 0 ? (FBConfig.ListString?)(new FBConfig.ListString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int DependenciesLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string EllieSpeech { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetEllieSpeechBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetEllieSpeechBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetEllieSpeechArray() { return __p.__vector_as_array<byte>(12); }
  public string FennecSpeech { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFennecSpeechBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetFennecSpeechBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetFennecSpeechArray() { return __p.__vector_as_array<byte>(14); }
  public string EllieIntroSpeech { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetEllieIntroSpeechBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetEllieIntroSpeechBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetEllieIntroSpeechArray() { return __p.__vector_as_array<byte>(16); }
  public string FennecIntroSpeech { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFennecIntroSpeechBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetFennecIntroSpeechBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetFennecIntroSpeechArray() { return __p.__vector_as_array<byte>(18); }
  public float TaskTimeOverride { get { int o = __p.__offset(20); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateTaskTimeOverride(float task_time_override) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, task_time_override); return true; } else { return false; } }

  public static Offset<FBConfig.SceneTaskConfig> CreateSceneTaskConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      int star_cost = 0,
      VectorOffset dependenciesOffset = default(VectorOffset),
      StringOffset ellie_speechOffset = default(StringOffset),
      StringOffset fennec_speechOffset = default(StringOffset),
      StringOffset ellie_intro_speechOffset = default(StringOffset),
      StringOffset fennec_intro_speechOffset = default(StringOffset),
      float task_time_override = 0.0f) {
    builder.StartTable(9);
    SceneTaskConfig.AddTaskTimeOverride(builder, task_time_override);
    SceneTaskConfig.AddFennecIntroSpeech(builder, fennec_intro_speechOffset);
    SceneTaskConfig.AddEllieIntroSpeech(builder, ellie_intro_speechOffset);
    SceneTaskConfig.AddFennecSpeech(builder, fennec_speechOffset);
    SceneTaskConfig.AddEllieSpeech(builder, ellie_speechOffset);
    SceneTaskConfig.AddDependencies(builder, dependenciesOffset);
    SceneTaskConfig.AddStarCost(builder, star_cost);
    SceneTaskConfig.AddName(builder, nameOffset);
    SceneTaskConfig.AddUid(builder, uidOffset);
    return SceneTaskConfig.EndSceneTaskConfig(builder);
  }

  public static void StartSceneTaskConfig(FlatBufferBuilder builder) { builder.StartTable(9); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddStarCost(FlatBufferBuilder builder, int starCost) { builder.AddInt(2, starCost, 0); }
  public static void AddDependencies(FlatBufferBuilder builder, VectorOffset dependenciesOffset) { builder.AddOffset(3, dependenciesOffset.Value, 0); }
  public static VectorOffset CreateDependenciesVector(FlatBufferBuilder builder, Offset<FBConfig.ListString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ListString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ListString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ListString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDependenciesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddEllieSpeech(FlatBufferBuilder builder, StringOffset ellieSpeechOffset) { builder.AddOffset(4, ellieSpeechOffset.Value, 0); }
  public static void AddFennecSpeech(FlatBufferBuilder builder, StringOffset fennecSpeechOffset) { builder.AddOffset(5, fennecSpeechOffset.Value, 0); }
  public static void AddEllieIntroSpeech(FlatBufferBuilder builder, StringOffset ellieIntroSpeechOffset) { builder.AddOffset(6, ellieIntroSpeechOffset.Value, 0); }
  public static void AddFennecIntroSpeech(FlatBufferBuilder builder, StringOffset fennecIntroSpeechOffset) { builder.AddOffset(7, fennecIntroSpeechOffset.Value, 0); }
  public static void AddTaskTimeOverride(FlatBufferBuilder builder, float taskTimeOverride) { builder.AddFloat(8, taskTimeOverride, 0.0f); }
  public static Offset<FBConfig.SceneTaskConfig> EndSceneTaskConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SceneTaskConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSceneTaskConfig(FlatBufferBuilder builder, Offset<SceneTaskConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SceneTaskConfig> o1, Offset<SceneTaskConfig> o2) =>
        new SceneTaskConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SceneTaskConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SceneTaskConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SceneTaskConfig obj_ = new SceneTaskConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SceneTaskConfigT UnPack() {
    var _o = new SceneTaskConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SceneTaskConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.StarCost = this.StarCost;
    _o.Dependencies = new List<FBConfig.ListStringT>();
    for (var _j = 0; _j < this.DependenciesLength; ++_j) {_o.Dependencies.Add(this.Dependencies(_j).HasValue ? this.Dependencies(_j).Value.UnPack() : null);}
    _o.EllieSpeech = this.EllieSpeech;
    _o.FennecSpeech = this.FennecSpeech;
    _o.EllieIntroSpeech = this.EllieIntroSpeech;
    _o.FennecIntroSpeech = this.FennecIntroSpeech;
    _o.TaskTimeOverride = this.TaskTimeOverride;
  }
  public static Offset<FBConfig.SceneTaskConfig> Pack(FlatBufferBuilder builder, SceneTaskConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SceneTaskConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _dependencies = default(VectorOffset);
    if (_o.Dependencies != null) {
      var __dependencies = new Offset<FBConfig.ListString>[_o.Dependencies.Count];
      for (var _j = 0; _j < __dependencies.Length; ++_j) { __dependencies[_j] = FBConfig.ListString.Pack(builder, _o.Dependencies[_j]); }
      _dependencies = CreateDependenciesVector(builder, __dependencies);
    }
    var _ellie_speech = _o.EllieSpeech == null ? default(StringOffset) : builder.CreateString(_o.EllieSpeech);
    var _fennec_speech = _o.FennecSpeech == null ? default(StringOffset) : builder.CreateString(_o.FennecSpeech);
    var _ellie_intro_speech = _o.EllieIntroSpeech == null ? default(StringOffset) : builder.CreateString(_o.EllieIntroSpeech);
    var _fennec_intro_speech = _o.FennecIntroSpeech == null ? default(StringOffset) : builder.CreateString(_o.FennecIntroSpeech);
    return CreateSceneTaskConfig(
      builder,
      _uid,
      _name,
      _o.StarCost,
      _dependencies,
      _ellie_speech,
      _fennec_speech,
      _ellie_intro_speech,
      _fennec_intro_speech,
      _o.TaskTimeOverride);
  }
}

public class SceneTaskConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public int StarCost { get; set; }
  public List<FBConfig.ListStringT> Dependencies { get; set; }
  public string EllieSpeech { get; set; }
  public string FennecSpeech { get; set; }
  public string EllieIntroSpeech { get; set; }
  public string FennecIntroSpeech { get; set; }
  public float TaskTimeOverride { get; set; }

  public SceneTaskConfigT() {
    this.Uid = null;
    this.Name = null;
    this.StarCost = 0;
    this.Dependencies = null;
    this.EllieSpeech = null;
    this.FennecSpeech = null;
    this.EllieIntroSpeech = null;
    this.FennecIntroSpeech = null;
    this.TaskTimeOverride = 0.0f;
  }
}

public struct SceneTaskConfigDict : IFlatbufferConfigDict<SceneTaskConfig, SceneTaskConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SceneTaskConfigDict GetRootAsSceneTaskConfigDict(ByteBuffer _bb) { return GetRootAsSceneTaskConfigDict(_bb, new SceneTaskConfigDict()); }
  public static SceneTaskConfigDict GetRootAsSceneTaskConfigDict(ByteBuffer _bb, SceneTaskConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SceneTaskConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SceneTaskConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SceneTaskConfig?)(new FBConfig.SceneTaskConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SceneTaskConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SceneTaskConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SceneTaskConfigDict> CreateSceneTaskConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SceneTaskConfigDict.AddValues(builder, valuesOffset);
    return SceneTaskConfigDict.EndSceneTaskConfigDict(builder);
  }

  public static void StartSceneTaskConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SceneTaskConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SceneTaskConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SceneTaskConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SceneTaskConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SceneTaskConfigDict> EndSceneTaskConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SceneTaskConfigDict>(o);
  }
  public static void FinishSceneTaskConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SceneTaskConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSceneTaskConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SceneTaskConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SceneTaskConfigDictT UnPack() {
    var _o = new SceneTaskConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SceneTaskConfigDictT _o) {
    _o.Values = new List<FBConfig.SceneTaskConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SceneTaskConfigDict> Pack(FlatBufferBuilder builder, SceneTaskConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SceneTaskConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SceneTaskConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SceneTaskConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSceneTaskConfigDict(
      builder,
      _values);
  }
}

public class SceneTaskConfigDictT
{
  public List<FBConfig.SceneTaskConfigT> Values { get; set; }

  public SceneTaskConfigDictT() {
    this.Values = null;
  }
  public static SceneTaskConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SceneTaskConfigDict.GetRootAsSceneTaskConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SceneTaskConfigDict.FinishSceneTaskConfigDictBuffer(fbb, SceneTaskConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
