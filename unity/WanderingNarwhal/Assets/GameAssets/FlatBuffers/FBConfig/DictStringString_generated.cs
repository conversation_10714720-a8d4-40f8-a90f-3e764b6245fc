// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DictStringString : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DictStringString GetRootAsDictStringString(ByteBuffer _bb) { return GetRootAsDictStringString(_bb, new DictStringString()); }
  public static DictStringString GetRootAsDictStringString(ByteBuffer _bb, DictStringString obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DictStringString __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Key { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetKeyBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetKeyBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetKeyArray() { return __p.__vector_as_array<byte>(4); }
  public string Value { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetValueBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetValueBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetValueArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.DictStringString> CreateDictStringString(FlatBufferBuilder builder,
      StringOffset keyOffset = default(StringOffset),
      StringOffset valueOffset = default(StringOffset)) {
    builder.StartTable(2);
    DictStringString.AddValue(builder, valueOffset);
    DictStringString.AddKey(builder, keyOffset);
    return DictStringString.EndDictStringString(builder);
  }

  public static void StartDictStringString(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddKey(FlatBufferBuilder builder, StringOffset keyOffset) { builder.AddOffset(0, keyOffset.Value, 0); }
  public static void AddValue(FlatBufferBuilder builder, StringOffset valueOffset) { builder.AddOffset(1, valueOffset.Value, 0); }
  public static Offset<FBConfig.DictStringString> EndDictStringString(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DictStringString>(o);
  }
  public DictStringStringT UnPack() {
    var _o = new DictStringStringT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DictStringStringT _o) {
    _o.Key = this.Key;
    _o.Value = this.Value;
  }
  public static Offset<FBConfig.DictStringString> Pack(FlatBufferBuilder builder, DictStringStringT _o) {
    if (_o == null) return default(Offset<FBConfig.DictStringString>);
    var _key = _o.Key == null ? default(StringOffset) : builder.CreateString(_o.Key);
    var _value = _o.Value == null ? default(StringOffset) : builder.CreateString(_o.Value);
    return CreateDictStringString(
      builder,
      _key,
      _value);
  }
}

public class DictStringStringT
{
  public string Key { get; set; }
  public string Value { get; set; }

  public DictStringStringT() {
    this.Key = null;
    this.Value = null;
  }
}


}
