// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct RaceStageConfig : IFlatbufferConfig<RaceStageConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static RaceStageConfig GetRootAsRaceStageConfig(ByteBuffer _bb) { return GetRootAsRaceStageConfig(_bb, new RaceStageConfig()); }
  public static RaceStageConfig GetRootAsRaceStageConfig(ByteBuffer _bb, RaceStageConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public RaceStageConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string RaceEventUid { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRaceEventUidBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetRaceEventUidBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetRaceEventUidArray() { return __p.__vector_as_array<byte>(6); }
  public string NameTextId { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameTextIdBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetNameTextIdBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetNameTextIdArray() { return __p.__vector_as_array<byte>(8); }
  public string IntroTextId { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIntroTextIdBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetIntroTextIdBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetIntroTextIdArray() { return __p.__vector_as_array<byte>(10); }
  public string MainModalTextId { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMainModalTextIdBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetMainModalTextIdBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetMainModalTextIdArray() { return __p.__vector_as_array<byte>(12); }
  public string FirstPlaceReward { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFirstPlaceRewardBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetFirstPlaceRewardBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetFirstPlaceRewardArray() { return __p.__vector_as_array<byte>(14); }
  public int SortOrder { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSortOrder(int sort_order) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, sort_order); return true; } else { return false; } }
  public string FirstPlaceVictoryMainModalTextId { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFirstPlaceVictoryMainModalTextIdBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetFirstPlaceVictoryMainModalTextIdBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetFirstPlaceVictoryMainModalTextIdArray() { return __p.__vector_as_array<byte>(18); }
  public string LossMainModalTextId { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLossMainModalTextIdBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetLossMainModalTextIdBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetLossMainModalTextIdArray() { return __p.__vector_as_array<byte>(20); }
  public int ScoreGoal { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateScoreGoal(int score_goal) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, score_goal); return true; } else { return false; } }
  public string SecondPlaceReward { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSecondPlaceRewardBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetSecondPlaceRewardBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetSecondPlaceRewardArray() { return __p.__vector_as_array<byte>(24); }
  public string ThirdPlaceReward { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThirdPlaceRewardBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetThirdPlaceRewardBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetThirdPlaceRewardArray() { return __p.__vector_as_array<byte>(26); }
  public string SecondPlaceVictoryMainModalTextId { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSecondPlaceVictoryMainModalTextIdBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetSecondPlaceVictoryMainModalTextIdBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetSecondPlaceVictoryMainModalTextIdArray() { return __p.__vector_as_array<byte>(28); }
  public string ThirdPlaceVictoryMainModalTextId { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThirdPlaceVictoryMainModalTextIdBytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetThirdPlaceVictoryMainModalTextIdBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetThirdPlaceVictoryMainModalTextIdArray() { return __p.__vector_as_array<byte>(30); }
  public int RoundTimeLimit { get { int o = __p.__offset(32); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRoundTimeLimit(int round_time_limit) { int o = __p.__offset(32); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, round_time_limit); return true; } else { return false; } }
  public int MinBotTimeToReachGoalInSeconds { get { int o = __p.__offset(34); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMinBotTimeToReachGoalInSeconds(int min_bot_time_to_reach_goal_in_seconds) { int o = __p.__offset(34); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, min_bot_time_to_reach_goal_in_seconds); return true; } else { return false; } }
  public int MaxBotTimeToReachGoalInSeconds { get { int o = __p.__offset(36); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxBotTimeToReachGoalInSeconds(int max_bot_time_to_reach_goal_in_seconds) { int o = __p.__offset(36); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_bot_time_to_reach_goal_in_seconds); return true; } else { return false; } }
  public int RoundCoolDown { get { int o = __p.__offset(38); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRoundCoolDown(int round_cool_down) { int o = __p.__offset(38); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, round_cool_down); return true; } else { return false; } }

  public static Offset<FBConfig.RaceStageConfig> CreateRaceStageConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset race_event_uidOffset = default(StringOffset),
      StringOffset name_text_idOffset = default(StringOffset),
      StringOffset intro_text_idOffset = default(StringOffset),
      StringOffset main_modal_text_idOffset = default(StringOffset),
      StringOffset first_place_rewardOffset = default(StringOffset),
      int sort_order = 0,
      StringOffset first_place_victory_main_modal_text_idOffset = default(StringOffset),
      StringOffset loss_main_modal_text_idOffset = default(StringOffset),
      int score_goal = 0,
      StringOffset second_place_rewardOffset = default(StringOffset),
      StringOffset third_place_rewardOffset = default(StringOffset),
      StringOffset second_place_victory_main_modal_text_idOffset = default(StringOffset),
      StringOffset third_place_victory_main_modal_text_idOffset = default(StringOffset),
      int round_time_limit = 0,
      int min_bot_time_to_reach_goal_in_seconds = 0,
      int max_bot_time_to_reach_goal_in_seconds = 0,
      int round_cool_down = 0) {
    builder.StartTable(18);
    RaceStageConfig.AddRoundCoolDown(builder, round_cool_down);
    RaceStageConfig.AddMaxBotTimeToReachGoalInSeconds(builder, max_bot_time_to_reach_goal_in_seconds);
    RaceStageConfig.AddMinBotTimeToReachGoalInSeconds(builder, min_bot_time_to_reach_goal_in_seconds);
    RaceStageConfig.AddRoundTimeLimit(builder, round_time_limit);
    RaceStageConfig.AddThirdPlaceVictoryMainModalTextId(builder, third_place_victory_main_modal_text_idOffset);
    RaceStageConfig.AddSecondPlaceVictoryMainModalTextId(builder, second_place_victory_main_modal_text_idOffset);
    RaceStageConfig.AddThirdPlaceReward(builder, third_place_rewardOffset);
    RaceStageConfig.AddSecondPlaceReward(builder, second_place_rewardOffset);
    RaceStageConfig.AddScoreGoal(builder, score_goal);
    RaceStageConfig.AddLossMainModalTextId(builder, loss_main_modal_text_idOffset);
    RaceStageConfig.AddFirstPlaceVictoryMainModalTextId(builder, first_place_victory_main_modal_text_idOffset);
    RaceStageConfig.AddSortOrder(builder, sort_order);
    RaceStageConfig.AddFirstPlaceReward(builder, first_place_rewardOffset);
    RaceStageConfig.AddMainModalTextId(builder, main_modal_text_idOffset);
    RaceStageConfig.AddIntroTextId(builder, intro_text_idOffset);
    RaceStageConfig.AddNameTextId(builder, name_text_idOffset);
    RaceStageConfig.AddRaceEventUid(builder, race_event_uidOffset);
    RaceStageConfig.AddUid(builder, uidOffset);
    return RaceStageConfig.EndRaceStageConfig(builder);
  }

  public static void StartRaceStageConfig(FlatBufferBuilder builder) { builder.StartTable(18); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddRaceEventUid(FlatBufferBuilder builder, StringOffset raceEventUidOffset) { builder.AddOffset(1, raceEventUidOffset.Value, 0); }
  public static void AddNameTextId(FlatBufferBuilder builder, StringOffset nameTextIdOffset) { builder.AddOffset(2, nameTextIdOffset.Value, 0); }
  public static void AddIntroTextId(FlatBufferBuilder builder, StringOffset introTextIdOffset) { builder.AddOffset(3, introTextIdOffset.Value, 0); }
  public static void AddMainModalTextId(FlatBufferBuilder builder, StringOffset mainModalTextIdOffset) { builder.AddOffset(4, mainModalTextIdOffset.Value, 0); }
  public static void AddFirstPlaceReward(FlatBufferBuilder builder, StringOffset firstPlaceRewardOffset) { builder.AddOffset(5, firstPlaceRewardOffset.Value, 0); }
  public static void AddSortOrder(FlatBufferBuilder builder, int sortOrder) { builder.AddInt(6, sortOrder, 0); }
  public static void AddFirstPlaceVictoryMainModalTextId(FlatBufferBuilder builder, StringOffset firstPlaceVictoryMainModalTextIdOffset) { builder.AddOffset(7, firstPlaceVictoryMainModalTextIdOffset.Value, 0); }
  public static void AddLossMainModalTextId(FlatBufferBuilder builder, StringOffset lossMainModalTextIdOffset) { builder.AddOffset(8, lossMainModalTextIdOffset.Value, 0); }
  public static void AddScoreGoal(FlatBufferBuilder builder, int scoreGoal) { builder.AddInt(9, scoreGoal, 0); }
  public static void AddSecondPlaceReward(FlatBufferBuilder builder, StringOffset secondPlaceRewardOffset) { builder.AddOffset(10, secondPlaceRewardOffset.Value, 0); }
  public static void AddThirdPlaceReward(FlatBufferBuilder builder, StringOffset thirdPlaceRewardOffset) { builder.AddOffset(11, thirdPlaceRewardOffset.Value, 0); }
  public static void AddSecondPlaceVictoryMainModalTextId(FlatBufferBuilder builder, StringOffset secondPlaceVictoryMainModalTextIdOffset) { builder.AddOffset(12, secondPlaceVictoryMainModalTextIdOffset.Value, 0); }
  public static void AddThirdPlaceVictoryMainModalTextId(FlatBufferBuilder builder, StringOffset thirdPlaceVictoryMainModalTextIdOffset) { builder.AddOffset(13, thirdPlaceVictoryMainModalTextIdOffset.Value, 0); }
  public static void AddRoundTimeLimit(FlatBufferBuilder builder, int roundTimeLimit) { builder.AddInt(14, roundTimeLimit, 0); }
  public static void AddMinBotTimeToReachGoalInSeconds(FlatBufferBuilder builder, int minBotTimeToReachGoalInSeconds) { builder.AddInt(15, minBotTimeToReachGoalInSeconds, 0); }
  public static void AddMaxBotTimeToReachGoalInSeconds(FlatBufferBuilder builder, int maxBotTimeToReachGoalInSeconds) { builder.AddInt(16, maxBotTimeToReachGoalInSeconds, 0); }
  public static void AddRoundCoolDown(FlatBufferBuilder builder, int roundCoolDown) { builder.AddInt(17, roundCoolDown, 0); }
  public static Offset<FBConfig.RaceStageConfig> EndRaceStageConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.RaceStageConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfRaceStageConfig(FlatBufferBuilder builder, Offset<RaceStageConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<RaceStageConfig> o1, Offset<RaceStageConfig> o2) =>
        new RaceStageConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new RaceStageConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static RaceStageConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    RaceStageConfig obj_ = new RaceStageConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public RaceStageConfigT UnPack() {
    var _o = new RaceStageConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(RaceStageConfigT _o) {
    _o.Uid = this.Uid;
    _o.RaceEventUid = this.RaceEventUid;
    _o.NameTextId = this.NameTextId;
    _o.IntroTextId = this.IntroTextId;
    _o.MainModalTextId = this.MainModalTextId;
    _o.FirstPlaceReward = this.FirstPlaceReward;
    _o.SortOrder = this.SortOrder;
    _o.FirstPlaceVictoryMainModalTextId = this.FirstPlaceVictoryMainModalTextId;
    _o.LossMainModalTextId = this.LossMainModalTextId;
    _o.ScoreGoal = this.ScoreGoal;
    _o.SecondPlaceReward = this.SecondPlaceReward;
    _o.ThirdPlaceReward = this.ThirdPlaceReward;
    _o.SecondPlaceVictoryMainModalTextId = this.SecondPlaceVictoryMainModalTextId;
    _o.ThirdPlaceVictoryMainModalTextId = this.ThirdPlaceVictoryMainModalTextId;
    _o.RoundTimeLimit = this.RoundTimeLimit;
    _o.MinBotTimeToReachGoalInSeconds = this.MinBotTimeToReachGoalInSeconds;
    _o.MaxBotTimeToReachGoalInSeconds = this.MaxBotTimeToReachGoalInSeconds;
    _o.RoundCoolDown = this.RoundCoolDown;
  }
  public static Offset<FBConfig.RaceStageConfig> Pack(FlatBufferBuilder builder, RaceStageConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.RaceStageConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _race_event_uid = _o.RaceEventUid == null ? default(StringOffset) : builder.CreateString(_o.RaceEventUid);
    var _name_text_id = _o.NameTextId == null ? default(StringOffset) : builder.CreateString(_o.NameTextId);
    var _intro_text_id = _o.IntroTextId == null ? default(StringOffset) : builder.CreateString(_o.IntroTextId);
    var _main_modal_text_id = _o.MainModalTextId == null ? default(StringOffset) : builder.CreateString(_o.MainModalTextId);
    var _first_place_reward = _o.FirstPlaceReward == null ? default(StringOffset) : builder.CreateString(_o.FirstPlaceReward);
    var _first_place_victory_main_modal_text_id = _o.FirstPlaceVictoryMainModalTextId == null ? default(StringOffset) : builder.CreateString(_o.FirstPlaceVictoryMainModalTextId);
    var _loss_main_modal_text_id = _o.LossMainModalTextId == null ? default(StringOffset) : builder.CreateString(_o.LossMainModalTextId);
    var _second_place_reward = _o.SecondPlaceReward == null ? default(StringOffset) : builder.CreateString(_o.SecondPlaceReward);
    var _third_place_reward = _o.ThirdPlaceReward == null ? default(StringOffset) : builder.CreateString(_o.ThirdPlaceReward);
    var _second_place_victory_main_modal_text_id = _o.SecondPlaceVictoryMainModalTextId == null ? default(StringOffset) : builder.CreateString(_o.SecondPlaceVictoryMainModalTextId);
    var _third_place_victory_main_modal_text_id = _o.ThirdPlaceVictoryMainModalTextId == null ? default(StringOffset) : builder.CreateString(_o.ThirdPlaceVictoryMainModalTextId);
    return CreateRaceStageConfig(
      builder,
      _uid,
      _race_event_uid,
      _name_text_id,
      _intro_text_id,
      _main_modal_text_id,
      _first_place_reward,
      _o.SortOrder,
      _first_place_victory_main_modal_text_id,
      _loss_main_modal_text_id,
      _o.ScoreGoal,
      _second_place_reward,
      _third_place_reward,
      _second_place_victory_main_modal_text_id,
      _third_place_victory_main_modal_text_id,
      _o.RoundTimeLimit,
      _o.MinBotTimeToReachGoalInSeconds,
      _o.MaxBotTimeToReachGoalInSeconds,
      _o.RoundCoolDown);
  }
}

public class RaceStageConfigT
{
  public string Uid { get; set; }
  public string RaceEventUid { get; set; }
  public string NameTextId { get; set; }
  public string IntroTextId { get; set; }
  public string MainModalTextId { get; set; }
  public string FirstPlaceReward { get; set; }
  public int SortOrder { get; set; }
  public string FirstPlaceVictoryMainModalTextId { get; set; }
  public string LossMainModalTextId { get; set; }
  public int ScoreGoal { get; set; }
  public string SecondPlaceReward { get; set; }
  public string ThirdPlaceReward { get; set; }
  public string SecondPlaceVictoryMainModalTextId { get; set; }
  public string ThirdPlaceVictoryMainModalTextId { get; set; }
  public int RoundTimeLimit { get; set; }
  public int MinBotTimeToReachGoalInSeconds { get; set; }
  public int MaxBotTimeToReachGoalInSeconds { get; set; }
  public int RoundCoolDown { get; set; }

  public RaceStageConfigT() {
    this.Uid = null;
    this.RaceEventUid = null;
    this.NameTextId = null;
    this.IntroTextId = null;
    this.MainModalTextId = null;
    this.FirstPlaceReward = null;
    this.SortOrder = 0;
    this.FirstPlaceVictoryMainModalTextId = null;
    this.LossMainModalTextId = null;
    this.ScoreGoal = 0;
    this.SecondPlaceReward = null;
    this.ThirdPlaceReward = null;
    this.SecondPlaceVictoryMainModalTextId = null;
    this.ThirdPlaceVictoryMainModalTextId = null;
    this.RoundTimeLimit = 0;
    this.MinBotTimeToReachGoalInSeconds = 0;
    this.MaxBotTimeToReachGoalInSeconds = 0;
    this.RoundCoolDown = 0;
  }
}

public struct RaceStageConfigDict : IFlatbufferConfigDict<RaceStageConfig, RaceStageConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static RaceStageConfigDict GetRootAsRaceStageConfigDict(ByteBuffer _bb) { return GetRootAsRaceStageConfigDict(_bb, new RaceStageConfigDict()); }
  public static RaceStageConfigDict GetRootAsRaceStageConfigDict(ByteBuffer _bb, RaceStageConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public RaceStageConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.RaceStageConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.RaceStageConfig?)(new FBConfig.RaceStageConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.RaceStageConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.RaceStageConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.RaceStageConfigDict> CreateRaceStageConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    RaceStageConfigDict.AddValues(builder, valuesOffset);
    return RaceStageConfigDict.EndRaceStageConfigDict(builder);
  }

  public static void StartRaceStageConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.RaceStageConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.RaceStageConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.RaceStageConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.RaceStageConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.RaceStageConfigDict> EndRaceStageConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.RaceStageConfigDict>(o);
  }
  public static void FinishRaceStageConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.RaceStageConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedRaceStageConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.RaceStageConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public RaceStageConfigDictT UnPack() {
    var _o = new RaceStageConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(RaceStageConfigDictT _o) {
    _o.Values = new List<FBConfig.RaceStageConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.RaceStageConfigDict> Pack(FlatBufferBuilder builder, RaceStageConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.RaceStageConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.RaceStageConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.RaceStageConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateRaceStageConfigDict(
      builder,
      _values);
  }
}

public class RaceStageConfigDictT
{
  public List<FBConfig.RaceStageConfigT> Values { get; set; }

  public RaceStageConfigDictT() {
    this.Values = null;
  }
  public static RaceStageConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return RaceStageConfigDict.GetRootAsRaceStageConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    RaceStageConfigDict.FinishRaceStageConfigDictBuffer(fbb, RaceStageConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
