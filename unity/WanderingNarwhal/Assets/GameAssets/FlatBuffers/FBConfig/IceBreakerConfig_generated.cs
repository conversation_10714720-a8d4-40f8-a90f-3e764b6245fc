// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct IceBreakerConfig : IFlatbufferConfig<IceBreakerConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IceBreakerConfig GetRootAsIceBreakerConfig(ByteBuffer _bb) { return GetRootAsIceBreakerConfig(_bb, new IceBreakerConfig()); }
  public static IceBreakerConfig GetRootAsIceBreakerConfig(ByteBuffer _bb, IceBreakerConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public IceBreakerConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Date { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDateBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetDateBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetDateArray() { return __p.__vector_as_array<byte>(6); }
  public string Title { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(8); }
  public string Question { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetQuestionBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetQuestionBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetQuestionArray() { return __p.__vector_as_array<byte>(10); }
  public string Notification { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNotificationBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetNotificationBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetNotificationArray() { return __p.__vector_as_array<byte>(12); }
  public string CharacterIcon { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCharacterIconBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetCharacterIconBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetCharacterIconArray() { return __p.__vector_as_array<byte>(14); }
  public string FixedReward { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFixedRewardBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetFixedRewardBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetFixedRewardArray() { return __p.__vector_as_array<byte>(16); }
  public string Answers(int j) { int o = __p.__offset(18); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int AnswersLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.IceBreakerConfig> CreateIceBreakerConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset dateOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset questionOffset = default(StringOffset),
      StringOffset notificationOffset = default(StringOffset),
      StringOffset character_iconOffset = default(StringOffset),
      StringOffset fixed_rewardOffset = default(StringOffset),
      VectorOffset answersOffset = default(VectorOffset)) {
    builder.StartTable(8);
    IceBreakerConfig.AddAnswers(builder, answersOffset);
    IceBreakerConfig.AddFixedReward(builder, fixed_rewardOffset);
    IceBreakerConfig.AddCharacterIcon(builder, character_iconOffset);
    IceBreakerConfig.AddNotification(builder, notificationOffset);
    IceBreakerConfig.AddQuestion(builder, questionOffset);
    IceBreakerConfig.AddTitle(builder, titleOffset);
    IceBreakerConfig.AddDate(builder, dateOffset);
    IceBreakerConfig.AddUid(builder, uidOffset);
    return IceBreakerConfig.EndIceBreakerConfig(builder);
  }

  public static void StartIceBreakerConfig(FlatBufferBuilder builder) { builder.StartTable(8); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddDate(FlatBufferBuilder builder, StringOffset dateOffset) { builder.AddOffset(1, dateOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(2, titleOffset.Value, 0); }
  public static void AddQuestion(FlatBufferBuilder builder, StringOffset questionOffset) { builder.AddOffset(3, questionOffset.Value, 0); }
  public static void AddNotification(FlatBufferBuilder builder, StringOffset notificationOffset) { builder.AddOffset(4, notificationOffset.Value, 0); }
  public static void AddCharacterIcon(FlatBufferBuilder builder, StringOffset characterIconOffset) { builder.AddOffset(5, characterIconOffset.Value, 0); }
  public static void AddFixedReward(FlatBufferBuilder builder, StringOffset fixedRewardOffset) { builder.AddOffset(6, fixedRewardOffset.Value, 0); }
  public static void AddAnswers(FlatBufferBuilder builder, VectorOffset answersOffset) { builder.AddOffset(7, answersOffset.Value, 0); }
  public static VectorOffset CreateAnswersVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAnswersVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAnswersVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAnswersVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAnswersVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.IceBreakerConfig> EndIceBreakerConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.IceBreakerConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfIceBreakerConfig(FlatBufferBuilder builder, Offset<IceBreakerConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<IceBreakerConfig> o1, Offset<IceBreakerConfig> o2) =>
        new IceBreakerConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new IceBreakerConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static IceBreakerConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    IceBreakerConfig obj_ = new IceBreakerConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public IceBreakerConfigT UnPack() {
    var _o = new IceBreakerConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IceBreakerConfigT _o) {
    _o.Uid = this.Uid;
    _o.Date = this.Date;
    _o.Title = this.Title;
    _o.Question = this.Question;
    _o.Notification = this.Notification;
    _o.CharacterIcon = this.CharacterIcon;
    _o.FixedReward = this.FixedReward;
    _o.Answers = new List<string>();
    for (var _j = 0; _j < this.AnswersLength; ++_j) {_o.Answers.Add(this.Answers(_j));}
  }
  public static Offset<FBConfig.IceBreakerConfig> Pack(FlatBufferBuilder builder, IceBreakerConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.IceBreakerConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _date = _o.Date == null ? default(StringOffset) : builder.CreateString(_o.Date);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _question = _o.Question == null ? default(StringOffset) : builder.CreateString(_o.Question);
    var _notification = _o.Notification == null ? default(StringOffset) : builder.CreateString(_o.Notification);
    var _character_icon = _o.CharacterIcon == null ? default(StringOffset) : builder.CreateString(_o.CharacterIcon);
    var _fixed_reward = _o.FixedReward == null ? default(StringOffset) : builder.CreateString(_o.FixedReward);
    var _answers = default(VectorOffset);
    if (_o.Answers != null) {
      var __answers = new StringOffset[_o.Answers.Count];
      for (var _j = 0; _j < __answers.Length; ++_j) { __answers[_j] = builder.CreateString(_o.Answers[_j]); }
      _answers = CreateAnswersVector(builder, __answers);
    }
    return CreateIceBreakerConfig(
      builder,
      _uid,
      _date,
      _title,
      _question,
      _notification,
      _character_icon,
      _fixed_reward,
      _answers);
  }
}

public class IceBreakerConfigT
{
  public string Uid { get; set; }
  public string Date { get; set; }
  public string Title { get; set; }
  public string Question { get; set; }
  public string Notification { get; set; }
  public string CharacterIcon { get; set; }
  public string FixedReward { get; set; }
  public List<string> Answers { get; set; }

  public IceBreakerConfigT() {
    this.Uid = null;
    this.Date = null;
    this.Title = null;
    this.Question = null;
    this.Notification = null;
    this.CharacterIcon = null;
    this.FixedReward = null;
    this.Answers = null;
  }
}

public struct IceBreakerConfigDict : IFlatbufferConfigDict<IceBreakerConfig, IceBreakerConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IceBreakerConfigDict GetRootAsIceBreakerConfigDict(ByteBuffer _bb) { return GetRootAsIceBreakerConfigDict(_bb, new IceBreakerConfigDict()); }
  public static IceBreakerConfigDict GetRootAsIceBreakerConfigDict(ByteBuffer _bb, IceBreakerConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IceBreakerConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.IceBreakerConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.IceBreakerConfig?)(new FBConfig.IceBreakerConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.IceBreakerConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.IceBreakerConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.IceBreakerConfigDict> CreateIceBreakerConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    IceBreakerConfigDict.AddValues(builder, valuesOffset);
    return IceBreakerConfigDict.EndIceBreakerConfigDict(builder);
  }

  public static void StartIceBreakerConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.IceBreakerConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.IceBreakerConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.IceBreakerConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.IceBreakerConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.IceBreakerConfigDict> EndIceBreakerConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IceBreakerConfigDict>(o);
  }
  public static void FinishIceBreakerConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IceBreakerConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedIceBreakerConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IceBreakerConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public IceBreakerConfigDictT UnPack() {
    var _o = new IceBreakerConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IceBreakerConfigDictT _o) {
    _o.Values = new List<FBConfig.IceBreakerConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.IceBreakerConfigDict> Pack(FlatBufferBuilder builder, IceBreakerConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.IceBreakerConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.IceBreakerConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.IceBreakerConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateIceBreakerConfigDict(
      builder,
      _values);
  }
}

public class IceBreakerConfigDictT
{
  public List<FBConfig.IceBreakerConfigT> Values { get; set; }

  public IceBreakerConfigDictT() {
    this.Values = null;
  }
  public static IceBreakerConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return IceBreakerConfigDict.GetRootAsIceBreakerConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    IceBreakerConfigDict.FinishIceBreakerConfigDictBuffer(fbb, IceBreakerConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
