// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct Fragment : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Fragment GetRootAsFragment(ByteBuffer _bb) { return GetRootAsFragment(_bb, new Fragment()); }
  public static Fragment GetRootAsFragment(ByteBuffer _bb, Fragment obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Fragment __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.Point? Size { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }

  public static Offset<FBConfig.Fragment> CreateFragment(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      Offset<FBConfig.Point> sizeOffset = default(Offset<FBConfig.Point>)) {
    builder.StartTable(2);
    Fragment.AddSize(builder, sizeOffset);
    Fragment.AddUid(builder, uidOffset);
    return Fragment.EndFragment(builder);
  }

  public static void StartFragment(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSize(FlatBufferBuilder builder, Offset<FBConfig.Point> sizeOffset) { builder.AddOffset(1, sizeOffset.Value, 0); }
  public static Offset<FBConfig.Fragment> EndFragment(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.Fragment>(o);
  }
  public FragmentT UnPack() {
    var _o = new FragmentT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(FragmentT _o) {
    _o.Uid = this.Uid;
    _o.Size = this.Size.HasValue ? this.Size.Value.UnPack() : null;
  }
  public static Offset<FBConfig.Fragment> Pack(FlatBufferBuilder builder, FragmentT _o) {
    if (_o == null) return default(Offset<FBConfig.Fragment>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _size = _o.Size == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Size);
    return CreateFragment(
      builder,
      _uid,
      _size);
  }
}

public class FragmentT
{
  public string Uid { get; set; }
  public FBConfig.PointT Size { get; set; }

  public FragmentT() {
    this.Uid = null;
    this.Size = null;
  }
}

public struct LocationsPanelSettings : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LocationsPanelSettings GetRootAsLocationsPanelSettings(ByteBuffer _bb) { return GetRootAsLocationsPanelSettings(_bb, new LocationsPanelSettings()); }
  public static LocationsPanelSettings GetRootAsLocationsPanelSettings(ByteBuffer _bb, LocationsPanelSettings obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LocationsPanelSettings __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Thumbnail { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(4); }
  public string LockedThumbnail { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLockedThumbnailBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetLockedThumbnailBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetLockedThumbnailArray() { return __p.__vector_as_array<byte>(6); }
  public string FillerOverride { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFillerOverrideBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetFillerOverrideBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetFillerOverrideArray() { return __p.__vector_as_array<byte>(8); }
  public string M3(int j) { int o = __p.__offset(10); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int M3Length { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.LocationsPanelSettings> CreateLocationsPanelSettings(FlatBufferBuilder builder,
      StringOffset thumbnailOffset = default(StringOffset),
      StringOffset locked_thumbnailOffset = default(StringOffset),
      StringOffset filler_overrideOffset = default(StringOffset),
      VectorOffset m3Offset = default(VectorOffset)) {
    builder.StartTable(4);
    LocationsPanelSettings.AddM3(builder, m3Offset);
    LocationsPanelSettings.AddFillerOverride(builder, filler_overrideOffset);
    LocationsPanelSettings.AddLockedThumbnail(builder, locked_thumbnailOffset);
    LocationsPanelSettings.AddThumbnail(builder, thumbnailOffset);
    return LocationsPanelSettings.EndLocationsPanelSettings(builder);
  }

  public static void StartLocationsPanelSettings(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(0, thumbnailOffset.Value, 0); }
  public static void AddLockedThumbnail(FlatBufferBuilder builder, StringOffset lockedThumbnailOffset) { builder.AddOffset(1, lockedThumbnailOffset.Value, 0); }
  public static void AddFillerOverride(FlatBufferBuilder builder, StringOffset fillerOverrideOffset) { builder.AddOffset(2, fillerOverrideOffset.Value, 0); }
  public static void AddM3(FlatBufferBuilder builder, VectorOffset m3Offset) { builder.AddOffset(3, m3Offset.Value, 0); }
  public static VectorOffset CreateM3Vector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateM3VectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateM3VectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateM3VectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartM3Vector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LocationsPanelSettings> EndLocationsPanelSettings(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LocationsPanelSettings>(o);
  }
  public LocationsPanelSettingsT UnPack() {
    var _o = new LocationsPanelSettingsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LocationsPanelSettingsT _o) {
    _o.Thumbnail = this.Thumbnail;
    _o.LockedThumbnail = this.LockedThumbnail;
    _o.FillerOverride = this.FillerOverride;
    _o.M3 = new List<string>();
    for (var _j = 0; _j < this.M3Length; ++_j) {_o.M3.Add(this.M3(_j));}
  }
  public static Offset<FBConfig.LocationsPanelSettings> Pack(FlatBufferBuilder builder, LocationsPanelSettingsT _o) {
    if (_o == null) return default(Offset<FBConfig.LocationsPanelSettings>);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    var _locked_thumbnail = _o.LockedThumbnail == null ? default(StringOffset) : builder.CreateString(_o.LockedThumbnail);
    var _filler_override = _o.FillerOverride == null ? default(StringOffset) : builder.CreateString(_o.FillerOverride);
    var _m3 = default(VectorOffset);
    if (_o.M3 != null) {
      var __m3 = new StringOffset[_o.M3.Count];
      for (var _j = 0; _j < __m3.Length; ++_j) { __m3[_j] = builder.CreateString(_o.M3[_j]); }
      _m3 = CreateM3Vector(builder, __m3);
    }
    return CreateLocationsPanelSettings(
      builder,
      _thumbnail,
      _locked_thumbnail,
      _filler_override,
      _m3);
  }
}

public class LocationsPanelSettingsT
{
  public string Thumbnail { get; set; }
  public string LockedThumbnail { get; set; }
  public string FillerOverride { get; set; }
  public List<string> M3 { get; set; }

  public LocationsPanelSettingsT() {
    this.Thumbnail = null;
    this.LockedThumbnail = null;
    this.FillerOverride = null;
    this.M3 = null;
  }
}

public struct LocationConfig : IFlatbufferConfig<LocationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LocationConfig GetRootAsLocationConfig(ByteBuffer _bb) { return GetRootAsLocationConfig(_bb, new LocationConfig()); }
  public static LocationConfig GetRootAsLocationConfig(ByteBuffer _bb, LocationConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LocationConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.Fragment? Fragments(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.Fragment?)(new FBConfig.Fragment()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int FragmentsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Point? FragmentSize { get { int o = __p.__offset(8); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string CityListImageName { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCityListImageNameBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetCityListImageNameBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetCityListImageNameArray() { return __p.__vector_as_array<byte>(10); }
  public string CityListLockedImage { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCityListLockedImageBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetCityListLockedImageBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetCityListLockedImageArray() { return __p.__vector_as_array<byte>(12); }
  public string LocationName { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLocationNameBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetLocationNameBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetLocationNameArray() { return __p.__vector_as_array<byte>(14); }
  public FBConfig.Point3? MarkerPoint { get { int o = __p.__offset(16); return o != 0 ? (FBConfig.Point3?)(new FBConfig.Point3()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int CityOrder { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCityOrder(int city_order) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, city_order); return true; } else { return false; } }
  public string FlagImage { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFlagImageBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetFlagImageBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetFlagImageArray() { return __p.__vector_as_array<byte>(20); }
  public string AvailableSkins(int j) { int o = __p.__offset(22); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int AvailableSkinsLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string IconName { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconNameBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetIconNameBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetIconNameArray() { return __p.__vector_as_array<byte>(24); }
  public string LocationText { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLocationTextBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetLocationTextBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetLocationTextArray() { return __p.__vector_as_array<byte>(26); }
  public bool IsComingSoon { get { int o = __p.__offset(28); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateIsComingSoon(bool is_coming_soon) { int o = __p.__offset(28); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(is_coming_soon ? 1 : 0)); return true; } else { return false; } }
  public string TransitionPrefab { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTransitionPrefabBytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetTransitionPrefabBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetTransitionPrefabArray() { return __p.__vector_as_array<byte>(30); }
  public string BgMusicId { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBgMusicIdBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetBgMusicIdBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetBgMusicIdArray() { return __p.__vector_as_array<byte>(32); }
  public string AssetPrefix { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAssetPrefixBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetAssetPrefixBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetAssetPrefixArray() { return __p.__vector_as_array<byte>(34); }
  public int NumberOfFragments { get { int o = __p.__offset(36); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNumberOfFragments(int number_of_fragments) { int o = __p.__offset(36); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, number_of_fragments); return true; } else { return false; } }
  public FBConfig.Point? LastFragmentSize { get { int o = __p.__offset(38); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.LocationsPanelSettings? LocationsPanelSettings { get { int o = __p.__offset(40); return o != 0 ? (FBConfig.LocationsPanelSettings?)(new FBConfig.LocationsPanelSettings()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string CompletionReward { get { int o = __p.__offset(42); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCompletionRewardBytes() { return __p.__vector_as_span<byte>(42, 1); }
#else
  public ArraySegment<byte>? GetCompletionRewardBytes() { return __p.__vector_as_arraysegment(42); }
#endif
  public byte[] GetCompletionRewardArray() { return __p.__vector_as_array<byte>(42); }
  public string LevelMusicId { get { int o = __p.__offset(44); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLevelMusicIdBytes() { return __p.__vector_as_span<byte>(44, 1); }
#else
  public ArraySegment<byte>? GetLevelMusicIdBytes() { return __p.__vector_as_arraysegment(44); }
#endif
  public byte[] GetLevelMusicIdArray() { return __p.__vector_as_array<byte>(44); }
  public bool IsSideMapLocation { get { int o = __p.__offset(46); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateIsSideMapLocation(bool is_side_map_location) { int o = __p.__offset(46); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(is_side_map_location ? 1 : 0)); return true; } else { return false; } }
  public bool IsOutGlobeLocation { get { int o = __p.__offset(48); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateIsOutGlobeLocation(bool is_out_globe_location) { int o = __p.__offset(48); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(is_out_globe_location ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.LocationConfig> CreateLocationConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset fragmentsOffset = default(VectorOffset),
      Offset<FBConfig.Point> fragment_sizeOffset = default(Offset<FBConfig.Point>),
      StringOffset city_list_image_nameOffset = default(StringOffset),
      StringOffset city_list_locked_imageOffset = default(StringOffset),
      StringOffset location_nameOffset = default(StringOffset),
      Offset<FBConfig.Point3> marker_pointOffset = default(Offset<FBConfig.Point3>),
      int city_order = 0,
      StringOffset flag_imageOffset = default(StringOffset),
      VectorOffset available_skinsOffset = default(VectorOffset),
      StringOffset icon_nameOffset = default(StringOffset),
      StringOffset location_textOffset = default(StringOffset),
      bool is_coming_soon = false,
      StringOffset transition_prefabOffset = default(StringOffset),
      StringOffset bg_music_idOffset = default(StringOffset),
      StringOffset asset_prefixOffset = default(StringOffset),
      int number_of_fragments = 0,
      Offset<FBConfig.Point> last_fragment_sizeOffset = default(Offset<FBConfig.Point>),
      Offset<FBConfig.LocationsPanelSettings> locations_panel_settingsOffset = default(Offset<FBConfig.LocationsPanelSettings>),
      StringOffset completion_rewardOffset = default(StringOffset),
      StringOffset level_music_idOffset = default(StringOffset),
      bool is_side_map_location = false,
      bool is_out_globe_location = false) {
    builder.StartTable(23);
    LocationConfig.AddLevelMusicId(builder, level_music_idOffset);
    LocationConfig.AddCompletionReward(builder, completion_rewardOffset);
    LocationConfig.AddLocationsPanelSettings(builder, locations_panel_settingsOffset);
    LocationConfig.AddLastFragmentSize(builder, last_fragment_sizeOffset);
    LocationConfig.AddNumberOfFragments(builder, number_of_fragments);
    LocationConfig.AddAssetPrefix(builder, asset_prefixOffset);
    LocationConfig.AddBgMusicId(builder, bg_music_idOffset);
    LocationConfig.AddTransitionPrefab(builder, transition_prefabOffset);
    LocationConfig.AddLocationText(builder, location_textOffset);
    LocationConfig.AddIconName(builder, icon_nameOffset);
    LocationConfig.AddAvailableSkins(builder, available_skinsOffset);
    LocationConfig.AddFlagImage(builder, flag_imageOffset);
    LocationConfig.AddCityOrder(builder, city_order);
    LocationConfig.AddMarkerPoint(builder, marker_pointOffset);
    LocationConfig.AddLocationName(builder, location_nameOffset);
    LocationConfig.AddCityListLockedImage(builder, city_list_locked_imageOffset);
    LocationConfig.AddCityListImageName(builder, city_list_image_nameOffset);
    LocationConfig.AddFragmentSize(builder, fragment_sizeOffset);
    LocationConfig.AddFragments(builder, fragmentsOffset);
    LocationConfig.AddUid(builder, uidOffset);
    LocationConfig.AddIsOutGlobeLocation(builder, is_out_globe_location);
    LocationConfig.AddIsSideMapLocation(builder, is_side_map_location);
    LocationConfig.AddIsComingSoon(builder, is_coming_soon);
    return LocationConfig.EndLocationConfig(builder);
  }

  public static void StartLocationConfig(FlatBufferBuilder builder) { builder.StartTable(23); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddFragments(FlatBufferBuilder builder, VectorOffset fragmentsOffset) { builder.AddOffset(1, fragmentsOffset.Value, 0); }
  public static VectorOffset CreateFragmentsVector(FlatBufferBuilder builder, Offset<FBConfig.Fragment>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateFragmentsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.Fragment>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFragmentsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.Fragment>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFragmentsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.Fragment>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartFragmentsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddFragmentSize(FlatBufferBuilder builder, Offset<FBConfig.Point> fragmentSizeOffset) { builder.AddOffset(2, fragmentSizeOffset.Value, 0); }
  public static void AddCityListImageName(FlatBufferBuilder builder, StringOffset cityListImageNameOffset) { builder.AddOffset(3, cityListImageNameOffset.Value, 0); }
  public static void AddCityListLockedImage(FlatBufferBuilder builder, StringOffset cityListLockedImageOffset) { builder.AddOffset(4, cityListLockedImageOffset.Value, 0); }
  public static void AddLocationName(FlatBufferBuilder builder, StringOffset locationNameOffset) { builder.AddOffset(5, locationNameOffset.Value, 0); }
  public static void AddMarkerPoint(FlatBufferBuilder builder, Offset<FBConfig.Point3> markerPointOffset) { builder.AddOffset(6, markerPointOffset.Value, 0); }
  public static void AddCityOrder(FlatBufferBuilder builder, int cityOrder) { builder.AddInt(7, cityOrder, 0); }
  public static void AddFlagImage(FlatBufferBuilder builder, StringOffset flagImageOffset) { builder.AddOffset(8, flagImageOffset.Value, 0); }
  public static void AddAvailableSkins(FlatBufferBuilder builder, VectorOffset availableSkinsOffset) { builder.AddOffset(9, availableSkinsOffset.Value, 0); }
  public static VectorOffset CreateAvailableSkinsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAvailableSkinsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAvailableSkinsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAvailableSkinsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAvailableSkinsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddIconName(FlatBufferBuilder builder, StringOffset iconNameOffset) { builder.AddOffset(10, iconNameOffset.Value, 0); }
  public static void AddLocationText(FlatBufferBuilder builder, StringOffset locationTextOffset) { builder.AddOffset(11, locationTextOffset.Value, 0); }
  public static void AddIsComingSoon(FlatBufferBuilder builder, bool isComingSoon) { builder.AddBool(12, isComingSoon, false); }
  public static void AddTransitionPrefab(FlatBufferBuilder builder, StringOffset transitionPrefabOffset) { builder.AddOffset(13, transitionPrefabOffset.Value, 0); }
  public static void AddBgMusicId(FlatBufferBuilder builder, StringOffset bgMusicIdOffset) { builder.AddOffset(14, bgMusicIdOffset.Value, 0); }
  public static void AddAssetPrefix(FlatBufferBuilder builder, StringOffset assetPrefixOffset) { builder.AddOffset(15, assetPrefixOffset.Value, 0); }
  public static void AddNumberOfFragments(FlatBufferBuilder builder, int numberOfFragments) { builder.AddInt(16, numberOfFragments, 0); }
  public static void AddLastFragmentSize(FlatBufferBuilder builder, Offset<FBConfig.Point> lastFragmentSizeOffset) { builder.AddOffset(17, lastFragmentSizeOffset.Value, 0); }
  public static void AddLocationsPanelSettings(FlatBufferBuilder builder, Offset<FBConfig.LocationsPanelSettings> locationsPanelSettingsOffset) { builder.AddOffset(18, locationsPanelSettingsOffset.Value, 0); }
  public static void AddCompletionReward(FlatBufferBuilder builder, StringOffset completionRewardOffset) { builder.AddOffset(19, completionRewardOffset.Value, 0); }
  public static void AddLevelMusicId(FlatBufferBuilder builder, StringOffset levelMusicIdOffset) { builder.AddOffset(20, levelMusicIdOffset.Value, 0); }
  public static void AddIsSideMapLocation(FlatBufferBuilder builder, bool isSideMapLocation) { builder.AddBool(21, isSideMapLocation, false); }
  public static void AddIsOutGlobeLocation(FlatBufferBuilder builder, bool isOutGlobeLocation) { builder.AddBool(22, isOutGlobeLocation, false); }
  public static Offset<FBConfig.LocationConfig> EndLocationConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LocationConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLocationConfig(FlatBufferBuilder builder, Offset<LocationConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LocationConfig> o1, Offset<LocationConfig> o2) =>
        new LocationConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LocationConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LocationConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LocationConfig obj_ = new LocationConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LocationConfigT UnPack() {
    var _o = new LocationConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LocationConfigT _o) {
    _o.Uid = this.Uid;
    _o.Fragments = new List<FBConfig.FragmentT>();
    for (var _j = 0; _j < this.FragmentsLength; ++_j) {_o.Fragments.Add(this.Fragments(_j).HasValue ? this.Fragments(_j).Value.UnPack() : null);}
    _o.FragmentSize = this.FragmentSize.HasValue ? this.FragmentSize.Value.UnPack() : null;
    _o.CityListImageName = this.CityListImageName;
    _o.CityListLockedImage = this.CityListLockedImage;
    _o.LocationName = this.LocationName;
    _o.MarkerPoint = this.MarkerPoint.HasValue ? this.MarkerPoint.Value.UnPack() : null;
    _o.CityOrder = this.CityOrder;
    _o.FlagImage = this.FlagImage;
    _o.AvailableSkins = new List<string>();
    for (var _j = 0; _j < this.AvailableSkinsLength; ++_j) {_o.AvailableSkins.Add(this.AvailableSkins(_j));}
    _o.IconName = this.IconName;
    _o.LocationText = this.LocationText;
    _o.IsComingSoon = this.IsComingSoon;
    _o.TransitionPrefab = this.TransitionPrefab;
    _o.BgMusicId = this.BgMusicId;
    _o.AssetPrefix = this.AssetPrefix;
    _o.NumberOfFragments = this.NumberOfFragments;
    _o.LastFragmentSize = this.LastFragmentSize.HasValue ? this.LastFragmentSize.Value.UnPack() : null;
    _o.LocationsPanelSettings = this.LocationsPanelSettings.HasValue ? this.LocationsPanelSettings.Value.UnPack() : null;
    _o.CompletionReward = this.CompletionReward;
    _o.LevelMusicId = this.LevelMusicId;
    _o.IsSideMapLocation = this.IsSideMapLocation;
    _o.IsOutGlobeLocation = this.IsOutGlobeLocation;
  }
  public static Offset<FBConfig.LocationConfig> Pack(FlatBufferBuilder builder, LocationConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LocationConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _fragments = default(VectorOffset);
    if (_o.Fragments != null) {
      var __fragments = new Offset<FBConfig.Fragment>[_o.Fragments.Count];
      for (var _j = 0; _j < __fragments.Length; ++_j) { __fragments[_j] = FBConfig.Fragment.Pack(builder, _o.Fragments[_j]); }
      _fragments = CreateFragmentsVector(builder, __fragments);
    }
    var _fragment_size = _o.FragmentSize == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.FragmentSize);
    var _city_list_image_name = _o.CityListImageName == null ? default(StringOffset) : builder.CreateString(_o.CityListImageName);
    var _city_list_locked_image = _o.CityListLockedImage == null ? default(StringOffset) : builder.CreateString(_o.CityListLockedImage);
    var _location_name = _o.LocationName == null ? default(StringOffset) : builder.CreateString(_o.LocationName);
    var _marker_point = _o.MarkerPoint == null ? default(Offset<FBConfig.Point3>) : FBConfig.Point3.Pack(builder, _o.MarkerPoint);
    var _flag_image = _o.FlagImage == null ? default(StringOffset) : builder.CreateString(_o.FlagImage);
    var _available_skins = default(VectorOffset);
    if (_o.AvailableSkins != null) {
      var __available_skins = new StringOffset[_o.AvailableSkins.Count];
      for (var _j = 0; _j < __available_skins.Length; ++_j) { __available_skins[_j] = builder.CreateString(_o.AvailableSkins[_j]); }
      _available_skins = CreateAvailableSkinsVector(builder, __available_skins);
    }
    var _icon_name = _o.IconName == null ? default(StringOffset) : builder.CreateString(_o.IconName);
    var _location_text = _o.LocationText == null ? default(StringOffset) : builder.CreateString(_o.LocationText);
    var _transition_prefab = _o.TransitionPrefab == null ? default(StringOffset) : builder.CreateString(_o.TransitionPrefab);
    var _bg_music_id = _o.BgMusicId == null ? default(StringOffset) : builder.CreateString(_o.BgMusicId);
    var _asset_prefix = _o.AssetPrefix == null ? default(StringOffset) : builder.CreateString(_o.AssetPrefix);
    var _last_fragment_size = _o.LastFragmentSize == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.LastFragmentSize);
    var _locations_panel_settings = _o.LocationsPanelSettings == null ? default(Offset<FBConfig.LocationsPanelSettings>) : FBConfig.LocationsPanelSettings.Pack(builder, _o.LocationsPanelSettings);
    var _completion_reward = _o.CompletionReward == null ? default(StringOffset) : builder.CreateString(_o.CompletionReward);
    var _level_music_id = _o.LevelMusicId == null ? default(StringOffset) : builder.CreateString(_o.LevelMusicId);
    return CreateLocationConfig(
      builder,
      _uid,
      _fragments,
      _fragment_size,
      _city_list_image_name,
      _city_list_locked_image,
      _location_name,
      _marker_point,
      _o.CityOrder,
      _flag_image,
      _available_skins,
      _icon_name,
      _location_text,
      _o.IsComingSoon,
      _transition_prefab,
      _bg_music_id,
      _asset_prefix,
      _o.NumberOfFragments,
      _last_fragment_size,
      _locations_panel_settings,
      _completion_reward,
      _level_music_id,
      _o.IsSideMapLocation,
      _o.IsOutGlobeLocation);
  }
}

public class LocationConfigT
{
  public string Uid { get; set; }
  public List<FBConfig.FragmentT> Fragments { get; set; }
  public FBConfig.PointT FragmentSize { get; set; }
  public string CityListImageName { get; set; }
  public string CityListLockedImage { get; set; }
  public string LocationName { get; set; }
  public FBConfig.Point3T MarkerPoint { get; set; }
  public int CityOrder { get; set; }
  public string FlagImage { get; set; }
  public List<string> AvailableSkins { get; set; }
  public string IconName { get; set; }
  public string LocationText { get; set; }
  public bool IsComingSoon { get; set; }
  public string TransitionPrefab { get; set; }
  public string BgMusicId { get; set; }
  public string AssetPrefix { get; set; }
  public int NumberOfFragments { get; set; }
  public FBConfig.PointT LastFragmentSize { get; set; }
  public FBConfig.LocationsPanelSettingsT LocationsPanelSettings { get; set; }
  public string CompletionReward { get; set; }
  public string LevelMusicId { get; set; }
  public bool IsSideMapLocation { get; set; }
  public bool IsOutGlobeLocation { get; set; }

  public LocationConfigT() {
    this.Uid = null;
    this.Fragments = null;
    this.FragmentSize = null;
    this.CityListImageName = null;
    this.CityListLockedImage = null;
    this.LocationName = null;
    this.MarkerPoint = null;
    this.CityOrder = 0;
    this.FlagImage = null;
    this.AvailableSkins = null;
    this.IconName = null;
    this.LocationText = null;
    this.IsComingSoon = false;
    this.TransitionPrefab = null;
    this.BgMusicId = null;
    this.AssetPrefix = null;
    this.NumberOfFragments = 0;
    this.LastFragmentSize = null;
    this.LocationsPanelSettings = null;
    this.CompletionReward = null;
    this.LevelMusicId = null;
    this.IsSideMapLocation = false;
    this.IsOutGlobeLocation = false;
  }
}

public struct LocationConfigDict : IFlatbufferConfigDict<LocationConfig, LocationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LocationConfigDict GetRootAsLocationConfigDict(ByteBuffer _bb) { return GetRootAsLocationConfigDict(_bb, new LocationConfigDict()); }
  public static LocationConfigDict GetRootAsLocationConfigDict(ByteBuffer _bb, LocationConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LocationConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LocationConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LocationConfig?)(new FBConfig.LocationConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LocationConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LocationConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LocationConfigDict> CreateLocationConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LocationConfigDict.AddValues(builder, valuesOffset);
    return LocationConfigDict.EndLocationConfigDict(builder);
  }

  public static void StartLocationConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LocationConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LocationConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LocationConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LocationConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LocationConfigDict> EndLocationConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LocationConfigDict>(o);
  }
  public static void FinishLocationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LocationConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLocationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LocationConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LocationConfigDictT UnPack() {
    var _o = new LocationConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LocationConfigDictT _o) {
    _o.Values = new List<FBConfig.LocationConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LocationConfigDict> Pack(FlatBufferBuilder builder, LocationConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LocationConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LocationConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LocationConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLocationConfigDict(
      builder,
      _values);
  }
}

public class LocationConfigDictT
{
  public List<FBConfig.LocationConfigT> Values { get; set; }

  public LocationConfigDictT() {
    this.Values = null;
  }
  public static LocationConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LocationConfigDict.GetRootAsLocationConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LocationConfigDict.FinishLocationConfigDictBuffer(fbb, LocationConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
