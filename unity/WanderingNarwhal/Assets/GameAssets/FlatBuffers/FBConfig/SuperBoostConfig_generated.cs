// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SuperBoostConfig : IFlatbufferConfig<SuperBoostConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SuperBoostConfig GetRootAsSuperBoostConfig(ByteBuffer _bb) { return GetRootAsSuperBoostConfig(_bb, new SuperBoostConfig()); }
  public static SuperBoostConfig GetRootAsSuperBoostConfig(ByteBuffer _bb, SuperBoostConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SuperBoostConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public float RainPercent { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateRainPercent(float rain_percent) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, rain_percent); return true; } else { return false; } }
  public float FillSpeed { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateFillSpeed(float fill_speed) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, fill_speed); return true; } else { return false; } }

  public static Offset<FBConfig.SuperBoostConfig> CreateSuperBoostConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      float rain_percent = 0.0f,
      float fill_speed = 0.0f) {
    builder.StartTable(3);
    SuperBoostConfig.AddFillSpeed(builder, fill_speed);
    SuperBoostConfig.AddRainPercent(builder, rain_percent);
    SuperBoostConfig.AddUid(builder, uidOffset);
    return SuperBoostConfig.EndSuperBoostConfig(builder);
  }

  public static void StartSuperBoostConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddRainPercent(FlatBufferBuilder builder, float rainPercent) { builder.AddFloat(1, rainPercent, 0.0f); }
  public static void AddFillSpeed(FlatBufferBuilder builder, float fillSpeed) { builder.AddFloat(2, fillSpeed, 0.0f); }
  public static Offset<FBConfig.SuperBoostConfig> EndSuperBoostConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SuperBoostConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSuperBoostConfig(FlatBufferBuilder builder, Offset<SuperBoostConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SuperBoostConfig> o1, Offset<SuperBoostConfig> o2) =>
        new SuperBoostConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SuperBoostConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SuperBoostConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SuperBoostConfig obj_ = new SuperBoostConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SuperBoostConfigT UnPack() {
    var _o = new SuperBoostConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SuperBoostConfigT _o) {
    _o.Uid = this.Uid;
    _o.RainPercent = this.RainPercent;
    _o.FillSpeed = this.FillSpeed;
  }
  public static Offset<FBConfig.SuperBoostConfig> Pack(FlatBufferBuilder builder, SuperBoostConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SuperBoostConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateSuperBoostConfig(
      builder,
      _uid,
      _o.RainPercent,
      _o.FillSpeed);
  }
}

public class SuperBoostConfigT
{
  public string Uid { get; set; }
  public float RainPercent { get; set; }
  public float FillSpeed { get; set; }

  public SuperBoostConfigT() {
    this.Uid = null;
    this.RainPercent = 0.0f;
    this.FillSpeed = 0.0f;
  }
}

public struct SuperBoostConfigDict : IFlatbufferConfigDict<SuperBoostConfig, SuperBoostConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SuperBoostConfigDict GetRootAsSuperBoostConfigDict(ByteBuffer _bb) { return GetRootAsSuperBoostConfigDict(_bb, new SuperBoostConfigDict()); }
  public static SuperBoostConfigDict GetRootAsSuperBoostConfigDict(ByteBuffer _bb, SuperBoostConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SuperBoostConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SuperBoostConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SuperBoostConfig?)(new FBConfig.SuperBoostConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SuperBoostConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SuperBoostConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SuperBoostConfigDict> CreateSuperBoostConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SuperBoostConfigDict.AddValues(builder, valuesOffset);
    return SuperBoostConfigDict.EndSuperBoostConfigDict(builder);
  }

  public static void StartSuperBoostConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SuperBoostConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SuperBoostConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SuperBoostConfigDict> EndSuperBoostConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SuperBoostConfigDict>(o);
  }
  public static void FinishSuperBoostConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSuperBoostConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SuperBoostConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SuperBoostConfigDictT UnPack() {
    var _o = new SuperBoostConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SuperBoostConfigDictT _o) {
    _o.Values = new List<FBConfig.SuperBoostConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SuperBoostConfigDict> Pack(FlatBufferBuilder builder, SuperBoostConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SuperBoostConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SuperBoostConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SuperBoostConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSuperBoostConfigDict(
      builder,
      _values);
  }
}

public class SuperBoostConfigDictT
{
  public List<FBConfig.SuperBoostConfigT> Values { get; set; }

  public SuperBoostConfigDictT() {
    this.Values = null;
  }
  public static SuperBoostConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SuperBoostConfigDict.GetRootAsSuperBoostConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SuperBoostConfigDict.FinishSuperBoostConfigDictBuffer(fbb, SuperBoostConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
