// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct AimingValues : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AimingValues GetRootAsAimingValues(ByteBuffer _bb) { return GetRootAsAimingValues(_bb, new AimingValues()); }
  public static AimingValues GetRootAsAimingValues(ByteBuffer _bb, AimingValues obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AimingValues __assign(int _i, Byte<PERSON><PERSON>er _bb) { __init(_i, _bb); return this; }

  public float WinLossModifier { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateWinLossModifier(float win_loss_modifier) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, win_loss_modifier); return true; } else { return false; } }
  public float UseDensityDefThreshold { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateUseDensityDefThreshold(float use_density_def_threshold) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, use_density_def_threshold); return true; } else { return false; } }
  public float AutomatchHelpThreshold { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateAutomatchHelpThreshold(float automatch_help_threshold) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, automatch_help_threshold); return true; } else { return false; } }
  public int AutomatchHelpCooldown { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateAutomatchHelpCooldown(int automatch_help_cooldown) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, automatch_help_cooldown); return true; } else { return false; } }

  public static Offset<FBConfig.AimingValues> CreateAimingValues(FlatBufferBuilder builder,
      float win_loss_modifier = 0.0f,
      float use_density_def_threshold = 0.0f,
      float automatch_help_threshold = 0.0f,
      int automatch_help_cooldown = 0) {
    builder.StartTable(4);
    AimingValues.AddAutomatchHelpCooldown(builder, automatch_help_cooldown);
    AimingValues.AddAutomatchHelpThreshold(builder, automatch_help_threshold);
    AimingValues.AddUseDensityDefThreshold(builder, use_density_def_threshold);
    AimingValues.AddWinLossModifier(builder, win_loss_modifier);
    return AimingValues.EndAimingValues(builder);
  }

  public static void StartAimingValues(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddWinLossModifier(FlatBufferBuilder builder, float winLossModifier) { builder.AddFloat(0, winLossModifier, 0.0f); }
  public static void AddUseDensityDefThreshold(FlatBufferBuilder builder, float useDensityDefThreshold) { builder.AddFloat(1, useDensityDefThreshold, 0.0f); }
  public static void AddAutomatchHelpThreshold(FlatBufferBuilder builder, float automatchHelpThreshold) { builder.AddFloat(2, automatchHelpThreshold, 0.0f); }
  public static void AddAutomatchHelpCooldown(FlatBufferBuilder builder, int automatchHelpCooldown) { builder.AddInt(3, automatchHelpCooldown, 0); }
  public static Offset<FBConfig.AimingValues> EndAimingValues(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AimingValues>(o);
  }
  public AimingValuesT UnPack() {
    var _o = new AimingValuesT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AimingValuesT _o) {
    _o.WinLossModifier = this.WinLossModifier;
    _o.UseDensityDefThreshold = this.UseDensityDefThreshold;
    _o.AutomatchHelpThreshold = this.AutomatchHelpThreshold;
    _o.AutomatchHelpCooldown = this.AutomatchHelpCooldown;
  }
  public static Offset<FBConfig.AimingValues> Pack(FlatBufferBuilder builder, AimingValuesT _o) {
    if (_o == null) return default(Offset<FBConfig.AimingValues>);
    return CreateAimingValues(
      builder,
      _o.WinLossModifier,
      _o.UseDensityDefThreshold,
      _o.AutomatchHelpThreshold,
      _o.AutomatchHelpCooldown);
  }
}

public class AimingValuesT
{
  public float WinLossModifier { get; set; }
  public float UseDensityDefThreshold { get; set; }
  public float AutomatchHelpThreshold { get; set; }
  public int AutomatchHelpCooldown { get; set; }

  public AimingValuesT() {
    this.WinLossModifier = 0.0f;
    this.UseDensityDefThreshold = 0.0f;
    this.AutomatchHelpThreshold = 0.0f;
    this.AutomatchHelpCooldown = 0;
  }
}

public struct HelpLevelValues : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HelpLevelValues GetRootAsHelpLevelValues(ByteBuffer _bb) { return GetRootAsHelpLevelValues(_bb, new HelpLevelValues()); }
  public static HelpLevelValues GetRootAsHelpLevelValues(ByteBuffer _bb, HelpLevelValues obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HelpLevelValues __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float Threshold { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateThreshold(float threshold) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, threshold); return true; } else { return false; } }
  public float GridIndex { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateGridIndex(float grid_index) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, grid_index); return true; } else { return false; } }

  public static Offset<FBConfig.HelpLevelValues> CreateHelpLevelValues(FlatBufferBuilder builder,
      float threshold = 0.0f,
      float grid_index = 0.0f) {
    builder.StartTable(2);
    HelpLevelValues.AddGridIndex(builder, grid_index);
    HelpLevelValues.AddThreshold(builder, threshold);
    return HelpLevelValues.EndHelpLevelValues(builder);
  }

  public static void StartHelpLevelValues(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddThreshold(FlatBufferBuilder builder, float threshold) { builder.AddFloat(0, threshold, 0.0f); }
  public static void AddGridIndex(FlatBufferBuilder builder, float gridIndex) { builder.AddFloat(1, gridIndex, 0.0f); }
  public static Offset<FBConfig.HelpLevelValues> EndHelpLevelValues(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.HelpLevelValues>(o);
  }
  public HelpLevelValuesT UnPack() {
    var _o = new HelpLevelValuesT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HelpLevelValuesT _o) {
    _o.Threshold = this.Threshold;
    _o.GridIndex = this.GridIndex;
  }
  public static Offset<FBConfig.HelpLevelValues> Pack(FlatBufferBuilder builder, HelpLevelValuesT _o) {
    if (_o == null) return default(Offset<FBConfig.HelpLevelValues>);
    return CreateHelpLevelValues(
      builder,
      _o.Threshold,
      _o.GridIndex);
  }
}

public class HelpLevelValuesT
{
  public float Threshold { get; set; }
  public float GridIndex { get; set; }

  public HelpLevelValuesT() {
    this.Threshold = 0.0f;
    this.GridIndex = 0.0f;
  }
}

public struct AssistSystemConfig : IFlatbufferConfig<AssistSystemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AssistSystemConfig GetRootAsAssistSystemConfig(ByteBuffer _bb) { return GetRootAsAssistSystemConfig(_bb, new AssistSystemConfig()); }
  public static AssistSystemConfig GetRootAsAssistSystemConfig(ByteBuffer _bb, AssistSystemConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AssistSystemConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.AimingValues? AimingToWin { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.AimingValues?)(new FBConfig.AimingValues()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.AimingValues? AimingToLose { get { int o = __p.__offset(8); return o != 0 ? (FBConfig.AimingValues?)(new FBConfig.AimingValues()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int GridHelpCooldown { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateGridHelpCooldown(int grid_help_cooldown) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, grid_help_cooldown); return true; } else { return false; } }
  public float SkipGridHelpCooldownThreshold { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateSkipGridHelpCooldownThreshold(float skip_grid_help_cooldown_threshold) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, skip_grid_help_cooldown_threshold); return true; } else { return false; } }
  public FBConfig.HelpLevelValues? HelpLevels(int j) { int o = __p.__offset(14); return o != 0 ? (FBConfig.HelpLevelValues?)(new FBConfig.HelpLevelValues()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int HelpLevelsLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int MaxAutomatches { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxAutomatches(int max_automatches) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_automatches); return true; } else { return false; } }
  public float UsefulMovesOnlyThreshold { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateUsefulMovesOnlyThreshold(float useful_moves_only_threshold) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, useful_moves_only_threshold); return true; } else { return false; } }
  public float TasLoseRateModifier { get { int o = __p.__offset(20); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateTasLoseRateModifier(float tas_lose_rate_modifier) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, tas_lose_rate_modifier); return true; } else { return false; } }
  public int SimulationsToRun { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSimulationsToRun(int simulations_to_run) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, simulations_to_run); return true; } else { return false; } }
  public float LossValuePolynomial(int j) { int o = __p.__offset(24); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int LossValuePolynomialLength { get { int o = __p.__offset(24); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetLossValuePolynomialBytes() { return __p.__vector_as_span<float>(24, 4); }
#else
  public ArraySegment<byte>? GetLossValuePolynomialBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public float[] GetLossValuePolynomialArray() { return __p.__vector_as_array<float>(24); }
  public bool MutateLossValuePolynomial(int j, float loss_value_polynomial) { int o = __p.__offset(24); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, loss_value_polynomial); return true; } else { return false; } }
  public float WinValuePolynomial(int j) { int o = __p.__offset(26); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int WinValuePolynomialLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetWinValuePolynomialBytes() { return __p.__vector_as_span<float>(26, 4); }
#else
  public ArraySegment<byte>? GetWinValuePolynomialBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public float[] GetWinValuePolynomialArray() { return __p.__vector_as_array<float>(26); }
  public bool MutateWinValuePolynomial(int j, float win_value_polynomial) { int o = __p.__offset(26); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, win_value_polynomial); return true; } else { return false; } }

  public static Offset<FBConfig.AssistSystemConfig> CreateAssistSystemConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      Offset<FBConfig.AimingValues> aiming_to_winOffset = default(Offset<FBConfig.AimingValues>),
      Offset<FBConfig.AimingValues> aiming_to_loseOffset = default(Offset<FBConfig.AimingValues>),
      int grid_help_cooldown = 0,
      float skip_grid_help_cooldown_threshold = 0.0f,
      VectorOffset help_levelsOffset = default(VectorOffset),
      int max_automatches = 0,
      float useful_moves_only_threshold = 0.0f,
      float tas_lose_rate_modifier = 0.0f,
      int simulations_to_run = 0,
      VectorOffset loss_value_polynomialOffset = default(VectorOffset),
      VectorOffset win_value_polynomialOffset = default(VectorOffset)) {
    builder.StartTable(12);
    AssistSystemConfig.AddWinValuePolynomial(builder, win_value_polynomialOffset);
    AssistSystemConfig.AddLossValuePolynomial(builder, loss_value_polynomialOffset);
    AssistSystemConfig.AddSimulationsToRun(builder, simulations_to_run);
    AssistSystemConfig.AddTasLoseRateModifier(builder, tas_lose_rate_modifier);
    AssistSystemConfig.AddUsefulMovesOnlyThreshold(builder, useful_moves_only_threshold);
    AssistSystemConfig.AddMaxAutomatches(builder, max_automatches);
    AssistSystemConfig.AddHelpLevels(builder, help_levelsOffset);
    AssistSystemConfig.AddSkipGridHelpCooldownThreshold(builder, skip_grid_help_cooldown_threshold);
    AssistSystemConfig.AddGridHelpCooldown(builder, grid_help_cooldown);
    AssistSystemConfig.AddAimingToLose(builder, aiming_to_loseOffset);
    AssistSystemConfig.AddAimingToWin(builder, aiming_to_winOffset);
    AssistSystemConfig.AddUid(builder, uidOffset);
    return AssistSystemConfig.EndAssistSystemConfig(builder);
  }

  public static void StartAssistSystemConfig(FlatBufferBuilder builder) { builder.StartTable(12); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddAimingToWin(FlatBufferBuilder builder, Offset<FBConfig.AimingValues> aimingToWinOffset) { builder.AddOffset(1, aimingToWinOffset.Value, 0); }
  public static void AddAimingToLose(FlatBufferBuilder builder, Offset<FBConfig.AimingValues> aimingToLoseOffset) { builder.AddOffset(2, aimingToLoseOffset.Value, 0); }
  public static void AddGridHelpCooldown(FlatBufferBuilder builder, int gridHelpCooldown) { builder.AddInt(3, gridHelpCooldown, 0); }
  public static void AddSkipGridHelpCooldownThreshold(FlatBufferBuilder builder, float skipGridHelpCooldownThreshold) { builder.AddFloat(4, skipGridHelpCooldownThreshold, 0.0f); }
  public static void AddHelpLevels(FlatBufferBuilder builder, VectorOffset helpLevelsOffset) { builder.AddOffset(5, helpLevelsOffset.Value, 0); }
  public static VectorOffset CreateHelpLevelsVector(FlatBufferBuilder builder, Offset<FBConfig.HelpLevelValues>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateHelpLevelsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.HelpLevelValues>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHelpLevelsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.HelpLevelValues>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHelpLevelsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.HelpLevelValues>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartHelpLevelsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddMaxAutomatches(FlatBufferBuilder builder, int maxAutomatches) { builder.AddInt(6, maxAutomatches, 0); }
  public static void AddUsefulMovesOnlyThreshold(FlatBufferBuilder builder, float usefulMovesOnlyThreshold) { builder.AddFloat(7, usefulMovesOnlyThreshold, 0.0f); }
  public static void AddTasLoseRateModifier(FlatBufferBuilder builder, float tasLoseRateModifier) { builder.AddFloat(8, tasLoseRateModifier, 0.0f); }
  public static void AddSimulationsToRun(FlatBufferBuilder builder, int simulationsToRun) { builder.AddInt(9, simulationsToRun, 0); }
  public static void AddLossValuePolynomial(FlatBufferBuilder builder, VectorOffset lossValuePolynomialOffset) { builder.AddOffset(10, lossValuePolynomialOffset.Value, 0); }
  public static VectorOffset CreateLossValuePolynomialVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateLossValuePolynomialVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLossValuePolynomialVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLossValuePolynomialVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLossValuePolynomialVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddWinValuePolynomial(FlatBufferBuilder builder, VectorOffset winValuePolynomialOffset) { builder.AddOffset(11, winValuePolynomialOffset.Value, 0); }
  public static VectorOffset CreateWinValuePolynomialVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateWinValuePolynomialVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWinValuePolynomialVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWinValuePolynomialVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartWinValuePolynomialVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.AssistSystemConfig> EndAssistSystemConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.AssistSystemConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfAssistSystemConfig(FlatBufferBuilder builder, Offset<AssistSystemConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<AssistSystemConfig> o1, Offset<AssistSystemConfig> o2) =>
        new AssistSystemConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new AssistSystemConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static AssistSystemConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    AssistSystemConfig obj_ = new AssistSystemConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public AssistSystemConfigT UnPack() {
    var _o = new AssistSystemConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AssistSystemConfigT _o) {
    _o.Uid = this.Uid;
    _o.AimingToWin = this.AimingToWin.HasValue ? this.AimingToWin.Value.UnPack() : null;
    _o.AimingToLose = this.AimingToLose.HasValue ? this.AimingToLose.Value.UnPack() : null;
    _o.GridHelpCooldown = this.GridHelpCooldown;
    _o.SkipGridHelpCooldownThreshold = this.SkipGridHelpCooldownThreshold;
    _o.HelpLevels = new List<FBConfig.HelpLevelValuesT>();
    for (var _j = 0; _j < this.HelpLevelsLength; ++_j) {_o.HelpLevels.Add(this.HelpLevels(_j).HasValue ? this.HelpLevels(_j).Value.UnPack() : null);}
    _o.MaxAutomatches = this.MaxAutomatches;
    _o.UsefulMovesOnlyThreshold = this.UsefulMovesOnlyThreshold;
    _o.TasLoseRateModifier = this.TasLoseRateModifier;
    _o.SimulationsToRun = this.SimulationsToRun;
    _o.LossValuePolynomial = new List<float>();
    for (var _j = 0; _j < this.LossValuePolynomialLength; ++_j) {_o.LossValuePolynomial.Add(this.LossValuePolynomial(_j));}
    _o.WinValuePolynomial = new List<float>();
    for (var _j = 0; _j < this.WinValuePolynomialLength; ++_j) {_o.WinValuePolynomial.Add(this.WinValuePolynomial(_j));}
  }
  public static Offset<FBConfig.AssistSystemConfig> Pack(FlatBufferBuilder builder, AssistSystemConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.AssistSystemConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _aiming_to_win = _o.AimingToWin == null ? default(Offset<FBConfig.AimingValues>) : FBConfig.AimingValues.Pack(builder, _o.AimingToWin);
    var _aiming_to_lose = _o.AimingToLose == null ? default(Offset<FBConfig.AimingValues>) : FBConfig.AimingValues.Pack(builder, _o.AimingToLose);
    var _help_levels = default(VectorOffset);
    if (_o.HelpLevels != null) {
      var __help_levels = new Offset<FBConfig.HelpLevelValues>[_o.HelpLevels.Count];
      for (var _j = 0; _j < __help_levels.Length; ++_j) { __help_levels[_j] = FBConfig.HelpLevelValues.Pack(builder, _o.HelpLevels[_j]); }
      _help_levels = CreateHelpLevelsVector(builder, __help_levels);
    }
    var _loss_value_polynomial = default(VectorOffset);
    if (_o.LossValuePolynomial != null) {
      var __loss_value_polynomial = _o.LossValuePolynomial.ToArray();
      _loss_value_polynomial = CreateLossValuePolynomialVector(builder, __loss_value_polynomial);
    }
    var _win_value_polynomial = default(VectorOffset);
    if (_o.WinValuePolynomial != null) {
      var __win_value_polynomial = _o.WinValuePolynomial.ToArray();
      _win_value_polynomial = CreateWinValuePolynomialVector(builder, __win_value_polynomial);
    }
    return CreateAssistSystemConfig(
      builder,
      _uid,
      _aiming_to_win,
      _aiming_to_lose,
      _o.GridHelpCooldown,
      _o.SkipGridHelpCooldownThreshold,
      _help_levels,
      _o.MaxAutomatches,
      _o.UsefulMovesOnlyThreshold,
      _o.TasLoseRateModifier,
      _o.SimulationsToRun,
      _loss_value_polynomial,
      _win_value_polynomial);
  }
}

public class AssistSystemConfigT
{
  public string Uid { get; set; }
  public FBConfig.AimingValuesT AimingToWin { get; set; }
  public FBConfig.AimingValuesT AimingToLose { get; set; }
  public int GridHelpCooldown { get; set; }
  public float SkipGridHelpCooldownThreshold { get; set; }
  public List<FBConfig.HelpLevelValuesT> HelpLevels { get; set; }
  public int MaxAutomatches { get; set; }
  public float UsefulMovesOnlyThreshold { get; set; }
  public float TasLoseRateModifier { get; set; }
  public int SimulationsToRun { get; set; }
  public List<float> LossValuePolynomial { get; set; }
  public List<float> WinValuePolynomial { get; set; }

  public AssistSystemConfigT() {
    this.Uid = null;
    this.AimingToWin = null;
    this.AimingToLose = null;
    this.GridHelpCooldown = 0;
    this.SkipGridHelpCooldownThreshold = 0.0f;
    this.HelpLevels = null;
    this.MaxAutomatches = 0;
    this.UsefulMovesOnlyThreshold = 0.0f;
    this.TasLoseRateModifier = 0.0f;
    this.SimulationsToRun = 0;
    this.LossValuePolynomial = null;
    this.WinValuePolynomial = null;
  }
}

public struct AssistSystemConfigDict : IFlatbufferConfigDict<AssistSystemConfig, AssistSystemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AssistSystemConfigDict GetRootAsAssistSystemConfigDict(ByteBuffer _bb) { return GetRootAsAssistSystemConfigDict(_bb, new AssistSystemConfigDict()); }
  public static AssistSystemConfigDict GetRootAsAssistSystemConfigDict(ByteBuffer _bb, AssistSystemConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AssistSystemConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.AssistSystemConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.AssistSystemConfig?)(new FBConfig.AssistSystemConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.AssistSystemConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.AssistSystemConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.AssistSystemConfigDict> CreateAssistSystemConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    AssistSystemConfigDict.AddValues(builder, valuesOffset);
    return AssistSystemConfigDict.EndAssistSystemConfigDict(builder);
  }

  public static void StartAssistSystemConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.AssistSystemConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.AssistSystemConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.AssistSystemConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.AssistSystemConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.AssistSystemConfigDict> EndAssistSystemConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AssistSystemConfigDict>(o);
  }
  public static void FinishAssistSystemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AssistSystemConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedAssistSystemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AssistSystemConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public AssistSystemConfigDictT UnPack() {
    var _o = new AssistSystemConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AssistSystemConfigDictT _o) {
    _o.Values = new List<FBConfig.AssistSystemConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.AssistSystemConfigDict> Pack(FlatBufferBuilder builder, AssistSystemConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.AssistSystemConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.AssistSystemConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.AssistSystemConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateAssistSystemConfigDict(
      builder,
      _values);
  }
}

public class AssistSystemConfigDictT
{
  public List<FBConfig.AssistSystemConfigT> Values { get; set; }

  public AssistSystemConfigDictT() {
    this.Values = null;
  }
  public static AssistSystemConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return AssistSystemConfigDict.GetRootAsAssistSystemConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    AssistSystemConfigDict.FinishAssistSystemConfigDictBuffer(fbb, AssistSystemConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
