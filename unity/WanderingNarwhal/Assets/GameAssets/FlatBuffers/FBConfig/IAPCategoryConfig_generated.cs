// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct IAPCategoryConfig : IFlatbufferConfig<IAPCategoryConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPCategoryConfig GetRootAsIAPCategoryConfig(ByteBuffer _bb) { return GetRootAsIAPCategoryConfig(_bb, new IAPCategoryConfig()); }
  public static IAPCategoryConfig GetRootAsIAPCategoryConfig(ByteBuffer _bb, IAPCategoryConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPCategoryConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Title { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(6); }
  public string Thumbnail { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(8); }
  public int Order { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }
  public bool Visible { get { int o = __p.__offset(12); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateVisible(bool visible) { int o = __p.__offset(12); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(visible ? 1 : 0)); return true; } else { return false; } }
  public string PromotionUid { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPromotionUidBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetPromotionUidBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetPromotionUidArray() { return __p.__vector_as_array<byte>(14); }

  public static Offset<FBConfig.IAPCategoryConfig> CreateIAPCategoryConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset thumbnailOffset = default(StringOffset),
      int order = 0,
      bool visible = false,
      StringOffset promotion_uidOffset = default(StringOffset)) {
    builder.StartTable(6);
    IAPCategoryConfig.AddPromotionUid(builder, promotion_uidOffset);
    IAPCategoryConfig.AddOrder(builder, order);
    IAPCategoryConfig.AddThumbnail(builder, thumbnailOffset);
    IAPCategoryConfig.AddTitle(builder, titleOffset);
    IAPCategoryConfig.AddUid(builder, uidOffset);
    IAPCategoryConfig.AddVisible(builder, visible);
    return IAPCategoryConfig.EndIAPCategoryConfig(builder);
  }

  public static void StartIAPCategoryConfig(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(1, titleOffset.Value, 0); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(2, thumbnailOffset.Value, 0); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(3, order, 0); }
  public static void AddVisible(FlatBufferBuilder builder, bool visible) { builder.AddBool(4, visible, false); }
  public static void AddPromotionUid(FlatBufferBuilder builder, StringOffset promotionUidOffset) { builder.AddOffset(5, promotionUidOffset.Value, 0); }
  public static Offset<FBConfig.IAPCategoryConfig> EndIAPCategoryConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.IAPCategoryConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfIAPCategoryConfig(FlatBufferBuilder builder, Offset<IAPCategoryConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<IAPCategoryConfig> o1, Offset<IAPCategoryConfig> o2) =>
        new IAPCategoryConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new IAPCategoryConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static IAPCategoryConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    IAPCategoryConfig obj_ = new IAPCategoryConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public IAPCategoryConfigT UnPack() {
    var _o = new IAPCategoryConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPCategoryConfigT _o) {
    _o.Uid = this.Uid;
    _o.Title = this.Title;
    _o.Thumbnail = this.Thumbnail;
    _o.Order = this.Order;
    _o.Visible = this.Visible;
    _o.PromotionUid = this.PromotionUid;
  }
  public static Offset<FBConfig.IAPCategoryConfig> Pack(FlatBufferBuilder builder, IAPCategoryConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPCategoryConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    var _promotion_uid = _o.PromotionUid == null ? default(StringOffset) : builder.CreateString(_o.PromotionUid);
    return CreateIAPCategoryConfig(
      builder,
      _uid,
      _title,
      _thumbnail,
      _o.Order,
      _o.Visible,
      _promotion_uid);
  }
}

public class IAPCategoryConfigT
{
  public string Uid { get; set; }
  public string Title { get; set; }
  public string Thumbnail { get; set; }
  public int Order { get; set; }
  public bool Visible { get; set; }
  public string PromotionUid { get; set; }

  public IAPCategoryConfigT() {
    this.Uid = null;
    this.Title = null;
    this.Thumbnail = null;
    this.Order = 0;
    this.Visible = false;
    this.PromotionUid = null;
  }
}

public struct IAPCategoryConfigDict : IFlatbufferConfigDict<IAPCategoryConfig, IAPCategoryConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPCategoryConfigDict GetRootAsIAPCategoryConfigDict(ByteBuffer _bb) { return GetRootAsIAPCategoryConfigDict(_bb, new IAPCategoryConfigDict()); }
  public static IAPCategoryConfigDict GetRootAsIAPCategoryConfigDict(ByteBuffer _bb, IAPCategoryConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPCategoryConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.IAPCategoryConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.IAPCategoryConfig?)(new FBConfig.IAPCategoryConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.IAPCategoryConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.IAPCategoryConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.IAPCategoryConfigDict> CreateIAPCategoryConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    IAPCategoryConfigDict.AddValues(builder, valuesOffset);
    return IAPCategoryConfigDict.EndIAPCategoryConfigDict(builder);
  }

  public static void StartIAPCategoryConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.IAPCategoryConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.IAPCategoryConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.IAPCategoryConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.IAPCategoryConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.IAPCategoryConfigDict> EndIAPCategoryConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPCategoryConfigDict>(o);
  }
  public static void FinishIAPCategoryConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPCategoryConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedIAPCategoryConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPCategoryConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public IAPCategoryConfigDictT UnPack() {
    var _o = new IAPCategoryConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPCategoryConfigDictT _o) {
    _o.Values = new List<FBConfig.IAPCategoryConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.IAPCategoryConfigDict> Pack(FlatBufferBuilder builder, IAPCategoryConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPCategoryConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.IAPCategoryConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.IAPCategoryConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateIAPCategoryConfigDict(
      builder,
      _values);
  }
}

public class IAPCategoryConfigDictT
{
  public List<FBConfig.IAPCategoryConfigT> Values { get; set; }

  public IAPCategoryConfigDictT() {
    this.Values = null;
  }
  public static IAPCategoryConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return IAPCategoryConfigDict.GetRootAsIAPCategoryConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    IAPCategoryConfigDict.FinishIAPCategoryConfigDictBuffer(fbb, IAPCategoryConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
