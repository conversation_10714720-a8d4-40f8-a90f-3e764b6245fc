// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct MapPlaceableConfig : IFlatbufferConfig<MapPlaceableConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static MapPlaceableConfig GetRootAsMapPlaceableConfig(ByteBuffer _bb) { return GetRootAsMapPlaceableConfig(_bb, new MapPlaceableConfig()); }
  public static MapPlaceableConfig GetRootAsMapPlaceableConfig(ByteBuffer _bb, MapPlaceableConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public MapPlaceableConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Thumbnail { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(6); }
  public string LocationUid { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLocationUidBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetLocationUidBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetLocationUidArray() { return __p.__vector_as_array<byte>(8); }
  public string LevelUid { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLevelUidBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetLevelUidBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetLevelUidArray() { return __p.__vector_as_array<byte>(10); }
  public string UnlockAtLevelUid { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUnlockAtLevelUidBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetUnlockAtLevelUidBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetUnlockAtLevelUidArray() { return __p.__vector_as_array<byte>(12); }
  public string BlueprintUid { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBlueprintUidBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetBlueprintUidBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetBlueprintUidArray() { return __p.__vector_as_array<byte>(14); }
  public FBConfig.Point? Position { get { int o = __p.__offset(16); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.Point? Size { get { int o = __p.__offset(18); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.Price? PriceFb { get { int o = __p.__offset(20); return o != 0 ? (FBConfig.Price?)(new FBConfig.Price()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.Price? InstantFinishPriceFb { get { int o = __p.__offset(22); return o != 0 ? (FBConfig.Price?)(new FBConfig.Price()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public float ConstructionTime { get { int o = __p.__offset(24); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateConstructionTime(float construction_time) { int o = __p.__offset(24); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, construction_time); return true; } else { return false; } }
  public string QuestUid { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetQuestUidBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetQuestUidBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetQuestUidArray() { return __p.__vector_as_array<byte>(26); }
  public string BlueprintObjectiveUid { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBlueprintObjectiveUidBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetBlueprintObjectiveUidBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetBlueprintObjectiveUidArray() { return __p.__vector_as_array<byte>(28); }
  public string PopupThumbnail { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPopupThumbnailBytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetPopupThumbnailBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetPopupThumbnailArray() { return __p.__vector_as_array<byte>(30); }
  public float ProgressBarOffset { get { int o = __p.__offset(32); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateProgressBarOffset(float progress_bar_offset) { int o = __p.__offset(32); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, progress_bar_offset); return true; } else { return false; } }
  public bool Fake { get { int o = __p.__offset(34); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateFake(bool fake) { int o = __p.__offset(34); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(fake ? 1 : 0)); return true; } else { return false; } }
  public int LevelProgressionRequirements(int j) { int o = __p.__offset(36); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int LevelProgressionRequirementsLength { get { int o = __p.__offset(36); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetLevelProgressionRequirementsBytes() { return __p.__vector_as_span<int>(36, 4); }
#else
  public ArraySegment<byte>? GetLevelProgressionRequirementsBytes() { return __p.__vector_as_arraysegment(36); }
#endif
  public int[] GetLevelProgressionRequirementsArray() { return __p.__vector_as_array<int>(36); }
  public bool MutateLevelProgressionRequirements(int j, int level_progression_requirements) { int o = __p.__offset(36); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, level_progression_requirements); return true; } else { return false; } }
  public FBConfig.DictStringInt? RewardFb(int j) { int o = __p.__offset(38); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardFbLength { get { int o = __p.__offset(38); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Point? Curve(int j) { int o = __p.__offset(40); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int CurveLength { get { int o = __p.__offset(40); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.MapPlaceableConfig> CreateMapPlaceableConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset thumbnailOffset = default(StringOffset),
      StringOffset location_uidOffset = default(StringOffset),
      StringOffset level_uidOffset = default(StringOffset),
      StringOffset unlock_at_level_uidOffset = default(StringOffset),
      StringOffset blueprint_uidOffset = default(StringOffset),
      Offset<FBConfig.Point> positionOffset = default(Offset<FBConfig.Point>),
      Offset<FBConfig.Point> sizeOffset = default(Offset<FBConfig.Point>),
      Offset<FBConfig.Price> price_fbOffset = default(Offset<FBConfig.Price>),
      Offset<FBConfig.Price> instant_finish_price_fbOffset = default(Offset<FBConfig.Price>),
      float construction_time = 0.0f,
      StringOffset quest_uidOffset = default(StringOffset),
      StringOffset blueprint_objective_uidOffset = default(StringOffset),
      StringOffset popup_thumbnailOffset = default(StringOffset),
      float progress_bar_offset = 0.0f,
      bool fake = false,
      VectorOffset level_progression_requirementsOffset = default(VectorOffset),
      VectorOffset reward_fbOffset = default(VectorOffset),
      VectorOffset curveOffset = default(VectorOffset)) {
    builder.StartTable(19);
    MapPlaceableConfig.AddCurve(builder, curveOffset);
    MapPlaceableConfig.AddRewardFb(builder, reward_fbOffset);
    MapPlaceableConfig.AddLevelProgressionRequirements(builder, level_progression_requirementsOffset);
    MapPlaceableConfig.AddProgressBarOffset(builder, progress_bar_offset);
    MapPlaceableConfig.AddPopupThumbnail(builder, popup_thumbnailOffset);
    MapPlaceableConfig.AddBlueprintObjectiveUid(builder, blueprint_objective_uidOffset);
    MapPlaceableConfig.AddQuestUid(builder, quest_uidOffset);
    MapPlaceableConfig.AddConstructionTime(builder, construction_time);
    MapPlaceableConfig.AddInstantFinishPriceFb(builder, instant_finish_price_fbOffset);
    MapPlaceableConfig.AddPriceFb(builder, price_fbOffset);
    MapPlaceableConfig.AddSize(builder, sizeOffset);
    MapPlaceableConfig.AddPosition(builder, positionOffset);
    MapPlaceableConfig.AddBlueprintUid(builder, blueprint_uidOffset);
    MapPlaceableConfig.AddUnlockAtLevelUid(builder, unlock_at_level_uidOffset);
    MapPlaceableConfig.AddLevelUid(builder, level_uidOffset);
    MapPlaceableConfig.AddLocationUid(builder, location_uidOffset);
    MapPlaceableConfig.AddThumbnail(builder, thumbnailOffset);
    MapPlaceableConfig.AddUid(builder, uidOffset);
    MapPlaceableConfig.AddFake(builder, fake);
    return MapPlaceableConfig.EndMapPlaceableConfig(builder);
  }

  public static void StartMapPlaceableConfig(FlatBufferBuilder builder) { builder.StartTable(19); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(1, thumbnailOffset.Value, 0); }
  public static void AddLocationUid(FlatBufferBuilder builder, StringOffset locationUidOffset) { builder.AddOffset(2, locationUidOffset.Value, 0); }
  public static void AddLevelUid(FlatBufferBuilder builder, StringOffset levelUidOffset) { builder.AddOffset(3, levelUidOffset.Value, 0); }
  public static void AddUnlockAtLevelUid(FlatBufferBuilder builder, StringOffset unlockAtLevelUidOffset) { builder.AddOffset(4, unlockAtLevelUidOffset.Value, 0); }
  public static void AddBlueprintUid(FlatBufferBuilder builder, StringOffset blueprintUidOffset) { builder.AddOffset(5, blueprintUidOffset.Value, 0); }
  public static void AddPosition(FlatBufferBuilder builder, Offset<FBConfig.Point> positionOffset) { builder.AddOffset(6, positionOffset.Value, 0); }
  public static void AddSize(FlatBufferBuilder builder, Offset<FBConfig.Point> sizeOffset) { builder.AddOffset(7, sizeOffset.Value, 0); }
  public static void AddPriceFb(FlatBufferBuilder builder, Offset<FBConfig.Price> priceFbOffset) { builder.AddOffset(8, priceFbOffset.Value, 0); }
  public static void AddInstantFinishPriceFb(FlatBufferBuilder builder, Offset<FBConfig.Price> instantFinishPriceFbOffset) { builder.AddOffset(9, instantFinishPriceFbOffset.Value, 0); }
  public static void AddConstructionTime(FlatBufferBuilder builder, float constructionTime) { builder.AddFloat(10, constructionTime, 0.0f); }
  public static void AddQuestUid(FlatBufferBuilder builder, StringOffset questUidOffset) { builder.AddOffset(11, questUidOffset.Value, 0); }
  public static void AddBlueprintObjectiveUid(FlatBufferBuilder builder, StringOffset blueprintObjectiveUidOffset) { builder.AddOffset(12, blueprintObjectiveUidOffset.Value, 0); }
  public static void AddPopupThumbnail(FlatBufferBuilder builder, StringOffset popupThumbnailOffset) { builder.AddOffset(13, popupThumbnailOffset.Value, 0); }
  public static void AddProgressBarOffset(FlatBufferBuilder builder, float progressBarOffset) { builder.AddFloat(14, progressBarOffset, 0.0f); }
  public static void AddFake(FlatBufferBuilder builder, bool fake) { builder.AddBool(15, fake, false); }
  public static void AddLevelProgressionRequirements(FlatBufferBuilder builder, VectorOffset levelProgressionRequirementsOffset) { builder.AddOffset(16, levelProgressionRequirementsOffset.Value, 0); }
  public static VectorOffset CreateLevelProgressionRequirementsVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateLevelProgressionRequirementsVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLevelProgressionRequirementsVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLevelProgressionRequirementsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLevelProgressionRequirementsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddRewardFb(FlatBufferBuilder builder, VectorOffset rewardFbOffset) { builder.AddOffset(17, rewardFbOffset.Value, 0); }
  public static VectorOffset CreateRewardFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddCurve(FlatBufferBuilder builder, VectorOffset curveOffset) { builder.AddOffset(18, curveOffset.Value, 0); }
  public static VectorOffset CreateCurveVector(FlatBufferBuilder builder, Offset<FBConfig.Point>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCurveVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.Point>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCurveVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.Point>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCurveVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.Point>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCurveVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.MapPlaceableConfig> EndMapPlaceableConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.MapPlaceableConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfMapPlaceableConfig(FlatBufferBuilder builder, Offset<MapPlaceableConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<MapPlaceableConfig> o1, Offset<MapPlaceableConfig> o2) =>
        new MapPlaceableConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new MapPlaceableConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static MapPlaceableConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    MapPlaceableConfig obj_ = new MapPlaceableConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public MapPlaceableConfigT UnPack() {
    var _o = new MapPlaceableConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(MapPlaceableConfigT _o) {
    _o.Uid = this.Uid;
    _o.Thumbnail = this.Thumbnail;
    _o.LocationUid = this.LocationUid;
    _o.LevelUid = this.LevelUid;
    _o.UnlockAtLevelUid = this.UnlockAtLevelUid;
    _o.BlueprintUid = this.BlueprintUid;
    _o.Position = this.Position.HasValue ? this.Position.Value.UnPack() : null;
    _o.Size = this.Size.HasValue ? this.Size.Value.UnPack() : null;
    _o.PriceFb = this.PriceFb.HasValue ? this.PriceFb.Value.UnPack() : null;
    _o.InstantFinishPriceFb = this.InstantFinishPriceFb.HasValue ? this.InstantFinishPriceFb.Value.UnPack() : null;
    _o.ConstructionTime = this.ConstructionTime;
    _o.QuestUid = this.QuestUid;
    _o.BlueprintObjectiveUid = this.BlueprintObjectiveUid;
    _o.PopupThumbnail = this.PopupThumbnail;
    _o.ProgressBarOffset = this.ProgressBarOffset;
    _o.Fake = this.Fake;
    _o.LevelProgressionRequirements = new List<int>();
    for (var _j = 0; _j < this.LevelProgressionRequirementsLength; ++_j) {_o.LevelProgressionRequirements.Add(this.LevelProgressionRequirements(_j));}
    _o.RewardFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardFbLength; ++_j) {_o.RewardFb.Add(this.RewardFb(_j).HasValue ? this.RewardFb(_j).Value.UnPack() : null);}
    _o.Curve = new List<FBConfig.PointT>();
    for (var _j = 0; _j < this.CurveLength; ++_j) {_o.Curve.Add(this.Curve(_j).HasValue ? this.Curve(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.MapPlaceableConfig> Pack(FlatBufferBuilder builder, MapPlaceableConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.MapPlaceableConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    var _location_uid = _o.LocationUid == null ? default(StringOffset) : builder.CreateString(_o.LocationUid);
    var _level_uid = _o.LevelUid == null ? default(StringOffset) : builder.CreateString(_o.LevelUid);
    var _unlock_at_level_uid = _o.UnlockAtLevelUid == null ? default(StringOffset) : builder.CreateString(_o.UnlockAtLevelUid);
    var _blueprint_uid = _o.BlueprintUid == null ? default(StringOffset) : builder.CreateString(_o.BlueprintUid);
    var _position = _o.Position == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Position);
    var _size = _o.Size == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Size);
    var _price_fb = _o.PriceFb == null ? default(Offset<FBConfig.Price>) : FBConfig.Price.Pack(builder, _o.PriceFb);
    var _instant_finish_price_fb = _o.InstantFinishPriceFb == null ? default(Offset<FBConfig.Price>) : FBConfig.Price.Pack(builder, _o.InstantFinishPriceFb);
    var _quest_uid = _o.QuestUid == null ? default(StringOffset) : builder.CreateString(_o.QuestUid);
    var _blueprint_objective_uid = _o.BlueprintObjectiveUid == null ? default(StringOffset) : builder.CreateString(_o.BlueprintObjectiveUid);
    var _popup_thumbnail = _o.PopupThumbnail == null ? default(StringOffset) : builder.CreateString(_o.PopupThumbnail);
    var _level_progression_requirements = default(VectorOffset);
    if (_o.LevelProgressionRequirements != null) {
      var __level_progression_requirements = _o.LevelProgressionRequirements.ToArray();
      _level_progression_requirements = CreateLevelProgressionRequirementsVector(builder, __level_progression_requirements);
    }
    var _reward_fb = default(VectorOffset);
    if (_o.RewardFb != null) {
      var __reward_fb = new Offset<FBConfig.DictStringInt>[_o.RewardFb.Count];
      for (var _j = 0; _j < __reward_fb.Length; ++_j) { __reward_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.RewardFb[_j]); }
      _reward_fb = CreateRewardFbVector(builder, __reward_fb);
    }
    var _curve = default(VectorOffset);
    if (_o.Curve != null) {
      var __curve = new Offset<FBConfig.Point>[_o.Curve.Count];
      for (var _j = 0; _j < __curve.Length; ++_j) { __curve[_j] = FBConfig.Point.Pack(builder, _o.Curve[_j]); }
      _curve = CreateCurveVector(builder, __curve);
    }
    return CreateMapPlaceableConfig(
      builder,
      _uid,
      _thumbnail,
      _location_uid,
      _level_uid,
      _unlock_at_level_uid,
      _blueprint_uid,
      _position,
      _size,
      _price_fb,
      _instant_finish_price_fb,
      _o.ConstructionTime,
      _quest_uid,
      _blueprint_objective_uid,
      _popup_thumbnail,
      _o.ProgressBarOffset,
      _o.Fake,
      _level_progression_requirements,
      _reward_fb,
      _curve);
  }
}

public class MapPlaceableConfigT
{
  public string Uid { get; set; }
  public string Thumbnail { get; set; }
  public string LocationUid { get; set; }
  public string LevelUid { get; set; }
  public string UnlockAtLevelUid { get; set; }
  public string BlueprintUid { get; set; }
  public FBConfig.PointT Position { get; set; }
  public FBConfig.PointT Size { get; set; }
  public FBConfig.PriceT PriceFb { get; set; }
  public FBConfig.PriceT InstantFinishPriceFb { get; set; }
  public float ConstructionTime { get; set; }
  public string QuestUid { get; set; }
  public string BlueprintObjectiveUid { get; set; }
  public string PopupThumbnail { get; set; }
  public float ProgressBarOffset { get; set; }
  public bool Fake { get; set; }
  public List<int> LevelProgressionRequirements { get; set; }
  public List<FBConfig.DictStringIntT> RewardFb { get; set; }
  public List<FBConfig.PointT> Curve { get; set; }

  public MapPlaceableConfigT() {
    this.Uid = null;
    this.Thumbnail = null;
    this.LocationUid = null;
    this.LevelUid = null;
    this.UnlockAtLevelUid = null;
    this.BlueprintUid = null;
    this.Position = null;
    this.Size = null;
    this.PriceFb = null;
    this.InstantFinishPriceFb = null;
    this.ConstructionTime = 0.0f;
    this.QuestUid = null;
    this.BlueprintObjectiveUid = null;
    this.PopupThumbnail = null;
    this.ProgressBarOffset = 0.0f;
    this.Fake = false;
    this.LevelProgressionRequirements = null;
    this.RewardFb = null;
    this.Curve = null;
  }
}

public struct MapPlaceableConfigDict : IFlatbufferConfigDict<MapPlaceableConfig, MapPlaceableConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static MapPlaceableConfigDict GetRootAsMapPlaceableConfigDict(ByteBuffer _bb) { return GetRootAsMapPlaceableConfigDict(_bb, new MapPlaceableConfigDict()); }
  public static MapPlaceableConfigDict GetRootAsMapPlaceableConfigDict(ByteBuffer _bb, MapPlaceableConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public MapPlaceableConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.MapPlaceableConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.MapPlaceableConfig?)(new FBConfig.MapPlaceableConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.MapPlaceableConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.MapPlaceableConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.MapPlaceableConfigDict> CreateMapPlaceableConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    MapPlaceableConfigDict.AddValues(builder, valuesOffset);
    return MapPlaceableConfigDict.EndMapPlaceableConfigDict(builder);
  }

  public static void StartMapPlaceableConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.MapPlaceableConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.MapPlaceableConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.MapPlaceableConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.MapPlaceableConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.MapPlaceableConfigDict> EndMapPlaceableConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.MapPlaceableConfigDict>(o);
  }
  public static void FinishMapPlaceableConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.MapPlaceableConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedMapPlaceableConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.MapPlaceableConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public MapPlaceableConfigDictT UnPack() {
    var _o = new MapPlaceableConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(MapPlaceableConfigDictT _o) {
    _o.Values = new List<FBConfig.MapPlaceableConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.MapPlaceableConfigDict> Pack(FlatBufferBuilder builder, MapPlaceableConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.MapPlaceableConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.MapPlaceableConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.MapPlaceableConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateMapPlaceableConfigDict(
      builder,
      _values);
  }
}

public class MapPlaceableConfigDictT
{
  public List<FBConfig.MapPlaceableConfigT> Values { get; set; }

  public MapPlaceableConfigDictT() {
    this.Values = null;
  }
  public static MapPlaceableConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return MapPlaceableConfigDict.GetRootAsMapPlaceableConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    MapPlaceableConfigDict.FinishMapPlaceableConfigDictBuffer(fbb, MapPlaceableConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
