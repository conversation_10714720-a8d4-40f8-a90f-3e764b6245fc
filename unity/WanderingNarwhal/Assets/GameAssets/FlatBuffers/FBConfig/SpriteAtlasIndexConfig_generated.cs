// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SpriteAtlasIndexConfig : IFlatbufferConfig<SpriteAtlasIndexConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SpriteAtlasIndexConfig GetRootAsSpriteAtlasIndexConfig(ByteBuffer _bb) { return GetRootAsSpriteAtlasIndexConfig(_bb, new SpriteAtlasIndexConfig()); }
  public static SpriteAtlasIndexConfig GetRootAsSpriteAtlasIndexConfig(ByteBuffer _bb, SpriteAtlasIndexConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SpriteAtlasIndexConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Sprites(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int SpritesLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.SpriteAtlasIndexConfig> CreateSpriteAtlasIndexConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset spritesOffset = default(VectorOffset)) {
    builder.StartTable(2);
    SpriteAtlasIndexConfig.AddSprites(builder, spritesOffset);
    SpriteAtlasIndexConfig.AddUid(builder, uidOffset);
    return SpriteAtlasIndexConfig.EndSpriteAtlasIndexConfig(builder);
  }

  public static void StartSpriteAtlasIndexConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSprites(FlatBufferBuilder builder, VectorOffset spritesOffset) { builder.AddOffset(1, spritesOffset.Value, 0); }
  public static VectorOffset CreateSpritesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSpritesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSpritesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSpritesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSpritesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SpriteAtlasIndexConfig> EndSpriteAtlasIndexConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SpriteAtlasIndexConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSpriteAtlasIndexConfig(FlatBufferBuilder builder, Offset<SpriteAtlasIndexConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SpriteAtlasIndexConfig> o1, Offset<SpriteAtlasIndexConfig> o2) =>
        new SpriteAtlasIndexConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SpriteAtlasIndexConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SpriteAtlasIndexConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SpriteAtlasIndexConfig obj_ = new SpriteAtlasIndexConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SpriteAtlasIndexConfigT UnPack() {
    var _o = new SpriteAtlasIndexConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SpriteAtlasIndexConfigT _o) {
    _o.Uid = this.Uid;
    _o.Sprites = new List<string>();
    for (var _j = 0; _j < this.SpritesLength; ++_j) {_o.Sprites.Add(this.Sprites(_j));}
  }
  public static Offset<FBConfig.SpriteAtlasIndexConfig> Pack(FlatBufferBuilder builder, SpriteAtlasIndexConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SpriteAtlasIndexConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _sprites = default(VectorOffset);
    if (_o.Sprites != null) {
      var __sprites = new StringOffset[_o.Sprites.Count];
      for (var _j = 0; _j < __sprites.Length; ++_j) { __sprites[_j] = builder.CreateString(_o.Sprites[_j]); }
      _sprites = CreateSpritesVector(builder, __sprites);
    }
    return CreateSpriteAtlasIndexConfig(
      builder,
      _uid,
      _sprites);
  }
}

public class SpriteAtlasIndexConfigT
{
  public string Uid { get; set; }
  public List<string> Sprites { get; set; }

  public SpriteAtlasIndexConfigT() {
    this.Uid = null;
    this.Sprites = null;
  }
}

public struct SpriteAtlasIndexConfigDict : IFlatbufferConfigDict<SpriteAtlasIndexConfig, SpriteAtlasIndexConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SpriteAtlasIndexConfigDict GetRootAsSpriteAtlasIndexConfigDict(ByteBuffer _bb) { return GetRootAsSpriteAtlasIndexConfigDict(_bb, new SpriteAtlasIndexConfigDict()); }
  public static SpriteAtlasIndexConfigDict GetRootAsSpriteAtlasIndexConfigDict(ByteBuffer _bb, SpriteAtlasIndexConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SpriteAtlasIndexConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SpriteAtlasIndexConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SpriteAtlasIndexConfig?)(new FBConfig.SpriteAtlasIndexConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SpriteAtlasIndexConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SpriteAtlasIndexConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SpriteAtlasIndexConfigDict> CreateSpriteAtlasIndexConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SpriteAtlasIndexConfigDict.AddValues(builder, valuesOffset);
    return SpriteAtlasIndexConfigDict.EndSpriteAtlasIndexConfigDict(builder);
  }

  public static void StartSpriteAtlasIndexConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SpriteAtlasIndexConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SpriteAtlasIndexConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SpriteAtlasIndexConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SpriteAtlasIndexConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SpriteAtlasIndexConfigDict> EndSpriteAtlasIndexConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SpriteAtlasIndexConfigDict>(o);
  }
  public static void FinishSpriteAtlasIndexConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SpriteAtlasIndexConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSpriteAtlasIndexConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SpriteAtlasIndexConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SpriteAtlasIndexConfigDictT UnPack() {
    var _o = new SpriteAtlasIndexConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SpriteAtlasIndexConfigDictT _o) {
    _o.Values = new List<FBConfig.SpriteAtlasIndexConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SpriteAtlasIndexConfigDict> Pack(FlatBufferBuilder builder, SpriteAtlasIndexConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SpriteAtlasIndexConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SpriteAtlasIndexConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SpriteAtlasIndexConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSpriteAtlasIndexConfigDict(
      builder,
      _values);
  }
}

public class SpriteAtlasIndexConfigDictT
{
  public List<FBConfig.SpriteAtlasIndexConfigT> Values { get; set; }

  public SpriteAtlasIndexConfigDictT() {
    this.Values = null;
  }
  public static SpriteAtlasIndexConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SpriteAtlasIndexConfigDict.GetRootAsSpriteAtlasIndexConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SpriteAtlasIndexConfigDict.FinishSpriteAtlasIndexConfigDictBuffer(fbb, SpriteAtlasIndexConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
