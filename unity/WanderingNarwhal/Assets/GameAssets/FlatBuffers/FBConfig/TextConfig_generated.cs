// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct TextConfig : IFlatbufferConfig<TextConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TextConfig GetRootAsTextConfig(ByteBuffer _bb) { return GetRootAsTextConfig(_bb, new TextConfig()); }
  public static TextConfig GetRootAsTextConfig(ByteBuffer _bb, TextConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TextConfig __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Text { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTextBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTextBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTextArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.TextConfig> CreateTextConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset textOffset = default(StringOffset)) {
    builder.StartTable(2);
    TextConfig.AddText(builder, textOffset);
    TextConfig.AddUid(builder, uidOffset);
    return TextConfig.EndTextConfig(builder);
  }

  public static void StartTextConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddText(FlatBufferBuilder builder, StringOffset textOffset) { builder.AddOffset(1, textOffset.Value, 0); }
  public static Offset<FBConfig.TextConfig> EndTextConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.TextConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfTextConfig(FlatBufferBuilder builder, Offset<TextConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<TextConfig> o1, Offset<TextConfig> o2) =>
        new TextConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new TextConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static TextConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    TextConfig obj_ = new TextConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public TextConfigT UnPack() {
    var _o = new TextConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TextConfigT _o) {
    _o.Uid = this.Uid;
    _o.Text = this.Text;
  }
  public static Offset<FBConfig.TextConfig> Pack(FlatBufferBuilder builder, TextConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.TextConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _text = _o.Text == null ? default(StringOffset) : builder.CreateString(_o.Text);
    return CreateTextConfig(
      builder,
      _uid,
      _text);
  }
}

public class TextConfigT
{
  public string Uid { get; set; }
  public string Text { get; set; }

  public TextConfigT() {
    this.Uid = null;
    this.Text = null;
  }
}

public struct TextConfigDict : IFlatbufferConfigDict<TextConfig, TextConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TextConfigDict GetRootAsTextConfigDict(ByteBuffer _bb) { return GetRootAsTextConfigDict(_bb, new TextConfigDict()); }
  public static TextConfigDict GetRootAsTextConfigDict(ByteBuffer _bb, TextConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TextConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.TextConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.TextConfig?)(new FBConfig.TextConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.TextConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.TextConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.TextConfigDict> CreateTextConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    TextConfigDict.AddValues(builder, valuesOffset);
    return TextConfigDict.EndTextConfigDict(builder);
  }

  public static void StartTextConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.TextConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TextConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TextConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TextConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.TextConfigDict> EndTextConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TextConfigDict>(o);
  }
  public static void FinishTextConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TextConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedTextConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TextConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public TextConfigDictT UnPack() {
    var _o = new TextConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TextConfigDictT _o) {
    _o.Values = new List<FBConfig.TextConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.TextConfigDict> Pack(FlatBufferBuilder builder, TextConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.TextConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.TextConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.TextConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateTextConfigDict(
      builder,
      _values);
  }
}

public class TextConfigDictT
{
  public List<FBConfig.TextConfigT> Values { get; set; }

  public TextConfigDictT() {
    this.Values = null;
  }
  public static TextConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return TextConfigDict.GetRootAsTextConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    TextConfigDict.FinishTextConfigDictBuffer(fbb, TextConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
