// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct TransitionTipsConfig : IFlatbufferConfig<TransitionTipsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TransitionTipsConfig GetRootAsTransitionTipsConfig(ByteBuffer _bb) { return GetRootAsTransitionTipsConfig(_bb, new TransitionTipsConfig()); }
  public static TransitionTipsConfig GetRootAsTransitionTipsConfig(ByteBuffer _bb, TransitionTipsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TransitionTipsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string TipLocalizedTextUid { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTipLocalizedTextUidBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTipLocalizedTextUidBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTipLocalizedTextUidArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.TransitionTipsConfig> CreateTransitionTipsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset tip_localized_text_uidOffset = default(StringOffset)) {
    builder.StartTable(2);
    TransitionTipsConfig.AddTipLocalizedTextUid(builder, tip_localized_text_uidOffset);
    TransitionTipsConfig.AddUid(builder, uidOffset);
    return TransitionTipsConfig.EndTransitionTipsConfig(builder);
  }

  public static void StartTransitionTipsConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTipLocalizedTextUid(FlatBufferBuilder builder, StringOffset tipLocalizedTextUidOffset) { builder.AddOffset(1, tipLocalizedTextUidOffset.Value, 0); }
  public static Offset<FBConfig.TransitionTipsConfig> EndTransitionTipsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.TransitionTipsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfTransitionTipsConfig(FlatBufferBuilder builder, Offset<TransitionTipsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<TransitionTipsConfig> o1, Offset<TransitionTipsConfig> o2) =>
        new TransitionTipsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new TransitionTipsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static TransitionTipsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    TransitionTipsConfig obj_ = new TransitionTipsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public TransitionTipsConfigT UnPack() {
    var _o = new TransitionTipsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TransitionTipsConfigT _o) {
    _o.Uid = this.Uid;
    _o.TipLocalizedTextUid = this.TipLocalizedTextUid;
  }
  public static Offset<FBConfig.TransitionTipsConfig> Pack(FlatBufferBuilder builder, TransitionTipsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.TransitionTipsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _tip_localized_text_uid = _o.TipLocalizedTextUid == null ? default(StringOffset) : builder.CreateString(_o.TipLocalizedTextUid);
    return CreateTransitionTipsConfig(
      builder,
      _uid,
      _tip_localized_text_uid);
  }
}

public class TransitionTipsConfigT
{
  public string Uid { get; set; }
  public string TipLocalizedTextUid { get; set; }

  public TransitionTipsConfigT() {
    this.Uid = null;
    this.TipLocalizedTextUid = null;
  }
}

public struct TransitionTipsConfigDict : IFlatbufferConfigDict<TransitionTipsConfig, TransitionTipsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TransitionTipsConfigDict GetRootAsTransitionTipsConfigDict(ByteBuffer _bb) { return GetRootAsTransitionTipsConfigDict(_bb, new TransitionTipsConfigDict()); }
  public static TransitionTipsConfigDict GetRootAsTransitionTipsConfigDict(ByteBuffer _bb, TransitionTipsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TransitionTipsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.TransitionTipsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.TransitionTipsConfig?)(new FBConfig.TransitionTipsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.TransitionTipsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.TransitionTipsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.TransitionTipsConfigDict> CreateTransitionTipsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    TransitionTipsConfigDict.AddValues(builder, valuesOffset);
    return TransitionTipsConfigDict.EndTransitionTipsConfigDict(builder);
  }

  public static void StartTransitionTipsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.TransitionTipsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TransitionTipsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TransitionTipsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TransitionTipsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.TransitionTipsConfigDict> EndTransitionTipsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TransitionTipsConfigDict>(o);
  }
  public static void FinishTransitionTipsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TransitionTipsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedTransitionTipsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TransitionTipsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public TransitionTipsConfigDictT UnPack() {
    var _o = new TransitionTipsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TransitionTipsConfigDictT _o) {
    _o.Values = new List<FBConfig.TransitionTipsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.TransitionTipsConfigDict> Pack(FlatBufferBuilder builder, TransitionTipsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.TransitionTipsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.TransitionTipsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.TransitionTipsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateTransitionTipsConfigDict(
      builder,
      _values);
  }
}

public class TransitionTipsConfigDictT
{
  public List<FBConfig.TransitionTipsConfigT> Values { get; set; }

  public TransitionTipsConfigDictT() {
    this.Values = null;
  }
  public static TransitionTipsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return TransitionTipsConfigDict.GetRootAsTransitionTipsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    TransitionTipsConfigDict.FinishTransitionTipsConfigDictBuffer(fbb, TransitionTipsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
