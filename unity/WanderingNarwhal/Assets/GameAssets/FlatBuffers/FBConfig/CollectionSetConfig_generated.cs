// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct CollectionSetConfig : IFlatbufferConfig<CollectionSetConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CollectionSetConfig GetRootAsCollectionSetConfig(ByteBuffer _bb) { return GetRootAsCollectionSetConfig(_bb, new CollectionSetConfig()); }
  public static CollectionSetConfig GetRootAsCollectionSetConfig(ByteBuffer _bb, CollectionSetConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CollectionSetConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public FBConfig.DictStringInt? Reward(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int Order { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }
  public string UnlockLevel { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUnlockLevelBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetUnlockLevelBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetUnlockLevelArray() { return __p.__vector_as_array<byte>(12); }
  public string SpriteName { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSpriteNameBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetSpriteNameBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetSpriteNameArray() { return __p.__vector_as_array<byte>(14); }
  public string AnalyticsName { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAnalyticsNameBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetAnalyticsNameBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetAnalyticsNameArray() { return __p.__vector_as_array<byte>(16); }

  public static Offset<FBConfig.CollectionSetConfig> CreateCollectionSetConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      VectorOffset rewardOffset = default(VectorOffset),
      int order = 0,
      StringOffset unlock_levelOffset = default(StringOffset),
      StringOffset sprite_nameOffset = default(StringOffset),
      StringOffset analytics_nameOffset = default(StringOffset)) {
    builder.StartTable(7);
    CollectionSetConfig.AddAnalyticsName(builder, analytics_nameOffset);
    CollectionSetConfig.AddSpriteName(builder, sprite_nameOffset);
    CollectionSetConfig.AddUnlockLevel(builder, unlock_levelOffset);
    CollectionSetConfig.AddOrder(builder, order);
    CollectionSetConfig.AddReward(builder, rewardOffset);
    CollectionSetConfig.AddName(builder, nameOffset);
    CollectionSetConfig.AddUid(builder, uidOffset);
    return CollectionSetConfig.EndCollectionSetConfig(builder);
  }

  public static void StartCollectionSetConfig(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddReward(FlatBufferBuilder builder, VectorOffset rewardOffset) { builder.AddOffset(2, rewardOffset.Value, 0); }
  public static VectorOffset CreateRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(3, order, 0); }
  public static void AddUnlockLevel(FlatBufferBuilder builder, StringOffset unlockLevelOffset) { builder.AddOffset(4, unlockLevelOffset.Value, 0); }
  public static void AddSpriteName(FlatBufferBuilder builder, StringOffset spriteNameOffset) { builder.AddOffset(5, spriteNameOffset.Value, 0); }
  public static void AddAnalyticsName(FlatBufferBuilder builder, StringOffset analyticsNameOffset) { builder.AddOffset(6, analyticsNameOffset.Value, 0); }
  public static Offset<FBConfig.CollectionSetConfig> EndCollectionSetConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.CollectionSetConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfCollectionSetConfig(FlatBufferBuilder builder, Offset<CollectionSetConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<CollectionSetConfig> o1, Offset<CollectionSetConfig> o2) =>
        new CollectionSetConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new CollectionSetConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static CollectionSetConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    CollectionSetConfig obj_ = new CollectionSetConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public CollectionSetConfigT UnPack() {
    var _o = new CollectionSetConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CollectionSetConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Reward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardLength; ++_j) {_o.Reward.Add(this.Reward(_j).HasValue ? this.Reward(_j).Value.UnPack() : null);}
    _o.Order = this.Order;
    _o.UnlockLevel = this.UnlockLevel;
    _o.SpriteName = this.SpriteName;
    _o.AnalyticsName = this.AnalyticsName;
  }
  public static Offset<FBConfig.CollectionSetConfig> Pack(FlatBufferBuilder builder, CollectionSetConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.CollectionSetConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _reward = default(VectorOffset);
    if (_o.Reward != null) {
      var __reward = new Offset<FBConfig.DictStringInt>[_o.Reward.Count];
      for (var _j = 0; _j < __reward.Length; ++_j) { __reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.Reward[_j]); }
      _reward = CreateRewardVector(builder, __reward);
    }
    var _unlock_level = _o.UnlockLevel == null ? default(StringOffset) : builder.CreateString(_o.UnlockLevel);
    var _sprite_name = _o.SpriteName == null ? default(StringOffset) : builder.CreateString(_o.SpriteName);
    var _analytics_name = _o.AnalyticsName == null ? default(StringOffset) : builder.CreateString(_o.AnalyticsName);
    return CreateCollectionSetConfig(
      builder,
      _uid,
      _name,
      _reward,
      _o.Order,
      _unlock_level,
      _sprite_name,
      _analytics_name);
  }
}

public class CollectionSetConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public List<FBConfig.DictStringIntT> Reward { get; set; }
  public int Order { get; set; }
  public string UnlockLevel { get; set; }
  public string SpriteName { get; set; }
  public string AnalyticsName { get; set; }

  public CollectionSetConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Reward = null;
    this.Order = 0;
    this.UnlockLevel = null;
    this.SpriteName = null;
    this.AnalyticsName = null;
  }
}

public struct CollectionSetConfigDict : IFlatbufferConfigDict<CollectionSetConfig, CollectionSetConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CollectionSetConfigDict GetRootAsCollectionSetConfigDict(ByteBuffer _bb) { return GetRootAsCollectionSetConfigDict(_bb, new CollectionSetConfigDict()); }
  public static CollectionSetConfigDict GetRootAsCollectionSetConfigDict(ByteBuffer _bb, CollectionSetConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CollectionSetConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.CollectionSetConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.CollectionSetConfig?)(new FBConfig.CollectionSetConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.CollectionSetConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.CollectionSetConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.CollectionSetConfigDict> CreateCollectionSetConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    CollectionSetConfigDict.AddValues(builder, valuesOffset);
    return CollectionSetConfigDict.EndCollectionSetConfigDict(builder);
  }

  public static void StartCollectionSetConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.CollectionSetConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.CollectionSetConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.CollectionSetConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.CollectionSetConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.CollectionSetConfigDict> EndCollectionSetConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CollectionSetConfigDict>(o);
  }
  public static void FinishCollectionSetConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CollectionSetConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedCollectionSetConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CollectionSetConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public CollectionSetConfigDictT UnPack() {
    var _o = new CollectionSetConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CollectionSetConfigDictT _o) {
    _o.Values = new List<FBConfig.CollectionSetConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.CollectionSetConfigDict> Pack(FlatBufferBuilder builder, CollectionSetConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.CollectionSetConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.CollectionSetConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.CollectionSetConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateCollectionSetConfigDict(
      builder,
      _values);
  }
}

public class CollectionSetConfigDictT
{
  public List<FBConfig.CollectionSetConfigT> Values { get; set; }

  public CollectionSetConfigDictT() {
    this.Values = null;
  }
  public static CollectionSetConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return CollectionSetConfigDict.GetRootAsCollectionSetConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    CollectionSetConfigDict.FinishCollectionSetConfigDictBuffer(fbb, CollectionSetConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
