// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct CurrencyPair : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CurrencyPair GetRootAsCurrencyPair(ByteBuffer _bb) { return GetRootAsCurrencyPair(_bb, new CurrencyPair()); }
  public static CurrencyPair GetRootAsCurrencyPair(ByteBuffer _bb, CurrencyPair obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CurrencyPair __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public long Count { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetLong(o + __p.bb_pos) : (long)0; } }
  public bool MutateCount(long count) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutLong(o + __p.bb_pos, count); return true; } else { return false; } }

  public static Offset<FBConfig.CurrencyPair> CreateCurrencyPair(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      long count = 0) {
    builder.StartTable(2);
    CurrencyPair.AddCount(builder, count);
    CurrencyPair.AddUid(builder, uidOffset);
    return CurrencyPair.EndCurrencyPair(builder);
  }

  public static void StartCurrencyPair(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCount(FlatBufferBuilder builder, long count) { builder.AddLong(1, count, 0); }
  public static Offset<FBConfig.CurrencyPair> EndCurrencyPair(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CurrencyPair>(o);
  }
  public CurrencyPairT UnPack() {
    var _o = new CurrencyPairT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CurrencyPairT _o) {
    _o.Uid = this.Uid;
    _o.Count = this.Count;
  }
  public static Offset<FBConfig.CurrencyPair> Pack(FlatBufferBuilder builder, CurrencyPairT _o) {
    if (_o == null) return default(Offset<FBConfig.CurrencyPair>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateCurrencyPair(
      builder,
      _uid,
      _o.Count);
  }
}

public class CurrencyPairT
{
  public string Uid { get; set; }
  public long Count { get; set; }

  public CurrencyPairT() {
    this.Uid = null;
    this.Count = 0;
  }
}

public struct IAPRewardOrdered : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPRewardOrdered GetRootAsIAPRewardOrdered(ByteBuffer _bb) { return GetRootAsIAPRewardOrdered(_bb, new IAPRewardOrdered()); }
  public static IAPRewardOrdered GetRootAsIAPRewardOrdered(ByteBuffer _bb, IAPRewardOrdered obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPRewardOrdered __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.CurrencyPair? Reward(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.CurrencyPair?)(new FBConfig.CurrencyPair()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string Expression { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetExpressionBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetExpressionBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetExpressionArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.IAPRewardOrdered> CreateIAPRewardOrdered(FlatBufferBuilder builder,
      VectorOffset rewardOffset = default(VectorOffset),
      StringOffset expressionOffset = default(StringOffset)) {
    builder.StartTable(2);
    IAPRewardOrdered.AddExpression(builder, expressionOffset);
    IAPRewardOrdered.AddReward(builder, rewardOffset);
    return IAPRewardOrdered.EndIAPRewardOrdered(builder);
  }

  public static void StartIAPRewardOrdered(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddReward(FlatBufferBuilder builder, VectorOffset rewardOffset) { builder.AddOffset(0, rewardOffset.Value, 0); }
  public static VectorOffset CreateRewardVector(FlatBufferBuilder builder, Offset<FBConfig.CurrencyPair>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.CurrencyPair>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.CurrencyPair>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.CurrencyPair>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddExpression(FlatBufferBuilder builder, StringOffset expressionOffset) { builder.AddOffset(1, expressionOffset.Value, 0); }
  public static Offset<FBConfig.IAPRewardOrdered> EndIAPRewardOrdered(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPRewardOrdered>(o);
  }
  public IAPRewardOrderedT UnPack() {
    var _o = new IAPRewardOrderedT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPRewardOrderedT _o) {
    _o.Reward = new List<FBConfig.CurrencyPairT>();
    for (var _j = 0; _j < this.RewardLength; ++_j) {_o.Reward.Add(this.Reward(_j).HasValue ? this.Reward(_j).Value.UnPack() : null);}
    _o.Expression = this.Expression;
  }
  public static Offset<FBConfig.IAPRewardOrdered> Pack(FlatBufferBuilder builder, IAPRewardOrderedT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPRewardOrdered>);
    var _reward = default(VectorOffset);
    if (_o.Reward != null) {
      var __reward = new Offset<FBConfig.CurrencyPair>[_o.Reward.Count];
      for (var _j = 0; _j < __reward.Length; ++_j) { __reward[_j] = FBConfig.CurrencyPair.Pack(builder, _o.Reward[_j]); }
      _reward = CreateRewardVector(builder, __reward);
    }
    var _expression = _o.Expression == null ? default(StringOffset) : builder.CreateString(_o.Expression);
    return CreateIAPRewardOrdered(
      builder,
      _reward,
      _expression);
  }
}

public class IAPRewardOrderedT
{
  public List<FBConfig.CurrencyPairT> Reward { get; set; }
  public string Expression { get; set; }

  public IAPRewardOrderedT() {
    this.Reward = null;
    this.Expression = null;
  }
}

public struct IAPStoreMarketItemConfig : IFlatbufferConfig<IAPStoreMarketItemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreMarketItemConfig GetRootAsIAPStoreMarketItemConfig(ByteBuffer _bb) { return GetRootAsIAPStoreMarketItemConfig(_bb, new IAPStoreMarketItemConfig()); }
  public static IAPStoreMarketItemConfig GetRootAsIAPStoreMarketItemConfig(ByteBuffer _bb, IAPStoreMarketItemConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreMarketItemConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string Description { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescriptionBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetDescriptionBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetDescriptionArray() { return __p.__vector_as_array<byte>(8); }
  public int ProductType { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateProductType(int product_type) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, product_type); return true; } else { return false; } }
  public string Icon { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetIconBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetIconArray() { return __p.__vector_as_array<byte>(12); }
  public int Amount { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateAmount(int amount) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, amount); return true; } else { return false; } }
  public bool Enabled { get { int o = __p.__offset(16); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateEnabled(bool enabled) { int o = __p.__offset(16); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(enabled ? 1 : 0)); return true; } else { return false; } }
  public int Order { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }
  public string AppleId { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAppleIdBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetAppleIdBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetAppleIdArray() { return __p.__vector_as_array<byte>(20); }
  public string GoogleId { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGoogleIdBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetGoogleIdBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetGoogleIdArray() { return __p.__vector_as_array<byte>(22); }
  public string AmazonId { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAmazonIdBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetAmazonIdBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetAmazonIdArray() { return __p.__vector_as_array<byte>(24); }
  public string WindowsId { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetWindowsIdBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetWindowsIdBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetWindowsIdArray() { return __p.__vector_as_array<byte>(26); }
  public double PriceUsd { get { int o = __p.__offset(28); return o != 0 ? __p.bb.GetDouble(o + __p.bb_pos) : (double)0.0; } }
  public bool MutatePriceUsd(double price_usd) { int o = __p.__offset(28); if (o != 0) { __p.bb.PutDouble(o + __p.bb_pos, price_usd); return true; } else { return false; } }
  public string DiscountOfId { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDiscountOfIdBytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetDiscountOfIdBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetDiscountOfIdArray() { return __p.__vector_as_array<byte>(30); }
  public string SaleBadgeText { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSaleBadgeTextBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetSaleBadgeTextBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetSaleBadgeTextArray() { return __p.__vector_as_array<byte>(32); }
  public string Category { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(34); }
  public FBConfig.IAPRewardOrdered? RewardOrdered { get { int o = __p.__offset(36); return o != 0 ? (FBConfig.IAPRewardOrdered?)(new FBConfig.IAPRewardOrdered()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public bool Visible { get { int o = __p.__offset(38); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateVisible(bool visible) { int o = __p.__offset(38); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(visible ? 1 : 0)); return true; } else { return false; } }
  public string DealBadgeText { get { int o = __p.__offset(40); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDealBadgeTextBytes() { return __p.__vector_as_span<byte>(40, 1); }
#else
  public ArraySegment<byte>? GetDealBadgeTextBytes() { return __p.__vector_as_arraysegment(40); }
#endif
  public byte[] GetDealBadgeTextArray() { return __p.__vector_as_array<byte>(40); }
  public int Type { get { int o = __p.__offset(42); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateType(int type) { int o = __p.__offset(42); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, type); return true; } else { return false; } }
  public string IconPrefab { get { int o = __p.__offset(44); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconPrefabBytes() { return __p.__vector_as_span<byte>(44, 1); }
#else
  public ArraySegment<byte>? GetIconPrefabBytes() { return __p.__vector_as_arraysegment(44); }
#endif
  public byte[] GetIconPrefabArray() { return __p.__vector_as_array<byte>(44); }
  public string DescriptionSecond { get { int o = __p.__offset(46); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescriptionSecondBytes() { return __p.__vector_as_span<byte>(46, 1); }
#else
  public ArraySegment<byte>? GetDescriptionSecondBytes() { return __p.__vector_as_arraysegment(46); }
#endif
  public byte[] GetDescriptionSecondArray() { return __p.__vector_as_array<byte>(46); }
  public string DescriptionThird { get { int o = __p.__offset(48); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescriptionThirdBytes() { return __p.__vector_as_span<byte>(48, 1); }
#else
  public ArraySegment<byte>? GetDescriptionThirdBytes() { return __p.__vector_as_arraysegment(48); }
#endif
  public byte[] GetDescriptionThirdArray() { return __p.__vector_as_array<byte>(48); }
  public string SocialSharedReward { get { int o = __p.__offset(50); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSocialSharedRewardBytes() { return __p.__vector_as_span<byte>(50, 1); }
#else
  public ArraySegment<byte>? GetSocialSharedRewardBytes() { return __p.__vector_as_arraysegment(50); }
#endif
  public byte[] GetSocialSharedRewardArray() { return __p.__vector_as_array<byte>(50); }
  public double DiscountPriceMultiplier { get { int o = __p.__offset(52); return o != 0 ? __p.bb.GetDouble(o + __p.bb_pos) : (double)0.0; } }
  public bool MutateDiscountPriceMultiplier(double discount_price_multiplier) { int o = __p.__offset(52); if (o != 0) { __p.bb.PutDouble(o + __p.bb_pos, discount_price_multiplier); return true; } else { return false; } }

  public static Offset<FBConfig.IAPStoreMarketItemConfig> CreateIAPStoreMarketItemConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset descriptionOffset = default(StringOffset),
      int product_type = 0,
      StringOffset iconOffset = default(StringOffset),
      int amount = 0,
      bool enabled = false,
      int order = 0,
      StringOffset apple_idOffset = default(StringOffset),
      StringOffset google_idOffset = default(StringOffset),
      StringOffset amazon_idOffset = default(StringOffset),
      StringOffset windows_idOffset = default(StringOffset),
      double price_usd = 0.0,
      StringOffset discount_of_idOffset = default(StringOffset),
      StringOffset sale_badge_textOffset = default(StringOffset),
      StringOffset categoryOffset = default(StringOffset),
      Offset<FBConfig.IAPRewardOrdered> reward_orderedOffset = default(Offset<FBConfig.IAPRewardOrdered>),
      bool visible = false,
      StringOffset deal_badge_textOffset = default(StringOffset),
      int type = 0,
      StringOffset icon_prefabOffset = default(StringOffset),
      StringOffset description_secondOffset = default(StringOffset),
      StringOffset description_thirdOffset = default(StringOffset),
      StringOffset social_shared_rewardOffset = default(StringOffset),
      double discount_price_multiplier = 0.0) {
    builder.StartTable(25);
    IAPStoreMarketItemConfig.AddDiscountPriceMultiplier(builder, discount_price_multiplier);
    IAPStoreMarketItemConfig.AddPriceUsd(builder, price_usd);
    IAPStoreMarketItemConfig.AddSocialSharedReward(builder, social_shared_rewardOffset);
    IAPStoreMarketItemConfig.AddDescriptionThird(builder, description_thirdOffset);
    IAPStoreMarketItemConfig.AddDescriptionSecond(builder, description_secondOffset);
    IAPStoreMarketItemConfig.AddIconPrefab(builder, icon_prefabOffset);
    IAPStoreMarketItemConfig.AddType(builder, type);
    IAPStoreMarketItemConfig.AddDealBadgeText(builder, deal_badge_textOffset);
    IAPStoreMarketItemConfig.AddRewardOrdered(builder, reward_orderedOffset);
    IAPStoreMarketItemConfig.AddCategory(builder, categoryOffset);
    IAPStoreMarketItemConfig.AddSaleBadgeText(builder, sale_badge_textOffset);
    IAPStoreMarketItemConfig.AddDiscountOfId(builder, discount_of_idOffset);
    IAPStoreMarketItemConfig.AddWindowsId(builder, windows_idOffset);
    IAPStoreMarketItemConfig.AddAmazonId(builder, amazon_idOffset);
    IAPStoreMarketItemConfig.AddGoogleId(builder, google_idOffset);
    IAPStoreMarketItemConfig.AddAppleId(builder, apple_idOffset);
    IAPStoreMarketItemConfig.AddOrder(builder, order);
    IAPStoreMarketItemConfig.AddAmount(builder, amount);
    IAPStoreMarketItemConfig.AddIcon(builder, iconOffset);
    IAPStoreMarketItemConfig.AddProductType(builder, product_type);
    IAPStoreMarketItemConfig.AddDescription(builder, descriptionOffset);
    IAPStoreMarketItemConfig.AddName(builder, nameOffset);
    IAPStoreMarketItemConfig.AddUid(builder, uidOffset);
    IAPStoreMarketItemConfig.AddVisible(builder, visible);
    IAPStoreMarketItemConfig.AddEnabled(builder, enabled);
    return IAPStoreMarketItemConfig.EndIAPStoreMarketItemConfig(builder);
  }

  public static void StartIAPStoreMarketItemConfig(FlatBufferBuilder builder) { builder.StartTable(25); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddDescription(FlatBufferBuilder builder, StringOffset descriptionOffset) { builder.AddOffset(2, descriptionOffset.Value, 0); }
  public static void AddProductType(FlatBufferBuilder builder, int productType) { builder.AddInt(3, productType, 0); }
  public static void AddIcon(FlatBufferBuilder builder, StringOffset iconOffset) { builder.AddOffset(4, iconOffset.Value, 0); }
  public static void AddAmount(FlatBufferBuilder builder, int amount) { builder.AddInt(5, amount, 0); }
  public static void AddEnabled(FlatBufferBuilder builder, bool enabled) { builder.AddBool(6, enabled, false); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(7, order, 0); }
  public static void AddAppleId(FlatBufferBuilder builder, StringOffset appleIdOffset) { builder.AddOffset(8, appleIdOffset.Value, 0); }
  public static void AddGoogleId(FlatBufferBuilder builder, StringOffset googleIdOffset) { builder.AddOffset(9, googleIdOffset.Value, 0); }
  public static void AddAmazonId(FlatBufferBuilder builder, StringOffset amazonIdOffset) { builder.AddOffset(10, amazonIdOffset.Value, 0); }
  public static void AddWindowsId(FlatBufferBuilder builder, StringOffset windowsIdOffset) { builder.AddOffset(11, windowsIdOffset.Value, 0); }
  public static void AddPriceUsd(FlatBufferBuilder builder, double priceUsd) { builder.AddDouble(12, priceUsd, 0.0); }
  public static void AddDiscountOfId(FlatBufferBuilder builder, StringOffset discountOfIdOffset) { builder.AddOffset(13, discountOfIdOffset.Value, 0); }
  public static void AddSaleBadgeText(FlatBufferBuilder builder, StringOffset saleBadgeTextOffset) { builder.AddOffset(14, saleBadgeTextOffset.Value, 0); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(15, categoryOffset.Value, 0); }
  public static void AddRewardOrdered(FlatBufferBuilder builder, Offset<FBConfig.IAPRewardOrdered> rewardOrderedOffset) { builder.AddOffset(16, rewardOrderedOffset.Value, 0); }
  public static void AddVisible(FlatBufferBuilder builder, bool visible) { builder.AddBool(17, visible, false); }
  public static void AddDealBadgeText(FlatBufferBuilder builder, StringOffset dealBadgeTextOffset) { builder.AddOffset(18, dealBadgeTextOffset.Value, 0); }
  public static void AddType(FlatBufferBuilder builder, int type) { builder.AddInt(19, type, 0); }
  public static void AddIconPrefab(FlatBufferBuilder builder, StringOffset iconPrefabOffset) { builder.AddOffset(20, iconPrefabOffset.Value, 0); }
  public static void AddDescriptionSecond(FlatBufferBuilder builder, StringOffset descriptionSecondOffset) { builder.AddOffset(21, descriptionSecondOffset.Value, 0); }
  public static void AddDescriptionThird(FlatBufferBuilder builder, StringOffset descriptionThirdOffset) { builder.AddOffset(22, descriptionThirdOffset.Value, 0); }
  public static void AddSocialSharedReward(FlatBufferBuilder builder, StringOffset socialSharedRewardOffset) { builder.AddOffset(23, socialSharedRewardOffset.Value, 0); }
  public static void AddDiscountPriceMultiplier(FlatBufferBuilder builder, double discountPriceMultiplier) { builder.AddDouble(24, discountPriceMultiplier, 0.0); }
  public static Offset<FBConfig.IAPStoreMarketItemConfig> EndIAPStoreMarketItemConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.IAPStoreMarketItemConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfIAPStoreMarketItemConfig(FlatBufferBuilder builder, Offset<IAPStoreMarketItemConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<IAPStoreMarketItemConfig> o1, Offset<IAPStoreMarketItemConfig> o2) =>
        new IAPStoreMarketItemConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new IAPStoreMarketItemConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static IAPStoreMarketItemConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    IAPStoreMarketItemConfig obj_ = new IAPStoreMarketItemConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public IAPStoreMarketItemConfigT UnPack() {
    var _o = new IAPStoreMarketItemConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreMarketItemConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Description = this.Description;
    _o.ProductType = this.ProductType;
    _o.Icon = this.Icon;
    _o.Amount = this.Amount;
    _o.Enabled = this.Enabled;
    _o.Order = this.Order;
    _o.AppleId = this.AppleId;
    _o.GoogleId = this.GoogleId;
    _o.AmazonId = this.AmazonId;
    _o.WindowsId = this.WindowsId;
    _o.PriceUsd = this.PriceUsd;
    _o.DiscountOfId = this.DiscountOfId;
    _o.SaleBadgeText = this.SaleBadgeText;
    _o.Category = this.Category;
    _o.RewardOrdered = this.RewardOrdered.HasValue ? this.RewardOrdered.Value.UnPack() : null;
    _o.Visible = this.Visible;
    _o.DealBadgeText = this.DealBadgeText;
    _o.Type = this.Type;
    _o.IconPrefab = this.IconPrefab;
    _o.DescriptionSecond = this.DescriptionSecond;
    _o.DescriptionThird = this.DescriptionThird;
    _o.SocialSharedReward = this.SocialSharedReward;
    _o.DiscountPriceMultiplier = this.DiscountPriceMultiplier;
  }
  public static Offset<FBConfig.IAPStoreMarketItemConfig> Pack(FlatBufferBuilder builder, IAPStoreMarketItemConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreMarketItemConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _description = _o.Description == null ? default(StringOffset) : builder.CreateString(_o.Description);
    var _icon = _o.Icon == null ? default(StringOffset) : builder.CreateString(_o.Icon);
    var _apple_id = _o.AppleId == null ? default(StringOffset) : builder.CreateString(_o.AppleId);
    var _google_id = _o.GoogleId == null ? default(StringOffset) : builder.CreateString(_o.GoogleId);
    var _amazon_id = _o.AmazonId == null ? default(StringOffset) : builder.CreateString(_o.AmazonId);
    var _windows_id = _o.WindowsId == null ? default(StringOffset) : builder.CreateString(_o.WindowsId);
    var _discount_of_id = _o.DiscountOfId == null ? default(StringOffset) : builder.CreateString(_o.DiscountOfId);
    var _sale_badge_text = _o.SaleBadgeText == null ? default(StringOffset) : builder.CreateString(_o.SaleBadgeText);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    var _reward_ordered = _o.RewardOrdered == null ? default(Offset<FBConfig.IAPRewardOrdered>) : FBConfig.IAPRewardOrdered.Pack(builder, _o.RewardOrdered);
    var _deal_badge_text = _o.DealBadgeText == null ? default(StringOffset) : builder.CreateString(_o.DealBadgeText);
    var _icon_prefab = _o.IconPrefab == null ? default(StringOffset) : builder.CreateString(_o.IconPrefab);
    var _description_second = _o.DescriptionSecond == null ? default(StringOffset) : builder.CreateString(_o.DescriptionSecond);
    var _description_third = _o.DescriptionThird == null ? default(StringOffset) : builder.CreateString(_o.DescriptionThird);
    var _social_shared_reward = _o.SocialSharedReward == null ? default(StringOffset) : builder.CreateString(_o.SocialSharedReward);
    return CreateIAPStoreMarketItemConfig(
      builder,
      _uid,
      _name,
      _description,
      _o.ProductType,
      _icon,
      _o.Amount,
      _o.Enabled,
      _o.Order,
      _apple_id,
      _google_id,
      _amazon_id,
      _windows_id,
      _o.PriceUsd,
      _discount_of_id,
      _sale_badge_text,
      _category,
      _reward_ordered,
      _o.Visible,
      _deal_badge_text,
      _o.Type,
      _icon_prefab,
      _description_second,
      _description_third,
      _social_shared_reward,
      _o.DiscountPriceMultiplier);
  }
}

public class IAPStoreMarketItemConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string Description { get; set; }
  public int ProductType { get; set; }
  public string Icon { get; set; }
  public int Amount { get; set; }
  public bool Enabled { get; set; }
  public int Order { get; set; }
  public string AppleId { get; set; }
  public string GoogleId { get; set; }
  public string AmazonId { get; set; }
  public string WindowsId { get; set; }
  public double PriceUsd { get; set; }
  public string DiscountOfId { get; set; }
  public string SaleBadgeText { get; set; }
  public string Category { get; set; }
  public FBConfig.IAPRewardOrderedT RewardOrdered { get; set; }
  public bool Visible { get; set; }
  public string DealBadgeText { get; set; }
  public int Type { get; set; }
  public string IconPrefab { get; set; }
  public string DescriptionSecond { get; set; }
  public string DescriptionThird { get; set; }
  public string SocialSharedReward { get; set; }
  public double DiscountPriceMultiplier { get; set; }

  public IAPStoreMarketItemConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Description = null;
    this.ProductType = 0;
    this.Icon = null;
    this.Amount = 0;
    this.Enabled = false;
    this.Order = 0;
    this.AppleId = null;
    this.GoogleId = null;
    this.AmazonId = null;
    this.WindowsId = null;
    this.PriceUsd = 0.0;
    this.DiscountOfId = null;
    this.SaleBadgeText = null;
    this.Category = null;
    this.RewardOrdered = null;
    this.Visible = false;
    this.DealBadgeText = null;
    this.Type = 0;
    this.IconPrefab = null;
    this.DescriptionSecond = null;
    this.DescriptionThird = null;
    this.SocialSharedReward = null;
    this.DiscountPriceMultiplier = 0.0;
  }
}

public struct IAPStoreMarketItemConfigDict : IFlatbufferConfigDict<IAPStoreMarketItemConfig, IAPStoreMarketItemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreMarketItemConfigDict GetRootAsIAPStoreMarketItemConfigDict(ByteBuffer _bb) { return GetRootAsIAPStoreMarketItemConfigDict(_bb, new IAPStoreMarketItemConfigDict()); }
  public static IAPStoreMarketItemConfigDict GetRootAsIAPStoreMarketItemConfigDict(ByteBuffer _bb, IAPStoreMarketItemConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreMarketItemConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.IAPStoreMarketItemConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.IAPStoreMarketItemConfig?)(new FBConfig.IAPStoreMarketItemConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.IAPStoreMarketItemConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.IAPStoreMarketItemConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.IAPStoreMarketItemConfigDict> CreateIAPStoreMarketItemConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    IAPStoreMarketItemConfigDict.AddValues(builder, valuesOffset);
    return IAPStoreMarketItemConfigDict.EndIAPStoreMarketItemConfigDict(builder);
  }

  public static void StartIAPStoreMarketItemConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreMarketItemConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreMarketItemConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.IAPStoreMarketItemConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.IAPStoreMarketItemConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.IAPStoreMarketItemConfigDict> EndIAPStoreMarketItemConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPStoreMarketItemConfigDict>(o);
  }
  public static void FinishIAPStoreMarketItemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreMarketItemConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedIAPStoreMarketItemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreMarketItemConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public IAPStoreMarketItemConfigDictT UnPack() {
    var _o = new IAPStoreMarketItemConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreMarketItemConfigDictT _o) {
    _o.Values = new List<FBConfig.IAPStoreMarketItemConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.IAPStoreMarketItemConfigDict> Pack(FlatBufferBuilder builder, IAPStoreMarketItemConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreMarketItemConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.IAPStoreMarketItemConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.IAPStoreMarketItemConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateIAPStoreMarketItemConfigDict(
      builder,
      _values);
  }
}

public class IAPStoreMarketItemConfigDictT
{
  public List<FBConfig.IAPStoreMarketItemConfigT> Values { get; set; }

  public IAPStoreMarketItemConfigDictT() {
    this.Values = null;
  }
  public static IAPStoreMarketItemConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return IAPStoreMarketItemConfigDict.GetRootAsIAPStoreMarketItemConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    IAPStoreMarketItemConfigDict.FinishIAPStoreMarketItemConfigDictBuffer(fbb, IAPStoreMarketItemConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
