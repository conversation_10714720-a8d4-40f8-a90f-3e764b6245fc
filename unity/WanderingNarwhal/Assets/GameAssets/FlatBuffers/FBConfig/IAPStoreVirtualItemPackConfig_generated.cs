// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct IAPStoreVirtualItemPackDescription : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreVirtualItemPackDescription GetRootAsIAPStoreVirtualItemPackDescription(ByteBuffer _bb) { return GetRootAsIAPStoreVirtualItemPackDescription(_bb, new IAPStoreVirtualItemPackDescription()); }
  public static IAPStoreVirtualItemPackDescription GetRootAsIAPStoreVirtualItemPackDescription(ByteBuffer _bb, IAPStoreVirtualItemPackDescription obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreVirtualItemPackDescription __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int Amount { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateAmount(int amount) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, amount); return true; } else { return false; } }
  public FBConfig.Price? Price { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.Price?)(new FBConfig.Price()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }

  public static Offset<FBConfig.IAPStoreVirtualItemPackDescription> CreateIAPStoreVirtualItemPackDescription(FlatBufferBuilder builder,
      int amount = 0,
      Offset<FBConfig.Price> priceOffset = default(Offset<FBConfig.Price>)) {
    builder.StartTable(2);
    IAPStoreVirtualItemPackDescription.AddPrice(builder, priceOffset);
    IAPStoreVirtualItemPackDescription.AddAmount(builder, amount);
    return IAPStoreVirtualItemPackDescription.EndIAPStoreVirtualItemPackDescription(builder);
  }

  public static void StartIAPStoreVirtualItemPackDescription(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddAmount(FlatBufferBuilder builder, int amount) { builder.AddInt(0, amount, 0); }
  public static void AddPrice(FlatBufferBuilder builder, Offset<FBConfig.Price> priceOffset) { builder.AddOffset(1, priceOffset.Value, 0); }
  public static Offset<FBConfig.IAPStoreVirtualItemPackDescription> EndIAPStoreVirtualItemPackDescription(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPStoreVirtualItemPackDescription>(o);
  }
  public IAPStoreVirtualItemPackDescriptionT UnPack() {
    var _o = new IAPStoreVirtualItemPackDescriptionT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreVirtualItemPackDescriptionT _o) {
    _o.Amount = this.Amount;
    _o.Price = this.Price.HasValue ? this.Price.Value.UnPack() : null;
  }
  public static Offset<FBConfig.IAPStoreVirtualItemPackDescription> Pack(FlatBufferBuilder builder, IAPStoreVirtualItemPackDescriptionT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreVirtualItemPackDescription>);
    var _price = _o.Price == null ? default(Offset<FBConfig.Price>) : FBConfig.Price.Pack(builder, _o.Price);
    return CreateIAPStoreVirtualItemPackDescription(
      builder,
      _o.Amount,
      _price);
  }
}

public class IAPStoreVirtualItemPackDescriptionT
{
  public int Amount { get; set; }
  public FBConfig.PriceT Price { get; set; }

  public IAPStoreVirtualItemPackDescriptionT() {
    this.Amount = 0;
    this.Price = null;
  }
}

public struct IAPStoreVirtualItemPackConfig : IFlatbufferConfig<IAPStoreVirtualItemPackConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreVirtualItemPackConfig GetRootAsIAPStoreVirtualItemPackConfig(ByteBuffer _bb) { return GetRootAsIAPStoreVirtualItemPackConfig(_bb, new IAPStoreVirtualItemPackConfig()); }
  public static IAPStoreVirtualItemPackConfig GetRootAsIAPStoreVirtualItemPackConfig(ByteBuffer _bb, IAPStoreVirtualItemPackConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreVirtualItemPackConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Type { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateType(int type) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, type); return true; } else { return false; } }
  public string ItemUid { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetItemUidBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetItemUidBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetItemUidArray() { return __p.__vector_as_array<byte>(8); }
  public string Name { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(10); }
  public string Icon { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetIconBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetIconArray() { return __p.__vector_as_array<byte>(12); }
  public string Description { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescriptionBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetDescriptionBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetDescriptionArray() { return __p.__vector_as_array<byte>(14); }
  public FBConfig.IAPStoreVirtualItemPackDescription? PacksFb(int j) { int o = __p.__offset(16); return o != 0 ? (FBConfig.IAPStoreVirtualItemPackDescription?)(new FBConfig.IAPStoreVirtualItemPackDescription()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PacksFbLength { get { int o = __p.__offset(16); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.IAPStoreVirtualItemPackConfig> CreateIAPStoreVirtualItemPackConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int type = 0,
      StringOffset item_uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset iconOffset = default(StringOffset),
      StringOffset descriptionOffset = default(StringOffset),
      VectorOffset packs_fbOffset = default(VectorOffset)) {
    builder.StartTable(7);
    IAPStoreVirtualItemPackConfig.AddPacksFb(builder, packs_fbOffset);
    IAPStoreVirtualItemPackConfig.AddDescription(builder, descriptionOffset);
    IAPStoreVirtualItemPackConfig.AddIcon(builder, iconOffset);
    IAPStoreVirtualItemPackConfig.AddName(builder, nameOffset);
    IAPStoreVirtualItemPackConfig.AddItemUid(builder, item_uidOffset);
    IAPStoreVirtualItemPackConfig.AddType(builder, type);
    IAPStoreVirtualItemPackConfig.AddUid(builder, uidOffset);
    return IAPStoreVirtualItemPackConfig.EndIAPStoreVirtualItemPackConfig(builder);
  }

  public static void StartIAPStoreVirtualItemPackConfig(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddType(FlatBufferBuilder builder, int type) { builder.AddInt(1, type, 0); }
  public static void AddItemUid(FlatBufferBuilder builder, StringOffset itemUidOffset) { builder.AddOffset(2, itemUidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(3, nameOffset.Value, 0); }
  public static void AddIcon(FlatBufferBuilder builder, StringOffset iconOffset) { builder.AddOffset(4, iconOffset.Value, 0); }
  public static void AddDescription(FlatBufferBuilder builder, StringOffset descriptionOffset) { builder.AddOffset(5, descriptionOffset.Value, 0); }
  public static void AddPacksFb(FlatBufferBuilder builder, VectorOffset packsFbOffset) { builder.AddOffset(6, packsFbOffset.Value, 0); }
  public static VectorOffset CreatePacksFbVector(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreVirtualItemPackDescription>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePacksFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreVirtualItemPackDescription>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePacksFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.IAPStoreVirtualItemPackDescription>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePacksFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.IAPStoreVirtualItemPackDescription>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPacksFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.IAPStoreVirtualItemPackConfig> EndIAPStoreVirtualItemPackConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.IAPStoreVirtualItemPackConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfIAPStoreVirtualItemPackConfig(FlatBufferBuilder builder, Offset<IAPStoreVirtualItemPackConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<IAPStoreVirtualItemPackConfig> o1, Offset<IAPStoreVirtualItemPackConfig> o2) =>
        new IAPStoreVirtualItemPackConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new IAPStoreVirtualItemPackConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static IAPStoreVirtualItemPackConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    IAPStoreVirtualItemPackConfig obj_ = new IAPStoreVirtualItemPackConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public IAPStoreVirtualItemPackConfigT UnPack() {
    var _o = new IAPStoreVirtualItemPackConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreVirtualItemPackConfigT _o) {
    _o.Uid = this.Uid;
    _o.Type = this.Type;
    _o.ItemUid = this.ItemUid;
    _o.Name = this.Name;
    _o.Icon = this.Icon;
    _o.Description = this.Description;
    _o.PacksFb = new List<FBConfig.IAPStoreVirtualItemPackDescriptionT>();
    for (var _j = 0; _j < this.PacksFbLength; ++_j) {_o.PacksFb.Add(this.PacksFb(_j).HasValue ? this.PacksFb(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.IAPStoreVirtualItemPackConfig> Pack(FlatBufferBuilder builder, IAPStoreVirtualItemPackConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreVirtualItemPackConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _item_uid = _o.ItemUid == null ? default(StringOffset) : builder.CreateString(_o.ItemUid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _icon = _o.Icon == null ? default(StringOffset) : builder.CreateString(_o.Icon);
    var _description = _o.Description == null ? default(StringOffset) : builder.CreateString(_o.Description);
    var _packs_fb = default(VectorOffset);
    if (_o.PacksFb != null) {
      var __packs_fb = new Offset<FBConfig.IAPStoreVirtualItemPackDescription>[_o.PacksFb.Count];
      for (var _j = 0; _j < __packs_fb.Length; ++_j) { __packs_fb[_j] = FBConfig.IAPStoreVirtualItemPackDescription.Pack(builder, _o.PacksFb[_j]); }
      _packs_fb = CreatePacksFbVector(builder, __packs_fb);
    }
    return CreateIAPStoreVirtualItemPackConfig(
      builder,
      _uid,
      _o.Type,
      _item_uid,
      _name,
      _icon,
      _description,
      _packs_fb);
  }
}

public class IAPStoreVirtualItemPackConfigT
{
  public string Uid { get; set; }
  public int Type { get; set; }
  public string ItemUid { get; set; }
  public string Name { get; set; }
  public string Icon { get; set; }
  public string Description { get; set; }
  public List<FBConfig.IAPStoreVirtualItemPackDescriptionT> PacksFb { get; set; }

  public IAPStoreVirtualItemPackConfigT() {
    this.Uid = null;
    this.Type = 0;
    this.ItemUid = null;
    this.Name = null;
    this.Icon = null;
    this.Description = null;
    this.PacksFb = null;
  }
}

public struct IAPStoreVirtualItemPackConfigDict : IFlatbufferConfigDict<IAPStoreVirtualItemPackConfig, IAPStoreVirtualItemPackConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPStoreVirtualItemPackConfigDict GetRootAsIAPStoreVirtualItemPackConfigDict(ByteBuffer _bb) { return GetRootAsIAPStoreVirtualItemPackConfigDict(_bb, new IAPStoreVirtualItemPackConfigDict()); }
  public static IAPStoreVirtualItemPackConfigDict GetRootAsIAPStoreVirtualItemPackConfigDict(ByteBuffer _bb, IAPStoreVirtualItemPackConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPStoreVirtualItemPackConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.IAPStoreVirtualItemPackConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.IAPStoreVirtualItemPackConfig?)(new FBConfig.IAPStoreVirtualItemPackConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.IAPStoreVirtualItemPackConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.IAPStoreVirtualItemPackConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.IAPStoreVirtualItemPackConfigDict> CreateIAPStoreVirtualItemPackConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    IAPStoreVirtualItemPackConfigDict.AddValues(builder, valuesOffset);
    return IAPStoreVirtualItemPackConfigDict.EndIAPStoreVirtualItemPackConfigDict(builder);
  }

  public static void StartIAPStoreVirtualItemPackConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreVirtualItemPackConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreVirtualItemPackConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.IAPStoreVirtualItemPackConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.IAPStoreVirtualItemPackConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.IAPStoreVirtualItemPackConfigDict> EndIAPStoreVirtualItemPackConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPStoreVirtualItemPackConfigDict>(o);
  }
  public static void FinishIAPStoreVirtualItemPackConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreVirtualItemPackConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedIAPStoreVirtualItemPackConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.IAPStoreVirtualItemPackConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public IAPStoreVirtualItemPackConfigDictT UnPack() {
    var _o = new IAPStoreVirtualItemPackConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPStoreVirtualItemPackConfigDictT _o) {
    _o.Values = new List<FBConfig.IAPStoreVirtualItemPackConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.IAPStoreVirtualItemPackConfigDict> Pack(FlatBufferBuilder builder, IAPStoreVirtualItemPackConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPStoreVirtualItemPackConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.IAPStoreVirtualItemPackConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.IAPStoreVirtualItemPackConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateIAPStoreVirtualItemPackConfigDict(
      builder,
      _values);
  }
}

public class IAPStoreVirtualItemPackConfigDictT
{
  public List<FBConfig.IAPStoreVirtualItemPackConfigT> Values { get; set; }

  public IAPStoreVirtualItemPackConfigDictT() {
    this.Values = null;
  }
  public static IAPStoreVirtualItemPackConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return IAPStoreVirtualItemPackConfigDict.GetRootAsIAPStoreVirtualItemPackConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    IAPStoreVirtualItemPackConfigDict.FinishIAPStoreVirtualItemPackConfigDictBuffer(fbb, IAPStoreVirtualItemPackConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
