// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct PlayerSkillConfig : IFlatbufferConfig<PlayerSkillConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static PlayerSkillConfig GetRootAsPlayerSkillConfig(ByteBuffer _bb) { return GetRootAsPlayerSkillConfig(_bb, new PlayerSkillConfig()); }
  public static PlayerSkillConfig GetRootAsPlayerSkillConfig(ByteBuffer _bb, PlayerSkillConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, By<PERSON><PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public PlayerSkillConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public float WinSkillStep { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateWinSkillStep(float win_skill_step) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, win_skill_step); return true; } else { return false; } }
  public float LoseSkillStep { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateLoseSkillStep(float lose_skill_step) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, lose_skill_step); return true; } else { return false; } }
  public int NotNearWinThreshold { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNotNearWinThreshold(int not_near_win_threshold) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, not_near_win_threshold); return true; } else { return false; } }
  public int NotNearLossThreshold { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNotNearLossThreshold(int not_near_loss_threshold) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, not_near_loss_threshold); return true; } else { return false; } }
  public int WinWhenAimToLoseThreshold { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateWinWhenAimToLoseThreshold(int win_when_aim_to_lose_threshold) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, win_when_aim_to_lose_threshold); return true; } else { return false; } }
  public int LoseWhenAimToWinThreshold { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLoseWhenAimToWinThreshold(int lose_when_aim_to_win_threshold) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, lose_when_aim_to_win_threshold); return true; } else { return false; } }
  public float WinValueForNearLoss { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateWinValueForNearLoss(float win_value_for_near_loss) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, win_value_for_near_loss); return true; } else { return false; } }
  public float WinSkillModifierCap(int j) { int o = __p.__offset(20); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int WinSkillModifierCapLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetWinSkillModifierCapBytes() { return __p.__vector_as_span<float>(20, 4); }
#else
  public ArraySegment<byte>? GetWinSkillModifierCapBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public float[] GetWinSkillModifierCapArray() { return __p.__vector_as_array<float>(20); }
  public bool MutateWinSkillModifierCap(int j, float win_skill_modifier_cap) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, win_skill_modifier_cap); return true; } else { return false; } }
  public float LoseSkillModifierCap(int j) { int o = __p.__offset(22); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int LoseSkillModifierCapLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetLoseSkillModifierCapBytes() { return __p.__vector_as_span<float>(22, 4); }
#else
  public ArraySegment<byte>? GetLoseSkillModifierCapBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public float[] GetLoseSkillModifierCapArray() { return __p.__vector_as_array<float>(22); }
  public bool MutateLoseSkillModifierCap(int j, float lose_skill_modifier_cap) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, lose_skill_modifier_cap); return true; } else { return false; } }
  public int WinRateThreshold { get { int o = __p.__offset(24); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateWinRateThreshold(int win_rate_threshold) { int o = __p.__offset(24); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, win_rate_threshold); return true; } else { return false; } }

  public static Offset<FBConfig.PlayerSkillConfig> CreatePlayerSkillConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      float win_skill_step = 0.0f,
      float lose_skill_step = 0.0f,
      int not_near_win_threshold = 0,
      int not_near_loss_threshold = 0,
      int win_when_aim_to_lose_threshold = 0,
      int lose_when_aim_to_win_threshold = 0,
      float win_value_for_near_loss = 0.0f,
      VectorOffset win_skill_modifier_capOffset = default(VectorOffset),
      VectorOffset lose_skill_modifier_capOffset = default(VectorOffset),
      int win_rate_threshold = 0) {
    builder.StartTable(11);
    PlayerSkillConfig.AddWinRateThreshold(builder, win_rate_threshold);
    PlayerSkillConfig.AddLoseSkillModifierCap(builder, lose_skill_modifier_capOffset);
    PlayerSkillConfig.AddWinSkillModifierCap(builder, win_skill_modifier_capOffset);
    PlayerSkillConfig.AddWinValueForNearLoss(builder, win_value_for_near_loss);
    PlayerSkillConfig.AddLoseWhenAimToWinThreshold(builder, lose_when_aim_to_win_threshold);
    PlayerSkillConfig.AddWinWhenAimToLoseThreshold(builder, win_when_aim_to_lose_threshold);
    PlayerSkillConfig.AddNotNearLossThreshold(builder, not_near_loss_threshold);
    PlayerSkillConfig.AddNotNearWinThreshold(builder, not_near_win_threshold);
    PlayerSkillConfig.AddLoseSkillStep(builder, lose_skill_step);
    PlayerSkillConfig.AddWinSkillStep(builder, win_skill_step);
    PlayerSkillConfig.AddUid(builder, uidOffset);
    return PlayerSkillConfig.EndPlayerSkillConfig(builder);
  }

  public static void StartPlayerSkillConfig(FlatBufferBuilder builder) { builder.StartTable(11); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddWinSkillStep(FlatBufferBuilder builder, float winSkillStep) { builder.AddFloat(1, winSkillStep, 0.0f); }
  public static void AddLoseSkillStep(FlatBufferBuilder builder, float loseSkillStep) { builder.AddFloat(2, loseSkillStep, 0.0f); }
  public static void AddNotNearWinThreshold(FlatBufferBuilder builder, int notNearWinThreshold) { builder.AddInt(3, notNearWinThreshold, 0); }
  public static void AddNotNearLossThreshold(FlatBufferBuilder builder, int notNearLossThreshold) { builder.AddInt(4, notNearLossThreshold, 0); }
  public static void AddWinWhenAimToLoseThreshold(FlatBufferBuilder builder, int winWhenAimToLoseThreshold) { builder.AddInt(5, winWhenAimToLoseThreshold, 0); }
  public static void AddLoseWhenAimToWinThreshold(FlatBufferBuilder builder, int loseWhenAimToWinThreshold) { builder.AddInt(6, loseWhenAimToWinThreshold, 0); }
  public static void AddWinValueForNearLoss(FlatBufferBuilder builder, float winValueForNearLoss) { builder.AddFloat(7, winValueForNearLoss, 0.0f); }
  public static void AddWinSkillModifierCap(FlatBufferBuilder builder, VectorOffset winSkillModifierCapOffset) { builder.AddOffset(8, winSkillModifierCapOffset.Value, 0); }
  public static VectorOffset CreateWinSkillModifierCapVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateWinSkillModifierCapVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWinSkillModifierCapVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWinSkillModifierCapVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartWinSkillModifierCapVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLoseSkillModifierCap(FlatBufferBuilder builder, VectorOffset loseSkillModifierCapOffset) { builder.AddOffset(9, loseSkillModifierCapOffset.Value, 0); }
  public static VectorOffset CreateLoseSkillModifierCapVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateLoseSkillModifierCapVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLoseSkillModifierCapVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLoseSkillModifierCapVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLoseSkillModifierCapVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddWinRateThreshold(FlatBufferBuilder builder, int winRateThreshold) { builder.AddInt(10, winRateThreshold, 0); }
  public static Offset<FBConfig.PlayerSkillConfig> EndPlayerSkillConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.PlayerSkillConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfPlayerSkillConfig(FlatBufferBuilder builder, Offset<PlayerSkillConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<PlayerSkillConfig> o1, Offset<PlayerSkillConfig> o2) =>
        new PlayerSkillConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new PlayerSkillConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static PlayerSkillConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    PlayerSkillConfig obj_ = new PlayerSkillConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public PlayerSkillConfigT UnPack() {
    var _o = new PlayerSkillConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(PlayerSkillConfigT _o) {
    _o.Uid = this.Uid;
    _o.WinSkillStep = this.WinSkillStep;
    _o.LoseSkillStep = this.LoseSkillStep;
    _o.NotNearWinThreshold = this.NotNearWinThreshold;
    _o.NotNearLossThreshold = this.NotNearLossThreshold;
    _o.WinWhenAimToLoseThreshold = this.WinWhenAimToLoseThreshold;
    _o.LoseWhenAimToWinThreshold = this.LoseWhenAimToWinThreshold;
    _o.WinValueForNearLoss = this.WinValueForNearLoss;
    _o.WinSkillModifierCap = new List<float>();
    for (var _j = 0; _j < this.WinSkillModifierCapLength; ++_j) {_o.WinSkillModifierCap.Add(this.WinSkillModifierCap(_j));}
    _o.LoseSkillModifierCap = new List<float>();
    for (var _j = 0; _j < this.LoseSkillModifierCapLength; ++_j) {_o.LoseSkillModifierCap.Add(this.LoseSkillModifierCap(_j));}
    _o.WinRateThreshold = this.WinRateThreshold;
  }
  public static Offset<FBConfig.PlayerSkillConfig> Pack(FlatBufferBuilder builder, PlayerSkillConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.PlayerSkillConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _win_skill_modifier_cap = default(VectorOffset);
    if (_o.WinSkillModifierCap != null) {
      var __win_skill_modifier_cap = _o.WinSkillModifierCap.ToArray();
      _win_skill_modifier_cap = CreateWinSkillModifierCapVector(builder, __win_skill_modifier_cap);
    }
    var _lose_skill_modifier_cap = default(VectorOffset);
    if (_o.LoseSkillModifierCap != null) {
      var __lose_skill_modifier_cap = _o.LoseSkillModifierCap.ToArray();
      _lose_skill_modifier_cap = CreateLoseSkillModifierCapVector(builder, __lose_skill_modifier_cap);
    }
    return CreatePlayerSkillConfig(
      builder,
      _uid,
      _o.WinSkillStep,
      _o.LoseSkillStep,
      _o.NotNearWinThreshold,
      _o.NotNearLossThreshold,
      _o.WinWhenAimToLoseThreshold,
      _o.LoseWhenAimToWinThreshold,
      _o.WinValueForNearLoss,
      _win_skill_modifier_cap,
      _lose_skill_modifier_cap,
      _o.WinRateThreshold);
  }
}

public class PlayerSkillConfigT
{
  public string Uid { get; set; }
  public float WinSkillStep { get; set; }
  public float LoseSkillStep { get; set; }
  public int NotNearWinThreshold { get; set; }
  public int NotNearLossThreshold { get; set; }
  public int WinWhenAimToLoseThreshold { get; set; }
  public int LoseWhenAimToWinThreshold { get; set; }
  public float WinValueForNearLoss { get; set; }
  public List<float> WinSkillModifierCap { get; set; }
  public List<float> LoseSkillModifierCap { get; set; }
  public int WinRateThreshold { get; set; }

  public PlayerSkillConfigT() {
    this.Uid = null;
    this.WinSkillStep = 0.0f;
    this.LoseSkillStep = 0.0f;
    this.NotNearWinThreshold = 0;
    this.NotNearLossThreshold = 0;
    this.WinWhenAimToLoseThreshold = 0;
    this.LoseWhenAimToWinThreshold = 0;
    this.WinValueForNearLoss = 0.0f;
    this.WinSkillModifierCap = null;
    this.LoseSkillModifierCap = null;
    this.WinRateThreshold = 0;
  }
}

public struct PlayerSkillConfigDict : IFlatbufferConfigDict<PlayerSkillConfig, PlayerSkillConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static PlayerSkillConfigDict GetRootAsPlayerSkillConfigDict(ByteBuffer _bb) { return GetRootAsPlayerSkillConfigDict(_bb, new PlayerSkillConfigDict()); }
  public static PlayerSkillConfigDict GetRootAsPlayerSkillConfigDict(ByteBuffer _bb, PlayerSkillConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public PlayerSkillConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.PlayerSkillConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.PlayerSkillConfig?)(new FBConfig.PlayerSkillConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.PlayerSkillConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.PlayerSkillConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.PlayerSkillConfigDict> CreatePlayerSkillConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    PlayerSkillConfigDict.AddValues(builder, valuesOffset);
    return PlayerSkillConfigDict.EndPlayerSkillConfigDict(builder);
  }

  public static void StartPlayerSkillConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.PlayerSkillConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.PlayerSkillConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.PlayerSkillConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.PlayerSkillConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.PlayerSkillConfigDict> EndPlayerSkillConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.PlayerSkillConfigDict>(o);
  }
  public static void FinishPlayerSkillConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.PlayerSkillConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedPlayerSkillConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.PlayerSkillConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public PlayerSkillConfigDictT UnPack() {
    var _o = new PlayerSkillConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(PlayerSkillConfigDictT _o) {
    _o.Values = new List<FBConfig.PlayerSkillConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.PlayerSkillConfigDict> Pack(FlatBufferBuilder builder, PlayerSkillConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.PlayerSkillConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.PlayerSkillConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.PlayerSkillConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreatePlayerSkillConfigDict(
      builder,
      _values);
  }
}

public class PlayerSkillConfigDictT
{
  public List<FBConfig.PlayerSkillConfigT> Values { get; set; }

  public PlayerSkillConfigDictT() {
    this.Values = null;
  }
  public static PlayerSkillConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return PlayerSkillConfigDict.GetRootAsPlayerSkillConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    PlayerSkillConfigDict.FinishPlayerSkillConfigDictBuffer(fbb, PlayerSkillConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
