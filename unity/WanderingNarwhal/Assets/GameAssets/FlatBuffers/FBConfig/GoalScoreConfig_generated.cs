// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ScoreMultipliers : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ScoreMultipliers GetRootAsScoreMultipliers(ByteBuffer _bb) { return GetRootAsScoreMultipliers(_bb, new ScoreMultipliers()); }
  public static ScoreMultipliers GetRootAsScoreMultipliers(ByteBuffer _bb, ScoreMultipliers obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ScoreMultipliers __assign(int _i, Byte<PERSON><PERSON>er _bb) { __init(_i, _bb); return this; }

  public float GoodScoreMult { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateGoodScoreMult(float good_score_mult) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, good_score_mult); return true; } else { return false; } }
  public float BetterScoreMult { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateBetterScoreMult(float better_score_mult) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, better_score_mult); return true; } else { return false; } }
  public float BestScoreMult { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateBestScoreMult(float best_score_mult) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, best_score_mult); return true; } else { return false; } }
  public float ComboMult { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateComboMult(float combo_mult) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, combo_mult); return true; } else { return false; } }

  public static Offset<FBConfig.ScoreMultipliers> CreateScoreMultipliers(FlatBufferBuilder builder,
      float good_score_mult = 0.0f,
      float better_score_mult = 0.0f,
      float best_score_mult = 0.0f,
      float combo_mult = 0.0f) {
    builder.StartTable(4);
    ScoreMultipliers.AddComboMult(builder, combo_mult);
    ScoreMultipliers.AddBestScoreMult(builder, best_score_mult);
    ScoreMultipliers.AddBetterScoreMult(builder, better_score_mult);
    ScoreMultipliers.AddGoodScoreMult(builder, good_score_mult);
    return ScoreMultipliers.EndScoreMultipliers(builder);
  }

  public static void StartScoreMultipliers(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddGoodScoreMult(FlatBufferBuilder builder, float goodScoreMult) { builder.AddFloat(0, goodScoreMult, 0.0f); }
  public static void AddBetterScoreMult(FlatBufferBuilder builder, float betterScoreMult) { builder.AddFloat(1, betterScoreMult, 0.0f); }
  public static void AddBestScoreMult(FlatBufferBuilder builder, float bestScoreMult) { builder.AddFloat(2, bestScoreMult, 0.0f); }
  public static void AddComboMult(FlatBufferBuilder builder, float comboMult) { builder.AddFloat(3, comboMult, 0.0f); }
  public static Offset<FBConfig.ScoreMultipliers> EndScoreMultipliers(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ScoreMultipliers>(o);
  }
  public ScoreMultipliersT UnPack() {
    var _o = new ScoreMultipliersT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ScoreMultipliersT _o) {
    _o.GoodScoreMult = this.GoodScoreMult;
    _o.BetterScoreMult = this.BetterScoreMult;
    _o.BestScoreMult = this.BestScoreMult;
    _o.ComboMult = this.ComboMult;
  }
  public static Offset<FBConfig.ScoreMultipliers> Pack(FlatBufferBuilder builder, ScoreMultipliersT _o) {
    if (_o == null) return default(Offset<FBConfig.ScoreMultipliers>);
    return CreateScoreMultipliers(
      builder,
      _o.GoodScoreMult,
      _o.BetterScoreMult,
      _o.BestScoreMult,
      _o.ComboMult);
  }
}

public class ScoreMultipliersT
{
  public float GoodScoreMult { get; set; }
  public float BetterScoreMult { get; set; }
  public float BestScoreMult { get; set; }
  public float ComboMult { get; set; }

  public ScoreMultipliersT() {
    this.GoodScoreMult = 0.0f;
    this.BetterScoreMult = 0.0f;
    this.BestScoreMult = 0.0f;
    this.ComboMult = 0.0f;
  }
}

public struct BaseScores : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BaseScores GetRootAsBaseScores(ByteBuffer _bb) { return GetRootAsBaseScores(_bb, new BaseScores()); }
  public static BaseScores GetRootAsBaseScores(ByteBuffer _bb, BaseScores obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BaseScores __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int NormalTileScore { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNormalTileScore(int normal_tile_score) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, normal_tile_score); return true; } else { return false; } }
  public int CometBaseScore { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCometBaseScore(int comet_base_score) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, comet_base_score); return true; } else { return false; } }
  public int BackgroundDamagedScore { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBackgroundDamagedScore(int background_damaged_score) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, background_damaged_score); return true; } else { return false; } }
  public int BackgroundRemovedScore { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBackgroundRemovedScore(int background_removed_score) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, background_removed_score); return true; } else { return false; } }

  public static Offset<FBConfig.BaseScores> CreateBaseScores(FlatBufferBuilder builder,
      int normal_tile_score = 0,
      int comet_base_score = 0,
      int background_damaged_score = 0,
      int background_removed_score = 0) {
    builder.StartTable(4);
    BaseScores.AddBackgroundRemovedScore(builder, background_removed_score);
    BaseScores.AddBackgroundDamagedScore(builder, background_damaged_score);
    BaseScores.AddCometBaseScore(builder, comet_base_score);
    BaseScores.AddNormalTileScore(builder, normal_tile_score);
    return BaseScores.EndBaseScores(builder);
  }

  public static void StartBaseScores(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddNormalTileScore(FlatBufferBuilder builder, int normalTileScore) { builder.AddInt(0, normalTileScore, 0); }
  public static void AddCometBaseScore(FlatBufferBuilder builder, int cometBaseScore) { builder.AddInt(1, cometBaseScore, 0); }
  public static void AddBackgroundDamagedScore(FlatBufferBuilder builder, int backgroundDamagedScore) { builder.AddInt(2, backgroundDamagedScore, 0); }
  public static void AddBackgroundRemovedScore(FlatBufferBuilder builder, int backgroundRemovedScore) { builder.AddInt(3, backgroundRemovedScore, 0); }
  public static Offset<FBConfig.BaseScores> EndBaseScores(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.BaseScores>(o);
  }
  public BaseScoresT UnPack() {
    var _o = new BaseScoresT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BaseScoresT _o) {
    _o.NormalTileScore = this.NormalTileScore;
    _o.CometBaseScore = this.CometBaseScore;
    _o.BackgroundDamagedScore = this.BackgroundDamagedScore;
    _o.BackgroundRemovedScore = this.BackgroundRemovedScore;
  }
  public static Offset<FBConfig.BaseScores> Pack(FlatBufferBuilder builder, BaseScoresT _o) {
    if (_o == null) return default(Offset<FBConfig.BaseScores>);
    return CreateBaseScores(
      builder,
      _o.NormalTileScore,
      _o.CometBaseScore,
      _o.BackgroundDamagedScore,
      _o.BackgroundRemovedScore);
  }
}

public class BaseScoresT
{
  public int NormalTileScore { get; set; }
  public int CometBaseScore { get; set; }
  public int BackgroundDamagedScore { get; set; }
  public int BackgroundRemovedScore { get; set; }

  public BaseScoresT() {
    this.NormalTileScore = 0;
    this.CometBaseScore = 0;
    this.BackgroundDamagedScore = 0;
    this.BackgroundRemovedScore = 0;
  }
}

public struct TileStateHpScore : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TileStateHpScore GetRootAsTileStateHpScore(ByteBuffer _bb) { return GetRootAsTileStateHpScore(_bb, new TileStateHpScore()); }
  public static TileStateHpScore GetRootAsTileStateHpScore(ByteBuffer _bb, TileStateHpScore obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TileStateHpScore __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string State { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetStateBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetStateBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetStateArray() { return __p.__vector_as_array<byte>(4); }
  public int Score { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateScore(int score) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, score); return true; } else { return false; } }
  public int Hp { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateHp(int hp) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, hp); return true; } else { return false; } }

  public static Offset<FBConfig.TileStateHpScore> CreateTileStateHpScore(FlatBufferBuilder builder,
      StringOffset stateOffset = default(StringOffset),
      int score = 0,
      int hp = 0) {
    builder.StartTable(3);
    TileStateHpScore.AddHp(builder, hp);
    TileStateHpScore.AddScore(builder, score);
    TileStateHpScore.AddState(builder, stateOffset);
    return TileStateHpScore.EndTileStateHpScore(builder);
  }

  public static void StartTileStateHpScore(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddState(FlatBufferBuilder builder, StringOffset stateOffset) { builder.AddOffset(0, stateOffset.Value, 0); }
  public static void AddScore(FlatBufferBuilder builder, int score) { builder.AddInt(1, score, 0); }
  public static void AddHp(FlatBufferBuilder builder, int hp) { builder.AddInt(2, hp, 0); }
  public static Offset<FBConfig.TileStateHpScore> EndTileStateHpScore(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TileStateHpScore>(o);
  }
  public TileStateHpScoreT UnPack() {
    var _o = new TileStateHpScoreT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TileStateHpScoreT _o) {
    _o.State = this.State;
    _o.Score = this.Score;
    _o.Hp = this.Hp;
  }
  public static Offset<FBConfig.TileStateHpScore> Pack(FlatBufferBuilder builder, TileStateHpScoreT _o) {
    if (_o == null) return default(Offset<FBConfig.TileStateHpScore>);
    var _state = _o.State == null ? default(StringOffset) : builder.CreateString(_o.State);
    return CreateTileStateHpScore(
      builder,
      _state,
      _o.Score,
      _o.Hp);
  }
}

public class TileStateHpScoreT
{
  public string State { get; set; }
  public int Score { get; set; }
  public int Hp { get; set; }

  public TileStateHpScoreT() {
    this.State = null;
    this.Score = 0;
    this.Hp = 0;
  }
}

public struct TileStateScore : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TileStateScore GetRootAsTileStateScore(ByteBuffer _bb) { return GetRootAsTileStateScore(_bb, new TileStateScore()); }
  public static TileStateScore GetRootAsTileStateScore(ByteBuffer _bb, TileStateScore obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TileStateScore __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string State { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetStateBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetStateBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetStateArray() { return __p.__vector_as_array<byte>(4); }
  public int Score { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateScore(int score) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, score); return true; } else { return false; } }

  public static Offset<FBConfig.TileStateScore> CreateTileStateScore(FlatBufferBuilder builder,
      StringOffset stateOffset = default(StringOffset),
      int score = 0) {
    builder.StartTable(2);
    TileStateScore.AddScore(builder, score);
    TileStateScore.AddState(builder, stateOffset);
    return TileStateScore.EndTileStateScore(builder);
  }

  public static void StartTileStateScore(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddState(FlatBufferBuilder builder, StringOffset stateOffset) { builder.AddOffset(0, stateOffset.Value, 0); }
  public static void AddScore(FlatBufferBuilder builder, int score) { builder.AddInt(1, score, 0); }
  public static Offset<FBConfig.TileStateScore> EndTileStateScore(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TileStateScore>(o);
  }
  public TileStateScoreT UnPack() {
    var _o = new TileStateScoreT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TileStateScoreT _o) {
    _o.State = this.State;
    _o.Score = this.Score;
  }
  public static Offset<FBConfig.TileStateScore> Pack(FlatBufferBuilder builder, TileStateScoreT _o) {
    if (_o == null) return default(Offset<FBConfig.TileStateScore>);
    var _state = _o.State == null ? default(StringOffset) : builder.CreateString(_o.State);
    return CreateTileStateScore(
      builder,
      _state,
      _o.Score);
  }
}

public class TileStateScoreT
{
  public string State { get; set; }
  public int Score { get; set; }

  public TileStateScoreT() {
    this.State = null;
    this.Score = 0;
  }
}

public struct GoalScoreConfig : IFlatbufferConfig<GoalScoreConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GoalScoreConfig GetRootAsGoalScoreConfig(ByteBuffer _bb) { return GetRootAsGoalScoreConfig(_bb, new GoalScoreConfig()); }
  public static GoalScoreConfig GetRootAsGoalScoreConfig(ByteBuffer _bb, GoalScoreConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GoalScoreConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.ScoreMultipliers? ScoreMultipliers { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.ScoreMultipliers?)(new FBConfig.ScoreMultipliers()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.BaseScores? BaseScores { get { int o = __p.__offset(8); return o != 0 ? (FBConfig.BaseScores?)(new FBConfig.BaseScores()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.TileStateHpScore? TileStateHpDecreaseScores(int j) { int o = __p.__offset(10); return o != 0 ? (FBConfig.TileStateHpScore?)(new FBConfig.TileStateHpScore()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TileStateHpDecreaseScoresLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.TileStateScore? TileStateDestroyScores(int j) { int o = __p.__offset(12); return o != 0 ? (FBConfig.TileStateScore?)(new FBConfig.TileStateScore()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TileStateDestroyScoresLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? TileSpecDestroyScoresFb(int j) { int o = __p.__offset(14); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TileSpecDestroyScoresFbLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? TileSpecAdjacentDamageFb(int j) { int o = __p.__offset(16); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TileSpecAdjacentDamageFbLength { get { int o = __p.__offset(16); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? SpecialScoresFb(int j) { int o = __p.__offset(18); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int SpecialScoresFbLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? DamageSourceBonusScoresFb(int j) { int o = __p.__offset(20); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int DamageSourceBonusScoresFbLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.GoalScoreConfig> CreateGoalScoreConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      Offset<FBConfig.ScoreMultipliers> score_multipliersOffset = default(Offset<FBConfig.ScoreMultipliers>),
      Offset<FBConfig.BaseScores> base_scoresOffset = default(Offset<FBConfig.BaseScores>),
      VectorOffset tile_state_hp_decrease_scoresOffset = default(VectorOffset),
      VectorOffset tile_state_destroy_scoresOffset = default(VectorOffset),
      VectorOffset tile_spec_destroy_scores_fbOffset = default(VectorOffset),
      VectorOffset tile_spec_adjacent_damage_fbOffset = default(VectorOffset),
      VectorOffset special_scores_fbOffset = default(VectorOffset),
      VectorOffset damage_source_bonus_scores_fbOffset = default(VectorOffset)) {
    builder.StartTable(9);
    GoalScoreConfig.AddDamageSourceBonusScoresFb(builder, damage_source_bonus_scores_fbOffset);
    GoalScoreConfig.AddSpecialScoresFb(builder, special_scores_fbOffset);
    GoalScoreConfig.AddTileSpecAdjacentDamageFb(builder, tile_spec_adjacent_damage_fbOffset);
    GoalScoreConfig.AddTileSpecDestroyScoresFb(builder, tile_spec_destroy_scores_fbOffset);
    GoalScoreConfig.AddTileStateDestroyScores(builder, tile_state_destroy_scoresOffset);
    GoalScoreConfig.AddTileStateHpDecreaseScores(builder, tile_state_hp_decrease_scoresOffset);
    GoalScoreConfig.AddBaseScores(builder, base_scoresOffset);
    GoalScoreConfig.AddScoreMultipliers(builder, score_multipliersOffset);
    GoalScoreConfig.AddUid(builder, uidOffset);
    return GoalScoreConfig.EndGoalScoreConfig(builder);
  }

  public static void StartGoalScoreConfig(FlatBufferBuilder builder) { builder.StartTable(9); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddScoreMultipliers(FlatBufferBuilder builder, Offset<FBConfig.ScoreMultipliers> scoreMultipliersOffset) { builder.AddOffset(1, scoreMultipliersOffset.Value, 0); }
  public static void AddBaseScores(FlatBufferBuilder builder, Offset<FBConfig.BaseScores> baseScoresOffset) { builder.AddOffset(2, baseScoresOffset.Value, 0); }
  public static void AddTileStateHpDecreaseScores(FlatBufferBuilder builder, VectorOffset tileStateHpDecreaseScoresOffset) { builder.AddOffset(3, tileStateHpDecreaseScoresOffset.Value, 0); }
  public static VectorOffset CreateTileStateHpDecreaseScoresVector(FlatBufferBuilder builder, Offset<FBConfig.TileStateHpScore>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTileStateHpDecreaseScoresVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TileStateHpScore>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileStateHpDecreaseScoresVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TileStateHpScore>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileStateHpDecreaseScoresVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TileStateHpScore>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTileStateHpDecreaseScoresVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTileStateDestroyScores(FlatBufferBuilder builder, VectorOffset tileStateDestroyScoresOffset) { builder.AddOffset(4, tileStateDestroyScoresOffset.Value, 0); }
  public static VectorOffset CreateTileStateDestroyScoresVector(FlatBufferBuilder builder, Offset<FBConfig.TileStateScore>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTileStateDestroyScoresVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TileStateScore>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileStateDestroyScoresVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TileStateScore>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileStateDestroyScoresVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TileStateScore>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTileStateDestroyScoresVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTileSpecDestroyScoresFb(FlatBufferBuilder builder, VectorOffset tileSpecDestroyScoresFbOffset) { builder.AddOffset(5, tileSpecDestroyScoresFbOffset.Value, 0); }
  public static VectorOffset CreateTileSpecDestroyScoresFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTileSpecDestroyScoresFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileSpecDestroyScoresFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileSpecDestroyScoresFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTileSpecDestroyScoresFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTileSpecAdjacentDamageFb(FlatBufferBuilder builder, VectorOffset tileSpecAdjacentDamageFbOffset) { builder.AddOffset(6, tileSpecAdjacentDamageFbOffset.Value, 0); }
  public static VectorOffset CreateTileSpecAdjacentDamageFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTileSpecAdjacentDamageFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileSpecAdjacentDamageFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTileSpecAdjacentDamageFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTileSpecAdjacentDamageFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSpecialScoresFb(FlatBufferBuilder builder, VectorOffset specialScoresFbOffset) { builder.AddOffset(7, specialScoresFbOffset.Value, 0); }
  public static VectorOffset CreateSpecialScoresFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSpecialScoresFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSpecialScoresFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSpecialScoresFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSpecialScoresFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddDamageSourceBonusScoresFb(FlatBufferBuilder builder, VectorOffset damageSourceBonusScoresFbOffset) { builder.AddOffset(8, damageSourceBonusScoresFbOffset.Value, 0); }
  public static VectorOffset CreateDamageSourceBonusScoresFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDamageSourceBonusScoresFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDamageSourceBonusScoresFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDamageSourceBonusScoresFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDamageSourceBonusScoresFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.GoalScoreConfig> EndGoalScoreConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.GoalScoreConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfGoalScoreConfig(FlatBufferBuilder builder, Offset<GoalScoreConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<GoalScoreConfig> o1, Offset<GoalScoreConfig> o2) =>
        new GoalScoreConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new GoalScoreConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static GoalScoreConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    GoalScoreConfig obj_ = new GoalScoreConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public GoalScoreConfigT UnPack() {
    var _o = new GoalScoreConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GoalScoreConfigT _o) {
    _o.Uid = this.Uid;
    _o.ScoreMultipliers = this.ScoreMultipliers.HasValue ? this.ScoreMultipliers.Value.UnPack() : null;
    _o.BaseScores = this.BaseScores.HasValue ? this.BaseScores.Value.UnPack() : null;
    _o.TileStateHpDecreaseScores = new List<FBConfig.TileStateHpScoreT>();
    for (var _j = 0; _j < this.TileStateHpDecreaseScoresLength; ++_j) {_o.TileStateHpDecreaseScores.Add(this.TileStateHpDecreaseScores(_j).HasValue ? this.TileStateHpDecreaseScores(_j).Value.UnPack() : null);}
    _o.TileStateDestroyScores = new List<FBConfig.TileStateScoreT>();
    for (var _j = 0; _j < this.TileStateDestroyScoresLength; ++_j) {_o.TileStateDestroyScores.Add(this.TileStateDestroyScores(_j).HasValue ? this.TileStateDestroyScores(_j).Value.UnPack() : null);}
    _o.TileSpecDestroyScoresFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.TileSpecDestroyScoresFbLength; ++_j) {_o.TileSpecDestroyScoresFb.Add(this.TileSpecDestroyScoresFb(_j).HasValue ? this.TileSpecDestroyScoresFb(_j).Value.UnPack() : null);}
    _o.TileSpecAdjacentDamageFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.TileSpecAdjacentDamageFbLength; ++_j) {_o.TileSpecAdjacentDamageFb.Add(this.TileSpecAdjacentDamageFb(_j).HasValue ? this.TileSpecAdjacentDamageFb(_j).Value.UnPack() : null);}
    _o.SpecialScoresFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.SpecialScoresFbLength; ++_j) {_o.SpecialScoresFb.Add(this.SpecialScoresFb(_j).HasValue ? this.SpecialScoresFb(_j).Value.UnPack() : null);}
    _o.DamageSourceBonusScoresFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.DamageSourceBonusScoresFbLength; ++_j) {_o.DamageSourceBonusScoresFb.Add(this.DamageSourceBonusScoresFb(_j).HasValue ? this.DamageSourceBonusScoresFb(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.GoalScoreConfig> Pack(FlatBufferBuilder builder, GoalScoreConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.GoalScoreConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _score_multipliers = _o.ScoreMultipliers == null ? default(Offset<FBConfig.ScoreMultipliers>) : FBConfig.ScoreMultipliers.Pack(builder, _o.ScoreMultipliers);
    var _base_scores = _o.BaseScores == null ? default(Offset<FBConfig.BaseScores>) : FBConfig.BaseScores.Pack(builder, _o.BaseScores);
    var _tile_state_hp_decrease_scores = default(VectorOffset);
    if (_o.TileStateHpDecreaseScores != null) {
      var __tile_state_hp_decrease_scores = new Offset<FBConfig.TileStateHpScore>[_o.TileStateHpDecreaseScores.Count];
      for (var _j = 0; _j < __tile_state_hp_decrease_scores.Length; ++_j) { __tile_state_hp_decrease_scores[_j] = FBConfig.TileStateHpScore.Pack(builder, _o.TileStateHpDecreaseScores[_j]); }
      _tile_state_hp_decrease_scores = CreateTileStateHpDecreaseScoresVector(builder, __tile_state_hp_decrease_scores);
    }
    var _tile_state_destroy_scores = default(VectorOffset);
    if (_o.TileStateDestroyScores != null) {
      var __tile_state_destroy_scores = new Offset<FBConfig.TileStateScore>[_o.TileStateDestroyScores.Count];
      for (var _j = 0; _j < __tile_state_destroy_scores.Length; ++_j) { __tile_state_destroy_scores[_j] = FBConfig.TileStateScore.Pack(builder, _o.TileStateDestroyScores[_j]); }
      _tile_state_destroy_scores = CreateTileStateDestroyScoresVector(builder, __tile_state_destroy_scores);
    }
    var _tile_spec_destroy_scores_fb = default(VectorOffset);
    if (_o.TileSpecDestroyScoresFb != null) {
      var __tile_spec_destroy_scores_fb = new Offset<FBConfig.DictStringInt>[_o.TileSpecDestroyScoresFb.Count];
      for (var _j = 0; _j < __tile_spec_destroy_scores_fb.Length; ++_j) { __tile_spec_destroy_scores_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.TileSpecDestroyScoresFb[_j]); }
      _tile_spec_destroy_scores_fb = CreateTileSpecDestroyScoresFbVector(builder, __tile_spec_destroy_scores_fb);
    }
    var _tile_spec_adjacent_damage_fb = default(VectorOffset);
    if (_o.TileSpecAdjacentDamageFb != null) {
      var __tile_spec_adjacent_damage_fb = new Offset<FBConfig.DictStringInt>[_o.TileSpecAdjacentDamageFb.Count];
      for (var _j = 0; _j < __tile_spec_adjacent_damage_fb.Length; ++_j) { __tile_spec_adjacent_damage_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.TileSpecAdjacentDamageFb[_j]); }
      _tile_spec_adjacent_damage_fb = CreateTileSpecAdjacentDamageFbVector(builder, __tile_spec_adjacent_damage_fb);
    }
    var _special_scores_fb = default(VectorOffset);
    if (_o.SpecialScoresFb != null) {
      var __special_scores_fb = new Offset<FBConfig.DictStringInt>[_o.SpecialScoresFb.Count];
      for (var _j = 0; _j < __special_scores_fb.Length; ++_j) { __special_scores_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.SpecialScoresFb[_j]); }
      _special_scores_fb = CreateSpecialScoresFbVector(builder, __special_scores_fb);
    }
    var _damage_source_bonus_scores_fb = default(VectorOffset);
    if (_o.DamageSourceBonusScoresFb != null) {
      var __damage_source_bonus_scores_fb = new Offset<FBConfig.DictStringInt>[_o.DamageSourceBonusScoresFb.Count];
      for (var _j = 0; _j < __damage_source_bonus_scores_fb.Length; ++_j) { __damage_source_bonus_scores_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.DamageSourceBonusScoresFb[_j]); }
      _damage_source_bonus_scores_fb = CreateDamageSourceBonusScoresFbVector(builder, __damage_source_bonus_scores_fb);
    }
    return CreateGoalScoreConfig(
      builder,
      _uid,
      _score_multipliers,
      _base_scores,
      _tile_state_hp_decrease_scores,
      _tile_state_destroy_scores,
      _tile_spec_destroy_scores_fb,
      _tile_spec_adjacent_damage_fb,
      _special_scores_fb,
      _damage_source_bonus_scores_fb);
  }
}

public class GoalScoreConfigT
{
  public string Uid { get; set; }
  public FBConfig.ScoreMultipliersT ScoreMultipliers { get; set; }
  public FBConfig.BaseScoresT BaseScores { get; set; }
  public List<FBConfig.TileStateHpScoreT> TileStateHpDecreaseScores { get; set; }
  public List<FBConfig.TileStateScoreT> TileStateDestroyScores { get; set; }
  public List<FBConfig.DictStringIntT> TileSpecDestroyScoresFb { get; set; }
  public List<FBConfig.DictStringIntT> TileSpecAdjacentDamageFb { get; set; }
  public List<FBConfig.DictStringIntT> SpecialScoresFb { get; set; }
  public List<FBConfig.DictStringIntT> DamageSourceBonusScoresFb { get; set; }

  public GoalScoreConfigT() {
    this.Uid = null;
    this.ScoreMultipliers = null;
    this.BaseScores = null;
    this.TileStateHpDecreaseScores = null;
    this.TileStateDestroyScores = null;
    this.TileSpecDestroyScoresFb = null;
    this.TileSpecAdjacentDamageFb = null;
    this.SpecialScoresFb = null;
    this.DamageSourceBonusScoresFb = null;
  }
}

public struct GoalScoreConfigDict : IFlatbufferConfigDict<GoalScoreConfig, GoalScoreConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GoalScoreConfigDict GetRootAsGoalScoreConfigDict(ByteBuffer _bb) { return GetRootAsGoalScoreConfigDict(_bb, new GoalScoreConfigDict()); }
  public static GoalScoreConfigDict GetRootAsGoalScoreConfigDict(ByteBuffer _bb, GoalScoreConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GoalScoreConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.GoalScoreConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.GoalScoreConfig?)(new FBConfig.GoalScoreConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.GoalScoreConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.GoalScoreConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.GoalScoreConfigDict> CreateGoalScoreConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    GoalScoreConfigDict.AddValues(builder, valuesOffset);
    return GoalScoreConfigDict.EndGoalScoreConfigDict(builder);
  }

  public static void StartGoalScoreConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.GoalScoreConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.GoalScoreConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.GoalScoreConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.GoalScoreConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.GoalScoreConfigDict> EndGoalScoreConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GoalScoreConfigDict>(o);
  }
  public static void FinishGoalScoreConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GoalScoreConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedGoalScoreConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GoalScoreConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public GoalScoreConfigDictT UnPack() {
    var _o = new GoalScoreConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GoalScoreConfigDictT _o) {
    _o.Values = new List<FBConfig.GoalScoreConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.GoalScoreConfigDict> Pack(FlatBufferBuilder builder, GoalScoreConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.GoalScoreConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.GoalScoreConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.GoalScoreConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateGoalScoreConfigDict(
      builder,
      _values);
  }
}

public class GoalScoreConfigDictT
{
  public List<FBConfig.GoalScoreConfigT> Values { get; set; }

  public GoalScoreConfigDictT() {
    this.Values = null;
  }
  public static GoalScoreConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return GoalScoreConfigDict.GetRootAsGoalScoreConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    GoalScoreConfigDict.FinishGoalScoreConfigDictBuffer(fbb, GoalScoreConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
