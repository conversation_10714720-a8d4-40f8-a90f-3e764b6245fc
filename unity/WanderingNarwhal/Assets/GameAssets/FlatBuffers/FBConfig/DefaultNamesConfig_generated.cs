// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DefaultNamesConfig : IFlatbufferConfig<DefaultNamesConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DefaultNamesConfig GetRootAsDefaultNamesConfig(ByteBuffer _bb) { return GetRootAsDefaultNamesConfig(_bb, new DefaultNamesConfig()); }
  public static DefaultNamesConfig GetRootAsDefaultNamesConfig(ByteBuffer _bb, DefaultNamesConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DefaultNamesConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string FirstWord(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int FirstWordLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string SecondWord(int j) { int o = __p.__offset(8); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int SecondWordLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.DefaultNamesConfig> CreateDefaultNamesConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset first_wordOffset = default(VectorOffset),
      VectorOffset second_wordOffset = default(VectorOffset)) {
    builder.StartTable(3);
    DefaultNamesConfig.AddSecondWord(builder, second_wordOffset);
    DefaultNamesConfig.AddFirstWord(builder, first_wordOffset);
    DefaultNamesConfig.AddUid(builder, uidOffset);
    return DefaultNamesConfig.EndDefaultNamesConfig(builder);
  }

  public static void StartDefaultNamesConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddFirstWord(FlatBufferBuilder builder, VectorOffset firstWordOffset) { builder.AddOffset(1, firstWordOffset.Value, 0); }
  public static VectorOffset CreateFirstWordVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateFirstWordVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFirstWordVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFirstWordVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartFirstWordVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSecondWord(FlatBufferBuilder builder, VectorOffset secondWordOffset) { builder.AddOffset(2, secondWordOffset.Value, 0); }
  public static VectorOffset CreateSecondWordVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSecondWordVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSecondWordVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSecondWordVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSecondWordVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.DefaultNamesConfig> EndDefaultNamesConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.DefaultNamesConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfDefaultNamesConfig(FlatBufferBuilder builder, Offset<DefaultNamesConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<DefaultNamesConfig> o1, Offset<DefaultNamesConfig> o2) =>
        new DefaultNamesConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new DefaultNamesConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static DefaultNamesConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    DefaultNamesConfig obj_ = new DefaultNamesConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public DefaultNamesConfigT UnPack() {
    var _o = new DefaultNamesConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DefaultNamesConfigT _o) {
    _o.Uid = this.Uid;
    _o.FirstWord = new List<string>();
    for (var _j = 0; _j < this.FirstWordLength; ++_j) {_o.FirstWord.Add(this.FirstWord(_j));}
    _o.SecondWord = new List<string>();
    for (var _j = 0; _j < this.SecondWordLength; ++_j) {_o.SecondWord.Add(this.SecondWord(_j));}
  }
  public static Offset<FBConfig.DefaultNamesConfig> Pack(FlatBufferBuilder builder, DefaultNamesConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.DefaultNamesConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _first_word = default(VectorOffset);
    if (_o.FirstWord != null) {
      var __first_word = new StringOffset[_o.FirstWord.Count];
      for (var _j = 0; _j < __first_word.Length; ++_j) { __first_word[_j] = builder.CreateString(_o.FirstWord[_j]); }
      _first_word = CreateFirstWordVector(builder, __first_word);
    }
    var _second_word = default(VectorOffset);
    if (_o.SecondWord != null) {
      var __second_word = new StringOffset[_o.SecondWord.Count];
      for (var _j = 0; _j < __second_word.Length; ++_j) { __second_word[_j] = builder.CreateString(_o.SecondWord[_j]); }
      _second_word = CreateSecondWordVector(builder, __second_word);
    }
    return CreateDefaultNamesConfig(
      builder,
      _uid,
      _first_word,
      _second_word);
  }
}

public class DefaultNamesConfigT
{
  public string Uid { get; set; }
  public List<string> FirstWord { get; set; }
  public List<string> SecondWord { get; set; }

  public DefaultNamesConfigT() {
    this.Uid = null;
    this.FirstWord = null;
    this.SecondWord = null;
  }
}

public struct DefaultNamesConfigDict : IFlatbufferConfigDict<DefaultNamesConfig, DefaultNamesConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DefaultNamesConfigDict GetRootAsDefaultNamesConfigDict(ByteBuffer _bb) { return GetRootAsDefaultNamesConfigDict(_bb, new DefaultNamesConfigDict()); }
  public static DefaultNamesConfigDict GetRootAsDefaultNamesConfigDict(ByteBuffer _bb, DefaultNamesConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DefaultNamesConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.DefaultNamesConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.DefaultNamesConfig?)(new FBConfig.DefaultNamesConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DefaultNamesConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.DefaultNamesConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.DefaultNamesConfigDict> CreateDefaultNamesConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    DefaultNamesConfigDict.AddValues(builder, valuesOffset);
    return DefaultNamesConfigDict.EndDefaultNamesConfigDict(builder);
  }

  public static void StartDefaultNamesConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.DefaultNamesConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DefaultNamesConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DefaultNamesConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DefaultNamesConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.DefaultNamesConfigDict> EndDefaultNamesConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DefaultNamesConfigDict>(o);
  }
  public static void FinishDefaultNamesConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.DefaultNamesConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedDefaultNamesConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.DefaultNamesConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public DefaultNamesConfigDictT UnPack() {
    var _o = new DefaultNamesConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DefaultNamesConfigDictT _o) {
    _o.Values = new List<FBConfig.DefaultNamesConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.DefaultNamesConfigDict> Pack(FlatBufferBuilder builder, DefaultNamesConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.DefaultNamesConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.DefaultNamesConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.DefaultNamesConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateDefaultNamesConfigDict(
      builder,
      _values);
  }
}

public class DefaultNamesConfigDictT
{
  public List<FBConfig.DefaultNamesConfigT> Values { get; set; }

  public DefaultNamesConfigDictT() {
    this.Values = null;
  }
  public static DefaultNamesConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return DefaultNamesConfigDict.GetRootAsDefaultNamesConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    DefaultNamesConfigDict.FinishDefaultNamesConfigDictBuffer(fbb, DefaultNamesConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
