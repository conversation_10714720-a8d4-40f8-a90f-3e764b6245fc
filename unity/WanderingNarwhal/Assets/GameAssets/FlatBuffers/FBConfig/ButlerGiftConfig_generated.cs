// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ButlerGiftConfig : IFlatbufferConfig<ButlerGiftConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ButlerGiftConfig GetRootAsButlerGiftConfig(ByteBuffer _bb) { return GetRootAsButlerGiftConfig(_bb, new ButlerGiftConfig()); }
  public static ButlerGiftConfig GetRootAsButlerGiftConfig(ByteBuffer _bb, ButlerGiftConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public ButlerGiftConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.DictStringInt? Gifts(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int GiftsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.ButlerGiftConfig> CreateButlerGiftConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset giftsOffset = default(VectorOffset)) {
    builder.StartTable(2);
    ButlerGiftConfig.AddGifts(builder, giftsOffset);
    ButlerGiftConfig.AddUid(builder, uidOffset);
    return ButlerGiftConfig.EndButlerGiftConfig(builder);
  }

  public static void StartButlerGiftConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddGifts(FlatBufferBuilder builder, VectorOffset giftsOffset) { builder.AddOffset(1, giftsOffset.Value, 0); }
  public static VectorOffset CreateGiftsVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGiftsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGiftsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGiftsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGiftsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ButlerGiftConfig> EndButlerGiftConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ButlerGiftConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfButlerGiftConfig(FlatBufferBuilder builder, Offset<ButlerGiftConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ButlerGiftConfig> o1, Offset<ButlerGiftConfig> o2) =>
        new ButlerGiftConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ButlerGiftConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ButlerGiftConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ButlerGiftConfig obj_ = new ButlerGiftConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ButlerGiftConfigT UnPack() {
    var _o = new ButlerGiftConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ButlerGiftConfigT _o) {
    _o.Uid = this.Uid;
    _o.Gifts = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.GiftsLength; ++_j) {_o.Gifts.Add(this.Gifts(_j).HasValue ? this.Gifts(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ButlerGiftConfig> Pack(FlatBufferBuilder builder, ButlerGiftConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ButlerGiftConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _gifts = default(VectorOffset);
    if (_o.Gifts != null) {
      var __gifts = new Offset<FBConfig.DictStringInt>[_o.Gifts.Count];
      for (var _j = 0; _j < __gifts.Length; ++_j) { __gifts[_j] = FBConfig.DictStringInt.Pack(builder, _o.Gifts[_j]); }
      _gifts = CreateGiftsVector(builder, __gifts);
    }
    return CreateButlerGiftConfig(
      builder,
      _uid,
      _gifts);
  }
}

public class ButlerGiftConfigT
{
  public string Uid { get; set; }
  public List<FBConfig.DictStringIntT> Gifts { get; set; }

  public ButlerGiftConfigT() {
    this.Uid = null;
    this.Gifts = null;
  }
}

public struct ButlerGiftConfigDict : IFlatbufferConfigDict<ButlerGiftConfig, ButlerGiftConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ButlerGiftConfigDict GetRootAsButlerGiftConfigDict(ByteBuffer _bb) { return GetRootAsButlerGiftConfigDict(_bb, new ButlerGiftConfigDict()); }
  public static ButlerGiftConfigDict GetRootAsButlerGiftConfigDict(ByteBuffer _bb, ButlerGiftConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ButlerGiftConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ButlerGiftConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ButlerGiftConfig?)(new FBConfig.ButlerGiftConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ButlerGiftConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ButlerGiftConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ButlerGiftConfigDict> CreateButlerGiftConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ButlerGiftConfigDict.AddValues(builder, valuesOffset);
    return ButlerGiftConfigDict.EndButlerGiftConfigDict(builder);
  }

  public static void StartButlerGiftConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ButlerGiftConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ButlerGiftConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ButlerGiftConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ButlerGiftConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ButlerGiftConfigDict> EndButlerGiftConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ButlerGiftConfigDict>(o);
  }
  public static void FinishButlerGiftConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ButlerGiftConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedButlerGiftConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ButlerGiftConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ButlerGiftConfigDictT UnPack() {
    var _o = new ButlerGiftConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ButlerGiftConfigDictT _o) {
    _o.Values = new List<FBConfig.ButlerGiftConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ButlerGiftConfigDict> Pack(FlatBufferBuilder builder, ButlerGiftConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ButlerGiftConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ButlerGiftConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ButlerGiftConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateButlerGiftConfigDict(
      builder,
      _values);
  }
}

public class ButlerGiftConfigDictT
{
  public List<FBConfig.ButlerGiftConfigT> Values { get; set; }

  public ButlerGiftConfigDictT() {
    this.Values = null;
  }
  public static ButlerGiftConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ButlerGiftConfigDict.GetRootAsButlerGiftConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ButlerGiftConfigDict.FinishButlerGiftConfigDictBuffer(fbb, ButlerGiftConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
