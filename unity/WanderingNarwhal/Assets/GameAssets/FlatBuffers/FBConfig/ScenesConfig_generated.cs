// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ScenesConfig : IFlatbufferConfig<ScenesConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ScenesConfig GetRootAsScenesConfig(ByteBuffer _bb) { return GetRootAsScenesConfig(_bb, new ScenesConfig()); }
  public static ScenesConfig GetRootAsScenesConfig(ByteBuffer _bb, ScenesConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ScenesConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public FBConfig.ListString? Dependencies(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.ListString?)(new FBConfig.ListString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int DependenciesLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string CountryName { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCountryNameBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetCountryNameBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetCountryNameArray() { return __p.__vector_as_array<byte>(10); }
  public string UniqueInfo { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUniqueInfoBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetUniqueInfoBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetUniqueInfoArray() { return __p.__vector_as_array<byte>(12); }
  public FBConfig.DictStringInt? Reward(int j) { int o = __p.__offset(14); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string BgMusicId { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBgMusicIdBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetBgMusicIdBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetBgMusicIdArray() { return __p.__vector_as_array<byte>(16); }
  public string LevelMusicId { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLevelMusicIdBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetLevelMusicIdBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetLevelMusicIdArray() { return __p.__vector_as_array<byte>(18); }
  public string SubTitle { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubTitleBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetSubTitleBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetSubTitleArray() { return __p.__vector_as_array<byte>(20); }
  public int UiOrder { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateUiOrder(int ui_order) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, ui_order); return true; } else { return false; } }
  public string SceneName { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSceneNameBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetSceneNameBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetSceneNameArray() { return __p.__vector_as_array<byte>(24); }
  public string TransitionName { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTransitionNameBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetTransitionNameBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetTransitionNameArray() { return __p.__vector_as_array<byte>(26); }
  public string TravelJournalTitleName { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTravelJournalTitleNameBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetTravelJournalTitleNameBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetTravelJournalTitleNameArray() { return __p.__vector_as_array<byte>(28); }
  public string BundlesLoadLevelUid { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBundlesLoadLevelUidBytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetBundlesLoadLevelUidBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetBundlesLoadLevelUidArray() { return __p.__vector_as_array<byte>(30); }

  public static Offset<FBConfig.ScenesConfig> CreateScenesConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      VectorOffset dependenciesOffset = default(VectorOffset),
      StringOffset country_nameOffset = default(StringOffset),
      StringOffset unique_infoOffset = default(StringOffset),
      VectorOffset rewardOffset = default(VectorOffset),
      StringOffset bg_music_idOffset = default(StringOffset),
      StringOffset level_music_idOffset = default(StringOffset),
      StringOffset sub_titleOffset = default(StringOffset),
      int ui_order = 0,
      StringOffset scene_nameOffset = default(StringOffset),
      StringOffset transition_nameOffset = default(StringOffset),
      StringOffset travel_journal_title_nameOffset = default(StringOffset),
      StringOffset bundles_load_level_uidOffset = default(StringOffset)) {
    builder.StartTable(14);
    ScenesConfig.AddBundlesLoadLevelUid(builder, bundles_load_level_uidOffset);
    ScenesConfig.AddTravelJournalTitleName(builder, travel_journal_title_nameOffset);
    ScenesConfig.AddTransitionName(builder, transition_nameOffset);
    ScenesConfig.AddSceneName(builder, scene_nameOffset);
    ScenesConfig.AddUiOrder(builder, ui_order);
    ScenesConfig.AddSubTitle(builder, sub_titleOffset);
    ScenesConfig.AddLevelMusicId(builder, level_music_idOffset);
    ScenesConfig.AddBgMusicId(builder, bg_music_idOffset);
    ScenesConfig.AddReward(builder, rewardOffset);
    ScenesConfig.AddUniqueInfo(builder, unique_infoOffset);
    ScenesConfig.AddCountryName(builder, country_nameOffset);
    ScenesConfig.AddDependencies(builder, dependenciesOffset);
    ScenesConfig.AddName(builder, nameOffset);
    ScenesConfig.AddUid(builder, uidOffset);
    return ScenesConfig.EndScenesConfig(builder);
  }

  public static void StartScenesConfig(FlatBufferBuilder builder) { builder.StartTable(14); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddDependencies(FlatBufferBuilder builder, VectorOffset dependenciesOffset) { builder.AddOffset(2, dependenciesOffset.Value, 0); }
  public static VectorOffset CreateDependenciesVector(FlatBufferBuilder builder, Offset<FBConfig.ListString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ListString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ListString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDependenciesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ListString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDependenciesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddCountryName(FlatBufferBuilder builder, StringOffset countryNameOffset) { builder.AddOffset(3, countryNameOffset.Value, 0); }
  public static void AddUniqueInfo(FlatBufferBuilder builder, StringOffset uniqueInfoOffset) { builder.AddOffset(4, uniqueInfoOffset.Value, 0); }
  public static void AddReward(FlatBufferBuilder builder, VectorOffset rewardOffset) { builder.AddOffset(5, rewardOffset.Value, 0); }
  public static VectorOffset CreateRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBgMusicId(FlatBufferBuilder builder, StringOffset bgMusicIdOffset) { builder.AddOffset(6, bgMusicIdOffset.Value, 0); }
  public static void AddLevelMusicId(FlatBufferBuilder builder, StringOffset levelMusicIdOffset) { builder.AddOffset(7, levelMusicIdOffset.Value, 0); }
  public static void AddSubTitle(FlatBufferBuilder builder, StringOffset subTitleOffset) { builder.AddOffset(8, subTitleOffset.Value, 0); }
  public static void AddUiOrder(FlatBufferBuilder builder, int uiOrder) { builder.AddInt(9, uiOrder, 0); }
  public static void AddSceneName(FlatBufferBuilder builder, StringOffset sceneNameOffset) { builder.AddOffset(10, sceneNameOffset.Value, 0); }
  public static void AddTransitionName(FlatBufferBuilder builder, StringOffset transitionNameOffset) { builder.AddOffset(11, transitionNameOffset.Value, 0); }
  public static void AddTravelJournalTitleName(FlatBufferBuilder builder, StringOffset travelJournalTitleNameOffset) { builder.AddOffset(12, travelJournalTitleNameOffset.Value, 0); }
  public static void AddBundlesLoadLevelUid(FlatBufferBuilder builder, StringOffset bundlesLoadLevelUidOffset) { builder.AddOffset(13, bundlesLoadLevelUidOffset.Value, 0); }
  public static Offset<FBConfig.ScenesConfig> EndScenesConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ScenesConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfScenesConfig(FlatBufferBuilder builder, Offset<ScenesConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ScenesConfig> o1, Offset<ScenesConfig> o2) =>
        new ScenesConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ScenesConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ScenesConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ScenesConfig obj_ = new ScenesConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ScenesConfigT UnPack() {
    var _o = new ScenesConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ScenesConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Dependencies = new List<FBConfig.ListStringT>();
    for (var _j = 0; _j < this.DependenciesLength; ++_j) {_o.Dependencies.Add(this.Dependencies(_j).HasValue ? this.Dependencies(_j).Value.UnPack() : null);}
    _o.CountryName = this.CountryName;
    _o.UniqueInfo = this.UniqueInfo;
    _o.Reward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardLength; ++_j) {_o.Reward.Add(this.Reward(_j).HasValue ? this.Reward(_j).Value.UnPack() : null);}
    _o.BgMusicId = this.BgMusicId;
    _o.LevelMusicId = this.LevelMusicId;
    _o.SubTitle = this.SubTitle;
    _o.UiOrder = this.UiOrder;
    _o.SceneName = this.SceneName;
    _o.TransitionName = this.TransitionName;
    _o.TravelJournalTitleName = this.TravelJournalTitleName;
    _o.BundlesLoadLevelUid = this.BundlesLoadLevelUid;
  }
  public static Offset<FBConfig.ScenesConfig> Pack(FlatBufferBuilder builder, ScenesConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ScenesConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _dependencies = default(VectorOffset);
    if (_o.Dependencies != null) {
      var __dependencies = new Offset<FBConfig.ListString>[_o.Dependencies.Count];
      for (var _j = 0; _j < __dependencies.Length; ++_j) { __dependencies[_j] = FBConfig.ListString.Pack(builder, _o.Dependencies[_j]); }
      _dependencies = CreateDependenciesVector(builder, __dependencies);
    }
    var _country_name = _o.CountryName == null ? default(StringOffset) : builder.CreateString(_o.CountryName);
    var _unique_info = _o.UniqueInfo == null ? default(StringOffset) : builder.CreateString(_o.UniqueInfo);
    var _reward = default(VectorOffset);
    if (_o.Reward != null) {
      var __reward = new Offset<FBConfig.DictStringInt>[_o.Reward.Count];
      for (var _j = 0; _j < __reward.Length; ++_j) { __reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.Reward[_j]); }
      _reward = CreateRewardVector(builder, __reward);
    }
    var _bg_music_id = _o.BgMusicId == null ? default(StringOffset) : builder.CreateString(_o.BgMusicId);
    var _level_music_id = _o.LevelMusicId == null ? default(StringOffset) : builder.CreateString(_o.LevelMusicId);
    var _sub_title = _o.SubTitle == null ? default(StringOffset) : builder.CreateString(_o.SubTitle);
    var _scene_name = _o.SceneName == null ? default(StringOffset) : builder.CreateString(_o.SceneName);
    var _transition_name = _o.TransitionName == null ? default(StringOffset) : builder.CreateString(_o.TransitionName);
    var _travel_journal_title_name = _o.TravelJournalTitleName == null ? default(StringOffset) : builder.CreateString(_o.TravelJournalTitleName);
    var _bundles_load_level_uid = _o.BundlesLoadLevelUid == null ? default(StringOffset) : builder.CreateString(_o.BundlesLoadLevelUid);
    return CreateScenesConfig(
      builder,
      _uid,
      _name,
      _dependencies,
      _country_name,
      _unique_info,
      _reward,
      _bg_music_id,
      _level_music_id,
      _sub_title,
      _o.UiOrder,
      _scene_name,
      _transition_name,
      _travel_journal_title_name,
      _bundles_load_level_uid);
  }
}

public class ScenesConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public List<FBConfig.ListStringT> Dependencies { get; set; }
  public string CountryName { get; set; }
  public string UniqueInfo { get; set; }
  public List<FBConfig.DictStringIntT> Reward { get; set; }
  public string BgMusicId { get; set; }
  public string LevelMusicId { get; set; }
  public string SubTitle { get; set; }
  public int UiOrder { get; set; }
  public string SceneName { get; set; }
  public string TransitionName { get; set; }
  public string TravelJournalTitleName { get; set; }
  public string BundlesLoadLevelUid { get; set; }

  public ScenesConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Dependencies = null;
    this.CountryName = null;
    this.UniqueInfo = null;
    this.Reward = null;
    this.BgMusicId = null;
    this.LevelMusicId = null;
    this.SubTitle = null;
    this.UiOrder = 0;
    this.SceneName = null;
    this.TransitionName = null;
    this.TravelJournalTitleName = null;
    this.BundlesLoadLevelUid = null;
  }
}

public struct ScenesConfigDict : IFlatbufferConfigDict<ScenesConfig, ScenesConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ScenesConfigDict GetRootAsScenesConfigDict(ByteBuffer _bb) { return GetRootAsScenesConfigDict(_bb, new ScenesConfigDict()); }
  public static ScenesConfigDict GetRootAsScenesConfigDict(ByteBuffer _bb, ScenesConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ScenesConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ScenesConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ScenesConfig?)(new FBConfig.ScenesConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ScenesConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ScenesConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ScenesConfigDict> CreateScenesConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ScenesConfigDict.AddValues(builder, valuesOffset);
    return ScenesConfigDict.EndScenesConfigDict(builder);
  }

  public static void StartScenesConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ScenesConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ScenesConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ScenesConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ScenesConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ScenesConfigDict> EndScenesConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ScenesConfigDict>(o);
  }
  public static void FinishScenesConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ScenesConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedScenesConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ScenesConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ScenesConfigDictT UnPack() {
    var _o = new ScenesConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ScenesConfigDictT _o) {
    _o.Values = new List<FBConfig.ScenesConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ScenesConfigDict> Pack(FlatBufferBuilder builder, ScenesConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ScenesConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ScenesConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ScenesConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateScenesConfigDict(
      builder,
      _values);
  }
}

public class ScenesConfigDictT
{
  public List<FBConfig.ScenesConfigT> Values { get; set; }

  public ScenesConfigDictT() {
    this.Values = null;
  }
  public static ScenesConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ScenesConfigDict.GetRootAsScenesConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ScenesConfigDict.FinishScenesConfigDictBuffer(fbb, ScenesConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
