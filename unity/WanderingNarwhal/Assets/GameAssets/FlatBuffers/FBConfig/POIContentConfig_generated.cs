// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct POIContentConfig : IFlatbufferConfig<POIContentConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POIContentConfig GetRootAsPOIContentConfig(ByteBuffer _bb) { return GetRootAsPOIContentConfig(_bb, new POIContentConfig()); }
  public static POIContentConfig GetRootAsPOIContentConfig(ByteBuffer _bb, POIContentConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POIContentConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public float AspectRatio { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateAspectRatio(float aspect_ratio) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, aspect_ratio); return true; } else { return false; } }
  public string ImageUrl { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetImageUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetImageUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetImageUrlArray() { return __p.__vector_as_array<byte>(8); }
  public string VideoUrl { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetVideoUrlBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetVideoUrlBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetVideoUrlArray() { return __p.__vector_as_array<byte>(10); }
  public string TextUrl { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTextUrlBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetTextUrlBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetTextUrlArray() { return __p.__vector_as_array<byte>(12); }
  public string Category { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(14); }
  public string SocialUrl { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSocialUrlBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetSocialUrlBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetSocialUrlArray() { return __p.__vector_as_array<byte>(16); }
  public string SocialUrlName { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSocialUrlNameBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetSocialUrlNameBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetSocialUrlNameArray() { return __p.__vector_as_array<byte>(18); }
  public string TitleText { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleTextBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetTitleTextBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetTitleTextArray() { return __p.__vector_as_array<byte>(20); }
  public string SubTitleText { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubTitleTextBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetSubTitleTextBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetSubTitleTextArray() { return __p.__vector_as_array<byte>(22); }
  public string PreviewText { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPreviewTextBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetPreviewTextBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetPreviewTextArray() { return __p.__vector_as_array<byte>(24); }
  public string AuthorAvatarUrl { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAuthorAvatarUrlBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetAuthorAvatarUrlBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetAuthorAvatarUrlArray() { return __p.__vector_as_array<byte>(26); }
  public string PoiEntityUid { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPoiEntityUidBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetPoiEntityUidBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetPoiEntityUidArray() { return __p.__vector_as_array<byte>(28); }
  public int Row { get { int o = __p.__offset(30); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRow(int row) { int o = __p.__offset(30); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, row); return true; } else { return false; } }
  public int Column { get { int o = __p.__offset(32); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateColumn(int column) { int o = __p.__offset(32); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, column); return true; } else { return false; } }

  public static Offset<FBConfig.POIContentConfig> CreatePOIContentConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      float aspect_ratio = 0.0f,
      StringOffset image_urlOffset = default(StringOffset),
      StringOffset video_urlOffset = default(StringOffset),
      StringOffset text_urlOffset = default(StringOffset),
      StringOffset categoryOffset = default(StringOffset),
      StringOffset social_urlOffset = default(StringOffset),
      StringOffset social_url_nameOffset = default(StringOffset),
      StringOffset title_textOffset = default(StringOffset),
      StringOffset sub_title_textOffset = default(StringOffset),
      StringOffset preview_textOffset = default(StringOffset),
      StringOffset author_avatar_urlOffset = default(StringOffset),
      StringOffset poi_entity_uidOffset = default(StringOffset),
      int row = 0,
      int column = 0) {
    builder.StartTable(15);
    POIContentConfig.AddColumn(builder, column);
    POIContentConfig.AddRow(builder, row);
    POIContentConfig.AddPoiEntityUid(builder, poi_entity_uidOffset);
    POIContentConfig.AddAuthorAvatarUrl(builder, author_avatar_urlOffset);
    POIContentConfig.AddPreviewText(builder, preview_textOffset);
    POIContentConfig.AddSubTitleText(builder, sub_title_textOffset);
    POIContentConfig.AddTitleText(builder, title_textOffset);
    POIContentConfig.AddSocialUrlName(builder, social_url_nameOffset);
    POIContentConfig.AddSocialUrl(builder, social_urlOffset);
    POIContentConfig.AddCategory(builder, categoryOffset);
    POIContentConfig.AddTextUrl(builder, text_urlOffset);
    POIContentConfig.AddVideoUrl(builder, video_urlOffset);
    POIContentConfig.AddImageUrl(builder, image_urlOffset);
    POIContentConfig.AddAspectRatio(builder, aspect_ratio);
    POIContentConfig.AddUid(builder, uidOffset);
    return POIContentConfig.EndPOIContentConfig(builder);
  }

  public static void StartPOIContentConfig(FlatBufferBuilder builder) { builder.StartTable(15); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddAspectRatio(FlatBufferBuilder builder, float aspectRatio) { builder.AddFloat(1, aspectRatio, 0.0f); }
  public static void AddImageUrl(FlatBufferBuilder builder, StringOffset imageUrlOffset) { builder.AddOffset(2, imageUrlOffset.Value, 0); }
  public static void AddVideoUrl(FlatBufferBuilder builder, StringOffset videoUrlOffset) { builder.AddOffset(3, videoUrlOffset.Value, 0); }
  public static void AddTextUrl(FlatBufferBuilder builder, StringOffset textUrlOffset) { builder.AddOffset(4, textUrlOffset.Value, 0); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(5, categoryOffset.Value, 0); }
  public static void AddSocialUrl(FlatBufferBuilder builder, StringOffset socialUrlOffset) { builder.AddOffset(6, socialUrlOffset.Value, 0); }
  public static void AddSocialUrlName(FlatBufferBuilder builder, StringOffset socialUrlNameOffset) { builder.AddOffset(7, socialUrlNameOffset.Value, 0); }
  public static void AddTitleText(FlatBufferBuilder builder, StringOffset titleTextOffset) { builder.AddOffset(8, titleTextOffset.Value, 0); }
  public static void AddSubTitleText(FlatBufferBuilder builder, StringOffset subTitleTextOffset) { builder.AddOffset(9, subTitleTextOffset.Value, 0); }
  public static void AddPreviewText(FlatBufferBuilder builder, StringOffset previewTextOffset) { builder.AddOffset(10, previewTextOffset.Value, 0); }
  public static void AddAuthorAvatarUrl(FlatBufferBuilder builder, StringOffset authorAvatarUrlOffset) { builder.AddOffset(11, authorAvatarUrlOffset.Value, 0); }
  public static void AddPoiEntityUid(FlatBufferBuilder builder, StringOffset poiEntityUidOffset) { builder.AddOffset(12, poiEntityUidOffset.Value, 0); }
  public static void AddRow(FlatBufferBuilder builder, int row) { builder.AddInt(13, row, 0); }
  public static void AddColumn(FlatBufferBuilder builder, int column) { builder.AddInt(14, column, 0); }
  public static Offset<FBConfig.POIContentConfig> EndPOIContentConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.POIContentConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfPOIContentConfig(FlatBufferBuilder builder, Offset<POIContentConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<POIContentConfig> o1, Offset<POIContentConfig> o2) =>
        new POIContentConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new POIContentConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static POIContentConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    POIContentConfig obj_ = new POIContentConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public POIContentConfigT UnPack() {
    var _o = new POIContentConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POIContentConfigT _o) {
    _o.Uid = this.Uid;
    _o.AspectRatio = this.AspectRatio;
    _o.ImageUrl = this.ImageUrl;
    _o.VideoUrl = this.VideoUrl;
    _o.TextUrl = this.TextUrl;
    _o.Category = this.Category;
    _o.SocialUrl = this.SocialUrl;
    _o.SocialUrlName = this.SocialUrlName;
    _o.TitleText = this.TitleText;
    _o.SubTitleText = this.SubTitleText;
    _o.PreviewText = this.PreviewText;
    _o.AuthorAvatarUrl = this.AuthorAvatarUrl;
    _o.PoiEntityUid = this.PoiEntityUid;
    _o.Row = this.Row;
    _o.Column = this.Column;
  }
  public static Offset<FBConfig.POIContentConfig> Pack(FlatBufferBuilder builder, POIContentConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.POIContentConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _image_url = _o.ImageUrl == null ? default(StringOffset) : builder.CreateString(_o.ImageUrl);
    var _video_url = _o.VideoUrl == null ? default(StringOffset) : builder.CreateString(_o.VideoUrl);
    var _text_url = _o.TextUrl == null ? default(StringOffset) : builder.CreateString(_o.TextUrl);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    var _social_url = _o.SocialUrl == null ? default(StringOffset) : builder.CreateString(_o.SocialUrl);
    var _social_url_name = _o.SocialUrlName == null ? default(StringOffset) : builder.CreateString(_o.SocialUrlName);
    var _title_text = _o.TitleText == null ? default(StringOffset) : builder.CreateString(_o.TitleText);
    var _sub_title_text = _o.SubTitleText == null ? default(StringOffset) : builder.CreateString(_o.SubTitleText);
    var _preview_text = _o.PreviewText == null ? default(StringOffset) : builder.CreateString(_o.PreviewText);
    var _author_avatar_url = _o.AuthorAvatarUrl == null ? default(StringOffset) : builder.CreateString(_o.AuthorAvatarUrl);
    var _poi_entity_uid = _o.PoiEntityUid == null ? default(StringOffset) : builder.CreateString(_o.PoiEntityUid);
    return CreatePOIContentConfig(
      builder,
      _uid,
      _o.AspectRatio,
      _image_url,
      _video_url,
      _text_url,
      _category,
      _social_url,
      _social_url_name,
      _title_text,
      _sub_title_text,
      _preview_text,
      _author_avatar_url,
      _poi_entity_uid,
      _o.Row,
      _o.Column);
  }
}

public class POIContentConfigT
{
  public string Uid { get; set; }
  public float AspectRatio { get; set; }
  public string ImageUrl { get; set; }
  public string VideoUrl { get; set; }
  public string TextUrl { get; set; }
  public string Category { get; set; }
  public string SocialUrl { get; set; }
  public string SocialUrlName { get; set; }
  public string TitleText { get; set; }
  public string SubTitleText { get; set; }
  public string PreviewText { get; set; }
  public string AuthorAvatarUrl { get; set; }
  public string PoiEntityUid { get; set; }
  public int Row { get; set; }
  public int Column { get; set; }

  public POIContentConfigT() {
    this.Uid = null;
    this.AspectRatio = 0.0f;
    this.ImageUrl = null;
    this.VideoUrl = null;
    this.TextUrl = null;
    this.Category = null;
    this.SocialUrl = null;
    this.SocialUrlName = null;
    this.TitleText = null;
    this.SubTitleText = null;
    this.PreviewText = null;
    this.AuthorAvatarUrl = null;
    this.PoiEntityUid = null;
    this.Row = 0;
    this.Column = 0;
  }
}

public struct POIContentConfigDict : IFlatbufferConfigDict<POIContentConfig, POIContentConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POIContentConfigDict GetRootAsPOIContentConfigDict(ByteBuffer _bb) { return GetRootAsPOIContentConfigDict(_bb, new POIContentConfigDict()); }
  public static POIContentConfigDict GetRootAsPOIContentConfigDict(ByteBuffer _bb, POIContentConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POIContentConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.POIContentConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.POIContentConfig?)(new FBConfig.POIContentConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.POIContentConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.POIContentConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.POIContentConfigDict> CreatePOIContentConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    POIContentConfigDict.AddValues(builder, valuesOffset);
    return POIContentConfigDict.EndPOIContentConfigDict(builder);
  }

  public static void StartPOIContentConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.POIContentConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.POIContentConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.POIContentConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.POIContentConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.POIContentConfigDict> EndPOIContentConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.POIContentConfigDict>(o);
  }
  public static void FinishPOIContentConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POIContentConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedPOIContentConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POIContentConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public POIContentConfigDictT UnPack() {
    var _o = new POIContentConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POIContentConfigDictT _o) {
    _o.Values = new List<FBConfig.POIContentConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.POIContentConfigDict> Pack(FlatBufferBuilder builder, POIContentConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.POIContentConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.POIContentConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.POIContentConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreatePOIContentConfigDict(
      builder,
      _values);
  }
}

public class POIContentConfigDictT
{
  public List<FBConfig.POIContentConfigT> Values { get; set; }

  public POIContentConfigDictT() {
    this.Values = null;
  }
  public static POIContentConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return POIContentConfigDict.GetRootAsPOIContentConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    POIContentConfigDict.FinishPOIContentConfigDictBuffer(fbb, POIContentConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
