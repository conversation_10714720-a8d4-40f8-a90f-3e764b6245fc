// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct AdsConfig : IFlatbufferConfig<AdsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AdsConfig GetRootAsAdsConfig(ByteBuffer _bb) { return GetRootAsAdsConfig(_bb, new AdsConfig()); }
  public static AdsConfig GetRootAsAdsConfig(ByteBuffer _bb, AdsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AdsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int NumAdsToWatch { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNumAdsToWatch(int num_ads_to_watch) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, num_ads_to_watch); return true; } else { return false; } }
  public int CooldownTime { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCooldownTime(int cooldown_time) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, cooldown_time); return true; } else { return false; } }
  public int MaxFailedAttempts { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxFailedAttempts(int max_failed_attempts) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_failed_attempts); return true; } else { return false; } }
  public int FailCooldownTime { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateFailCooldownTime(int fail_cooldown_time) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, fail_cooldown_time); return true; } else { return false; } }
  public int BuildingSavedPercent { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBuildingSavedPercent(int building_saved_percent) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, building_saved_percent); return true; } else { return false; } }
  public int BuildingMinSavedTime { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBuildingMinSavedTime(int building_min_saved_time) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, building_min_saved_time); return true; } else { return false; } }
  public int BuildingMinTotalDuration { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBuildingMinTotalDuration(int building_min_total_duration) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, building_min_total_duration); return true; } else { return false; } }

  public static Offset<FBConfig.AdsConfig> CreateAdsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int num_ads_to_watch = 0,
      int cooldown_time = 0,
      int max_failed_attempts = 0,
      int fail_cooldown_time = 0,
      int building_saved_percent = 0,
      int building_min_saved_time = 0,
      int building_min_total_duration = 0) {
    builder.StartTable(8);
    AdsConfig.AddBuildingMinTotalDuration(builder, building_min_total_duration);
    AdsConfig.AddBuildingMinSavedTime(builder, building_min_saved_time);
    AdsConfig.AddBuildingSavedPercent(builder, building_saved_percent);
    AdsConfig.AddFailCooldownTime(builder, fail_cooldown_time);
    AdsConfig.AddMaxFailedAttempts(builder, max_failed_attempts);
    AdsConfig.AddCooldownTime(builder, cooldown_time);
    AdsConfig.AddNumAdsToWatch(builder, num_ads_to_watch);
    AdsConfig.AddUid(builder, uidOffset);
    return AdsConfig.EndAdsConfig(builder);
  }

  public static void StartAdsConfig(FlatBufferBuilder builder) { builder.StartTable(8); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddNumAdsToWatch(FlatBufferBuilder builder, int numAdsToWatch) { builder.AddInt(1, numAdsToWatch, 0); }
  public static void AddCooldownTime(FlatBufferBuilder builder, int cooldownTime) { builder.AddInt(2, cooldownTime, 0); }
  public static void AddMaxFailedAttempts(FlatBufferBuilder builder, int maxFailedAttempts) { builder.AddInt(3, maxFailedAttempts, 0); }
  public static void AddFailCooldownTime(FlatBufferBuilder builder, int failCooldownTime) { builder.AddInt(4, failCooldownTime, 0); }
  public static void AddBuildingSavedPercent(FlatBufferBuilder builder, int buildingSavedPercent) { builder.AddInt(5, buildingSavedPercent, 0); }
  public static void AddBuildingMinSavedTime(FlatBufferBuilder builder, int buildingMinSavedTime) { builder.AddInt(6, buildingMinSavedTime, 0); }
  public static void AddBuildingMinTotalDuration(FlatBufferBuilder builder, int buildingMinTotalDuration) { builder.AddInt(7, buildingMinTotalDuration, 0); }
  public static Offset<FBConfig.AdsConfig> EndAdsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.AdsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfAdsConfig(FlatBufferBuilder builder, Offset<AdsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<AdsConfig> o1, Offset<AdsConfig> o2) =>
        new AdsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new AdsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static AdsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    AdsConfig obj_ = new AdsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public AdsConfigT UnPack() {
    var _o = new AdsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AdsConfigT _o) {
    _o.Uid = this.Uid;
    _o.NumAdsToWatch = this.NumAdsToWatch;
    _o.CooldownTime = this.CooldownTime;
    _o.MaxFailedAttempts = this.MaxFailedAttempts;
    _o.FailCooldownTime = this.FailCooldownTime;
    _o.BuildingSavedPercent = this.BuildingSavedPercent;
    _o.BuildingMinSavedTime = this.BuildingMinSavedTime;
    _o.BuildingMinTotalDuration = this.BuildingMinTotalDuration;
  }
  public static Offset<FBConfig.AdsConfig> Pack(FlatBufferBuilder builder, AdsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.AdsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateAdsConfig(
      builder,
      _uid,
      _o.NumAdsToWatch,
      _o.CooldownTime,
      _o.MaxFailedAttempts,
      _o.FailCooldownTime,
      _o.BuildingSavedPercent,
      _o.BuildingMinSavedTime,
      _o.BuildingMinTotalDuration);
  }
}

public class AdsConfigT
{
  public string Uid { get; set; }
  public int NumAdsToWatch { get; set; }
  public int CooldownTime { get; set; }
  public int MaxFailedAttempts { get; set; }
  public int FailCooldownTime { get; set; }
  public int BuildingSavedPercent { get; set; }
  public int BuildingMinSavedTime { get; set; }
  public int BuildingMinTotalDuration { get; set; }

  public AdsConfigT() {
    this.Uid = null;
    this.NumAdsToWatch = 0;
    this.CooldownTime = 0;
    this.MaxFailedAttempts = 0;
    this.FailCooldownTime = 0;
    this.BuildingSavedPercent = 0;
    this.BuildingMinSavedTime = 0;
    this.BuildingMinTotalDuration = 0;
  }
}

public struct AdsConfigDict : IFlatbufferConfigDict<AdsConfig, AdsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AdsConfigDict GetRootAsAdsConfigDict(ByteBuffer _bb) { return GetRootAsAdsConfigDict(_bb, new AdsConfigDict()); }
  public static AdsConfigDict GetRootAsAdsConfigDict(ByteBuffer _bb, AdsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AdsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.AdsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.AdsConfig?)(new FBConfig.AdsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.AdsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.AdsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.AdsConfigDict> CreateAdsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    AdsConfigDict.AddValues(builder, valuesOffset);
    return AdsConfigDict.EndAdsConfigDict(builder);
  }

  public static void StartAdsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.AdsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.AdsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.AdsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.AdsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.AdsConfigDict> EndAdsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AdsConfigDict>(o);
  }
  public static void FinishAdsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AdsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedAdsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AdsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public AdsConfigDictT UnPack() {
    var _o = new AdsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AdsConfigDictT _o) {
    _o.Values = new List<FBConfig.AdsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.AdsConfigDict> Pack(FlatBufferBuilder builder, AdsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.AdsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.AdsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.AdsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateAdsConfigDict(
      builder,
      _values);
  }
}

public class AdsConfigDictT
{
  public List<FBConfig.AdsConfigT> Values { get; set; }

  public AdsConfigDictT() {
    this.Values = null;
  }
  public static AdsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return AdsConfigDict.GetRootAsAdsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    AdsConfigDict.FinishAdsConfigDictBuffer(fbb, AdsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
