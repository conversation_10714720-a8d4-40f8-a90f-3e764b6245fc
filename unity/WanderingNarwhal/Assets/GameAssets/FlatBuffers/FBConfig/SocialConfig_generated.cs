// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct SocialConfig : IFlatbufferConfig<SocialConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SocialConfig GetRootAsSocialConfig(ByteBuffer _bb) { return GetRootAsSocialConfig(_bb, new SocialConfig()); }
  public static SocialConfig GetRootAsSocialConfig(ByteBuffer _bb, SocialConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SocialConfig __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string FacebookUrl { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFacebookUrlBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetFacebookUrlBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetFacebookUrlArray() { return __p.__vector_as_array<byte>(6); }
  public string ForumUrl { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetForumUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetForumUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetForumUrlArray() { return __p.__vector_as_array<byte>(8); }
  public string TwitterUrl { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTwitterUrlBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetTwitterUrlBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetTwitterUrlArray() { return __p.__vector_as_array<byte>(10); }
  public string InstagramUrl { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetInstagramUrlBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetInstagramUrlBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetInstagramUrlArray() { return __p.__vector_as_array<byte>(12); }
  public string YoutubeUrl { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetYoutubeUrlBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetYoutubeUrlBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetYoutubeUrlArray() { return __p.__vector_as_array<byte>(14); }
  public string PrivacyPolicyUrl { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPrivacyPolicyUrlBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetPrivacyPolicyUrlBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetPrivacyPolicyUrlArray() { return __p.__vector_as_array<byte>(16); }
  public string TermsOfServiceUrl { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTermsOfServiceUrlBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetTermsOfServiceUrlBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetTermsOfServiceUrlArray() { return __p.__vector_as_array<byte>(18); }
  public string SnapimalsUrl { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSnapimalsUrlBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetSnapimalsUrlBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetSnapimalsUrlArray() { return __p.__vector_as_array<byte>(20); }
  public string SnapimalsAppUrl { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSnapimalsAppUrlBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetSnapimalsAppUrlBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetSnapimalsAppUrlArray() { return __p.__vector_as_array<byte>(22); }
  public string GameDiscordGroupUrl { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameDiscordGroupUrlBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetGameDiscordGroupUrlBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetGameDiscordGroupUrlArray() { return __p.__vector_as_array<byte>(24); }
  public string GameCommunityPageUrl { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameCommunityPageUrlBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetGameCommunityPageUrlBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetGameCommunityPageUrlArray() { return __p.__vector_as_array<byte>(26); }
  public string GameFacebookUrl { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameFacebookUrlBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetGameFacebookUrlBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetGameFacebookUrlArray() { return __p.__vector_as_array<byte>(28); }
  public string GameFacebookAppUri { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameFacebookAppUriBytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetGameFacebookAppUriBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetGameFacebookAppUriArray() { return __p.__vector_as_array<byte>(30); }
  public string GameFacebookGroupUrl { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameFacebookGroupUrlBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetGameFacebookGroupUrlBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetGameFacebookGroupUrlArray() { return __p.__vector_as_array<byte>(32); }
  public string GameFacebookAppGroupUri { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameFacebookAppGroupUriBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetGameFacebookAppGroupUriBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetGameFacebookAppGroupUriArray() { return __p.__vector_as_array<byte>(34); }
  public string GameInstagramUrl { get { int o = __p.__offset(36); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameInstagramUrlBytes() { return __p.__vector_as_span<byte>(36, 1); }
#else
  public ArraySegment<byte>? GetGameInstagramUrlBytes() { return __p.__vector_as_arraysegment(36); }
#endif
  public byte[] GetGameInstagramUrlArray() { return __p.__vector_as_array<byte>(36); }
  public string GameInstagramAppUri { get { int o = __p.__offset(38); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGameInstagramAppUriBytes() { return __p.__vector_as_span<byte>(38, 1); }
#else
  public ArraySegment<byte>? GetGameInstagramAppUriBytes() { return __p.__vector_as_arraysegment(38); }
#endif
  public byte[] GetGameInstagramAppUriArray() { return __p.__vector_as_array<byte>(38); }
  public string SupportUrl { get { int o = __p.__offset(40); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSupportUrlBytes() { return __p.__vector_as_span<byte>(40, 1); }
#else
  public ArraySegment<byte>? GetSupportUrlBytes() { return __p.__vector_as_arraysegment(40); }
#endif
  public byte[] GetSupportUrlArray() { return __p.__vector_as_array<byte>(40); }
  public int AskForLivesCooldown { get { int o = __p.__offset(42); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateAskForLivesCooldown(int ask_for_lives_cooldown) { int o = __p.__offset(42); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, ask_for_lives_cooldown); return true; } else { return false; } }
  public int NudgeCooldown { get { int o = __p.__offset(44); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNudgeCooldown(int nudge_cooldown) { int o = __p.__offset(44); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, nudge_cooldown); return true; } else { return false; } }

  public static Offset<FBConfig.SocialConfig> CreateSocialConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset facebook_urlOffset = default(StringOffset),
      StringOffset forum_urlOffset = default(StringOffset),
      StringOffset twitter_urlOffset = default(StringOffset),
      StringOffset instagram_urlOffset = default(StringOffset),
      StringOffset youtube_urlOffset = default(StringOffset),
      StringOffset privacy_policy_urlOffset = default(StringOffset),
      StringOffset terms_of_service_urlOffset = default(StringOffset),
      StringOffset snapimals_urlOffset = default(StringOffset),
      StringOffset snapimals_app_urlOffset = default(StringOffset),
      StringOffset game_discord_group_urlOffset = default(StringOffset),
      StringOffset game_community_page_urlOffset = default(StringOffset),
      StringOffset game_facebook_urlOffset = default(StringOffset),
      StringOffset game_facebook_app_uriOffset = default(StringOffset),
      StringOffset game_facebook_group_urlOffset = default(StringOffset),
      StringOffset game_facebook_app_group_uriOffset = default(StringOffset),
      StringOffset game_instagram_urlOffset = default(StringOffset),
      StringOffset game_instagram_app_uriOffset = default(StringOffset),
      StringOffset support_urlOffset = default(StringOffset),
      int ask_for_lives_cooldown = 0,
      int nudge_cooldown = 0) {
    builder.StartTable(21);
    SocialConfig.AddNudgeCooldown(builder, nudge_cooldown);
    SocialConfig.AddAskForLivesCooldown(builder, ask_for_lives_cooldown);
    SocialConfig.AddSupportUrl(builder, support_urlOffset);
    SocialConfig.AddGameInstagramAppUri(builder, game_instagram_app_uriOffset);
    SocialConfig.AddGameInstagramUrl(builder, game_instagram_urlOffset);
    SocialConfig.AddGameFacebookAppGroupUri(builder, game_facebook_app_group_uriOffset);
    SocialConfig.AddGameFacebookGroupUrl(builder, game_facebook_group_urlOffset);
    SocialConfig.AddGameFacebookAppUri(builder, game_facebook_app_uriOffset);
    SocialConfig.AddGameFacebookUrl(builder, game_facebook_urlOffset);
    SocialConfig.AddGameCommunityPageUrl(builder, game_community_page_urlOffset);
    SocialConfig.AddGameDiscordGroupUrl(builder, game_discord_group_urlOffset);
    SocialConfig.AddSnapimalsAppUrl(builder, snapimals_app_urlOffset);
    SocialConfig.AddSnapimalsUrl(builder, snapimals_urlOffset);
    SocialConfig.AddTermsOfServiceUrl(builder, terms_of_service_urlOffset);
    SocialConfig.AddPrivacyPolicyUrl(builder, privacy_policy_urlOffset);
    SocialConfig.AddYoutubeUrl(builder, youtube_urlOffset);
    SocialConfig.AddInstagramUrl(builder, instagram_urlOffset);
    SocialConfig.AddTwitterUrl(builder, twitter_urlOffset);
    SocialConfig.AddForumUrl(builder, forum_urlOffset);
    SocialConfig.AddFacebookUrl(builder, facebook_urlOffset);
    SocialConfig.AddUid(builder, uidOffset);
    return SocialConfig.EndSocialConfig(builder);
  }

  public static void StartSocialConfig(FlatBufferBuilder builder) { builder.StartTable(21); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddFacebookUrl(FlatBufferBuilder builder, StringOffset facebookUrlOffset) { builder.AddOffset(1, facebookUrlOffset.Value, 0); }
  public static void AddForumUrl(FlatBufferBuilder builder, StringOffset forumUrlOffset) { builder.AddOffset(2, forumUrlOffset.Value, 0); }
  public static void AddTwitterUrl(FlatBufferBuilder builder, StringOffset twitterUrlOffset) { builder.AddOffset(3, twitterUrlOffset.Value, 0); }
  public static void AddInstagramUrl(FlatBufferBuilder builder, StringOffset instagramUrlOffset) { builder.AddOffset(4, instagramUrlOffset.Value, 0); }
  public static void AddYoutubeUrl(FlatBufferBuilder builder, StringOffset youtubeUrlOffset) { builder.AddOffset(5, youtubeUrlOffset.Value, 0); }
  public static void AddPrivacyPolicyUrl(FlatBufferBuilder builder, StringOffset privacyPolicyUrlOffset) { builder.AddOffset(6, privacyPolicyUrlOffset.Value, 0); }
  public static void AddTermsOfServiceUrl(FlatBufferBuilder builder, StringOffset termsOfServiceUrlOffset) { builder.AddOffset(7, termsOfServiceUrlOffset.Value, 0); }
  public static void AddSnapimalsUrl(FlatBufferBuilder builder, StringOffset snapimalsUrlOffset) { builder.AddOffset(8, snapimalsUrlOffset.Value, 0); }
  public static void AddSnapimalsAppUrl(FlatBufferBuilder builder, StringOffset snapimalsAppUrlOffset) { builder.AddOffset(9, snapimalsAppUrlOffset.Value, 0); }
  public static void AddGameDiscordGroupUrl(FlatBufferBuilder builder, StringOffset gameDiscordGroupUrlOffset) { builder.AddOffset(10, gameDiscordGroupUrlOffset.Value, 0); }
  public static void AddGameCommunityPageUrl(FlatBufferBuilder builder, StringOffset gameCommunityPageUrlOffset) { builder.AddOffset(11, gameCommunityPageUrlOffset.Value, 0); }
  public static void AddGameFacebookUrl(FlatBufferBuilder builder, StringOffset gameFacebookUrlOffset) { builder.AddOffset(12, gameFacebookUrlOffset.Value, 0); }
  public static void AddGameFacebookAppUri(FlatBufferBuilder builder, StringOffset gameFacebookAppUriOffset) { builder.AddOffset(13, gameFacebookAppUriOffset.Value, 0); }
  public static void AddGameFacebookGroupUrl(FlatBufferBuilder builder, StringOffset gameFacebookGroupUrlOffset) { builder.AddOffset(14, gameFacebookGroupUrlOffset.Value, 0); }
  public static void AddGameFacebookAppGroupUri(FlatBufferBuilder builder, StringOffset gameFacebookAppGroupUriOffset) { builder.AddOffset(15, gameFacebookAppGroupUriOffset.Value, 0); }
  public static void AddGameInstagramUrl(FlatBufferBuilder builder, StringOffset gameInstagramUrlOffset) { builder.AddOffset(16, gameInstagramUrlOffset.Value, 0); }
  public static void AddGameInstagramAppUri(FlatBufferBuilder builder, StringOffset gameInstagramAppUriOffset) { builder.AddOffset(17, gameInstagramAppUriOffset.Value, 0); }
  public static void AddSupportUrl(FlatBufferBuilder builder, StringOffset supportUrlOffset) { builder.AddOffset(18, supportUrlOffset.Value, 0); }
  public static void AddAskForLivesCooldown(FlatBufferBuilder builder, int askForLivesCooldown) { builder.AddInt(19, askForLivesCooldown, 0); }
  public static void AddNudgeCooldown(FlatBufferBuilder builder, int nudgeCooldown) { builder.AddInt(20, nudgeCooldown, 0); }
  public static Offset<FBConfig.SocialConfig> EndSocialConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SocialConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSocialConfig(FlatBufferBuilder builder, Offset<SocialConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SocialConfig> o1, Offset<SocialConfig> o2) =>
        new SocialConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SocialConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SocialConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SocialConfig obj_ = new SocialConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SocialConfigT UnPack() {
    var _o = new SocialConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SocialConfigT _o) {
    _o.Uid = this.Uid;
    _o.FacebookUrl = this.FacebookUrl;
    _o.ForumUrl = this.ForumUrl;
    _o.TwitterUrl = this.TwitterUrl;
    _o.InstagramUrl = this.InstagramUrl;
    _o.YoutubeUrl = this.YoutubeUrl;
    _o.PrivacyPolicyUrl = this.PrivacyPolicyUrl;
    _o.TermsOfServiceUrl = this.TermsOfServiceUrl;
    _o.SnapimalsUrl = this.SnapimalsUrl;
    _o.SnapimalsAppUrl = this.SnapimalsAppUrl;
    _o.GameDiscordGroupUrl = this.GameDiscordGroupUrl;
    _o.GameCommunityPageUrl = this.GameCommunityPageUrl;
    _o.GameFacebookUrl = this.GameFacebookUrl;
    _o.GameFacebookAppUri = this.GameFacebookAppUri;
    _o.GameFacebookGroupUrl = this.GameFacebookGroupUrl;
    _o.GameFacebookAppGroupUri = this.GameFacebookAppGroupUri;
    _o.GameInstagramUrl = this.GameInstagramUrl;
    _o.GameInstagramAppUri = this.GameInstagramAppUri;
    _o.SupportUrl = this.SupportUrl;
    _o.AskForLivesCooldown = this.AskForLivesCooldown;
    _o.NudgeCooldown = this.NudgeCooldown;
  }
  public static Offset<FBConfig.SocialConfig> Pack(FlatBufferBuilder builder, SocialConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SocialConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _facebook_url = _o.FacebookUrl == null ? default(StringOffset) : builder.CreateString(_o.FacebookUrl);
    var _forum_url = _o.ForumUrl == null ? default(StringOffset) : builder.CreateString(_o.ForumUrl);
    var _twitter_url = _o.TwitterUrl == null ? default(StringOffset) : builder.CreateString(_o.TwitterUrl);
    var _instagram_url = _o.InstagramUrl == null ? default(StringOffset) : builder.CreateString(_o.InstagramUrl);
    var _youtube_url = _o.YoutubeUrl == null ? default(StringOffset) : builder.CreateString(_o.YoutubeUrl);
    var _privacy_policy_url = _o.PrivacyPolicyUrl == null ? default(StringOffset) : builder.CreateString(_o.PrivacyPolicyUrl);
    var _terms_of_service_url = _o.TermsOfServiceUrl == null ? default(StringOffset) : builder.CreateString(_o.TermsOfServiceUrl);
    var _snapimals_url = _o.SnapimalsUrl == null ? default(StringOffset) : builder.CreateString(_o.SnapimalsUrl);
    var _snapimals_app_url = _o.SnapimalsAppUrl == null ? default(StringOffset) : builder.CreateString(_o.SnapimalsAppUrl);
    var _game_discord_group_url = _o.GameDiscordGroupUrl == null ? default(StringOffset) : builder.CreateString(_o.GameDiscordGroupUrl);
    var _game_community_page_url = _o.GameCommunityPageUrl == null ? default(StringOffset) : builder.CreateString(_o.GameCommunityPageUrl);
    var _game_facebook_url = _o.GameFacebookUrl == null ? default(StringOffset) : builder.CreateString(_o.GameFacebookUrl);
    var _game_facebook_app_uri = _o.GameFacebookAppUri == null ? default(StringOffset) : builder.CreateString(_o.GameFacebookAppUri);
    var _game_facebook_group_url = _o.GameFacebookGroupUrl == null ? default(StringOffset) : builder.CreateString(_o.GameFacebookGroupUrl);
    var _game_facebook_app_group_uri = _o.GameFacebookAppGroupUri == null ? default(StringOffset) : builder.CreateString(_o.GameFacebookAppGroupUri);
    var _game_instagram_url = _o.GameInstagramUrl == null ? default(StringOffset) : builder.CreateString(_o.GameInstagramUrl);
    var _game_instagram_app_uri = _o.GameInstagramAppUri == null ? default(StringOffset) : builder.CreateString(_o.GameInstagramAppUri);
    var _support_url = _o.SupportUrl == null ? default(StringOffset) : builder.CreateString(_o.SupportUrl);
    return CreateSocialConfig(
      builder,
      _uid,
      _facebook_url,
      _forum_url,
      _twitter_url,
      _instagram_url,
      _youtube_url,
      _privacy_policy_url,
      _terms_of_service_url,
      _snapimals_url,
      _snapimals_app_url,
      _game_discord_group_url,
      _game_community_page_url,
      _game_facebook_url,
      _game_facebook_app_uri,
      _game_facebook_group_url,
      _game_facebook_app_group_uri,
      _game_instagram_url,
      _game_instagram_app_uri,
      _support_url,
      _o.AskForLivesCooldown,
      _o.NudgeCooldown);
  }
}

public class SocialConfigT
{
  public string Uid { get; set; }
  public string FacebookUrl { get; set; }
  public string ForumUrl { get; set; }
  public string TwitterUrl { get; set; }
  public string InstagramUrl { get; set; }
  public string YoutubeUrl { get; set; }
  public string PrivacyPolicyUrl { get; set; }
  public string TermsOfServiceUrl { get; set; }
  public string SnapimalsUrl { get; set; }
  public string SnapimalsAppUrl { get; set; }
  public string GameDiscordGroupUrl { get; set; }
  public string GameCommunityPageUrl { get; set; }
  public string GameFacebookUrl { get; set; }
  public string GameFacebookAppUri { get; set; }
  public string GameFacebookGroupUrl { get; set; }
  public string GameFacebookAppGroupUri { get; set; }
  public string GameInstagramUrl { get; set; }
  public string GameInstagramAppUri { get; set; }
  public string SupportUrl { get; set; }
  public int AskForLivesCooldown { get; set; }
  public int NudgeCooldown { get; set; }

  public SocialConfigT() {
    this.Uid = null;
    this.FacebookUrl = null;
    this.ForumUrl = null;
    this.TwitterUrl = null;
    this.InstagramUrl = null;
    this.YoutubeUrl = null;
    this.PrivacyPolicyUrl = null;
    this.TermsOfServiceUrl = null;
    this.SnapimalsUrl = null;
    this.SnapimalsAppUrl = null;
    this.GameDiscordGroupUrl = null;
    this.GameCommunityPageUrl = null;
    this.GameFacebookUrl = null;
    this.GameFacebookAppUri = null;
    this.GameFacebookGroupUrl = null;
    this.GameFacebookAppGroupUri = null;
    this.GameInstagramUrl = null;
    this.GameInstagramAppUri = null;
    this.SupportUrl = null;
    this.AskForLivesCooldown = 0;
    this.NudgeCooldown = 0;
  }
}

public struct SocialConfigDict : IFlatbufferConfigDict<SocialConfig, SocialConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SocialConfigDict GetRootAsSocialConfigDict(ByteBuffer _bb) { return GetRootAsSocialConfigDict(_bb, new SocialConfigDict()); }
  public static SocialConfigDict GetRootAsSocialConfigDict(ByteBuffer _bb, SocialConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SocialConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SocialConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SocialConfig?)(new FBConfig.SocialConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SocialConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SocialConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SocialConfigDict> CreateSocialConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SocialConfigDict.AddValues(builder, valuesOffset);
    return SocialConfigDict.EndSocialConfigDict(builder);
  }

  public static void StartSocialConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SocialConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SocialConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SocialConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SocialConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SocialConfigDict> EndSocialConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SocialConfigDict>(o);
  }
  public static void FinishSocialConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SocialConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSocialConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SocialConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SocialConfigDictT UnPack() {
    var _o = new SocialConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SocialConfigDictT _o) {
    _o.Values = new List<FBConfig.SocialConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SocialConfigDict> Pack(FlatBufferBuilder builder, SocialConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SocialConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SocialConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SocialConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSocialConfigDict(
      builder,
      _values);
  }
}

public class SocialConfigDictT
{
  public List<FBConfig.SocialConfigT> Values { get; set; }

  public SocialConfigDictT() {
    this.Values = null;
  }
  public static SocialConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SocialConfigDict.GetRootAsSocialConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SocialConfigDict.FinishSocialConfigDictBuffer(fbb, SocialConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
