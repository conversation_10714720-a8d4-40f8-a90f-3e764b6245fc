// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ChallengeTriviaConfig : IFlatbufferConfig<ChallengeTriviaConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeTriviaConfig GetRootAsChallengeTriviaConfig(ByteBuffer _bb) { return GetRootAsChallengeTriviaConfig(_bb, new ChallengeTriviaConfig()); }
  public static ChallengeTriviaConfig GetRootAsChallengeTriviaConfig(ByteBuffer _bb, ChallengeTriviaConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeTriviaConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Country { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCountryBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetCountryBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetCountryArray() { return __p.__vector_as_array<byte>(6); }
  public string Question { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetQuestionBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetQuestionBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetQuestionArray() { return __p.__vector_as_array<byte>(8); }
  public string QuestionImageUrl { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetQuestionImageUrlBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetQuestionImageUrlBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetQuestionImageUrlArray() { return __p.__vector_as_array<byte>(10); }
  public string AnswerImageUrl { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAnswerImageUrlBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetAnswerImageUrlBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetAnswerImageUrlArray() { return __p.__vector_as_array<byte>(12); }
  public string FirstAnswer { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFirstAnswerBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetFirstAnswerBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetFirstAnswerArray() { return __p.__vector_as_array<byte>(14); }
  public string SecondAnswer { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSecondAnswerBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetSecondAnswerBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetSecondAnswerArray() { return __p.__vector_as_array<byte>(16); }
  public string ThirdAnswer { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThirdAnswerBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetThirdAnswerBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetThirdAnswerArray() { return __p.__vector_as_array<byte>(18); }
  public string FourthAnswer { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFourthAnswerBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetFourthAnswerBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetFourthAnswerArray() { return __p.__vector_as_array<byte>(20); }
  public string CorrectExplanation { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCorrectExplanationBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetCorrectExplanationBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetCorrectExplanationArray() { return __p.__vector_as_array<byte>(22); }
  public string IncorrectExplanation { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIncorrectExplanationBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetIncorrectExplanationBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetIncorrectExplanationArray() { return __p.__vector_as_array<byte>(24); }
  public FBConfig.DictStringInt? BaseReward(int j) { int o = __p.__offset(26); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int BaseRewardLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.ChallengeTriviaConfig> CreateChallengeTriviaConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset countryOffset = default(StringOffset),
      StringOffset questionOffset = default(StringOffset),
      StringOffset question_image_urlOffset = default(StringOffset),
      StringOffset answer_image_urlOffset = default(StringOffset),
      StringOffset first_answerOffset = default(StringOffset),
      StringOffset second_answerOffset = default(StringOffset),
      StringOffset third_answerOffset = default(StringOffset),
      StringOffset fourth_answerOffset = default(StringOffset),
      StringOffset correct_explanationOffset = default(StringOffset),
      StringOffset incorrect_explanationOffset = default(StringOffset),
      VectorOffset base_rewardOffset = default(VectorOffset)) {
    builder.StartTable(12);
    ChallengeTriviaConfig.AddBaseReward(builder, base_rewardOffset);
    ChallengeTriviaConfig.AddIncorrectExplanation(builder, incorrect_explanationOffset);
    ChallengeTriviaConfig.AddCorrectExplanation(builder, correct_explanationOffset);
    ChallengeTriviaConfig.AddFourthAnswer(builder, fourth_answerOffset);
    ChallengeTriviaConfig.AddThirdAnswer(builder, third_answerOffset);
    ChallengeTriviaConfig.AddSecondAnswer(builder, second_answerOffset);
    ChallengeTriviaConfig.AddFirstAnswer(builder, first_answerOffset);
    ChallengeTriviaConfig.AddAnswerImageUrl(builder, answer_image_urlOffset);
    ChallengeTriviaConfig.AddQuestionImageUrl(builder, question_image_urlOffset);
    ChallengeTriviaConfig.AddQuestion(builder, questionOffset);
    ChallengeTriviaConfig.AddCountry(builder, countryOffset);
    ChallengeTriviaConfig.AddUid(builder, uidOffset);
    return ChallengeTriviaConfig.EndChallengeTriviaConfig(builder);
  }

  public static void StartChallengeTriviaConfig(FlatBufferBuilder builder) { builder.StartTable(12); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCountry(FlatBufferBuilder builder, StringOffset countryOffset) { builder.AddOffset(1, countryOffset.Value, 0); }
  public static void AddQuestion(FlatBufferBuilder builder, StringOffset questionOffset) { builder.AddOffset(2, questionOffset.Value, 0); }
  public static void AddQuestionImageUrl(FlatBufferBuilder builder, StringOffset questionImageUrlOffset) { builder.AddOffset(3, questionImageUrlOffset.Value, 0); }
  public static void AddAnswerImageUrl(FlatBufferBuilder builder, StringOffset answerImageUrlOffset) { builder.AddOffset(4, answerImageUrlOffset.Value, 0); }
  public static void AddFirstAnswer(FlatBufferBuilder builder, StringOffset firstAnswerOffset) { builder.AddOffset(5, firstAnswerOffset.Value, 0); }
  public static void AddSecondAnswer(FlatBufferBuilder builder, StringOffset secondAnswerOffset) { builder.AddOffset(6, secondAnswerOffset.Value, 0); }
  public static void AddThirdAnswer(FlatBufferBuilder builder, StringOffset thirdAnswerOffset) { builder.AddOffset(7, thirdAnswerOffset.Value, 0); }
  public static void AddFourthAnswer(FlatBufferBuilder builder, StringOffset fourthAnswerOffset) { builder.AddOffset(8, fourthAnswerOffset.Value, 0); }
  public static void AddCorrectExplanation(FlatBufferBuilder builder, StringOffset correctExplanationOffset) { builder.AddOffset(9, correctExplanationOffset.Value, 0); }
  public static void AddIncorrectExplanation(FlatBufferBuilder builder, StringOffset incorrectExplanationOffset) { builder.AddOffset(10, incorrectExplanationOffset.Value, 0); }
  public static void AddBaseReward(FlatBufferBuilder builder, VectorOffset baseRewardOffset) { builder.AddOffset(11, baseRewardOffset.Value, 0); }
  public static VectorOffset CreateBaseRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBaseRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBaseRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBaseRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBaseRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ChallengeTriviaConfig> EndChallengeTriviaConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ChallengeTriviaConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfChallengeTriviaConfig(FlatBufferBuilder builder, Offset<ChallengeTriviaConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ChallengeTriviaConfig> o1, Offset<ChallengeTriviaConfig> o2) =>
        new ChallengeTriviaConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ChallengeTriviaConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ChallengeTriviaConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ChallengeTriviaConfig obj_ = new ChallengeTriviaConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ChallengeTriviaConfigT UnPack() {
    var _o = new ChallengeTriviaConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeTriviaConfigT _o) {
    _o.Uid = this.Uid;
    _o.Country = this.Country;
    _o.Question = this.Question;
    _o.QuestionImageUrl = this.QuestionImageUrl;
    _o.AnswerImageUrl = this.AnswerImageUrl;
    _o.FirstAnswer = this.FirstAnswer;
    _o.SecondAnswer = this.SecondAnswer;
    _o.ThirdAnswer = this.ThirdAnswer;
    _o.FourthAnswer = this.FourthAnswer;
    _o.CorrectExplanation = this.CorrectExplanation;
    _o.IncorrectExplanation = this.IncorrectExplanation;
    _o.BaseReward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.BaseRewardLength; ++_j) {_o.BaseReward.Add(this.BaseReward(_j).HasValue ? this.BaseReward(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ChallengeTriviaConfig> Pack(FlatBufferBuilder builder, ChallengeTriviaConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeTriviaConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _country = _o.Country == null ? default(StringOffset) : builder.CreateString(_o.Country);
    var _question = _o.Question == null ? default(StringOffset) : builder.CreateString(_o.Question);
    var _question_image_url = _o.QuestionImageUrl == null ? default(StringOffset) : builder.CreateString(_o.QuestionImageUrl);
    var _answer_image_url = _o.AnswerImageUrl == null ? default(StringOffset) : builder.CreateString(_o.AnswerImageUrl);
    var _first_answer = _o.FirstAnswer == null ? default(StringOffset) : builder.CreateString(_o.FirstAnswer);
    var _second_answer = _o.SecondAnswer == null ? default(StringOffset) : builder.CreateString(_o.SecondAnswer);
    var _third_answer = _o.ThirdAnswer == null ? default(StringOffset) : builder.CreateString(_o.ThirdAnswer);
    var _fourth_answer = _o.FourthAnswer == null ? default(StringOffset) : builder.CreateString(_o.FourthAnswer);
    var _correct_explanation = _o.CorrectExplanation == null ? default(StringOffset) : builder.CreateString(_o.CorrectExplanation);
    var _incorrect_explanation = _o.IncorrectExplanation == null ? default(StringOffset) : builder.CreateString(_o.IncorrectExplanation);
    var _base_reward = default(VectorOffset);
    if (_o.BaseReward != null) {
      var __base_reward = new Offset<FBConfig.DictStringInt>[_o.BaseReward.Count];
      for (var _j = 0; _j < __base_reward.Length; ++_j) { __base_reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.BaseReward[_j]); }
      _base_reward = CreateBaseRewardVector(builder, __base_reward);
    }
    return CreateChallengeTriviaConfig(
      builder,
      _uid,
      _country,
      _question,
      _question_image_url,
      _answer_image_url,
      _first_answer,
      _second_answer,
      _third_answer,
      _fourth_answer,
      _correct_explanation,
      _incorrect_explanation,
      _base_reward);
  }
}

public class ChallengeTriviaConfigT
{
  public string Uid { get; set; }
  public string Country { get; set; }
  public string Question { get; set; }
  public string QuestionImageUrl { get; set; }
  public string AnswerImageUrl { get; set; }
  public string FirstAnswer { get; set; }
  public string SecondAnswer { get; set; }
  public string ThirdAnswer { get; set; }
  public string FourthAnswer { get; set; }
  public string CorrectExplanation { get; set; }
  public string IncorrectExplanation { get; set; }
  public List<FBConfig.DictStringIntT> BaseReward { get; set; }

  public ChallengeTriviaConfigT() {
    this.Uid = null;
    this.Country = null;
    this.Question = null;
    this.QuestionImageUrl = null;
    this.AnswerImageUrl = null;
    this.FirstAnswer = null;
    this.SecondAnswer = null;
    this.ThirdAnswer = null;
    this.FourthAnswer = null;
    this.CorrectExplanation = null;
    this.IncorrectExplanation = null;
    this.BaseReward = null;
  }
}

public struct ChallengeTriviaConfigDict : IFlatbufferConfigDict<ChallengeTriviaConfig, ChallengeTriviaConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeTriviaConfigDict GetRootAsChallengeTriviaConfigDict(ByteBuffer _bb) { return GetRootAsChallengeTriviaConfigDict(_bb, new ChallengeTriviaConfigDict()); }
  public static ChallengeTriviaConfigDict GetRootAsChallengeTriviaConfigDict(ByteBuffer _bb, ChallengeTriviaConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeTriviaConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ChallengeTriviaConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ChallengeTriviaConfig?)(new FBConfig.ChallengeTriviaConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ChallengeTriviaConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ChallengeTriviaConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ChallengeTriviaConfigDict> CreateChallengeTriviaConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ChallengeTriviaConfigDict.AddValues(builder, valuesOffset);
    return ChallengeTriviaConfigDict.EndChallengeTriviaConfigDict(builder);
  }

  public static void StartChallengeTriviaConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ChallengeTriviaConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ChallengeTriviaConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ChallengeTriviaConfigDict> EndChallengeTriviaConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ChallengeTriviaConfigDict>(o);
  }
  public static void FinishChallengeTriviaConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedChallengeTriviaConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ChallengeTriviaConfigDictT UnPack() {
    var _o = new ChallengeTriviaConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeTriviaConfigDictT _o) {
    _o.Values = new List<FBConfig.ChallengeTriviaConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ChallengeTriviaConfigDict> Pack(FlatBufferBuilder builder, ChallengeTriviaConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeTriviaConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ChallengeTriviaConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ChallengeTriviaConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateChallengeTriviaConfigDict(
      builder,
      _values);
  }
}

public class ChallengeTriviaConfigDictT
{
  public List<FBConfig.ChallengeTriviaConfigT> Values { get; set; }

  public ChallengeTriviaConfigDictT() {
    this.Values = null;
  }
  public static ChallengeTriviaConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ChallengeTriviaConfigDict.GetRootAsChallengeTriviaConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ChallengeTriviaConfigDict.FinishChallengeTriviaConfigDictBuffer(fbb, ChallengeTriviaConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
