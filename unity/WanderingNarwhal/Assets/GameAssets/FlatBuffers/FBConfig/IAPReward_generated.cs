// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct IAPReward : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static IAPReward GetRootAsIAPReward(ByteBuffer _bb) { return GetRootAsIAPReward(_bb, new IAPReward()); }
  public static IAPReward GetRootAsIAPReward(ByteBuffer _bb, IAPReward obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public IAPReward __assign(int _i, <PERSON>te<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Currencies(int j) { int o = __p.__offset(4); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int CurrenciesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string Resources(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int ResourcesLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string Boosters(int j) { int o = __p.__offset(8); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int BoostersLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string Expression { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetExpressionBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetExpressionBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetExpressionArray() { return __p.__vector_as_array<byte>(10); }

  public static Offset<FBConfig.IAPReward> CreateIAPReward(FlatBufferBuilder builder,
      VectorOffset currenciesOffset = default(VectorOffset),
      VectorOffset resourcesOffset = default(VectorOffset),
      VectorOffset boostersOffset = default(VectorOffset),
      StringOffset expressionOffset = default(StringOffset)) {
    builder.StartTable(4);
    IAPReward.AddExpression(builder, expressionOffset);
    IAPReward.AddBoosters(builder, boostersOffset);
    IAPReward.AddResources(builder, resourcesOffset);
    IAPReward.AddCurrencies(builder, currenciesOffset);
    return IAPReward.EndIAPReward(builder);
  }

  public static void StartIAPReward(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddCurrencies(FlatBufferBuilder builder, VectorOffset currenciesOffset) { builder.AddOffset(0, currenciesOffset.Value, 0); }
  public static VectorOffset CreateCurrenciesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCurrenciesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCurrenciesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCurrenciesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCurrenciesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddResources(FlatBufferBuilder builder, VectorOffset resourcesOffset) { builder.AddOffset(1, resourcesOffset.Value, 0); }
  public static VectorOffset CreateResourcesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateResourcesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateResourcesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateResourcesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartResourcesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBoosters(FlatBufferBuilder builder, VectorOffset boostersOffset) { builder.AddOffset(2, boostersOffset.Value, 0); }
  public static VectorOffset CreateBoostersVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBoostersVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBoostersVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBoostersVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBoostersVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddExpression(FlatBufferBuilder builder, StringOffset expressionOffset) { builder.AddOffset(3, expressionOffset.Value, 0); }
  public static Offset<FBConfig.IAPReward> EndIAPReward(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.IAPReward>(o);
  }
  public IAPRewardT UnPack() {
    var _o = new IAPRewardT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(IAPRewardT _o) {
    _o.Currencies = new List<string>();
    for (var _j = 0; _j < this.CurrenciesLength; ++_j) {_o.Currencies.Add(this.Currencies(_j));}
    _o.Resources = new List<string>();
    for (var _j = 0; _j < this.ResourcesLength; ++_j) {_o.Resources.Add(this.Resources(_j));}
    _o.Boosters = new List<string>();
    for (var _j = 0; _j < this.BoostersLength; ++_j) {_o.Boosters.Add(this.Boosters(_j));}
    _o.Expression = this.Expression;
  }
  public static Offset<FBConfig.IAPReward> Pack(FlatBufferBuilder builder, IAPRewardT _o) {
    if (_o == null) return default(Offset<FBConfig.IAPReward>);
    var _currencies = default(VectorOffset);
    if (_o.Currencies != null) {
      var __currencies = new StringOffset[_o.Currencies.Count];
      for (var _j = 0; _j < __currencies.Length; ++_j) { __currencies[_j] = builder.CreateString(_o.Currencies[_j]); }
      _currencies = CreateCurrenciesVector(builder, __currencies);
    }
    var _resources = default(VectorOffset);
    if (_o.Resources != null) {
      var __resources = new StringOffset[_o.Resources.Count];
      for (var _j = 0; _j < __resources.Length; ++_j) { __resources[_j] = builder.CreateString(_o.Resources[_j]); }
      _resources = CreateResourcesVector(builder, __resources);
    }
    var _boosters = default(VectorOffset);
    if (_o.Boosters != null) {
      var __boosters = new StringOffset[_o.Boosters.Count];
      for (var _j = 0; _j < __boosters.Length; ++_j) { __boosters[_j] = builder.CreateString(_o.Boosters[_j]); }
      _boosters = CreateBoostersVector(builder, __boosters);
    }
    var _expression = _o.Expression == null ? default(StringOffset) : builder.CreateString(_o.Expression);
    return CreateIAPReward(
      builder,
      _currencies,
      _resources,
      _boosters,
      _expression);
  }
}

public class IAPRewardT
{
  public List<string> Currencies { get; set; }
  public List<string> Resources { get; set; }
  public List<string> Boosters { get; set; }
  public string Expression { get; set; }

  public IAPRewardT() {
    this.Currencies = null;
    this.Resources = null;
    this.Boosters = null;
    this.Expression = null;
  }
}


}
