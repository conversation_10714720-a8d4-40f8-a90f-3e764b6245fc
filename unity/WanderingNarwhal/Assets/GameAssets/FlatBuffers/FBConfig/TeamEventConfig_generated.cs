// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct TeamCoopMilestoneConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TeamCoopMilestoneConfig GetRootAsTeamCoopMilestoneConfig(ByteBuffer _bb) { return GetRootAsTeamCoopMilestoneConfig(_bb, new TeamCoopMilestoneConfig()); }
  public static TeamCoopMilestoneConfig GetRootAsTeamCoopMilestoneConfig(ByteBuffer _bb, TeamCoopMilestoneConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TeamCoopMilestoneConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int Scores { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateScores(int scores) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, scores); return true; } else { return false; } }
  public string Rewards { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRewardsBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetRewardsBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetRewardsArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.TeamCoopMilestoneConfig> CreateTeamCoopMilestoneConfig(FlatBufferBuilder builder,
      int scores = 0,
      StringOffset rewardsOffset = default(StringOffset)) {
    builder.StartTable(2);
    TeamCoopMilestoneConfig.AddRewards(builder, rewardsOffset);
    TeamCoopMilestoneConfig.AddScores(builder, scores);
    return TeamCoopMilestoneConfig.EndTeamCoopMilestoneConfig(builder);
  }

  public static void StartTeamCoopMilestoneConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddScores(FlatBufferBuilder builder, int scores) { builder.AddInt(0, scores, 0); }
  public static void AddRewards(FlatBufferBuilder builder, StringOffset rewardsOffset) { builder.AddOffset(1, rewardsOffset.Value, 0); }
  public static Offset<FBConfig.TeamCoopMilestoneConfig> EndTeamCoopMilestoneConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TeamCoopMilestoneConfig>(o);
  }
  public TeamCoopMilestoneConfigT UnPack() {
    var _o = new TeamCoopMilestoneConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TeamCoopMilestoneConfigT _o) {
    _o.Scores = this.Scores;
    _o.Rewards = this.Rewards;
  }
  public static Offset<FBConfig.TeamCoopMilestoneConfig> Pack(FlatBufferBuilder builder, TeamCoopMilestoneConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.TeamCoopMilestoneConfig>);
    var _rewards = _o.Rewards == null ? default(StringOffset) : builder.CreateString(_o.Rewards);
    return CreateTeamCoopMilestoneConfig(
      builder,
      _o.Scores,
      _rewards);
  }
}

public class TeamCoopMilestoneConfigT
{
  public int Scores { get; set; }
  public string Rewards { get; set; }

  public TeamCoopMilestoneConfigT() {
    this.Scores = 0;
    this.Rewards = null;
  }
}

public struct TeamEventConfig : IFlatbufferConfig<TeamEventConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TeamEventConfig GetRootAsTeamEventConfig(ByteBuffer _bb) { return GetRootAsTeamEventConfig(_bb, new TeamEventConfig()); }
  public static TeamEventConfig GetRootAsTeamEventConfig(ByteBuffer _bb, TeamEventConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TeamEventConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string SchedulingType { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSchedulingTypeBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetSchedulingTypeBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetSchedulingTypeArray() { return __p.__vector_as_array<byte>(6); }
  public FBConfig.DayMonthYear? AbsoluteStartTime { get { int o = __p.__offset(8); return o != 0 ? (FBConfig.DayMonthYear?)(new FBConfig.DayMonthYear()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int DurationInDays { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDurationInDays(int duration_in_days) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, duration_in_days); return true; } else { return false; } }
  public string WeekdaysSchedule { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetWeekdaysScheduleBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetWeekdaysScheduleBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetWeekdaysScheduleArray() { return __p.__vector_as_array<byte>(12); }
  public int RelativeSortIndex { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRelativeSortIndex(int relative_sort_index) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, relative_sort_index); return true; } else { return false; } }
  public string RequiredLevelUid { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRequiredLevelUidBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetRequiredLevelUidBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetRequiredLevelUidArray() { return __p.__vector_as_array<byte>(16); }
  public string MainTitleLocTextId { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMainTitleLocTextIdBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetMainTitleLocTextIdBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetMainTitleLocTextIdArray() { return __p.__vector_as_array<byte>(18); }
  public FBConfig.TeamCoopMilestoneConfig? MilestoneConfigs(int j) { int o = __p.__offset(20); return o != 0 ? (FBConfig.TeamCoopMilestoneConfig?)(new FBConfig.TeamCoopMilestoneConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int MilestoneConfigsLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string TopPlayersRewards(int j) { int o = __p.__offset(22); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int TopPlayersRewardsLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int ScoreForLevelDifficulty(int j) { int o = __p.__offset(24); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int ScoreForLevelDifficultyLength { get { int o = __p.__offset(24); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetScoreForLevelDifficultyBytes() { return __p.__vector_as_span<int>(24, 4); }
#else
  public ArraySegment<byte>? GetScoreForLevelDifficultyBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public int[] GetScoreForLevelDifficultyArray() { return __p.__vector_as_array<int>(24); }
  public bool MutateScoreForLevelDifficulty(int j, int score_for_level_difficulty) { int o = __p.__offset(24); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, score_for_level_difficulty); return true; } else { return false; } }
  public string TopTeamsRewards(int j) { int o = __p.__offset(26); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int TopTeamsRewardsLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string TeamEventType { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTeamEventTypeBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetTeamEventTypeBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetTeamEventTypeArray() { return __p.__vector_as_array<byte>(28); }

  public static Offset<FBConfig.TeamEventConfig> CreateTeamEventConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset scheduling_typeOffset = default(StringOffset),
      Offset<FBConfig.DayMonthYear> absolute_start_timeOffset = default(Offset<FBConfig.DayMonthYear>),
      int duration_in_days = 0,
      StringOffset weekdays_scheduleOffset = default(StringOffset),
      int relative_sort_index = 0,
      StringOffset required_level_uidOffset = default(StringOffset),
      StringOffset main_title_loc_text_idOffset = default(StringOffset),
      VectorOffset milestone_configsOffset = default(VectorOffset),
      VectorOffset top_players_rewardsOffset = default(VectorOffset),
      VectorOffset score_for_level_difficultyOffset = default(VectorOffset),
      VectorOffset top_teams_rewardsOffset = default(VectorOffset),
      StringOffset team_event_typeOffset = default(StringOffset)) {
    builder.StartTable(13);
    TeamEventConfig.AddTeamEventType(builder, team_event_typeOffset);
    TeamEventConfig.AddTopTeamsRewards(builder, top_teams_rewardsOffset);
    TeamEventConfig.AddScoreForLevelDifficulty(builder, score_for_level_difficultyOffset);
    TeamEventConfig.AddTopPlayersRewards(builder, top_players_rewardsOffset);
    TeamEventConfig.AddMilestoneConfigs(builder, milestone_configsOffset);
    TeamEventConfig.AddMainTitleLocTextId(builder, main_title_loc_text_idOffset);
    TeamEventConfig.AddRequiredLevelUid(builder, required_level_uidOffset);
    TeamEventConfig.AddRelativeSortIndex(builder, relative_sort_index);
    TeamEventConfig.AddWeekdaysSchedule(builder, weekdays_scheduleOffset);
    TeamEventConfig.AddDurationInDays(builder, duration_in_days);
    TeamEventConfig.AddAbsoluteStartTime(builder, absolute_start_timeOffset);
    TeamEventConfig.AddSchedulingType(builder, scheduling_typeOffset);
    TeamEventConfig.AddUid(builder, uidOffset);
    return TeamEventConfig.EndTeamEventConfig(builder);
  }

  public static void StartTeamEventConfig(FlatBufferBuilder builder) { builder.StartTable(13); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddSchedulingType(FlatBufferBuilder builder, StringOffset schedulingTypeOffset) { builder.AddOffset(1, schedulingTypeOffset.Value, 0); }
  public static void AddAbsoluteStartTime(FlatBufferBuilder builder, Offset<FBConfig.DayMonthYear> absoluteStartTimeOffset) { builder.AddOffset(2, absoluteStartTimeOffset.Value, 0); }
  public static void AddDurationInDays(FlatBufferBuilder builder, int durationInDays) { builder.AddInt(3, durationInDays, 0); }
  public static void AddWeekdaysSchedule(FlatBufferBuilder builder, StringOffset weekdaysScheduleOffset) { builder.AddOffset(4, weekdaysScheduleOffset.Value, 0); }
  public static void AddRelativeSortIndex(FlatBufferBuilder builder, int relativeSortIndex) { builder.AddInt(5, relativeSortIndex, 0); }
  public static void AddRequiredLevelUid(FlatBufferBuilder builder, StringOffset requiredLevelUidOffset) { builder.AddOffset(6, requiredLevelUidOffset.Value, 0); }
  public static void AddMainTitleLocTextId(FlatBufferBuilder builder, StringOffset mainTitleLocTextIdOffset) { builder.AddOffset(7, mainTitleLocTextIdOffset.Value, 0); }
  public static void AddMilestoneConfigs(FlatBufferBuilder builder, VectorOffset milestoneConfigsOffset) { builder.AddOffset(8, milestoneConfigsOffset.Value, 0); }
  public static VectorOffset CreateMilestoneConfigsVector(FlatBufferBuilder builder, Offset<FBConfig.TeamCoopMilestoneConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateMilestoneConfigsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TeamCoopMilestoneConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateMilestoneConfigsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TeamCoopMilestoneConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateMilestoneConfigsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TeamCoopMilestoneConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartMilestoneConfigsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTopPlayersRewards(FlatBufferBuilder builder, VectorOffset topPlayersRewardsOffset) { builder.AddOffset(9, topPlayersRewardsOffset.Value, 0); }
  public static VectorOffset CreateTopPlayersRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTopPlayersRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTopPlayersRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTopPlayersRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTopPlayersRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddScoreForLevelDifficulty(FlatBufferBuilder builder, VectorOffset scoreForLevelDifficultyOffset) { builder.AddOffset(10, scoreForLevelDifficultyOffset.Value, 0); }
  public static VectorOffset CreateScoreForLevelDifficultyVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateScoreForLevelDifficultyVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreForLevelDifficultyVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreForLevelDifficultyVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartScoreForLevelDifficultyVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTopTeamsRewards(FlatBufferBuilder builder, VectorOffset topTeamsRewardsOffset) { builder.AddOffset(11, topTeamsRewardsOffset.Value, 0); }
  public static VectorOffset CreateTopTeamsRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTopTeamsRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTopTeamsRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTopTeamsRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTopTeamsRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTeamEventType(FlatBufferBuilder builder, StringOffset teamEventTypeOffset) { builder.AddOffset(12, teamEventTypeOffset.Value, 0); }
  public static Offset<FBConfig.TeamEventConfig> EndTeamEventConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.TeamEventConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfTeamEventConfig(FlatBufferBuilder builder, Offset<TeamEventConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<TeamEventConfig> o1, Offset<TeamEventConfig> o2) =>
        new TeamEventConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new TeamEventConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static TeamEventConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    TeamEventConfig obj_ = new TeamEventConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public TeamEventConfigT UnPack() {
    var _o = new TeamEventConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TeamEventConfigT _o) {
    _o.Uid = this.Uid;
    _o.SchedulingType = this.SchedulingType;
    _o.AbsoluteStartTime = this.AbsoluteStartTime.HasValue ? this.AbsoluteStartTime.Value.UnPack() : null;
    _o.DurationInDays = this.DurationInDays;
    _o.WeekdaysSchedule = this.WeekdaysSchedule;
    _o.RelativeSortIndex = this.RelativeSortIndex;
    _o.RequiredLevelUid = this.RequiredLevelUid;
    _o.MainTitleLocTextId = this.MainTitleLocTextId;
    _o.MilestoneConfigs = new List<FBConfig.TeamCoopMilestoneConfigT>();
    for (var _j = 0; _j < this.MilestoneConfigsLength; ++_j) {_o.MilestoneConfigs.Add(this.MilestoneConfigs(_j).HasValue ? this.MilestoneConfigs(_j).Value.UnPack() : null);}
    _o.TopPlayersRewards = new List<string>();
    for (var _j = 0; _j < this.TopPlayersRewardsLength; ++_j) {_o.TopPlayersRewards.Add(this.TopPlayersRewards(_j));}
    _o.ScoreForLevelDifficulty = new List<int>();
    for (var _j = 0; _j < this.ScoreForLevelDifficultyLength; ++_j) {_o.ScoreForLevelDifficulty.Add(this.ScoreForLevelDifficulty(_j));}
    _o.TopTeamsRewards = new List<string>();
    for (var _j = 0; _j < this.TopTeamsRewardsLength; ++_j) {_o.TopTeamsRewards.Add(this.TopTeamsRewards(_j));}
    _o.TeamEventType = this.TeamEventType;
  }
  public static Offset<FBConfig.TeamEventConfig> Pack(FlatBufferBuilder builder, TeamEventConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.TeamEventConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _scheduling_type = _o.SchedulingType == null ? default(StringOffset) : builder.CreateString(_o.SchedulingType);
    var _absolute_start_time = _o.AbsoluteStartTime == null ? default(Offset<FBConfig.DayMonthYear>) : FBConfig.DayMonthYear.Pack(builder, _o.AbsoluteStartTime);
    var _weekdays_schedule = _o.WeekdaysSchedule == null ? default(StringOffset) : builder.CreateString(_o.WeekdaysSchedule);
    var _required_level_uid = _o.RequiredLevelUid == null ? default(StringOffset) : builder.CreateString(_o.RequiredLevelUid);
    var _main_title_loc_text_id = _o.MainTitleLocTextId == null ? default(StringOffset) : builder.CreateString(_o.MainTitleLocTextId);
    var _milestone_configs = default(VectorOffset);
    if (_o.MilestoneConfigs != null) {
      var __milestone_configs = new Offset<FBConfig.TeamCoopMilestoneConfig>[_o.MilestoneConfigs.Count];
      for (var _j = 0; _j < __milestone_configs.Length; ++_j) { __milestone_configs[_j] = FBConfig.TeamCoopMilestoneConfig.Pack(builder, _o.MilestoneConfigs[_j]); }
      _milestone_configs = CreateMilestoneConfigsVector(builder, __milestone_configs);
    }
    var _top_players_rewards = default(VectorOffset);
    if (_o.TopPlayersRewards != null) {
      var __top_players_rewards = new StringOffset[_o.TopPlayersRewards.Count];
      for (var _j = 0; _j < __top_players_rewards.Length; ++_j) { __top_players_rewards[_j] = builder.CreateString(_o.TopPlayersRewards[_j]); }
      _top_players_rewards = CreateTopPlayersRewardsVector(builder, __top_players_rewards);
    }
    var _score_for_level_difficulty = default(VectorOffset);
    if (_o.ScoreForLevelDifficulty != null) {
      var __score_for_level_difficulty = _o.ScoreForLevelDifficulty.ToArray();
      _score_for_level_difficulty = CreateScoreForLevelDifficultyVector(builder, __score_for_level_difficulty);
    }
    var _top_teams_rewards = default(VectorOffset);
    if (_o.TopTeamsRewards != null) {
      var __top_teams_rewards = new StringOffset[_o.TopTeamsRewards.Count];
      for (var _j = 0; _j < __top_teams_rewards.Length; ++_j) { __top_teams_rewards[_j] = builder.CreateString(_o.TopTeamsRewards[_j]); }
      _top_teams_rewards = CreateTopTeamsRewardsVector(builder, __top_teams_rewards);
    }
    var _team_event_type = _o.TeamEventType == null ? default(StringOffset) : builder.CreateString(_o.TeamEventType);
    return CreateTeamEventConfig(
      builder,
      _uid,
      _scheduling_type,
      _absolute_start_time,
      _o.DurationInDays,
      _weekdays_schedule,
      _o.RelativeSortIndex,
      _required_level_uid,
      _main_title_loc_text_id,
      _milestone_configs,
      _top_players_rewards,
      _score_for_level_difficulty,
      _top_teams_rewards,
      _team_event_type);
  }
}

public class TeamEventConfigT
{
  public string Uid { get; set; }
  public string SchedulingType { get; set; }
  public FBConfig.DayMonthYearT AbsoluteStartTime { get; set; }
  public int DurationInDays { get; set; }
  public string WeekdaysSchedule { get; set; }
  public int RelativeSortIndex { get; set; }
  public string RequiredLevelUid { get; set; }
  public string MainTitleLocTextId { get; set; }
  public List<FBConfig.TeamCoopMilestoneConfigT> MilestoneConfigs { get; set; }
  public List<string> TopPlayersRewards { get; set; }
  public List<int> ScoreForLevelDifficulty { get; set; }
  public List<string> TopTeamsRewards { get; set; }
  public string TeamEventType { get; set; }

  public TeamEventConfigT() {
    this.Uid = null;
    this.SchedulingType = null;
    this.AbsoluteStartTime = null;
    this.DurationInDays = 0;
    this.WeekdaysSchedule = null;
    this.RelativeSortIndex = 0;
    this.RequiredLevelUid = null;
    this.MainTitleLocTextId = null;
    this.MilestoneConfigs = null;
    this.TopPlayersRewards = null;
    this.ScoreForLevelDifficulty = null;
    this.TopTeamsRewards = null;
    this.TeamEventType = null;
  }
}

public struct TeamEventConfigDict : IFlatbufferConfigDict<TeamEventConfig, TeamEventConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static TeamEventConfigDict GetRootAsTeamEventConfigDict(ByteBuffer _bb) { return GetRootAsTeamEventConfigDict(_bb, new TeamEventConfigDict()); }
  public static TeamEventConfigDict GetRootAsTeamEventConfigDict(ByteBuffer _bb, TeamEventConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public TeamEventConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.TeamEventConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.TeamEventConfig?)(new FBConfig.TeamEventConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.TeamEventConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.TeamEventConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.TeamEventConfigDict> CreateTeamEventConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    TeamEventConfigDict.AddValues(builder, valuesOffset);
    return TeamEventConfigDict.EndTeamEventConfigDict(builder);
  }

  public static void StartTeamEventConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.TeamEventConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.TeamEventConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.TeamEventConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.TeamEventConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.TeamEventConfigDict> EndTeamEventConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.TeamEventConfigDict>(o);
  }
  public static void FinishTeamEventConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TeamEventConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedTeamEventConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.TeamEventConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public TeamEventConfigDictT UnPack() {
    var _o = new TeamEventConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(TeamEventConfigDictT _o) {
    _o.Values = new List<FBConfig.TeamEventConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.TeamEventConfigDict> Pack(FlatBufferBuilder builder, TeamEventConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.TeamEventConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.TeamEventConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.TeamEventConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateTeamEventConfigDict(
      builder,
      _values);
  }
}

public class TeamEventConfigDictT
{
  public List<FBConfig.TeamEventConfigT> Values { get; set; }

  public TeamEventConfigDictT() {
    this.Values = null;
  }
  public static TeamEventConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return TeamEventConfigDict.GetRootAsTeamEventConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    TeamEventConfigDict.FinishTeamEventConfigDictBuffer(fbb, TeamEventConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
