// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct NewsConfig : IFlatbufferConfig<NewsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static NewsConfig GetRootAsNewsConfig(ByteBuffer _bb) { return GetRootAsNewsConfig(_bb, new NewsConfig()); }
  public static NewsConfig GetRootAsNewsConfig(ByteBuffer _bb, NewsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public NewsConfig __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Title { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(6); }
  public string Subtitle { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubtitleBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetSubtitleBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetSubtitleArray() { return __p.__vector_as_array<byte>(8); }
  public string Text { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTextBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetTextBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetTextArray() { return __p.__vector_as_array<byte>(10); }
  public string ImageText { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetImageTextBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetImageTextBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetImageTextArray() { return __p.__vector_as_array<byte>(12); }
  public string ButtonText { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetButtonTextBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetButtonTextBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetButtonTextArray() { return __p.__vector_as_array<byte>(14); }
  public FBConfig.DayMonthYear? Date { get { int o = __p.__offset(16); return o != 0 ? (FBConfig.DayMonthYear?)(new FBConfig.DayMonthYear()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string ImageLink { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetImageLinkBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetImageLinkBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetImageLinkArray() { return __p.__vector_as_array<byte>(18); }
  public string Reward { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRewardBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetRewardBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetRewardArray() { return __p.__vector_as_array<byte>(20); }
  public string Predicate { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPredicateBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetPredicateBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetPredicateArray() { return __p.__vector_as_array<byte>(22); }
  public string TakeMeAction { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTakeMeActionBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetTakeMeActionBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetTakeMeActionArray() { return __p.__vector_as_array<byte>(24); }
  public FBConfig.DictStringString? TakeMeParamsFb(int j) { int o = __p.__offset(26); return o != 0 ? (FBConfig.DictStringString?)(new FBConfig.DictStringString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TakeMeParamsFbLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int ShowsLimit { get { int o = __p.__offset(28); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateShowsLimit(int shows_limit) { int o = __p.__offset(28); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, shows_limit); return true; } else { return false; } }
  public int MinDaysBetweenShows { get { int o = __p.__offset(30); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMinDaysBetweenShows(int min_days_between_shows) { int o = __p.__offset(30); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, min_days_between_shows); return true; } else { return false; } }
  public string OutlineColor { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetOutlineColorBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetOutlineColorBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetOutlineColorArray() { return __p.__vector_as_array<byte>(32); }
  public bool ShouldOpenViaSettings { get { int o = __p.__offset(34); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateShouldOpenViaSettings(bool should_open_via_settings) { int o = __p.__offset(34); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(should_open_via_settings ? 1 : 0)); return true; } else { return false; } }
  public string ButtonIconLocalImage { get { int o = __p.__offset(36); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetButtonIconLocalImageBytes() { return __p.__vector_as_span<byte>(36, 1); }
#else
  public ArraySegment<byte>? GetButtonIconLocalImageBytes() { return __p.__vector_as_arraysegment(36); }
#endif
  public byte[] GetButtonIconLocalImageArray() { return __p.__vector_as_array<byte>(36); }
  public string ButtonIconImageUrl { get { int o = __p.__offset(38); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetButtonIconImageUrlBytes() { return __p.__vector_as_span<byte>(38, 1); }
#else
  public ArraySegment<byte>? GetButtonIconImageUrlBytes() { return __p.__vector_as_arraysegment(38); }
#endif
  public byte[] GetButtonIconImageUrlArray() { return __p.__vector_as_array<byte>(38); }

  public static Offset<FBConfig.NewsConfig> CreateNewsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset subtitleOffset = default(StringOffset),
      StringOffset textOffset = default(StringOffset),
      StringOffset image_textOffset = default(StringOffset),
      StringOffset button_textOffset = default(StringOffset),
      Offset<FBConfig.DayMonthYear> dateOffset = default(Offset<FBConfig.DayMonthYear>),
      StringOffset image_linkOffset = default(StringOffset),
      StringOffset rewardOffset = default(StringOffset),
      StringOffset predicateOffset = default(StringOffset),
      StringOffset take_me_actionOffset = default(StringOffset),
      VectorOffset take_me_params_fbOffset = default(VectorOffset),
      int shows_limit = 0,
      int min_days_between_shows = 0,
      StringOffset outline_colorOffset = default(StringOffset),
      bool should_open_via_settings = false,
      StringOffset button_icon_local_imageOffset = default(StringOffset),
      StringOffset button_icon_image_urlOffset = default(StringOffset)) {
    builder.StartTable(18);
    NewsConfig.AddButtonIconImageUrl(builder, button_icon_image_urlOffset);
    NewsConfig.AddButtonIconLocalImage(builder, button_icon_local_imageOffset);
    NewsConfig.AddOutlineColor(builder, outline_colorOffset);
    NewsConfig.AddMinDaysBetweenShows(builder, min_days_between_shows);
    NewsConfig.AddShowsLimit(builder, shows_limit);
    NewsConfig.AddTakeMeParamsFb(builder, take_me_params_fbOffset);
    NewsConfig.AddTakeMeAction(builder, take_me_actionOffset);
    NewsConfig.AddPredicate(builder, predicateOffset);
    NewsConfig.AddReward(builder, rewardOffset);
    NewsConfig.AddImageLink(builder, image_linkOffset);
    NewsConfig.AddDate(builder, dateOffset);
    NewsConfig.AddButtonText(builder, button_textOffset);
    NewsConfig.AddImageText(builder, image_textOffset);
    NewsConfig.AddText(builder, textOffset);
    NewsConfig.AddSubtitle(builder, subtitleOffset);
    NewsConfig.AddTitle(builder, titleOffset);
    NewsConfig.AddUid(builder, uidOffset);
    NewsConfig.AddShouldOpenViaSettings(builder, should_open_via_settings);
    return NewsConfig.EndNewsConfig(builder);
  }

  public static void StartNewsConfig(FlatBufferBuilder builder) { builder.StartTable(18); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(1, titleOffset.Value, 0); }
  public static void AddSubtitle(FlatBufferBuilder builder, StringOffset subtitleOffset) { builder.AddOffset(2, subtitleOffset.Value, 0); }
  public static void AddText(FlatBufferBuilder builder, StringOffset textOffset) { builder.AddOffset(3, textOffset.Value, 0); }
  public static void AddImageText(FlatBufferBuilder builder, StringOffset imageTextOffset) { builder.AddOffset(4, imageTextOffset.Value, 0); }
  public static void AddButtonText(FlatBufferBuilder builder, StringOffset buttonTextOffset) { builder.AddOffset(5, buttonTextOffset.Value, 0); }
  public static void AddDate(FlatBufferBuilder builder, Offset<FBConfig.DayMonthYear> dateOffset) { builder.AddOffset(6, dateOffset.Value, 0); }
  public static void AddImageLink(FlatBufferBuilder builder, StringOffset imageLinkOffset) { builder.AddOffset(7, imageLinkOffset.Value, 0); }
  public static void AddReward(FlatBufferBuilder builder, StringOffset rewardOffset) { builder.AddOffset(8, rewardOffset.Value, 0); }
  public static void AddPredicate(FlatBufferBuilder builder, StringOffset predicateOffset) { builder.AddOffset(9, predicateOffset.Value, 0); }
  public static void AddTakeMeAction(FlatBufferBuilder builder, StringOffset takeMeActionOffset) { builder.AddOffset(10, takeMeActionOffset.Value, 0); }
  public static void AddTakeMeParamsFb(FlatBufferBuilder builder, VectorOffset takeMeParamsFbOffset) { builder.AddOffset(11, takeMeParamsFbOffset.Value, 0); }
  public static VectorOffset CreateTakeMeParamsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTakeMeParamsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddShowsLimit(FlatBufferBuilder builder, int showsLimit) { builder.AddInt(12, showsLimit, 0); }
  public static void AddMinDaysBetweenShows(FlatBufferBuilder builder, int minDaysBetweenShows) { builder.AddInt(13, minDaysBetweenShows, 0); }
  public static void AddOutlineColor(FlatBufferBuilder builder, StringOffset outlineColorOffset) { builder.AddOffset(14, outlineColorOffset.Value, 0); }
  public static void AddShouldOpenViaSettings(FlatBufferBuilder builder, bool shouldOpenViaSettings) { builder.AddBool(15, shouldOpenViaSettings, false); }
  public static void AddButtonIconLocalImage(FlatBufferBuilder builder, StringOffset buttonIconLocalImageOffset) { builder.AddOffset(16, buttonIconLocalImageOffset.Value, 0); }
  public static void AddButtonIconImageUrl(FlatBufferBuilder builder, StringOffset buttonIconImageUrlOffset) { builder.AddOffset(17, buttonIconImageUrlOffset.Value, 0); }
  public static Offset<FBConfig.NewsConfig> EndNewsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.NewsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfNewsConfig(FlatBufferBuilder builder, Offset<NewsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<NewsConfig> o1, Offset<NewsConfig> o2) =>
        new NewsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new NewsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static NewsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    NewsConfig obj_ = new NewsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public NewsConfigT UnPack() {
    var _o = new NewsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(NewsConfigT _o) {
    _o.Uid = this.Uid;
    _o.Title = this.Title;
    _o.Subtitle = this.Subtitle;
    _o.Text = this.Text;
    _o.ImageText = this.ImageText;
    _o.ButtonText = this.ButtonText;
    _o.Date = this.Date.HasValue ? this.Date.Value.UnPack() : null;
    _o.ImageLink = this.ImageLink;
    _o.Reward = this.Reward;
    _o.Predicate = this.Predicate;
    _o.TakeMeAction = this.TakeMeAction;
    _o.TakeMeParamsFb = new List<FBConfig.DictStringStringT>();
    for (var _j = 0; _j < this.TakeMeParamsFbLength; ++_j) {_o.TakeMeParamsFb.Add(this.TakeMeParamsFb(_j).HasValue ? this.TakeMeParamsFb(_j).Value.UnPack() : null);}
    _o.ShowsLimit = this.ShowsLimit;
    _o.MinDaysBetweenShows = this.MinDaysBetweenShows;
    _o.OutlineColor = this.OutlineColor;
    _o.ShouldOpenViaSettings = this.ShouldOpenViaSettings;
    _o.ButtonIconLocalImage = this.ButtonIconLocalImage;
    _o.ButtonIconImageUrl = this.ButtonIconImageUrl;
  }
  public static Offset<FBConfig.NewsConfig> Pack(FlatBufferBuilder builder, NewsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.NewsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _subtitle = _o.Subtitle == null ? default(StringOffset) : builder.CreateString(_o.Subtitle);
    var _text = _o.Text == null ? default(StringOffset) : builder.CreateString(_o.Text);
    var _image_text = _o.ImageText == null ? default(StringOffset) : builder.CreateString(_o.ImageText);
    var _button_text = _o.ButtonText == null ? default(StringOffset) : builder.CreateString(_o.ButtonText);
    var _date = _o.Date == null ? default(Offset<FBConfig.DayMonthYear>) : FBConfig.DayMonthYear.Pack(builder, _o.Date);
    var _image_link = _o.ImageLink == null ? default(StringOffset) : builder.CreateString(_o.ImageLink);
    var _reward = _o.Reward == null ? default(StringOffset) : builder.CreateString(_o.Reward);
    var _predicate = _o.Predicate == null ? default(StringOffset) : builder.CreateString(_o.Predicate);
    var _take_me_action = _o.TakeMeAction == null ? default(StringOffset) : builder.CreateString(_o.TakeMeAction);
    var _take_me_params_fb = default(VectorOffset);
    if (_o.TakeMeParamsFb != null) {
      var __take_me_params_fb = new Offset<FBConfig.DictStringString>[_o.TakeMeParamsFb.Count];
      for (var _j = 0; _j < __take_me_params_fb.Length; ++_j) { __take_me_params_fb[_j] = FBConfig.DictStringString.Pack(builder, _o.TakeMeParamsFb[_j]); }
      _take_me_params_fb = CreateTakeMeParamsFbVector(builder, __take_me_params_fb);
    }
    var _outline_color = _o.OutlineColor == null ? default(StringOffset) : builder.CreateString(_o.OutlineColor);
    var _button_icon_local_image = _o.ButtonIconLocalImage == null ? default(StringOffset) : builder.CreateString(_o.ButtonIconLocalImage);
    var _button_icon_image_url = _o.ButtonIconImageUrl == null ? default(StringOffset) : builder.CreateString(_o.ButtonIconImageUrl);
    return CreateNewsConfig(
      builder,
      _uid,
      _title,
      _subtitle,
      _text,
      _image_text,
      _button_text,
      _date,
      _image_link,
      _reward,
      _predicate,
      _take_me_action,
      _take_me_params_fb,
      _o.ShowsLimit,
      _o.MinDaysBetweenShows,
      _outline_color,
      _o.ShouldOpenViaSettings,
      _button_icon_local_image,
      _button_icon_image_url);
  }
}

public class NewsConfigT
{
  public string Uid { get; set; }
  public string Title { get; set; }
  public string Subtitle { get; set; }
  public string Text { get; set; }
  public string ImageText { get; set; }
  public string ButtonText { get; set; }
  public FBConfig.DayMonthYearT Date { get; set; }
  public string ImageLink { get; set; }
  public string Reward { get; set; }
  public string Predicate { get; set; }
  public string TakeMeAction { get; set; }
  public List<FBConfig.DictStringStringT> TakeMeParamsFb { get; set; }
  public int ShowsLimit { get; set; }
  public int MinDaysBetweenShows { get; set; }
  public string OutlineColor { get; set; }
  public bool ShouldOpenViaSettings { get; set; }
  public string ButtonIconLocalImage { get; set; }
  public string ButtonIconImageUrl { get; set; }

  public NewsConfigT() {
    this.Uid = null;
    this.Title = null;
    this.Subtitle = null;
    this.Text = null;
    this.ImageText = null;
    this.ButtonText = null;
    this.Date = null;
    this.ImageLink = null;
    this.Reward = null;
    this.Predicate = null;
    this.TakeMeAction = null;
    this.TakeMeParamsFb = null;
    this.ShowsLimit = 0;
    this.MinDaysBetweenShows = 0;
    this.OutlineColor = null;
    this.ShouldOpenViaSettings = false;
    this.ButtonIconLocalImage = null;
    this.ButtonIconImageUrl = null;
  }
}

public struct NewsConfigDict : IFlatbufferConfigDict<NewsConfig, NewsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static NewsConfigDict GetRootAsNewsConfigDict(ByteBuffer _bb) { return GetRootAsNewsConfigDict(_bb, new NewsConfigDict()); }
  public static NewsConfigDict GetRootAsNewsConfigDict(ByteBuffer _bb, NewsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public NewsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.NewsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.NewsConfig?)(new FBConfig.NewsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.NewsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.NewsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.NewsConfigDict> CreateNewsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    NewsConfigDict.AddValues(builder, valuesOffset);
    return NewsConfigDict.EndNewsConfigDict(builder);
  }

  public static void StartNewsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.NewsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.NewsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.NewsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.NewsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.NewsConfigDict> EndNewsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.NewsConfigDict>(o);
  }
  public static void FinishNewsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.NewsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedNewsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.NewsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public NewsConfigDictT UnPack() {
    var _o = new NewsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(NewsConfigDictT _o) {
    _o.Values = new List<FBConfig.NewsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.NewsConfigDict> Pack(FlatBufferBuilder builder, NewsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.NewsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.NewsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.NewsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateNewsConfigDict(
      builder,
      _values);
  }
}

public class NewsConfigDictT
{
  public List<FBConfig.NewsConfigT> Values { get; set; }

  public NewsConfigDictT() {
    this.Values = null;
  }
  public static NewsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return NewsConfigDict.GetRootAsNewsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    NewsConfigDict.FinishNewsConfigDictBuffer(fbb, NewsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
