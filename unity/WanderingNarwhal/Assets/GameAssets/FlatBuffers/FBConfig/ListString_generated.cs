// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ListString : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ListString GetRootAsListString(ByteBuffer _bb) { return GetRootAsListString(_bb, new ListString()); }
  public static ListString GetRootAsListString(ByteBuffer _bb, ListString obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ListString __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Group(int j) { int o = __p.__offset(4); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int GroupLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.ListString> CreateListString(FlatBufferBuilder builder,
      VectorOffset groupOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ListString.AddGroup(builder, groupOffset);
    return ListString.EndListString(builder);
  }

  public static void StartListString(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddGroup(FlatBufferBuilder builder, VectorOffset groupOffset) { builder.AddOffset(0, groupOffset.Value, 0); }
  public static VectorOffset CreateGroupVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGroupVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGroupVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGroupVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGroupVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ListString> EndListString(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ListString>(o);
  }
  public ListStringT UnPack() {
    var _o = new ListStringT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ListStringT _o) {
    _o.Group = new List<string>();
    for (var _j = 0; _j < this.GroupLength; ++_j) {_o.Group.Add(this.Group(_j));}
  }
  public static Offset<FBConfig.ListString> Pack(FlatBufferBuilder builder, ListStringT _o) {
    if (_o == null) return default(Offset<FBConfig.ListString>);
    var _group = default(VectorOffset);
    if (_o.Group != null) {
      var __group = new StringOffset[_o.Group.Count];
      for (var _j = 0; _j < __group.Length; ++_j) { __group[_j] = builder.CreateString(_o.Group[_j]); }
      _group = CreateGroupVector(builder, __group);
    }
    return CreateListString(
      builder,
      _group);
  }
}

public class ListStringT
{
  public List<string> Group { get; set; }

  public ListStringT() {
    this.Group = null;
  }
}


}
