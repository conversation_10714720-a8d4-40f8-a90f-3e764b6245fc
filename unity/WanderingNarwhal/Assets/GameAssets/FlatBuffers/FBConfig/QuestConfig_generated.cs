// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ObjectiveAction : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ObjectiveAction GetRootAsObjectiveAction(ByteBuffer _bb) { return GetRootAsObjectiveAction(_bb, new ObjectiveAction()); }
  public static ObjectiveAction GetRootAsObjectiveAction(ByteBuffer _bb, ObjectiveAction obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ObjectiveAction __assign(int _i, By<PERSON><PERSON><PERSON><PERSON> _bb) { __init(_i, _bb); return this; }

  public string Condition { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetConditionBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetConditionBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetConditionArray() { return __p.__vector_as_array<byte>(4); }
  public string ActionName { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetActionNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetActionNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetActionNameArray() { return __p.__vector_as_array<byte>(6); }
  public FBConfig.DictStringString? ActionParams(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringString?)(new FBConfig.DictStringString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ActionParamsLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public bool DontSkipIfFail { get { int o = __p.__offset(10); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateDontSkipIfFail(bool dont_skip_if_fail) { int o = __p.__offset(10); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(dont_skip_if_fail ? 1 : 0)); return true; } else { return false; } }
  public string Uid { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.ObjectiveAction> CreateObjectiveAction(FlatBufferBuilder builder,
      StringOffset conditionOffset = default(StringOffset),
      StringOffset action_nameOffset = default(StringOffset),
      VectorOffset action_paramsOffset = default(VectorOffset),
      bool dont_skip_if_fail = false,
      StringOffset uidOffset = default(StringOffset)) {
    builder.StartTable(5);
    ObjectiveAction.AddUid(builder, uidOffset);
    ObjectiveAction.AddActionParams(builder, action_paramsOffset);
    ObjectiveAction.AddActionName(builder, action_nameOffset);
    ObjectiveAction.AddCondition(builder, conditionOffset);
    ObjectiveAction.AddDontSkipIfFail(builder, dont_skip_if_fail);
    return ObjectiveAction.EndObjectiveAction(builder);
  }

  public static void StartObjectiveAction(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddCondition(FlatBufferBuilder builder, StringOffset conditionOffset) { builder.AddOffset(0, conditionOffset.Value, 0); }
  public static void AddActionName(FlatBufferBuilder builder, StringOffset actionNameOffset) { builder.AddOffset(1, actionNameOffset.Value, 0); }
  public static void AddActionParams(FlatBufferBuilder builder, VectorOffset actionParamsOffset) { builder.AddOffset(2, actionParamsOffset.Value, 0); }
  public static VectorOffset CreateActionParamsVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateActionParamsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateActionParamsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateActionParamsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartActionParamsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddDontSkipIfFail(FlatBufferBuilder builder, bool dontSkipIfFail) { builder.AddBool(3, dontSkipIfFail, false); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(4, uidOffset.Value, 0); }
  public static Offset<FBConfig.ObjectiveAction> EndObjectiveAction(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ObjectiveAction>(o);
  }
  public ObjectiveActionT UnPack() {
    var _o = new ObjectiveActionT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ObjectiveActionT _o) {
    _o.Condition = this.Condition;
    _o.ActionName = this.ActionName;
    _o.ActionParams = new List<FBConfig.DictStringStringT>();
    for (var _j = 0; _j < this.ActionParamsLength; ++_j) {_o.ActionParams.Add(this.ActionParams(_j).HasValue ? this.ActionParams(_j).Value.UnPack() : null);}
    _o.DontSkipIfFail = this.DontSkipIfFail;
    _o.Uid = this.Uid;
  }
  public static Offset<FBConfig.ObjectiveAction> Pack(FlatBufferBuilder builder, ObjectiveActionT _o) {
    if (_o == null) return default(Offset<FBConfig.ObjectiveAction>);
    var _condition = _o.Condition == null ? default(StringOffset) : builder.CreateString(_o.Condition);
    var _action_name = _o.ActionName == null ? default(StringOffset) : builder.CreateString(_o.ActionName);
    var _action_params = default(VectorOffset);
    if (_o.ActionParams != null) {
      var __action_params = new Offset<FBConfig.DictStringString>[_o.ActionParams.Count];
      for (var _j = 0; _j < __action_params.Length; ++_j) { __action_params[_j] = FBConfig.DictStringString.Pack(builder, _o.ActionParams[_j]); }
      _action_params = CreateActionParamsVector(builder, __action_params);
    }
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateObjectiveAction(
      builder,
      _condition,
      _action_name,
      _action_params,
      _o.DontSkipIfFail,
      _uid);
  }
}

public class ObjectiveActionT
{
  public string Condition { get; set; }
  public string ActionName { get; set; }
  public List<FBConfig.DictStringStringT> ActionParams { get; set; }
  public bool DontSkipIfFail { get; set; }
  public string Uid { get; set; }

  public ObjectiveActionT() {
    this.Condition = null;
    this.ActionName = null;
    this.ActionParams = null;
    this.DontSkipIfFail = false;
    this.Uid = null;
  }
}

public struct ObjectiveReward : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ObjectiveReward GetRootAsObjectiveReward(ByteBuffer _bb) { return GetRootAsObjectiveReward(_bb, new ObjectiveReward()); }
  public static ObjectiveReward GetRootAsObjectiveReward(ByteBuffer _bb, ObjectiveReward obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ObjectiveReward __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Asset { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAssetBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetAssetBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetAssetArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.ObjectiveReward> CreateObjectiveReward(FlatBufferBuilder builder,
      StringOffset assetOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset)) {
    builder.StartTable(2);
    ObjectiveReward.AddName(builder, nameOffset);
    ObjectiveReward.AddAsset(builder, assetOffset);
    return ObjectiveReward.EndObjectiveReward(builder);
  }

  public static void StartObjectiveReward(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddAsset(FlatBufferBuilder builder, StringOffset assetOffset) { builder.AddOffset(0, assetOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static Offset<FBConfig.ObjectiveReward> EndObjectiveReward(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ObjectiveReward>(o);
  }
  public ObjectiveRewardT UnPack() {
    var _o = new ObjectiveRewardT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ObjectiveRewardT _o) {
    _o.Asset = this.Asset;
    _o.Name = this.Name;
  }
  public static Offset<FBConfig.ObjectiveReward> Pack(FlatBufferBuilder builder, ObjectiveRewardT _o) {
    if (_o == null) return default(Offset<FBConfig.ObjectiveReward>);
    var _asset = _o.Asset == null ? default(StringOffset) : builder.CreateString(_o.Asset);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    return CreateObjectiveReward(
      builder,
      _asset,
      _name);
  }
}

public class ObjectiveRewardT
{
  public string Asset { get; set; }
  public string Name { get; set; }

  public ObjectiveRewardT() {
    this.Asset = null;
    this.Name = null;
  }
}

public struct QuestObjectiveConfig : IFlatbufferConfig<QuestObjectiveConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static QuestObjectiveConfig GetRootAsQuestObjectiveConfig(ByteBuffer _bb) { return GetRootAsQuestObjectiveConfig(_bb, new QuestObjectiveConfig()); }
  public static QuestObjectiveConfig GetRootAsQuestObjectiveConfig(ByteBuffer _bb, QuestObjectiveConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public QuestObjectiveConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string QuestId { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetQuestIdBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetQuestIdBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetQuestIdArray() { return __p.__vector_as_array<byte>(4); }
  public string Title { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(6); }
  public string Desc { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetDescBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetDescArray() { return __p.__vector_as_array<byte>(8); }
  public string CompleteDesc { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCompleteDescBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetCompleteDescBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetCompleteDescArray() { return __p.__vector_as_array<byte>(10); }
  public string Thumbnail { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(12); }
  public string Message { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMessageBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetMessageBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetMessageArray() { return __p.__vector_as_array<byte>(14); }
  public string CompleteCondition { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCompleteConditionBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetCompleteConditionBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetCompleteConditionArray() { return __p.__vector_as_array<byte>(16); }
  public string Parent { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetParentBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetParentBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetParentArray() { return __p.__vector_as_array<byte>(18); }
  public string MessageFilter { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMessageFilterBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetMessageFilterBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetMessageFilterArray() { return __p.__vector_as_array<byte>(20); }
  public int ProgressiveObjective { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateProgressiveObjective(int progressive_objective) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, progressive_objective); return true; } else { return false; } }
  public string ProgressCondition { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetProgressConditionBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetProgressConditionBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetProgressConditionArray() { return __p.__vector_as_array<byte>(24); }
  public string TakeMeUid { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTakeMeUidBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetTakeMeUidBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetTakeMeUidArray() { return __p.__vector_as_array<byte>(26); }
  public string TakeMeParam { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTakeMeParamBytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetTakeMeParamBytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetTakeMeParamArray() { return __p.__vector_as_array<byte>(28); }
  public int StartProgress { get { int o = __p.__offset(30); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateStartProgress(int start_progress) { int o = __p.__offset(30); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, start_progress); return true; } else { return false; } }
  public FBConfig.DictStringInt? Reward(int j) { int o = __p.__offset(32); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardLength { get { int o = __p.__offset(32); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ObjectiveAction? Actions(int j) { int o = __p.__offset(34); return o != 0 ? (FBConfig.ObjectiveAction?)(new FBConfig.ObjectiveAction()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ActionsLength { get { int o = __p.__offset(34); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringString? TakeMeParams(int j) { int o = __p.__offset(36); return o != 0 ? (FBConfig.DictStringString?)(new FBConfig.DictStringString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TakeMeParamsLength { get { int o = __p.__offset(36); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ObjectiveReward? CarRewards(int j) { int o = __p.__offset(38); return o != 0 ? (FBConfig.ObjectiveReward?)(new FBConfig.ObjectiveReward()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int CarRewardsLength { get { int o = __p.__offset(38); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ObjectiveReward? PeopleRewards(int j) { int o = __p.__offset(40); return o != 0 ? (FBConfig.ObjectiveReward?)(new FBConfig.ObjectiveReward()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PeopleRewardsLength { get { int o = __p.__offset(40); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ObjectiveReward? DecorationRewards(int j) { int o = __p.__offset(42); return o != 0 ? (FBConfig.ObjectiveReward?)(new FBConfig.ObjectiveReward()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int DecorationRewardsLength { get { int o = __p.__offset(42); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string ObjectiveCategory { get { int o = __p.__offset(44); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetObjectiveCategoryBytes() { return __p.__vector_as_span<byte>(44, 1); }
#else
  public ArraySegment<byte>? GetObjectiveCategoryBytes() { return __p.__vector_as_arraysegment(44); }
#endif
  public byte[] GetObjectiveCategoryArray() { return __p.__vector_as_array<byte>(44); }
  public string ObjectiveCategoryParameter { get { int o = __p.__offset(46); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetObjectiveCategoryParameterBytes() { return __p.__vector_as_span<byte>(46, 1); }
#else
  public ArraySegment<byte>? GetObjectiveCategoryParameterBytes() { return __p.__vector_as_arraysegment(46); }
#endif
  public byte[] GetObjectiveCategoryParameterArray() { return __p.__vector_as_array<byte>(46); }
  public string RelatedLevel { get { int o = __p.__offset(48); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRelatedLevelBytes() { return __p.__vector_as_span<byte>(48, 1); }
#else
  public ArraySegment<byte>? GetRelatedLevelBytes() { return __p.__vector_as_arraysegment(48); }
#endif
  public byte[] GetRelatedLevelArray() { return __p.__vector_as_array<byte>(48); }
  public int Duration { get { int o = __p.__offset(50); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDuration(int duration) { int o = __p.__offset(50); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, duration); return true; } else { return false; } }
  public string Uid { get { int o = __p.__offset(52); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(52, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(52); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(52); }

  public static Offset<FBConfig.QuestObjectiveConfig> CreateQuestObjectiveConfig(FlatBufferBuilder builder,
      StringOffset quest_idOffset = default(StringOffset),
      StringOffset titleOffset = default(StringOffset),
      StringOffset descOffset = default(StringOffset),
      StringOffset complete_descOffset = default(StringOffset),
      StringOffset thumbnailOffset = default(StringOffset),
      StringOffset messageOffset = default(StringOffset),
      StringOffset complete_conditionOffset = default(StringOffset),
      StringOffset parentOffset = default(StringOffset),
      StringOffset message_filterOffset = default(StringOffset),
      int progressive_objective = 0,
      StringOffset progress_conditionOffset = default(StringOffset),
      StringOffset take_me_uidOffset = default(StringOffset),
      StringOffset take_me_paramOffset = default(StringOffset),
      int start_progress = 0,
      VectorOffset rewardOffset = default(VectorOffset),
      VectorOffset actionsOffset = default(VectorOffset),
      VectorOffset take_me_paramsOffset = default(VectorOffset),
      VectorOffset car_rewardsOffset = default(VectorOffset),
      VectorOffset people_rewardsOffset = default(VectorOffset),
      VectorOffset decoration_rewardsOffset = default(VectorOffset),
      StringOffset objective_categoryOffset = default(StringOffset),
      StringOffset objective_category_parameterOffset = default(StringOffset),
      StringOffset related_levelOffset = default(StringOffset),
      int duration = 0,
      StringOffset uidOffset = default(StringOffset)) {
    builder.StartTable(25);
    QuestObjectiveConfig.AddUid(builder, uidOffset);
    QuestObjectiveConfig.AddDuration(builder, duration);
    QuestObjectiveConfig.AddRelatedLevel(builder, related_levelOffset);
    QuestObjectiveConfig.AddObjectiveCategoryParameter(builder, objective_category_parameterOffset);
    QuestObjectiveConfig.AddObjectiveCategory(builder, objective_categoryOffset);
    QuestObjectiveConfig.AddDecorationRewards(builder, decoration_rewardsOffset);
    QuestObjectiveConfig.AddPeopleRewards(builder, people_rewardsOffset);
    QuestObjectiveConfig.AddCarRewards(builder, car_rewardsOffset);
    QuestObjectiveConfig.AddTakeMeParams(builder, take_me_paramsOffset);
    QuestObjectiveConfig.AddActions(builder, actionsOffset);
    QuestObjectiveConfig.AddReward(builder, rewardOffset);
    QuestObjectiveConfig.AddStartProgress(builder, start_progress);
    QuestObjectiveConfig.AddTakeMeParam(builder, take_me_paramOffset);
    QuestObjectiveConfig.AddTakeMeUid(builder, take_me_uidOffset);
    QuestObjectiveConfig.AddProgressCondition(builder, progress_conditionOffset);
    QuestObjectiveConfig.AddProgressiveObjective(builder, progressive_objective);
    QuestObjectiveConfig.AddMessageFilter(builder, message_filterOffset);
    QuestObjectiveConfig.AddParent(builder, parentOffset);
    QuestObjectiveConfig.AddCompleteCondition(builder, complete_conditionOffset);
    QuestObjectiveConfig.AddMessage(builder, messageOffset);
    QuestObjectiveConfig.AddThumbnail(builder, thumbnailOffset);
    QuestObjectiveConfig.AddCompleteDesc(builder, complete_descOffset);
    QuestObjectiveConfig.AddDesc(builder, descOffset);
    QuestObjectiveConfig.AddTitle(builder, titleOffset);
    QuestObjectiveConfig.AddQuestId(builder, quest_idOffset);
    return QuestObjectiveConfig.EndQuestObjectiveConfig(builder);
  }

  public static void StartQuestObjectiveConfig(FlatBufferBuilder builder) { builder.StartTable(25); }
  public static void AddQuestId(FlatBufferBuilder builder, StringOffset questIdOffset) { builder.AddOffset(0, questIdOffset.Value, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(1, titleOffset.Value, 0); }
  public static void AddDesc(FlatBufferBuilder builder, StringOffset descOffset) { builder.AddOffset(2, descOffset.Value, 0); }
  public static void AddCompleteDesc(FlatBufferBuilder builder, StringOffset completeDescOffset) { builder.AddOffset(3, completeDescOffset.Value, 0); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(4, thumbnailOffset.Value, 0); }
  public static void AddMessage(FlatBufferBuilder builder, StringOffset messageOffset) { builder.AddOffset(5, messageOffset.Value, 0); }
  public static void AddCompleteCondition(FlatBufferBuilder builder, StringOffset completeConditionOffset) { builder.AddOffset(6, completeConditionOffset.Value, 0); }
  public static void AddParent(FlatBufferBuilder builder, StringOffset parentOffset) { builder.AddOffset(7, parentOffset.Value, 0); }
  public static void AddMessageFilter(FlatBufferBuilder builder, StringOffset messageFilterOffset) { builder.AddOffset(8, messageFilterOffset.Value, 0); }
  public static void AddProgressiveObjective(FlatBufferBuilder builder, int progressiveObjective) { builder.AddInt(9, progressiveObjective, 0); }
  public static void AddProgressCondition(FlatBufferBuilder builder, StringOffset progressConditionOffset) { builder.AddOffset(10, progressConditionOffset.Value, 0); }
  public static void AddTakeMeUid(FlatBufferBuilder builder, StringOffset takeMeUidOffset) { builder.AddOffset(11, takeMeUidOffset.Value, 0); }
  public static void AddTakeMeParam(FlatBufferBuilder builder, StringOffset takeMeParamOffset) { builder.AddOffset(12, takeMeParamOffset.Value, 0); }
  public static void AddStartProgress(FlatBufferBuilder builder, int startProgress) { builder.AddInt(13, startProgress, 0); }
  public static void AddReward(FlatBufferBuilder builder, VectorOffset rewardOffset) { builder.AddOffset(14, rewardOffset.Value, 0); }
  public static VectorOffset CreateRewardVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddActions(FlatBufferBuilder builder, VectorOffset actionsOffset) { builder.AddOffset(15, actionsOffset.Value, 0); }
  public static VectorOffset CreateActionsVector(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveAction>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateActionsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveAction>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateActionsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ObjectiveAction>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateActionsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ObjectiveAction>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartActionsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTakeMeParams(FlatBufferBuilder builder, VectorOffset takeMeParamsOffset) { builder.AddOffset(16, takeMeParamsOffset.Value, 0); }
  public static VectorOffset CreateTakeMeParamsVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTakeMeParamsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddCarRewards(FlatBufferBuilder builder, VectorOffset carRewardsOffset) { builder.AddOffset(17, carRewardsOffset.Value, 0); }
  public static VectorOffset CreateCarRewardsVector(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveReward>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCarRewardsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveReward>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCarRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ObjectiveReward>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCarRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ObjectiveReward>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCarRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPeopleRewards(FlatBufferBuilder builder, VectorOffset peopleRewardsOffset) { builder.AddOffset(18, peopleRewardsOffset.Value, 0); }
  public static VectorOffset CreatePeopleRewardsVector(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveReward>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePeopleRewardsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveReward>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePeopleRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ObjectiveReward>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePeopleRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ObjectiveReward>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPeopleRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddDecorationRewards(FlatBufferBuilder builder, VectorOffset decorationRewardsOffset) { builder.AddOffset(19, decorationRewardsOffset.Value, 0); }
  public static VectorOffset CreateDecorationRewardsVector(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveReward>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDecorationRewardsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ObjectiveReward>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDecorationRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ObjectiveReward>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDecorationRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ObjectiveReward>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDecorationRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddObjectiveCategory(FlatBufferBuilder builder, StringOffset objectiveCategoryOffset) { builder.AddOffset(20, objectiveCategoryOffset.Value, 0); }
  public static void AddObjectiveCategoryParameter(FlatBufferBuilder builder, StringOffset objectiveCategoryParameterOffset) { builder.AddOffset(21, objectiveCategoryParameterOffset.Value, 0); }
  public static void AddRelatedLevel(FlatBufferBuilder builder, StringOffset relatedLevelOffset) { builder.AddOffset(22, relatedLevelOffset.Value, 0); }
  public static void AddDuration(FlatBufferBuilder builder, int duration) { builder.AddInt(23, duration, 0); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(24, uidOffset.Value, 0); }
  public static Offset<FBConfig.QuestObjectiveConfig> EndQuestObjectiveConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.QuestObjectiveConfig>(o);
  }
  public QuestObjectiveConfigT UnPack() {
    var _o = new QuestObjectiveConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(QuestObjectiveConfigT _o) {
    _o.QuestId = this.QuestId;
    _o.Title = this.Title;
    _o.Desc = this.Desc;
    _o.CompleteDesc = this.CompleteDesc;
    _o.Thumbnail = this.Thumbnail;
    _o.Message = this.Message;
    _o.CompleteCondition = this.CompleteCondition;
    _o.Parent = this.Parent;
    _o.MessageFilter = this.MessageFilter;
    _o.ProgressiveObjective = this.ProgressiveObjective;
    _o.ProgressCondition = this.ProgressCondition;
    _o.TakeMeUid = this.TakeMeUid;
    _o.TakeMeParam = this.TakeMeParam;
    _o.StartProgress = this.StartProgress;
    _o.Reward = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardLength; ++_j) {_o.Reward.Add(this.Reward(_j).HasValue ? this.Reward(_j).Value.UnPack() : null);}
    _o.Actions = new List<FBConfig.ObjectiveActionT>();
    for (var _j = 0; _j < this.ActionsLength; ++_j) {_o.Actions.Add(this.Actions(_j).HasValue ? this.Actions(_j).Value.UnPack() : null);}
    _o.TakeMeParams = new List<FBConfig.DictStringStringT>();
    for (var _j = 0; _j < this.TakeMeParamsLength; ++_j) {_o.TakeMeParams.Add(this.TakeMeParams(_j).HasValue ? this.TakeMeParams(_j).Value.UnPack() : null);}
    _o.CarRewards = new List<FBConfig.ObjectiveRewardT>();
    for (var _j = 0; _j < this.CarRewardsLength; ++_j) {_o.CarRewards.Add(this.CarRewards(_j).HasValue ? this.CarRewards(_j).Value.UnPack() : null);}
    _o.PeopleRewards = new List<FBConfig.ObjectiveRewardT>();
    for (var _j = 0; _j < this.PeopleRewardsLength; ++_j) {_o.PeopleRewards.Add(this.PeopleRewards(_j).HasValue ? this.PeopleRewards(_j).Value.UnPack() : null);}
    _o.DecorationRewards = new List<FBConfig.ObjectiveRewardT>();
    for (var _j = 0; _j < this.DecorationRewardsLength; ++_j) {_o.DecorationRewards.Add(this.DecorationRewards(_j).HasValue ? this.DecorationRewards(_j).Value.UnPack() : null);}
    _o.ObjectiveCategory = this.ObjectiveCategory;
    _o.ObjectiveCategoryParameter = this.ObjectiveCategoryParameter;
    _o.RelatedLevel = this.RelatedLevel;
    _o.Duration = this.Duration;
    _o.Uid = this.Uid;
  }
  public static Offset<FBConfig.QuestObjectiveConfig> Pack(FlatBufferBuilder builder, QuestObjectiveConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.QuestObjectiveConfig>);
    var _quest_id = _o.QuestId == null ? default(StringOffset) : builder.CreateString(_o.QuestId);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _desc = _o.Desc == null ? default(StringOffset) : builder.CreateString(_o.Desc);
    var _complete_desc = _o.CompleteDesc == null ? default(StringOffset) : builder.CreateString(_o.CompleteDesc);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    var _message = _o.Message == null ? default(StringOffset) : builder.CreateString(_o.Message);
    var _complete_condition = _o.CompleteCondition == null ? default(StringOffset) : builder.CreateString(_o.CompleteCondition);
    var _parent = _o.Parent == null ? default(StringOffset) : builder.CreateString(_o.Parent);
    var _message_filter = _o.MessageFilter == null ? default(StringOffset) : builder.CreateString(_o.MessageFilter);
    var _progress_condition = _o.ProgressCondition == null ? default(StringOffset) : builder.CreateString(_o.ProgressCondition);
    var _take_me_uid = _o.TakeMeUid == null ? default(StringOffset) : builder.CreateString(_o.TakeMeUid);
    var _take_me_param = _o.TakeMeParam == null ? default(StringOffset) : builder.CreateString(_o.TakeMeParam);
    var _reward = default(VectorOffset);
    if (_o.Reward != null) {
      var __reward = new Offset<FBConfig.DictStringInt>[_o.Reward.Count];
      for (var _j = 0; _j < __reward.Length; ++_j) { __reward[_j] = FBConfig.DictStringInt.Pack(builder, _o.Reward[_j]); }
      _reward = CreateRewardVector(builder, __reward);
    }
    var _actions = default(VectorOffset);
    if (_o.Actions != null) {
      var __actions = new Offset<FBConfig.ObjectiveAction>[_o.Actions.Count];
      for (var _j = 0; _j < __actions.Length; ++_j) { __actions[_j] = FBConfig.ObjectiveAction.Pack(builder, _o.Actions[_j]); }
      _actions = CreateActionsVector(builder, __actions);
    }
    var _take_me_params = default(VectorOffset);
    if (_o.TakeMeParams != null) {
      var __take_me_params = new Offset<FBConfig.DictStringString>[_o.TakeMeParams.Count];
      for (var _j = 0; _j < __take_me_params.Length; ++_j) { __take_me_params[_j] = FBConfig.DictStringString.Pack(builder, _o.TakeMeParams[_j]); }
      _take_me_params = CreateTakeMeParamsVector(builder, __take_me_params);
    }
    var _car_rewards = default(VectorOffset);
    if (_o.CarRewards != null) {
      var __car_rewards = new Offset<FBConfig.ObjectiveReward>[_o.CarRewards.Count];
      for (var _j = 0; _j < __car_rewards.Length; ++_j) { __car_rewards[_j] = FBConfig.ObjectiveReward.Pack(builder, _o.CarRewards[_j]); }
      _car_rewards = CreateCarRewardsVector(builder, __car_rewards);
    }
    var _people_rewards = default(VectorOffset);
    if (_o.PeopleRewards != null) {
      var __people_rewards = new Offset<FBConfig.ObjectiveReward>[_o.PeopleRewards.Count];
      for (var _j = 0; _j < __people_rewards.Length; ++_j) { __people_rewards[_j] = FBConfig.ObjectiveReward.Pack(builder, _o.PeopleRewards[_j]); }
      _people_rewards = CreatePeopleRewardsVector(builder, __people_rewards);
    }
    var _decoration_rewards = default(VectorOffset);
    if (_o.DecorationRewards != null) {
      var __decoration_rewards = new Offset<FBConfig.ObjectiveReward>[_o.DecorationRewards.Count];
      for (var _j = 0; _j < __decoration_rewards.Length; ++_j) { __decoration_rewards[_j] = FBConfig.ObjectiveReward.Pack(builder, _o.DecorationRewards[_j]); }
      _decoration_rewards = CreateDecorationRewardsVector(builder, __decoration_rewards);
    }
    var _objective_category = _o.ObjectiveCategory == null ? default(StringOffset) : builder.CreateString(_o.ObjectiveCategory);
    var _objective_category_parameter = _o.ObjectiveCategoryParameter == null ? default(StringOffset) : builder.CreateString(_o.ObjectiveCategoryParameter);
    var _related_level = _o.RelatedLevel == null ? default(StringOffset) : builder.CreateString(_o.RelatedLevel);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateQuestObjectiveConfig(
      builder,
      _quest_id,
      _title,
      _desc,
      _complete_desc,
      _thumbnail,
      _message,
      _complete_condition,
      _parent,
      _message_filter,
      _o.ProgressiveObjective,
      _progress_condition,
      _take_me_uid,
      _take_me_param,
      _o.StartProgress,
      _reward,
      _actions,
      _take_me_params,
      _car_rewards,
      _people_rewards,
      _decoration_rewards,
      _objective_category,
      _objective_category_parameter,
      _related_level,
      _o.Duration,
      _uid);
  }
}

public class QuestObjectiveConfigT
{
  public string QuestId { get; set; }
  public string Title { get; set; }
  public string Desc { get; set; }
  public string CompleteDesc { get; set; }
  public string Thumbnail { get; set; }
  public string Message { get; set; }
  public string CompleteCondition { get; set; }
  public string Parent { get; set; }
  public string MessageFilter { get; set; }
  public int ProgressiveObjective { get; set; }
  public string ProgressCondition { get; set; }
  public string TakeMeUid { get; set; }
  public string TakeMeParam { get; set; }
  public int StartProgress { get; set; }
  public List<FBConfig.DictStringIntT> Reward { get; set; }
  public List<FBConfig.ObjectiveActionT> Actions { get; set; }
  public List<FBConfig.DictStringStringT> TakeMeParams { get; set; }
  public List<FBConfig.ObjectiveRewardT> CarRewards { get; set; }
  public List<FBConfig.ObjectiveRewardT> PeopleRewards { get; set; }
  public List<FBConfig.ObjectiveRewardT> DecorationRewards { get; set; }
  public string ObjectiveCategory { get; set; }
  public string ObjectiveCategoryParameter { get; set; }
  public string RelatedLevel { get; set; }
  public int Duration { get; set; }
  public string Uid { get; set; }

  public QuestObjectiveConfigT() {
    this.QuestId = null;
    this.Title = null;
    this.Desc = null;
    this.CompleteDesc = null;
    this.Thumbnail = null;
    this.Message = null;
    this.CompleteCondition = null;
    this.Parent = null;
    this.MessageFilter = null;
    this.ProgressiveObjective = 0;
    this.ProgressCondition = null;
    this.TakeMeUid = null;
    this.TakeMeParam = null;
    this.StartProgress = 0;
    this.Reward = null;
    this.Actions = null;
    this.TakeMeParams = null;
    this.CarRewards = null;
    this.PeopleRewards = null;
    this.DecorationRewards = null;
    this.ObjectiveCategory = null;
    this.ObjectiveCategoryParameter = null;
    this.RelatedLevel = null;
    this.Duration = 0;
    this.Uid = null;
  }
}

public struct QuestConfig : IFlatbufferConfig<QuestConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static QuestConfig GetRootAsQuestConfig(ByteBuffer _bb) { return GetRootAsQuestConfig(_bb, new QuestConfig()); }
  public static QuestConfig GetRootAsQuestConfig(ByteBuffer _bb, QuestConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public QuestConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Category { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(6); }
  public string CharacterIcon { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCharacterIconBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetCharacterIconBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetCharacterIconArray() { return __p.__vector_as_array<byte>(8); }
  public string CharacterText { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCharacterTextBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetCharacterTextBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetCharacterTextArray() { return __p.__vector_as_array<byte>(10); }
  public string QuestSubtitle { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetQuestSubtitleBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetQuestSubtitleBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetQuestSubtitleArray() { return __p.__vector_as_array<byte>(12); }
  public int Priority { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePriority(int priority) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, priority); return true; } else { return false; } }
  public string EnableCondition { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetEnableConditionBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetEnableConditionBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetEnableConditionArray() { return __p.__vector_as_array<byte>(16); }
  public string Requirements(int j) { int o = __p.__offset(18); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int RequirementsLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.QuestObjectiveConfig? ObjectivesFb(int j) { int o = __p.__offset(20); return o != 0 ? (FBConfig.QuestObjectiveConfig?)(new FBConfig.QuestObjectiveConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ObjectivesFbLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int WonderGoalLevelRangeReward(int j) { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int WonderGoalLevelRangeRewardLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetWonderGoalLevelRangeRewardBytes() { return __p.__vector_as_span<int>(22, 4); }
#else
  public ArraySegment<byte>? GetWonderGoalLevelRangeRewardBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public int[] GetWonderGoalLevelRangeRewardArray() { return __p.__vector_as_array<int>(22); }
  public bool MutateWonderGoalLevelRangeReward(int j, int wonder_goal_level_range_reward) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, wonder_goal_level_range_reward); return true; } else { return false; } }
  public string Location { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLocationBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetLocationBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetLocationArray() { return __p.__vector_as_array<byte>(24); }
  public FBConfig.DictStringInt? RewardFb(int j) { int o = __p.__offset(26); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardFbLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.QuestConfig> CreateQuestConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset categoryOffset = default(StringOffset),
      StringOffset character_iconOffset = default(StringOffset),
      StringOffset character_textOffset = default(StringOffset),
      StringOffset quest_subtitleOffset = default(StringOffset),
      int priority = 0,
      StringOffset enable_conditionOffset = default(StringOffset),
      VectorOffset requirementsOffset = default(VectorOffset),
      VectorOffset objectives_fbOffset = default(VectorOffset),
      VectorOffset wonder_goal_level_range_rewardOffset = default(VectorOffset),
      StringOffset locationOffset = default(StringOffset),
      VectorOffset reward_fbOffset = default(VectorOffset)) {
    builder.StartTable(12);
    QuestConfig.AddRewardFb(builder, reward_fbOffset);
    QuestConfig.AddLocation(builder, locationOffset);
    QuestConfig.AddWonderGoalLevelRangeReward(builder, wonder_goal_level_range_rewardOffset);
    QuestConfig.AddObjectivesFb(builder, objectives_fbOffset);
    QuestConfig.AddRequirements(builder, requirementsOffset);
    QuestConfig.AddEnableCondition(builder, enable_conditionOffset);
    QuestConfig.AddPriority(builder, priority);
    QuestConfig.AddQuestSubtitle(builder, quest_subtitleOffset);
    QuestConfig.AddCharacterText(builder, character_textOffset);
    QuestConfig.AddCharacterIcon(builder, character_iconOffset);
    QuestConfig.AddCategory(builder, categoryOffset);
    QuestConfig.AddUid(builder, uidOffset);
    return QuestConfig.EndQuestConfig(builder);
  }

  public static void StartQuestConfig(FlatBufferBuilder builder) { builder.StartTable(12); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(1, categoryOffset.Value, 0); }
  public static void AddCharacterIcon(FlatBufferBuilder builder, StringOffset characterIconOffset) { builder.AddOffset(2, characterIconOffset.Value, 0); }
  public static void AddCharacterText(FlatBufferBuilder builder, StringOffset characterTextOffset) { builder.AddOffset(3, characterTextOffset.Value, 0); }
  public static void AddQuestSubtitle(FlatBufferBuilder builder, StringOffset questSubtitleOffset) { builder.AddOffset(4, questSubtitleOffset.Value, 0); }
  public static void AddPriority(FlatBufferBuilder builder, int priority) { builder.AddInt(5, priority, 0); }
  public static void AddEnableCondition(FlatBufferBuilder builder, StringOffset enableConditionOffset) { builder.AddOffset(6, enableConditionOffset.Value, 0); }
  public static void AddRequirements(FlatBufferBuilder builder, VectorOffset requirementsOffset) { builder.AddOffset(7, requirementsOffset.Value, 0); }
  public static VectorOffset CreateRequirementsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRequirementsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRequirementsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRequirementsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRequirementsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddObjectivesFb(FlatBufferBuilder builder, VectorOffset objectivesFbOffset) { builder.AddOffset(8, objectivesFbOffset.Value, 0); }
  public static VectorOffset CreateObjectivesFbVector(FlatBufferBuilder builder, Offset<FBConfig.QuestObjectiveConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateObjectivesFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.QuestObjectiveConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateObjectivesFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.QuestObjectiveConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateObjectivesFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.QuestObjectiveConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartObjectivesFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddWonderGoalLevelRangeReward(FlatBufferBuilder builder, VectorOffset wonderGoalLevelRangeRewardOffset) { builder.AddOffset(9, wonderGoalLevelRangeRewardOffset.Value, 0); }
  public static VectorOffset CreateWonderGoalLevelRangeRewardVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateWonderGoalLevelRangeRewardVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWonderGoalLevelRangeRewardVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWonderGoalLevelRangeRewardVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartWonderGoalLevelRangeRewardVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLocation(FlatBufferBuilder builder, StringOffset locationOffset) { builder.AddOffset(10, locationOffset.Value, 0); }
  public static void AddRewardFb(FlatBufferBuilder builder, VectorOffset rewardFbOffset) { builder.AddOffset(11, rewardFbOffset.Value, 0); }
  public static VectorOffset CreateRewardFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.QuestConfig> EndQuestConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.QuestConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfQuestConfig(FlatBufferBuilder builder, Offset<QuestConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<QuestConfig> o1, Offset<QuestConfig> o2) =>
        new QuestConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new QuestConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static QuestConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    QuestConfig obj_ = new QuestConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public QuestConfigT UnPack() {
    var _o = new QuestConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(QuestConfigT _o) {
    _o.Uid = this.Uid;
    _o.Category = this.Category;
    _o.CharacterIcon = this.CharacterIcon;
    _o.CharacterText = this.CharacterText;
    _o.QuestSubtitle = this.QuestSubtitle;
    _o.Priority = this.Priority;
    _o.EnableCondition = this.EnableCondition;
    _o.Requirements = new List<string>();
    for (var _j = 0; _j < this.RequirementsLength; ++_j) {_o.Requirements.Add(this.Requirements(_j));}
    _o.ObjectivesFb = new List<FBConfig.QuestObjectiveConfigT>();
    for (var _j = 0; _j < this.ObjectivesFbLength; ++_j) {_o.ObjectivesFb.Add(this.ObjectivesFb(_j).HasValue ? this.ObjectivesFb(_j).Value.UnPack() : null);}
    _o.WonderGoalLevelRangeReward = new List<int>();
    for (var _j = 0; _j < this.WonderGoalLevelRangeRewardLength; ++_j) {_o.WonderGoalLevelRangeReward.Add(this.WonderGoalLevelRangeReward(_j));}
    _o.Location = this.Location;
    _o.RewardFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardFbLength; ++_j) {_o.RewardFb.Add(this.RewardFb(_j).HasValue ? this.RewardFb(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.QuestConfig> Pack(FlatBufferBuilder builder, QuestConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.QuestConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    var _character_icon = _o.CharacterIcon == null ? default(StringOffset) : builder.CreateString(_o.CharacterIcon);
    var _character_text = _o.CharacterText == null ? default(StringOffset) : builder.CreateString(_o.CharacterText);
    var _quest_subtitle = _o.QuestSubtitle == null ? default(StringOffset) : builder.CreateString(_o.QuestSubtitle);
    var _enable_condition = _o.EnableCondition == null ? default(StringOffset) : builder.CreateString(_o.EnableCondition);
    var _requirements = default(VectorOffset);
    if (_o.Requirements != null) {
      var __requirements = new StringOffset[_o.Requirements.Count];
      for (var _j = 0; _j < __requirements.Length; ++_j) { __requirements[_j] = builder.CreateString(_o.Requirements[_j]); }
      _requirements = CreateRequirementsVector(builder, __requirements);
    }
    var _objectives_fb = default(VectorOffset);
    if (_o.ObjectivesFb != null) {
      var __objectives_fb = new Offset<FBConfig.QuestObjectiveConfig>[_o.ObjectivesFb.Count];
      for (var _j = 0; _j < __objectives_fb.Length; ++_j) { __objectives_fb[_j] = FBConfig.QuestObjectiveConfig.Pack(builder, _o.ObjectivesFb[_j]); }
      _objectives_fb = CreateObjectivesFbVector(builder, __objectives_fb);
    }
    var _wonder_goal_level_range_reward = default(VectorOffset);
    if (_o.WonderGoalLevelRangeReward != null) {
      var __wonder_goal_level_range_reward = _o.WonderGoalLevelRangeReward.ToArray();
      _wonder_goal_level_range_reward = CreateWonderGoalLevelRangeRewardVector(builder, __wonder_goal_level_range_reward);
    }
    var _location = _o.Location == null ? default(StringOffset) : builder.CreateString(_o.Location);
    var _reward_fb = default(VectorOffset);
    if (_o.RewardFb != null) {
      var __reward_fb = new Offset<FBConfig.DictStringInt>[_o.RewardFb.Count];
      for (var _j = 0; _j < __reward_fb.Length; ++_j) { __reward_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.RewardFb[_j]); }
      _reward_fb = CreateRewardFbVector(builder, __reward_fb);
    }
    return CreateQuestConfig(
      builder,
      _uid,
      _category,
      _character_icon,
      _character_text,
      _quest_subtitle,
      _o.Priority,
      _enable_condition,
      _requirements,
      _objectives_fb,
      _wonder_goal_level_range_reward,
      _location,
      _reward_fb);
  }
}

public class QuestConfigT
{
  public string Uid { get; set; }
  public string Category { get; set; }
  public string CharacterIcon { get; set; }
  public string CharacterText { get; set; }
  public string QuestSubtitle { get; set; }
  public int Priority { get; set; }
  public string EnableCondition { get; set; }
  public List<string> Requirements { get; set; }
  public List<FBConfig.QuestObjectiveConfigT> ObjectivesFb { get; set; }
  public List<int> WonderGoalLevelRangeReward { get; set; }
  public string Location { get; set; }
  public List<FBConfig.DictStringIntT> RewardFb { get; set; }

  public QuestConfigT() {
    this.Uid = null;
    this.Category = null;
    this.CharacterIcon = null;
    this.CharacterText = null;
    this.QuestSubtitle = null;
    this.Priority = 0;
    this.EnableCondition = null;
    this.Requirements = null;
    this.ObjectivesFb = null;
    this.WonderGoalLevelRangeReward = null;
    this.Location = null;
    this.RewardFb = null;
  }
}

public struct QuestConfigDict : IFlatbufferConfigDict<QuestConfig, QuestConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static QuestConfigDict GetRootAsQuestConfigDict(ByteBuffer _bb) { return GetRootAsQuestConfigDict(_bb, new QuestConfigDict()); }
  public static QuestConfigDict GetRootAsQuestConfigDict(ByteBuffer _bb, QuestConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public QuestConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.QuestConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.QuestConfig?)(new FBConfig.QuestConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.QuestConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.QuestConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.QuestConfigDict> CreateQuestConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    QuestConfigDict.AddValues(builder, valuesOffset);
    return QuestConfigDict.EndQuestConfigDict(builder);
  }

  public static void StartQuestConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.QuestConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.QuestConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.QuestConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.QuestConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.QuestConfigDict> EndQuestConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.QuestConfigDict>(o);
  }
  public static void FinishQuestConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.QuestConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedQuestConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.QuestConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public QuestConfigDictT UnPack() {
    var _o = new QuestConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(QuestConfigDictT _o) {
    _o.Values = new List<FBConfig.QuestConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.QuestConfigDict> Pack(FlatBufferBuilder builder, QuestConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.QuestConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.QuestConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.QuestConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateQuestConfigDict(
      builder,
      _values);
  }
}

public class QuestConfigDictT
{
  public List<FBConfig.QuestConfigT> Values { get; set; }

  public QuestConfigDictT() {
    this.Values = null;
  }
  public static QuestConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return QuestConfigDict.GetRootAsQuestConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    QuestConfigDict.FinishQuestConfigDictBuffer(fbb, QuestConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
