// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ModalsConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ModalsConfig GetRootAsModalsConfig(ByteBuffer _bb) { return GetRootAsModalsConfig(_bb, new ModalsConfig()); }
  public static ModalsConfig GetRootAsModalsConfig(ByteBuffer _bb, ModalsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ModalsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float ChanceToObserveModal { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateChanceToObserveModal(float chance_to_observe_modal) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, chance_to_observe_modal); return true; } else { return false; } }

  public static Offset<FBConfig.ModalsConfig> CreateModalsConfig(FlatBufferBuilder builder,
      float chance_to_observe_modal = 0.0f) {
    builder.StartTable(1);
    ModalsConfig.AddChanceToObserveModal(builder, chance_to_observe_modal);
    return ModalsConfig.EndModalsConfig(builder);
  }

  public static void StartModalsConfig(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddChanceToObserveModal(FlatBufferBuilder builder, float chanceToObserveModal) { builder.AddFloat(0, chanceToObserveModal, 0.0f); }
  public static Offset<FBConfig.ModalsConfig> EndModalsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ModalsConfig>(o);
  }
  public ModalsConfigT UnPack() {
    var _o = new ModalsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ModalsConfigT _o) {
    _o.ChanceToObserveModal = this.ChanceToObserveModal;
  }
  public static Offset<FBConfig.ModalsConfig> Pack(FlatBufferBuilder builder, ModalsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ModalsConfig>);
    return CreateModalsConfig(
      builder,
      _o.ChanceToObserveModal);
  }
}

public class ModalsConfigT
{
  public float ChanceToObserveModal { get; set; }

  public ModalsConfigT() {
    this.ChanceToObserveModal = 0.0f;
  }
}

public struct AssistWinRateAfterIap : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AssistWinRateAfterIap GetRootAsAssistWinRateAfterIap(ByteBuffer _bb) { return GetRootAsAssistWinRateAfterIap(_bb, new AssistWinRateAfterIap()); }
  public static AssistWinRateAfterIap GetRootAsAssistWinRateAfterIap(ByteBuffer _bb, AssistWinRateAfterIap obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AssistWinRateAfterIap __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int Increase { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateIncrease(int increase) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, increase); return true; } else { return false; } }
  public int Levels { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLevels(int levels) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, levels); return true; } else { return false; } }

  public static Offset<FBConfig.AssistWinRateAfterIap> CreateAssistWinRateAfterIap(FlatBufferBuilder builder,
      int increase = 0,
      int levels = 0) {
    builder.StartTable(2);
    AssistWinRateAfterIap.AddLevels(builder, levels);
    AssistWinRateAfterIap.AddIncrease(builder, increase);
    return AssistWinRateAfterIap.EndAssistWinRateAfterIap(builder);
  }

  public static void StartAssistWinRateAfterIap(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddIncrease(FlatBufferBuilder builder, int increase) { builder.AddInt(0, increase, 0); }
  public static void AddLevels(FlatBufferBuilder builder, int levels) { builder.AddInt(1, levels, 0); }
  public static Offset<FBConfig.AssistWinRateAfterIap> EndAssistWinRateAfterIap(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AssistWinRateAfterIap>(o);
  }
  public AssistWinRateAfterIapT UnPack() {
    var _o = new AssistWinRateAfterIapT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AssistWinRateAfterIapT _o) {
    _o.Increase = this.Increase;
    _o.Levels = this.Levels;
  }
  public static Offset<FBConfig.AssistWinRateAfterIap> Pack(FlatBufferBuilder builder, AssistWinRateAfterIapT _o) {
    if (_o == null) return default(Offset<FBConfig.AssistWinRateAfterIap>);
    return CreateAssistWinRateAfterIap(
      builder,
      _o.Increase,
      _o.Levels);
  }
}

public class AssistWinRateAfterIapT
{
  public int Increase { get; set; }
  public int Levels { get; set; }

  public AssistWinRateAfterIapT() {
    this.Increase = 0;
    this.Levels = 0;
  }
}

public struct AssistUidAfterIap : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AssistUidAfterIap GetRootAsAssistUidAfterIap(ByteBuffer _bb) { return GetRootAsAssistUidAfterIap(_bb, new AssistUidAfterIap()); }
  public static AssistUidAfterIap GetRootAsAssistUidAfterIap(ByteBuffer _bb, AssistUidAfterIap obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AssistUidAfterIap __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int Levels { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLevels(int levels) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, levels); return true; } else { return false; } }
  public string AssistUid { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAssistUidBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetAssistUidBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetAssistUidArray() { return __p.__vector_as_array<byte>(6); }

  public static Offset<FBConfig.AssistUidAfterIap> CreateAssistUidAfterIap(FlatBufferBuilder builder,
      int levels = 0,
      StringOffset assist_uidOffset = default(StringOffset)) {
    builder.StartTable(2);
    AssistUidAfterIap.AddAssistUid(builder, assist_uidOffset);
    AssistUidAfterIap.AddLevels(builder, levels);
    return AssistUidAfterIap.EndAssistUidAfterIap(builder);
  }

  public static void StartAssistUidAfterIap(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddLevels(FlatBufferBuilder builder, int levels) { builder.AddInt(0, levels, 0); }
  public static void AddAssistUid(FlatBufferBuilder builder, StringOffset assistUidOffset) { builder.AddOffset(1, assistUidOffset.Value, 0); }
  public static Offset<FBConfig.AssistUidAfterIap> EndAssistUidAfterIap(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AssistUidAfterIap>(o);
  }
  public AssistUidAfterIapT UnPack() {
    var _o = new AssistUidAfterIapT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AssistUidAfterIapT _o) {
    _o.Levels = this.Levels;
    _o.AssistUid = this.AssistUid;
  }
  public static Offset<FBConfig.AssistUidAfterIap> Pack(FlatBufferBuilder builder, AssistUidAfterIapT _o) {
    if (_o == null) return default(Offset<FBConfig.AssistUidAfterIap>);
    var _assist_uid = _o.AssistUid == null ? default(StringOffset) : builder.CreateString(_o.AssistUid);
    return CreateAssistUidAfterIap(
      builder,
      _o.Levels,
      _assist_uid);
  }
}

public class AssistUidAfterIapT
{
  public int Levels { get; set; }
  public string AssistUid { get; set; }

  public AssistUidAfterIapT() {
    this.Levels = 0;
    this.AssistUid = null;
  }
}

public struct BrainCloudSettings : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BrainCloudSettings GetRootAsBrainCloudSettings(ByteBuffer _bb) { return GetRootAsBrainCloudSettings(_bb, new BrainCloudSettings()); }
  public static BrainCloudSettings GetRootAsBrainCloudSettings(ByteBuffer _bb, BrainCloudSettings obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BrainCloudSettings __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string ServerUrl { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetServerUrlBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetServerUrlBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetServerUrlArray() { return __p.__vector_as_array<byte>(4); }
  public string SecretKey { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSecretKeyBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetSecretKeyBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetSecretKeyArray() { return __p.__vector_as_array<byte>(6); }
  public string AppId { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAppIdBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetAppIdBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetAppIdArray() { return __p.__vector_as_array<byte>(8); }

  public static Offset<FBConfig.BrainCloudSettings> CreateBrainCloudSettings(FlatBufferBuilder builder,
      StringOffset server_urlOffset = default(StringOffset),
      StringOffset secret_keyOffset = default(StringOffset),
      StringOffset app_idOffset = default(StringOffset)) {
    builder.StartTable(3);
    BrainCloudSettings.AddAppId(builder, app_idOffset);
    BrainCloudSettings.AddSecretKey(builder, secret_keyOffset);
    BrainCloudSettings.AddServerUrl(builder, server_urlOffset);
    return BrainCloudSettings.EndBrainCloudSettings(builder);
  }

  public static void StartBrainCloudSettings(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddServerUrl(FlatBufferBuilder builder, StringOffset serverUrlOffset) { builder.AddOffset(0, serverUrlOffset.Value, 0); }
  public static void AddSecretKey(FlatBufferBuilder builder, StringOffset secretKeyOffset) { builder.AddOffset(1, secretKeyOffset.Value, 0); }
  public static void AddAppId(FlatBufferBuilder builder, StringOffset appIdOffset) { builder.AddOffset(2, appIdOffset.Value, 0); }
  public static Offset<FBConfig.BrainCloudSettings> EndBrainCloudSettings(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.BrainCloudSettings>(o);
  }
  public BrainCloudSettingsT UnPack() {
    var _o = new BrainCloudSettingsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BrainCloudSettingsT _o) {
    _o.ServerUrl = this.ServerUrl;
    _o.SecretKey = this.SecretKey;
    _o.AppId = this.AppId;
  }
  public static Offset<FBConfig.BrainCloudSettings> Pack(FlatBufferBuilder builder, BrainCloudSettingsT _o) {
    if (_o == null) return default(Offset<FBConfig.BrainCloudSettings>);
    var _server_url = _o.ServerUrl == null ? default(StringOffset) : builder.CreateString(_o.ServerUrl);
    var _secret_key = _o.SecretKey == null ? default(StringOffset) : builder.CreateString(_o.SecretKey);
    var _app_id = _o.AppId == null ? default(StringOffset) : builder.CreateString(_o.AppId);
    return CreateBrainCloudSettings(
      builder,
      _server_url,
      _secret_key,
      _app_id);
  }
}

public class BrainCloudSettingsT
{
  public string ServerUrl { get; set; }
  public string SecretKey { get; set; }
  public string AppId { get; set; }

  public BrainCloudSettingsT() {
    this.ServerUrl = null;
    this.SecretKey = null;
    this.AppId = null;
  }
}

public struct SaveGameProgressConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SaveGameProgressConfig GetRootAsSaveGameProgressConfig(ByteBuffer _bb) { return GetRootAsSaveGameProgressConfig(_bb, new SaveGameProgressConfig()); }
  public static SaveGameProgressConfig GetRootAsSaveGameProgressConfig(ByteBuffer _bb, SaveGameProgressConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SaveGameProgressConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int DebounceDelay { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDebounceDelay(int debounce_delay) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, debounce_delay); return true; } else { return false; } }
  public int MaxDelay { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxDelay(int max_delay) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_delay); return true; } else { return false; } }

  public static Offset<FBConfig.SaveGameProgressConfig> CreateSaveGameProgressConfig(FlatBufferBuilder builder,
      int debounce_delay = 0,
      int max_delay = 0) {
    builder.StartTable(2);
    SaveGameProgressConfig.AddMaxDelay(builder, max_delay);
    SaveGameProgressConfig.AddDebounceDelay(builder, debounce_delay);
    return SaveGameProgressConfig.EndSaveGameProgressConfig(builder);
  }

  public static void StartSaveGameProgressConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddDebounceDelay(FlatBufferBuilder builder, int debounceDelay) { builder.AddInt(0, debounceDelay, 0); }
  public static void AddMaxDelay(FlatBufferBuilder builder, int maxDelay) { builder.AddInt(1, maxDelay, 0); }
  public static Offset<FBConfig.SaveGameProgressConfig> EndSaveGameProgressConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SaveGameProgressConfig>(o);
  }
  public SaveGameProgressConfigT UnPack() {
    var _o = new SaveGameProgressConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SaveGameProgressConfigT _o) {
    _o.DebounceDelay = this.DebounceDelay;
    _o.MaxDelay = this.MaxDelay;
  }
  public static Offset<FBConfig.SaveGameProgressConfig> Pack(FlatBufferBuilder builder, SaveGameProgressConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SaveGameProgressConfig>);
    return CreateSaveGameProgressConfig(
      builder,
      _o.DebounceDelay,
      _o.MaxDelay);
  }
}

public class SaveGameProgressConfigT
{
  public int DebounceDelay { get; set; }
  public int MaxDelay { get; set; }

  public SaveGameProgressConfigT() {
    this.DebounceDelay = 0;
    this.MaxDelay = 0;
  }
}

public struct SystemConfig : IFlatbufferConfig<SystemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SystemConfig GetRootAsSystemConfig(ByteBuffer _bb) { return GetRootAsSystemConfig(_bb, new SystemConfig()); }
  public static SystemConfig GetRootAsSystemConfig(ByteBuffer _bb, SystemConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SystemConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string TripstagramVideoView { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTripstagramVideoViewBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTripstagramVideoViewBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTripstagramVideoViewArray() { return __p.__vector_as_array<byte>(6); }
  public bool VibrationsEnabled { get { int o = __p.__offset(8); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateVibrationsEnabled(bool vibrations_enabled) { int o = __p.__offset(8); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(vibrations_enabled ? 1 : 0)); return true; } else { return false; } }
  public float HapticQueuePause { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateHapticQueuePause(float haptic_queue_pause) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, haptic_queue_pause); return true; } else { return false; } }
  public float BackgroundRestartTime { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateBackgroundRestartTime(float background_restart_time) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, background_restart_time); return true; } else { return false; } }
  public int MaxFriendsCount { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxFriendsCount(int max_friends_count) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_friends_count); return true; } else { return false; } }
  public int ReplayEnabled { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateReplayEnabled(int replay_enabled) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, replay_enabled); return true; } else { return false; } }
  public string LineEnabledCountries { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLineEnabledCountriesBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetLineEnabledCountriesBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetLineEnabledCountriesArray() { return __p.__vector_as_array<byte>(18); }
  public bool GoogleSignInEnabled { get { int o = __p.__offset(20); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateGoogleSignInEnabled(bool google_sign_in_enabled) { int o = __p.__offset(20); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(google_sign_in_enabled ? 1 : 0)); return true; } else { return false; } }
  public FBConfig.ModalsConfig? ModalsConfig { get { int o = __p.__offset(22); return o != 0 ? (FBConfig.ModalsConfig?)(new FBConfig.ModalsConfig()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string EnableCachingForScreensObsolete(int j) { int o = __p.__offset(24); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int EnableCachingForScreensObsoleteLength { get { int o = __p.__offset(24); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string EnableCachingForScreens(int j) { int o = __p.__offset(26); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int EnableCachingForScreensLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string LoginReminderLevels(int j) { int o = __p.__offset(28); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int LoginReminderLevelsLength { get { int o = __p.__offset(28); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int AppsflyerLevelsReporting(int j) { int o = __p.__offset(30); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int AppsflyerLevelsReportingLength { get { int o = __p.__offset(30); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetAppsflyerLevelsReportingBytes() { return __p.__vector_as_span<int>(30, 4); }
#else
  public ArraySegment<byte>? GetAppsflyerLevelsReportingBytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public int[] GetAppsflyerLevelsReportingArray() { return __p.__vector_as_array<int>(30); }
  public bool MutateAppsflyerLevelsReporting(int j, int appsflyer_levels_reporting) { int o = __p.__offset(30); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, appsflyer_levels_reporting); return true; } else { return false; } }
  public bool LoadingTrackingDisabled { get { int o = __p.__offset(32); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateLoadingTrackingDisabled(bool loading_tracking_disabled) { int o = __p.__offset(32); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(loading_tracking_disabled ? 1 : 0)); return true; } else { return false; } }
  public int LoadingTrackingLimit { get { int o = __p.__offset(34); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLoadingTrackingLimit(int loading_tracking_limit) { int o = __p.__offset(34); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, loading_tracking_limit); return true; } else { return false; } }
  public int LoadingTrackingThreshold { get { int o = __p.__offset(36); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLoadingTrackingThreshold(int loading_tracking_threshold) { int o = __p.__offset(36); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, loading_tracking_threshold); return true; } else { return false; } }
  public int HitchFpsThreshold { get { int o = __p.__offset(38); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateHitchFpsThreshold(int hitch_fps_threshold) { int o = __p.__offset(38); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, hitch_fps_threshold); return true; } else { return false; } }
  public bool FpsTrackingDisabled { get { int o = __p.__offset(40); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateFpsTrackingDisabled(bool fps_tracking_disabled) { int o = __p.__offset(40); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(fps_tracking_disabled ? 1 : 0)); return true; } else { return false; } }
  public bool SmartlookEnabled { get { int o = __p.__offset(42); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateSmartlookEnabled(bool smartlook_enabled) { int o = __p.__offset(42); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(smartlook_enabled ? 1 : 0)); return true; } else { return false; } }
  public int SmartlookSessionCountLimit { get { int o = __p.__offset(44); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSmartlookSessionCountLimit(int smartlook_session_count_limit) { int o = __p.__offset(44); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, smartlook_session_count_limit); return true; } else { return false; } }
  public float SmartlookUsersPercent { get { int o = __p.__offset(46); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateSmartlookUsersPercent(float smartlook_users_percent) { int o = __p.__offset(46); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, smartlook_users_percent); return true; } else { return false; } }
  public int SmartlookMaxLevel { get { int o = __p.__offset(48); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSmartlookMaxLevel(int smartlook_max_level) { int o = __p.__offset(48); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, smartlook_max_level); return true; } else { return false; } }
  public FBConfig.DictStringInt? LiveopsAnalytics(int j) { int o = __p.__offset(50); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int LiveopsAnalyticsLength { get { int o = __p.__offset(50); return o != 0 ? __p.__vector_len(o) : 0; } }
  public float DetailedAnalyticsUsersPercent { get { int o = __p.__offset(52); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateDetailedAnalyticsUsersPercent(float detailed_analytics_users_percent) { int o = __p.__offset(52); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, detailed_analytics_users_percent); return true; } else { return false; } }
  public int AssistWinAfterIap { get { int o = __p.__offset(54); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateAssistWinAfterIap(int assist_win_after_iap) { int o = __p.__offset(54); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, assist_win_after_iap); return true; } else { return false; } }
  public FBConfig.AssistWinRateAfterIap? AssistWinRateAfterIap { get { int o = __p.__offset(56); return o != 0 ? (FBConfig.AssistWinRateAfterIap?)(new FBConfig.AssistWinRateAfterIap()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int LevelsPerConfig { get { int o = __p.__offset(58); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLevelsPerConfig(int levels_per_config) { int o = __p.__offset(58); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, levels_per_config); return true; } else { return false; } }
  public int MaxProgressionLevel { get { int o = __p.__offset(60); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxProgressionLevel(int max_progression_level) { int o = __p.__offset(60); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_progression_level); return true; } else { return false; } }
  public FBConfig.AssistUidAfterIap? AssistUidAfterIap { get { int o = __p.__offset(62); return o != 0 ? (FBConfig.AssistUidAfterIap?)(new FBConfig.AssistUidAfterIap()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string DefaultAssistUidByLossLevel(int j) { int o = __p.__offset(64); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int DefaultAssistUidByLossLevelLength { get { int o = __p.__offset(64); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int DefaultAssistLossLevel(int j) { int o = __p.__offset(66); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int DefaultAssistLossLevelLength { get { int o = __p.__offset(66); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetDefaultAssistLossLevelBytes() { return __p.__vector_as_span<int>(66, 4); }
#else
  public ArraySegment<byte>? GetDefaultAssistLossLevelBytes() { return __p.__vector_as_arraysegment(66); }
#endif
  public int[] GetDefaultAssistLossLevelArray() { return __p.__vector_as_array<int>(66); }
  public bool MutateDefaultAssistLossLevel(int j, int default_assist_loss_level) { int o = __p.__offset(66); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, default_assist_loss_level); return true; } else { return false; } }
  public string LevelSuccessFennecAnimations(int j) { int o = __p.__offset(68); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int LevelSuccessFennecAnimationsLength { get { int o = __p.__offset(68); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.BrainCloudSettings? BrainCloudSettings { get { int o = __p.__offset(70); return o != 0 ? (FBConfig.BrainCloudSettings?)(new FBConfig.BrainCloudSettings()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.SaveGameProgressConfig? SaveGameProgressConfig { get { int o = __p.__offset(72); return o != 0 ? (FBConfig.SaveGameProgressConfig?)(new FBConfig.SaveGameProgressConfig()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public bool FacebookSignInEnabled { get { int o = __p.__offset(74); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateFacebookSignInEnabled(bool facebook_sign_in_enabled) { int o = __p.__offset(74); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(facebook_sign_in_enabled ? 1 : 0)); return true; } else { return false; } }
  public bool AppleSignInEnabled { get { int o = __p.__offset(76); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateAppleSignInEnabled(bool apple_sign_in_enabled) { int o = __p.__offset(76); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(apple_sign_in_enabled ? 1 : 0)); return true; } else { return false; } }
  public int LocalNotifierTimeOffsetInMinutes { get { int o = __p.__offset(78); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateLocalNotifierTimeOffsetInMinutes(int local_notifier_time_offset_in_minutes) { int o = __p.__offset(78); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, local_notifier_time_offset_in_minutes); return true; } else { return false; } }

  public static Offset<FBConfig.SystemConfig> CreateSystemConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset tripstagram_video_viewOffset = default(StringOffset),
      bool vibrations_enabled = false,
      float haptic_queue_pause = 0.0f,
      float background_restart_time = 0.0f,
      int max_friends_count = 0,
      int replay_enabled = 0,
      StringOffset line_enabled_countriesOffset = default(StringOffset),
      bool google_sign_in_enabled = false,
      Offset<FBConfig.ModalsConfig> modals_configOffset = default(Offset<FBConfig.ModalsConfig>),
      VectorOffset enable_caching_for_screens_obsoleteOffset = default(VectorOffset),
      VectorOffset enable_caching_for_screensOffset = default(VectorOffset),
      VectorOffset login_reminder_levelsOffset = default(VectorOffset),
      VectorOffset appsflyer_levels_reportingOffset = default(VectorOffset),
      bool loading_tracking_disabled = false,
      int loading_tracking_limit = 0,
      int loading_tracking_threshold = 0,
      int hitch_fps_threshold = 0,
      bool fps_tracking_disabled = false,
      bool smartlook_enabled = false,
      int smartlook_session_count_limit = 0,
      float smartlook_users_percent = 0.0f,
      int smartlook_max_level = 0,
      VectorOffset liveops_analyticsOffset = default(VectorOffset),
      float detailed_analytics_users_percent = 0.0f,
      int assist_win_after_iap = 0,
      Offset<FBConfig.AssistWinRateAfterIap> assist_win_rate_after_iapOffset = default(Offset<FBConfig.AssistWinRateAfterIap>),
      int levels_per_config = 0,
      int max_progression_level = 0,
      Offset<FBConfig.AssistUidAfterIap> assist_uid_after_iapOffset = default(Offset<FBConfig.AssistUidAfterIap>),
      VectorOffset default_assist_uid_by_loss_levelOffset = default(VectorOffset),
      VectorOffset default_assist_loss_levelOffset = default(VectorOffset),
      VectorOffset level_success_fennec_animationsOffset = default(VectorOffset),
      Offset<FBConfig.BrainCloudSettings> brain_cloud_settingsOffset = default(Offset<FBConfig.BrainCloudSettings>),
      Offset<FBConfig.SaveGameProgressConfig> save_game_progress_configOffset = default(Offset<FBConfig.SaveGameProgressConfig>),
      bool facebook_sign_in_enabled = false,
      bool apple_sign_in_enabled = false,
      int local_notifier_time_offset_in_minutes = 0) {
    builder.StartTable(38);
    SystemConfig.AddLocalNotifierTimeOffsetInMinutes(builder, local_notifier_time_offset_in_minutes);
    SystemConfig.AddSaveGameProgressConfig(builder, save_game_progress_configOffset);
    SystemConfig.AddBrainCloudSettings(builder, brain_cloud_settingsOffset);
    SystemConfig.AddLevelSuccessFennecAnimations(builder, level_success_fennec_animationsOffset);
    SystemConfig.AddDefaultAssistLossLevel(builder, default_assist_loss_levelOffset);
    SystemConfig.AddDefaultAssistUidByLossLevel(builder, default_assist_uid_by_loss_levelOffset);
    SystemConfig.AddAssistUidAfterIap(builder, assist_uid_after_iapOffset);
    SystemConfig.AddMaxProgressionLevel(builder, max_progression_level);
    SystemConfig.AddLevelsPerConfig(builder, levels_per_config);
    SystemConfig.AddAssistWinRateAfterIap(builder, assist_win_rate_after_iapOffset);
    SystemConfig.AddAssistWinAfterIap(builder, assist_win_after_iap);
    SystemConfig.AddDetailedAnalyticsUsersPercent(builder, detailed_analytics_users_percent);
    SystemConfig.AddLiveopsAnalytics(builder, liveops_analyticsOffset);
    SystemConfig.AddSmartlookMaxLevel(builder, smartlook_max_level);
    SystemConfig.AddSmartlookUsersPercent(builder, smartlook_users_percent);
    SystemConfig.AddSmartlookSessionCountLimit(builder, smartlook_session_count_limit);
    SystemConfig.AddHitchFpsThreshold(builder, hitch_fps_threshold);
    SystemConfig.AddLoadingTrackingThreshold(builder, loading_tracking_threshold);
    SystemConfig.AddLoadingTrackingLimit(builder, loading_tracking_limit);
    SystemConfig.AddAppsflyerLevelsReporting(builder, appsflyer_levels_reportingOffset);
    SystemConfig.AddLoginReminderLevels(builder, login_reminder_levelsOffset);
    SystemConfig.AddEnableCachingForScreens(builder, enable_caching_for_screensOffset);
    SystemConfig.AddEnableCachingForScreensObsolete(builder, enable_caching_for_screens_obsoleteOffset);
    SystemConfig.AddModalsConfig(builder, modals_configOffset);
    SystemConfig.AddLineEnabledCountries(builder, line_enabled_countriesOffset);
    SystemConfig.AddReplayEnabled(builder, replay_enabled);
    SystemConfig.AddMaxFriendsCount(builder, max_friends_count);
    SystemConfig.AddBackgroundRestartTime(builder, background_restart_time);
    SystemConfig.AddHapticQueuePause(builder, haptic_queue_pause);
    SystemConfig.AddTripstagramVideoView(builder, tripstagram_video_viewOffset);
    SystemConfig.AddUid(builder, uidOffset);
    SystemConfig.AddAppleSignInEnabled(builder, apple_sign_in_enabled);
    SystemConfig.AddFacebookSignInEnabled(builder, facebook_sign_in_enabled);
    SystemConfig.AddSmartlookEnabled(builder, smartlook_enabled);
    SystemConfig.AddFpsTrackingDisabled(builder, fps_tracking_disabled);
    SystemConfig.AddLoadingTrackingDisabled(builder, loading_tracking_disabled);
    SystemConfig.AddGoogleSignInEnabled(builder, google_sign_in_enabled);
    SystemConfig.AddVibrationsEnabled(builder, vibrations_enabled);
    return SystemConfig.EndSystemConfig(builder);
  }

  public static void StartSystemConfig(FlatBufferBuilder builder) { builder.StartTable(38); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTripstagramVideoView(FlatBufferBuilder builder, StringOffset tripstagramVideoViewOffset) { builder.AddOffset(1, tripstagramVideoViewOffset.Value, 0); }
  public static void AddVibrationsEnabled(FlatBufferBuilder builder, bool vibrationsEnabled) { builder.AddBool(2, vibrationsEnabled, false); }
  public static void AddHapticQueuePause(FlatBufferBuilder builder, float hapticQueuePause) { builder.AddFloat(3, hapticQueuePause, 0.0f); }
  public static void AddBackgroundRestartTime(FlatBufferBuilder builder, float backgroundRestartTime) { builder.AddFloat(4, backgroundRestartTime, 0.0f); }
  public static void AddMaxFriendsCount(FlatBufferBuilder builder, int maxFriendsCount) { builder.AddInt(5, maxFriendsCount, 0); }
  public static void AddReplayEnabled(FlatBufferBuilder builder, int replayEnabled) { builder.AddInt(6, replayEnabled, 0); }
  public static void AddLineEnabledCountries(FlatBufferBuilder builder, StringOffset lineEnabledCountriesOffset) { builder.AddOffset(7, lineEnabledCountriesOffset.Value, 0); }
  public static void AddGoogleSignInEnabled(FlatBufferBuilder builder, bool googleSignInEnabled) { builder.AddBool(8, googleSignInEnabled, false); }
  public static void AddModalsConfig(FlatBufferBuilder builder, Offset<FBConfig.ModalsConfig> modalsConfigOffset) { builder.AddOffset(9, modalsConfigOffset.Value, 0); }
  public static void AddEnableCachingForScreensObsolete(FlatBufferBuilder builder, VectorOffset enableCachingForScreensObsoleteOffset) { builder.AddOffset(10, enableCachingForScreensObsoleteOffset.Value, 0); }
  public static VectorOffset CreateEnableCachingForScreensObsoleteVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateEnableCachingForScreensObsoleteVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEnableCachingForScreensObsoleteVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEnableCachingForScreensObsoleteVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartEnableCachingForScreensObsoleteVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddEnableCachingForScreens(FlatBufferBuilder builder, VectorOffset enableCachingForScreensOffset) { builder.AddOffset(11, enableCachingForScreensOffset.Value, 0); }
  public static VectorOffset CreateEnableCachingForScreensVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateEnableCachingForScreensVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEnableCachingForScreensVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEnableCachingForScreensVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartEnableCachingForScreensVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLoginReminderLevels(FlatBufferBuilder builder, VectorOffset loginReminderLevelsOffset) { builder.AddOffset(12, loginReminderLevelsOffset.Value, 0); }
  public static VectorOffset CreateLoginReminderLevelsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLoginReminderLevelsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLoginReminderLevelsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLoginReminderLevelsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLoginReminderLevelsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAppsflyerLevelsReporting(FlatBufferBuilder builder, VectorOffset appsflyerLevelsReportingOffset) { builder.AddOffset(13, appsflyerLevelsReportingOffset.Value, 0); }
  public static VectorOffset CreateAppsflyerLevelsReportingVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateAppsflyerLevelsReportingVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAppsflyerLevelsReportingVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAppsflyerLevelsReportingVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAppsflyerLevelsReportingVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLoadingTrackingDisabled(FlatBufferBuilder builder, bool loadingTrackingDisabled) { builder.AddBool(14, loadingTrackingDisabled, false); }
  public static void AddLoadingTrackingLimit(FlatBufferBuilder builder, int loadingTrackingLimit) { builder.AddInt(15, loadingTrackingLimit, 0); }
  public static void AddLoadingTrackingThreshold(FlatBufferBuilder builder, int loadingTrackingThreshold) { builder.AddInt(16, loadingTrackingThreshold, 0); }
  public static void AddHitchFpsThreshold(FlatBufferBuilder builder, int hitchFpsThreshold) { builder.AddInt(17, hitchFpsThreshold, 0); }
  public static void AddFpsTrackingDisabled(FlatBufferBuilder builder, bool fpsTrackingDisabled) { builder.AddBool(18, fpsTrackingDisabled, false); }
  public static void AddSmartlookEnabled(FlatBufferBuilder builder, bool smartlookEnabled) { builder.AddBool(19, smartlookEnabled, false); }
  public static void AddSmartlookSessionCountLimit(FlatBufferBuilder builder, int smartlookSessionCountLimit) { builder.AddInt(20, smartlookSessionCountLimit, 0); }
  public static void AddSmartlookUsersPercent(FlatBufferBuilder builder, float smartlookUsersPercent) { builder.AddFloat(21, smartlookUsersPercent, 0.0f); }
  public static void AddSmartlookMaxLevel(FlatBufferBuilder builder, int smartlookMaxLevel) { builder.AddInt(22, smartlookMaxLevel, 0); }
  public static void AddLiveopsAnalytics(FlatBufferBuilder builder, VectorOffset liveopsAnalyticsOffset) { builder.AddOffset(23, liveopsAnalyticsOffset.Value, 0); }
  public static VectorOffset CreateLiveopsAnalyticsVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLiveopsAnalyticsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLiveopsAnalyticsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLiveopsAnalyticsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLiveopsAnalyticsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddDetailedAnalyticsUsersPercent(FlatBufferBuilder builder, float detailedAnalyticsUsersPercent) { builder.AddFloat(24, detailedAnalyticsUsersPercent, 0.0f); }
  public static void AddAssistWinAfterIap(FlatBufferBuilder builder, int assistWinAfterIap) { builder.AddInt(25, assistWinAfterIap, 0); }
  public static void AddAssistWinRateAfterIap(FlatBufferBuilder builder, Offset<FBConfig.AssistWinRateAfterIap> assistWinRateAfterIapOffset) { builder.AddOffset(26, assistWinRateAfterIapOffset.Value, 0); }
  public static void AddLevelsPerConfig(FlatBufferBuilder builder, int levelsPerConfig) { builder.AddInt(27, levelsPerConfig, 0); }
  public static void AddMaxProgressionLevel(FlatBufferBuilder builder, int maxProgressionLevel) { builder.AddInt(28, maxProgressionLevel, 0); }
  public static void AddAssistUidAfterIap(FlatBufferBuilder builder, Offset<FBConfig.AssistUidAfterIap> assistUidAfterIapOffset) { builder.AddOffset(29, assistUidAfterIapOffset.Value, 0); }
  public static void AddDefaultAssistUidByLossLevel(FlatBufferBuilder builder, VectorOffset defaultAssistUidByLossLevelOffset) { builder.AddOffset(30, defaultAssistUidByLossLevelOffset.Value, 0); }
  public static VectorOffset CreateDefaultAssistUidByLossLevelVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateDefaultAssistUidByLossLevelVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDefaultAssistUidByLossLevelVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDefaultAssistUidByLossLevelVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDefaultAssistUidByLossLevelVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddDefaultAssistLossLevel(FlatBufferBuilder builder, VectorOffset defaultAssistLossLevelOffset) { builder.AddOffset(31, defaultAssistLossLevelOffset.Value, 0); }
  public static VectorOffset CreateDefaultAssistLossLevelVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateDefaultAssistLossLevelVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDefaultAssistLossLevelVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateDefaultAssistLossLevelVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartDefaultAssistLossLevelVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLevelSuccessFennecAnimations(FlatBufferBuilder builder, VectorOffset levelSuccessFennecAnimationsOffset) { builder.AddOffset(32, levelSuccessFennecAnimationsOffset.Value, 0); }
  public static VectorOffset CreateLevelSuccessFennecAnimationsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLevelSuccessFennecAnimationsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLevelSuccessFennecAnimationsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLevelSuccessFennecAnimationsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLevelSuccessFennecAnimationsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBrainCloudSettings(FlatBufferBuilder builder, Offset<FBConfig.BrainCloudSettings> brainCloudSettingsOffset) { builder.AddOffset(33, brainCloudSettingsOffset.Value, 0); }
  public static void AddSaveGameProgressConfig(FlatBufferBuilder builder, Offset<FBConfig.SaveGameProgressConfig> saveGameProgressConfigOffset) { builder.AddOffset(34, saveGameProgressConfigOffset.Value, 0); }
  public static void AddFacebookSignInEnabled(FlatBufferBuilder builder, bool facebookSignInEnabled) { builder.AddBool(35, facebookSignInEnabled, false); }
  public static void AddAppleSignInEnabled(FlatBufferBuilder builder, bool appleSignInEnabled) { builder.AddBool(36, appleSignInEnabled, false); }
  public static void AddLocalNotifierTimeOffsetInMinutes(FlatBufferBuilder builder, int localNotifierTimeOffsetInMinutes) { builder.AddInt(37, localNotifierTimeOffsetInMinutes, 0); }
  public static Offset<FBConfig.SystemConfig> EndSystemConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.SystemConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfSystemConfig(FlatBufferBuilder builder, Offset<SystemConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<SystemConfig> o1, Offset<SystemConfig> o2) =>
        new SystemConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new SystemConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SystemConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    SystemConfig obj_ = new SystemConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public SystemConfigT UnPack() {
    var _o = new SystemConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SystemConfigT _o) {
    _o.Uid = this.Uid;
    _o.TripstagramVideoView = this.TripstagramVideoView;
    _o.VibrationsEnabled = this.VibrationsEnabled;
    _o.HapticQueuePause = this.HapticQueuePause;
    _o.BackgroundRestartTime = this.BackgroundRestartTime;
    _o.MaxFriendsCount = this.MaxFriendsCount;
    _o.ReplayEnabled = this.ReplayEnabled;
    _o.LineEnabledCountries = this.LineEnabledCountries;
    _o.GoogleSignInEnabled = this.GoogleSignInEnabled;
    _o.ModalsConfig = this.ModalsConfig.HasValue ? this.ModalsConfig.Value.UnPack() : null;
    _o.EnableCachingForScreensObsolete = new List<string>();
    for (var _j = 0; _j < this.EnableCachingForScreensObsoleteLength; ++_j) {_o.EnableCachingForScreensObsolete.Add(this.EnableCachingForScreensObsolete(_j));}
    _o.EnableCachingForScreens = new List<string>();
    for (var _j = 0; _j < this.EnableCachingForScreensLength; ++_j) {_o.EnableCachingForScreens.Add(this.EnableCachingForScreens(_j));}
    _o.LoginReminderLevels = new List<string>();
    for (var _j = 0; _j < this.LoginReminderLevelsLength; ++_j) {_o.LoginReminderLevels.Add(this.LoginReminderLevels(_j));}
    _o.AppsflyerLevelsReporting = new List<int>();
    for (var _j = 0; _j < this.AppsflyerLevelsReportingLength; ++_j) {_o.AppsflyerLevelsReporting.Add(this.AppsflyerLevelsReporting(_j));}
    _o.LoadingTrackingDisabled = this.LoadingTrackingDisabled;
    _o.LoadingTrackingLimit = this.LoadingTrackingLimit;
    _o.LoadingTrackingThreshold = this.LoadingTrackingThreshold;
    _o.HitchFpsThreshold = this.HitchFpsThreshold;
    _o.FpsTrackingDisabled = this.FpsTrackingDisabled;
    _o.SmartlookEnabled = this.SmartlookEnabled;
    _o.SmartlookSessionCountLimit = this.SmartlookSessionCountLimit;
    _o.SmartlookUsersPercent = this.SmartlookUsersPercent;
    _o.SmartlookMaxLevel = this.SmartlookMaxLevel;
    _o.LiveopsAnalytics = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.LiveopsAnalyticsLength; ++_j) {_o.LiveopsAnalytics.Add(this.LiveopsAnalytics(_j).HasValue ? this.LiveopsAnalytics(_j).Value.UnPack() : null);}
    _o.DetailedAnalyticsUsersPercent = this.DetailedAnalyticsUsersPercent;
    _o.AssistWinAfterIap = this.AssistWinAfterIap;
    _o.AssistWinRateAfterIap = this.AssistWinRateAfterIap.HasValue ? this.AssistWinRateAfterIap.Value.UnPack() : null;
    _o.LevelsPerConfig = this.LevelsPerConfig;
    _o.MaxProgressionLevel = this.MaxProgressionLevel;
    _o.AssistUidAfterIap = this.AssistUidAfterIap.HasValue ? this.AssistUidAfterIap.Value.UnPack() : null;
    _o.DefaultAssistUidByLossLevel = new List<string>();
    for (var _j = 0; _j < this.DefaultAssistUidByLossLevelLength; ++_j) {_o.DefaultAssistUidByLossLevel.Add(this.DefaultAssistUidByLossLevel(_j));}
    _o.DefaultAssistLossLevel = new List<int>();
    for (var _j = 0; _j < this.DefaultAssistLossLevelLength; ++_j) {_o.DefaultAssistLossLevel.Add(this.DefaultAssistLossLevel(_j));}
    _o.LevelSuccessFennecAnimations = new List<string>();
    for (var _j = 0; _j < this.LevelSuccessFennecAnimationsLength; ++_j) {_o.LevelSuccessFennecAnimations.Add(this.LevelSuccessFennecAnimations(_j));}
    _o.BrainCloudSettings = this.BrainCloudSettings.HasValue ? this.BrainCloudSettings.Value.UnPack() : null;
    _o.SaveGameProgressConfig = this.SaveGameProgressConfig.HasValue ? this.SaveGameProgressConfig.Value.UnPack() : null;
    _o.FacebookSignInEnabled = this.FacebookSignInEnabled;
    _o.AppleSignInEnabled = this.AppleSignInEnabled;
    _o.LocalNotifierTimeOffsetInMinutes = this.LocalNotifierTimeOffsetInMinutes;
  }
  public static Offset<FBConfig.SystemConfig> Pack(FlatBufferBuilder builder, SystemConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.SystemConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _tripstagram_video_view = _o.TripstagramVideoView == null ? default(StringOffset) : builder.CreateString(_o.TripstagramVideoView);
    var _line_enabled_countries = _o.LineEnabledCountries == null ? default(StringOffset) : builder.CreateString(_o.LineEnabledCountries);
    var _modals_config = _o.ModalsConfig == null ? default(Offset<FBConfig.ModalsConfig>) : FBConfig.ModalsConfig.Pack(builder, _o.ModalsConfig);
    var _enable_caching_for_screens_obsolete = default(VectorOffset);
    if (_o.EnableCachingForScreensObsolete != null) {
      var __enable_caching_for_screens_obsolete = new StringOffset[_o.EnableCachingForScreensObsolete.Count];
      for (var _j = 0; _j < __enable_caching_for_screens_obsolete.Length; ++_j) { __enable_caching_for_screens_obsolete[_j] = builder.CreateString(_o.EnableCachingForScreensObsolete[_j]); }
      _enable_caching_for_screens_obsolete = CreateEnableCachingForScreensObsoleteVector(builder, __enable_caching_for_screens_obsolete);
    }
    var _enable_caching_for_screens = default(VectorOffset);
    if (_o.EnableCachingForScreens != null) {
      var __enable_caching_for_screens = new StringOffset[_o.EnableCachingForScreens.Count];
      for (var _j = 0; _j < __enable_caching_for_screens.Length; ++_j) { __enable_caching_for_screens[_j] = builder.CreateString(_o.EnableCachingForScreens[_j]); }
      _enable_caching_for_screens = CreateEnableCachingForScreensVector(builder, __enable_caching_for_screens);
    }
    var _login_reminder_levels = default(VectorOffset);
    if (_o.LoginReminderLevels != null) {
      var __login_reminder_levels = new StringOffset[_o.LoginReminderLevels.Count];
      for (var _j = 0; _j < __login_reminder_levels.Length; ++_j) { __login_reminder_levels[_j] = builder.CreateString(_o.LoginReminderLevels[_j]); }
      _login_reminder_levels = CreateLoginReminderLevelsVector(builder, __login_reminder_levels);
    }
    var _appsflyer_levels_reporting = default(VectorOffset);
    if (_o.AppsflyerLevelsReporting != null) {
      var __appsflyer_levels_reporting = _o.AppsflyerLevelsReporting.ToArray();
      _appsflyer_levels_reporting = CreateAppsflyerLevelsReportingVector(builder, __appsflyer_levels_reporting);
    }
    var _liveops_analytics = default(VectorOffset);
    if (_o.LiveopsAnalytics != null) {
      var __liveops_analytics = new Offset<FBConfig.DictStringInt>[_o.LiveopsAnalytics.Count];
      for (var _j = 0; _j < __liveops_analytics.Length; ++_j) { __liveops_analytics[_j] = FBConfig.DictStringInt.Pack(builder, _o.LiveopsAnalytics[_j]); }
      _liveops_analytics = CreateLiveopsAnalyticsVector(builder, __liveops_analytics);
    }
    var _assist_win_rate_after_iap = _o.AssistWinRateAfterIap == null ? default(Offset<FBConfig.AssistWinRateAfterIap>) : FBConfig.AssistWinRateAfterIap.Pack(builder, _o.AssistWinRateAfterIap);
    var _assist_uid_after_iap = _o.AssistUidAfterIap == null ? default(Offset<FBConfig.AssistUidAfterIap>) : FBConfig.AssistUidAfterIap.Pack(builder, _o.AssistUidAfterIap);
    var _default_assist_uid_by_loss_level = default(VectorOffset);
    if (_o.DefaultAssistUidByLossLevel != null) {
      var __default_assist_uid_by_loss_level = new StringOffset[_o.DefaultAssistUidByLossLevel.Count];
      for (var _j = 0; _j < __default_assist_uid_by_loss_level.Length; ++_j) { __default_assist_uid_by_loss_level[_j] = builder.CreateString(_o.DefaultAssistUidByLossLevel[_j]); }
      _default_assist_uid_by_loss_level = CreateDefaultAssistUidByLossLevelVector(builder, __default_assist_uid_by_loss_level);
    }
    var _default_assist_loss_level = default(VectorOffset);
    if (_o.DefaultAssistLossLevel != null) {
      var __default_assist_loss_level = _o.DefaultAssistLossLevel.ToArray();
      _default_assist_loss_level = CreateDefaultAssistLossLevelVector(builder, __default_assist_loss_level);
    }
    var _level_success_fennec_animations = default(VectorOffset);
    if (_o.LevelSuccessFennecAnimations != null) {
      var __level_success_fennec_animations = new StringOffset[_o.LevelSuccessFennecAnimations.Count];
      for (var _j = 0; _j < __level_success_fennec_animations.Length; ++_j) { __level_success_fennec_animations[_j] = builder.CreateString(_o.LevelSuccessFennecAnimations[_j]); }
      _level_success_fennec_animations = CreateLevelSuccessFennecAnimationsVector(builder, __level_success_fennec_animations);
    }
    var _brain_cloud_settings = _o.BrainCloudSettings == null ? default(Offset<FBConfig.BrainCloudSettings>) : FBConfig.BrainCloudSettings.Pack(builder, _o.BrainCloudSettings);
    var _save_game_progress_config = _o.SaveGameProgressConfig == null ? default(Offset<FBConfig.SaveGameProgressConfig>) : FBConfig.SaveGameProgressConfig.Pack(builder, _o.SaveGameProgressConfig);
    return CreateSystemConfig(
      builder,
      _uid,
      _tripstagram_video_view,
      _o.VibrationsEnabled,
      _o.HapticQueuePause,
      _o.BackgroundRestartTime,
      _o.MaxFriendsCount,
      _o.ReplayEnabled,
      _line_enabled_countries,
      _o.GoogleSignInEnabled,
      _modals_config,
      _enable_caching_for_screens_obsolete,
      _enable_caching_for_screens,
      _login_reminder_levels,
      _appsflyer_levels_reporting,
      _o.LoadingTrackingDisabled,
      _o.LoadingTrackingLimit,
      _o.LoadingTrackingThreshold,
      _o.HitchFpsThreshold,
      _o.FpsTrackingDisabled,
      _o.SmartlookEnabled,
      _o.SmartlookSessionCountLimit,
      _o.SmartlookUsersPercent,
      _o.SmartlookMaxLevel,
      _liveops_analytics,
      _o.DetailedAnalyticsUsersPercent,
      _o.AssistWinAfterIap,
      _assist_win_rate_after_iap,
      _o.LevelsPerConfig,
      _o.MaxProgressionLevel,
      _assist_uid_after_iap,
      _default_assist_uid_by_loss_level,
      _default_assist_loss_level,
      _level_success_fennec_animations,
      _brain_cloud_settings,
      _save_game_progress_config,
      _o.FacebookSignInEnabled,
      _o.AppleSignInEnabled,
      _o.LocalNotifierTimeOffsetInMinutes);
  }
}

public class SystemConfigT
{
  public string Uid { get; set; }
  public string TripstagramVideoView { get; set; }
  public bool VibrationsEnabled { get; set; }
  public float HapticQueuePause { get; set; }
  public float BackgroundRestartTime { get; set; }
  public int MaxFriendsCount { get; set; }
  public int ReplayEnabled { get; set; }
  public string LineEnabledCountries { get; set; }
  public bool GoogleSignInEnabled { get; set; }
  public FBConfig.ModalsConfigT ModalsConfig { get; set; }
  public List<string> EnableCachingForScreensObsolete { get; set; }
  public List<string> EnableCachingForScreens { get; set; }
  public List<string> LoginReminderLevels { get; set; }
  public List<int> AppsflyerLevelsReporting { get; set; }
  public bool LoadingTrackingDisabled { get; set; }
  public int LoadingTrackingLimit { get; set; }
  public int LoadingTrackingThreshold { get; set; }
  public int HitchFpsThreshold { get; set; }
  public bool FpsTrackingDisabled { get; set; }
  public bool SmartlookEnabled { get; set; }
  public int SmartlookSessionCountLimit { get; set; }
  public float SmartlookUsersPercent { get; set; }
  public int SmartlookMaxLevel { get; set; }
  public List<FBConfig.DictStringIntT> LiveopsAnalytics { get; set; }
  public float DetailedAnalyticsUsersPercent { get; set; }
  public int AssistWinAfterIap { get; set; }
  public FBConfig.AssistWinRateAfterIapT AssistWinRateAfterIap { get; set; }
  public int LevelsPerConfig { get; set; }
  public int MaxProgressionLevel { get; set; }
  public FBConfig.AssistUidAfterIapT AssistUidAfterIap { get; set; }
  public List<string> DefaultAssistUidByLossLevel { get; set; }
  public List<int> DefaultAssistLossLevel { get; set; }
  public List<string> LevelSuccessFennecAnimations { get; set; }
  public FBConfig.BrainCloudSettingsT BrainCloudSettings { get; set; }
  public FBConfig.SaveGameProgressConfigT SaveGameProgressConfig { get; set; }
  public bool FacebookSignInEnabled { get; set; }
  public bool AppleSignInEnabled { get; set; }
  public int LocalNotifierTimeOffsetInMinutes { get; set; }

  public SystemConfigT() {
    this.Uid = null;
    this.TripstagramVideoView = null;
    this.VibrationsEnabled = false;
    this.HapticQueuePause = 0.0f;
    this.BackgroundRestartTime = 0.0f;
    this.MaxFriendsCount = 0;
    this.ReplayEnabled = 0;
    this.LineEnabledCountries = null;
    this.GoogleSignInEnabled = false;
    this.ModalsConfig = null;
    this.EnableCachingForScreensObsolete = null;
    this.EnableCachingForScreens = null;
    this.LoginReminderLevels = null;
    this.AppsflyerLevelsReporting = null;
    this.LoadingTrackingDisabled = false;
    this.LoadingTrackingLimit = 0;
    this.LoadingTrackingThreshold = 0;
    this.HitchFpsThreshold = 0;
    this.FpsTrackingDisabled = false;
    this.SmartlookEnabled = false;
    this.SmartlookSessionCountLimit = 0;
    this.SmartlookUsersPercent = 0.0f;
    this.SmartlookMaxLevel = 0;
    this.LiveopsAnalytics = null;
    this.DetailedAnalyticsUsersPercent = 0.0f;
    this.AssistWinAfterIap = 0;
    this.AssistWinRateAfterIap = null;
    this.LevelsPerConfig = 0;
    this.MaxProgressionLevel = 0;
    this.AssistUidAfterIap = null;
    this.DefaultAssistUidByLossLevel = null;
    this.DefaultAssistLossLevel = null;
    this.LevelSuccessFennecAnimations = null;
    this.BrainCloudSettings = null;
    this.SaveGameProgressConfig = null;
    this.FacebookSignInEnabled = false;
    this.AppleSignInEnabled = false;
    this.LocalNotifierTimeOffsetInMinutes = 0;
  }
}

public struct SystemConfigDict : IFlatbufferConfigDict<SystemConfig, SystemConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static SystemConfigDict GetRootAsSystemConfigDict(ByteBuffer _bb) { return GetRootAsSystemConfigDict(_bb, new SystemConfigDict()); }
  public static SystemConfigDict GetRootAsSystemConfigDict(ByteBuffer _bb, SystemConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SystemConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.SystemConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.SystemConfig?)(new FBConfig.SystemConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.SystemConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.SystemConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.SystemConfigDict> CreateSystemConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SystemConfigDict.AddValues(builder, valuesOffset);
    return SystemConfigDict.EndSystemConfigDict(builder);
  }

  public static void StartSystemConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.SystemConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.SystemConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.SystemConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.SystemConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.SystemConfigDict> EndSystemConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.SystemConfigDict>(o);
  }
  public static void FinishSystemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SystemConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSystemConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.SystemConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public SystemConfigDictT UnPack() {
    var _o = new SystemConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(SystemConfigDictT _o) {
    _o.Values = new List<FBConfig.SystemConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.SystemConfigDict> Pack(FlatBufferBuilder builder, SystemConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.SystemConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.SystemConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.SystemConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateSystemConfigDict(
      builder,
      _values);
  }
}

public class SystemConfigDictT
{
  public List<FBConfig.SystemConfigT> Values { get; set; }

  public SystemConfigDictT() {
    this.Values = null;
  }
  public static SystemConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return SystemConfigDict.GetRootAsSystemConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    SystemConfigDict.FinishSystemConfigDictBuffer(fbb, SystemConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
