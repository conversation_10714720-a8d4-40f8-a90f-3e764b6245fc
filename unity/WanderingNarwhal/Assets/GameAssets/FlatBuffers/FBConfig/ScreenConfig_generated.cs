// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ScreenConfig : IFlatbufferConfig<ScreenConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ScreenConfig GetRootAsScreenConfig(ByteBuffer _bb) { return GetRootAsScreenConfig(_bb, new ScreenConfig()); }
  public static ScreenConfig GetRootAsScreenConfig(ByteBuffer _bb, ScreenConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ScreenConfig __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Transition { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTransitionBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetTransitionBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetTransitionArray() { return __p.__vector_as_array<byte>(6); }
  public string TransitionPrefab { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTransitionPrefabBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetTransitionPrefabBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetTransitionPrefabArray() { return __p.__vector_as_array<byte>(8); }
  public int SkipGlobeScreenOnStartMinutes { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSkipGlobeScreenOnStartMinutes(int skip_globe_screen_on_start_minutes) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, skip_globe_screen_on_start_minutes); return true; } else { return false; } }

  public static Offset<FBConfig.ScreenConfig> CreateScreenConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset transitionOffset = default(StringOffset),
      StringOffset transition_prefabOffset = default(StringOffset),
      int skip_globe_screen_on_start_minutes = 0) {
    builder.StartTable(4);
    ScreenConfig.AddSkipGlobeScreenOnStartMinutes(builder, skip_globe_screen_on_start_minutes);
    ScreenConfig.AddTransitionPrefab(builder, transition_prefabOffset);
    ScreenConfig.AddTransition(builder, transitionOffset);
    ScreenConfig.AddUid(builder, uidOffset);
    return ScreenConfig.EndScreenConfig(builder);
  }

  public static void StartScreenConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddTransition(FlatBufferBuilder builder, StringOffset transitionOffset) { builder.AddOffset(1, transitionOffset.Value, 0); }
  public static void AddTransitionPrefab(FlatBufferBuilder builder, StringOffset transitionPrefabOffset) { builder.AddOffset(2, transitionPrefabOffset.Value, 0); }
  public static void AddSkipGlobeScreenOnStartMinutes(FlatBufferBuilder builder, int skipGlobeScreenOnStartMinutes) { builder.AddInt(3, skipGlobeScreenOnStartMinutes, 0); }
  public static Offset<FBConfig.ScreenConfig> EndScreenConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ScreenConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfScreenConfig(FlatBufferBuilder builder, Offset<ScreenConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ScreenConfig> o1, Offset<ScreenConfig> o2) =>
        new ScreenConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ScreenConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ScreenConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ScreenConfig obj_ = new ScreenConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ScreenConfigT UnPack() {
    var _o = new ScreenConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ScreenConfigT _o) {
    _o.Uid = this.Uid;
    _o.Transition = this.Transition;
    _o.TransitionPrefab = this.TransitionPrefab;
    _o.SkipGlobeScreenOnStartMinutes = this.SkipGlobeScreenOnStartMinutes;
  }
  public static Offset<FBConfig.ScreenConfig> Pack(FlatBufferBuilder builder, ScreenConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ScreenConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _transition = _o.Transition == null ? default(StringOffset) : builder.CreateString(_o.Transition);
    var _transition_prefab = _o.TransitionPrefab == null ? default(StringOffset) : builder.CreateString(_o.TransitionPrefab);
    return CreateScreenConfig(
      builder,
      _uid,
      _transition,
      _transition_prefab,
      _o.SkipGlobeScreenOnStartMinutes);
  }
}

public class ScreenConfigT
{
  public string Uid { get; set; }
  public string Transition { get; set; }
  public string TransitionPrefab { get; set; }
  public int SkipGlobeScreenOnStartMinutes { get; set; }

  public ScreenConfigT() {
    this.Uid = null;
    this.Transition = null;
    this.TransitionPrefab = null;
    this.SkipGlobeScreenOnStartMinutes = 0;
  }
}

public struct ScreenConfigDict : IFlatbufferConfigDict<ScreenConfig, ScreenConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ScreenConfigDict GetRootAsScreenConfigDict(ByteBuffer _bb) { return GetRootAsScreenConfigDict(_bb, new ScreenConfigDict()); }
  public static ScreenConfigDict GetRootAsScreenConfigDict(ByteBuffer _bb, ScreenConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ScreenConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ScreenConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ScreenConfig?)(new FBConfig.ScreenConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ScreenConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ScreenConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ScreenConfigDict> CreateScreenConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ScreenConfigDict.AddValues(builder, valuesOffset);
    return ScreenConfigDict.EndScreenConfigDict(builder);
  }

  public static void StartScreenConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ScreenConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ScreenConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ScreenConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ScreenConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ScreenConfigDict> EndScreenConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ScreenConfigDict>(o);
  }
  public static void FinishScreenConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ScreenConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedScreenConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ScreenConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ScreenConfigDictT UnPack() {
    var _o = new ScreenConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ScreenConfigDictT _o) {
    _o.Values = new List<FBConfig.ScreenConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ScreenConfigDict> Pack(FlatBufferBuilder builder, ScreenConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ScreenConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ScreenConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ScreenConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateScreenConfigDict(
      builder,
      _values);
  }
}

public class ScreenConfigDictT
{
  public List<FBConfig.ScreenConfigT> Values { get; set; }

  public ScreenConfigDictT() {
    this.Values = null;
  }
  public static ScreenConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ScreenConfigDict.GetRootAsScreenConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ScreenConfigDict.FinishScreenConfigDictBuffer(fbb, ScreenConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
