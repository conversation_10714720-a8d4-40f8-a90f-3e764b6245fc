// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ChallengeLocationConfig : IFlatbufferConfig<ChallengeLocationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeLocationConfig GetRootAsChallengeLocationConfig(ByteBuffer _bb) { return GetRootAsChallengeLocationConfig(_bb, new ChallengeLocationConfig()); }
  public static ChallengeLocationConfig GetRootAsChallengeLocationConfig(ByteBuffer _bb, ChallengeLocationConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeLocationConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string CountryCode { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCountryCodeBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetCountryCodeBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetCountryCodeArray() { return __p.__vector_as_array<byte>(6); }
  public string GroupUid { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGroupUidBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetGroupUidBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetGroupUidArray() { return __p.__vector_as_array<byte>(8); }
  public string CountryText { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCountryTextBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetCountryTextBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetCountryTextArray() { return __p.__vector_as_array<byte>(10); }
  public string Greetings(int j) { int o = __p.__offset(12); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int GreetingsLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string FlagImage { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFlagImageBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetFlagImageBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetFlagImageArray() { return __p.__vector_as_array<byte>(14); }
  public FBConfig.Point3? MarkerPoint { get { int o = __p.__offset(16); return o != 0 ? (FBConfig.Point3?)(new FBConfig.Point3()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public bool DefaultInRegion { get { int o = __p.__offset(18); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateDefaultInRegion(bool default_in_region) { int o = __p.__offset(18); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(default_in_region ? 1 : 0)); return true; } else { return false; } }
  public string KnownFor(int j) { int o = __p.__offset(20); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int KnownForLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.ChallengeLocationConfig> CreateChallengeLocationConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset country_codeOffset = default(StringOffset),
      StringOffset group_uidOffset = default(StringOffset),
      StringOffset country_textOffset = default(StringOffset),
      VectorOffset greetingsOffset = default(VectorOffset),
      StringOffset flag_imageOffset = default(StringOffset),
      Offset<FBConfig.Point3> marker_pointOffset = default(Offset<FBConfig.Point3>),
      bool default_in_region = false,
      VectorOffset known_forOffset = default(VectorOffset)) {
    builder.StartTable(9);
    ChallengeLocationConfig.AddKnownFor(builder, known_forOffset);
    ChallengeLocationConfig.AddMarkerPoint(builder, marker_pointOffset);
    ChallengeLocationConfig.AddFlagImage(builder, flag_imageOffset);
    ChallengeLocationConfig.AddGreetings(builder, greetingsOffset);
    ChallengeLocationConfig.AddCountryText(builder, country_textOffset);
    ChallengeLocationConfig.AddGroupUid(builder, group_uidOffset);
    ChallengeLocationConfig.AddCountryCode(builder, country_codeOffset);
    ChallengeLocationConfig.AddUid(builder, uidOffset);
    ChallengeLocationConfig.AddDefaultInRegion(builder, default_in_region);
    return ChallengeLocationConfig.EndChallengeLocationConfig(builder);
  }

  public static void StartChallengeLocationConfig(FlatBufferBuilder builder) { builder.StartTable(9); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCountryCode(FlatBufferBuilder builder, StringOffset countryCodeOffset) { builder.AddOffset(1, countryCodeOffset.Value, 0); }
  public static void AddGroupUid(FlatBufferBuilder builder, StringOffset groupUidOffset) { builder.AddOffset(2, groupUidOffset.Value, 0); }
  public static void AddCountryText(FlatBufferBuilder builder, StringOffset countryTextOffset) { builder.AddOffset(3, countryTextOffset.Value, 0); }
  public static void AddGreetings(FlatBufferBuilder builder, VectorOffset greetingsOffset) { builder.AddOffset(4, greetingsOffset.Value, 0); }
  public static VectorOffset CreateGreetingsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGreetingsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGreetingsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGreetingsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGreetingsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddFlagImage(FlatBufferBuilder builder, StringOffset flagImageOffset) { builder.AddOffset(5, flagImageOffset.Value, 0); }
  public static void AddMarkerPoint(FlatBufferBuilder builder, Offset<FBConfig.Point3> markerPointOffset) { builder.AddOffset(6, markerPointOffset.Value, 0); }
  public static void AddDefaultInRegion(FlatBufferBuilder builder, bool defaultInRegion) { builder.AddBool(7, defaultInRegion, false); }
  public static void AddKnownFor(FlatBufferBuilder builder, VectorOffset knownForOffset) { builder.AddOffset(8, knownForOffset.Value, 0); }
  public static VectorOffset CreateKnownForVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateKnownForVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateKnownForVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateKnownForVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartKnownForVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ChallengeLocationConfig> EndChallengeLocationConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ChallengeLocationConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfChallengeLocationConfig(FlatBufferBuilder builder, Offset<ChallengeLocationConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ChallengeLocationConfig> o1, Offset<ChallengeLocationConfig> o2) =>
        new ChallengeLocationConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ChallengeLocationConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ChallengeLocationConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ChallengeLocationConfig obj_ = new ChallengeLocationConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ChallengeLocationConfigT UnPack() {
    var _o = new ChallengeLocationConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeLocationConfigT _o) {
    _o.Uid = this.Uid;
    _o.CountryCode = this.CountryCode;
    _o.GroupUid = this.GroupUid;
    _o.CountryText = this.CountryText;
    _o.Greetings = new List<string>();
    for (var _j = 0; _j < this.GreetingsLength; ++_j) {_o.Greetings.Add(this.Greetings(_j));}
    _o.FlagImage = this.FlagImage;
    _o.MarkerPoint = this.MarkerPoint.HasValue ? this.MarkerPoint.Value.UnPack() : null;
    _o.DefaultInRegion = this.DefaultInRegion;
    _o.KnownFor = new List<string>();
    for (var _j = 0; _j < this.KnownForLength; ++_j) {_o.KnownFor.Add(this.KnownFor(_j));}
  }
  public static Offset<FBConfig.ChallengeLocationConfig> Pack(FlatBufferBuilder builder, ChallengeLocationConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeLocationConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _country_code = _o.CountryCode == null ? default(StringOffset) : builder.CreateString(_o.CountryCode);
    var _group_uid = _o.GroupUid == null ? default(StringOffset) : builder.CreateString(_o.GroupUid);
    var _country_text = _o.CountryText == null ? default(StringOffset) : builder.CreateString(_o.CountryText);
    var _greetings = default(VectorOffset);
    if (_o.Greetings != null) {
      var __greetings = new StringOffset[_o.Greetings.Count];
      for (var _j = 0; _j < __greetings.Length; ++_j) { __greetings[_j] = builder.CreateString(_o.Greetings[_j]); }
      _greetings = CreateGreetingsVector(builder, __greetings);
    }
    var _flag_image = _o.FlagImage == null ? default(StringOffset) : builder.CreateString(_o.FlagImage);
    var _marker_point = _o.MarkerPoint == null ? default(Offset<FBConfig.Point3>) : FBConfig.Point3.Pack(builder, _o.MarkerPoint);
    var _known_for = default(VectorOffset);
    if (_o.KnownFor != null) {
      var __known_for = new StringOffset[_o.KnownFor.Count];
      for (var _j = 0; _j < __known_for.Length; ++_j) { __known_for[_j] = builder.CreateString(_o.KnownFor[_j]); }
      _known_for = CreateKnownForVector(builder, __known_for);
    }
    return CreateChallengeLocationConfig(
      builder,
      _uid,
      _country_code,
      _group_uid,
      _country_text,
      _greetings,
      _flag_image,
      _marker_point,
      _o.DefaultInRegion,
      _known_for);
  }
}

public class ChallengeLocationConfigT
{
  public string Uid { get; set; }
  public string CountryCode { get; set; }
  public string GroupUid { get; set; }
  public string CountryText { get; set; }
  public List<string> Greetings { get; set; }
  public string FlagImage { get; set; }
  public FBConfig.Point3T MarkerPoint { get; set; }
  public bool DefaultInRegion { get; set; }
  public List<string> KnownFor { get; set; }

  public ChallengeLocationConfigT() {
    this.Uid = null;
    this.CountryCode = null;
    this.GroupUid = null;
    this.CountryText = null;
    this.Greetings = null;
    this.FlagImage = null;
    this.MarkerPoint = null;
    this.DefaultInRegion = false;
    this.KnownFor = null;
  }
}

public struct ChallengeLocationConfigDict : IFlatbufferConfigDict<ChallengeLocationConfig, ChallengeLocationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeLocationConfigDict GetRootAsChallengeLocationConfigDict(ByteBuffer _bb) { return GetRootAsChallengeLocationConfigDict(_bb, new ChallengeLocationConfigDict()); }
  public static ChallengeLocationConfigDict GetRootAsChallengeLocationConfigDict(ByteBuffer _bb, ChallengeLocationConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeLocationConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ChallengeLocationConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ChallengeLocationConfig?)(new FBConfig.ChallengeLocationConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ChallengeLocationConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ChallengeLocationConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ChallengeLocationConfigDict> CreateChallengeLocationConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ChallengeLocationConfigDict.AddValues(builder, valuesOffset);
    return ChallengeLocationConfigDict.EndChallengeLocationConfigDict(builder);
  }

  public static void StartChallengeLocationConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ChallengeLocationConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ChallengeLocationConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ChallengeLocationConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ChallengeLocationConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ChallengeLocationConfigDict> EndChallengeLocationConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ChallengeLocationConfigDict>(o);
  }
  public static void FinishChallengeLocationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeLocationConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedChallengeLocationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeLocationConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ChallengeLocationConfigDictT UnPack() {
    var _o = new ChallengeLocationConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeLocationConfigDictT _o) {
    _o.Values = new List<FBConfig.ChallengeLocationConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ChallengeLocationConfigDict> Pack(FlatBufferBuilder builder, ChallengeLocationConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeLocationConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ChallengeLocationConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ChallengeLocationConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateChallengeLocationConfigDict(
      builder,
      _values);
  }
}

public class ChallengeLocationConfigDictT
{
  public List<FBConfig.ChallengeLocationConfigT> Values { get; set; }

  public ChallengeLocationConfigDictT() {
    this.Values = null;
  }
  public static ChallengeLocationConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ChallengeLocationConfigDict.GetRootAsChallengeLocationConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ChallengeLocationConfigDict.FinishChallengeLocationConfigDictBuffer(fbb, ChallengeLocationConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
