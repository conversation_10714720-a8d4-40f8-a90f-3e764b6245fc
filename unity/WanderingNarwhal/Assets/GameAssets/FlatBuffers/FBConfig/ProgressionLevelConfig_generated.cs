// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ProgressionLevelConfig : IFlatbufferConfig<ProgressionLevelConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ProgressionLevelConfig GetRootAsProgressionLevelConfig(ByteBuffer _bb) { return GetRootAsProgressionLevelConfig(_bb, new ProgressionLevelConfig()); }
  public static ProgressionLevelConfig GetRootAsProgressionLevelConfig(ByteBuffer _bb, ProgressionLevelConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ProgressionLevelConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string LocationUid { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLocationUidBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetLocationUidBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetLocationUidArray() { return __p.__vector_as_array<byte>(8); }
  public string StartupBoosts(int j) { int o = __p.__offset(10); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int StartupBoostsLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string EligibleBoosts(int j) { int o = __p.__offset(12); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int EligibleBoostsLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int GoodTurns { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateGoodTurns(int good_turns) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, good_turns); return true; } else { return false; } }
  public int BetterTurns { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBetterTurns(int better_turns) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, better_turns); return true; } else { return false; } }
  public int BestTurns { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBestTurns(int best_turns) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, best_turns); return true; } else { return false; } }
  public int GoodScore { get { int o = __p.__offset(20); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateGoodScore(int good_score) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, good_score); return true; } else { return false; } }
  public int BetterScore { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBetterScore(int better_score) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, better_score); return true; } else { return false; } }
  public int BestScore { get { int o = __p.__offset(24); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBestScore(int best_score) { int o = __p.__offset(24); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, best_score); return true; } else { return false; } }
  public FBConfig.DictStringInt? GoodTileKindGoalsFb(int j) { int o = __p.__offset(26); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int GoodTileKindGoalsFbLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? BetterTileKindGoalsFb(int j) { int o = __p.__offset(28); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int BetterTileKindGoalsFbLength { get { int o = __p.__offset(28); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringInt? BestTileKindGoalsFb(int j) { int o = __p.__offset(30); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int BestTileKindGoalsFbLength { get { int o = __p.__offset(30); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string GoodReward { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGoodRewardBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetGoodRewardBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetGoodRewardArray() { return __p.__vector_as_array<byte>(32); }
  public string BetterReward { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBetterRewardBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetBetterRewardBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetBetterRewardArray() { return __p.__vector_as_array<byte>(34); }
  public string BestReward { get { int o = __p.__offset(36); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBestRewardBytes() { return __p.__vector_as_span<byte>(36, 1); }
#else
  public ArraySegment<byte>? GetBestRewardBytes() { return __p.__vector_as_arraysegment(36); }
#endif
  public byte[] GetBestRewardArray() { return __p.__vector_as_array<byte>(36); }
  public float ScoreCoefs(int j) { int o = __p.__offset(38); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int ScoreCoefsLength { get { int o = __p.__offset(38); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetScoreCoefsBytes() { return __p.__vector_as_span<float>(38, 4); }
#else
  public ArraySegment<byte>? GetScoreCoefsBytes() { return __p.__vector_as_arraysegment(38); }
#endif
  public float[] GetScoreCoefsArray() { return __p.__vector_as_array<float>(38); }
  public bool MutateScoreCoefs(int j, float score_coefs) { int o = __p.__offset(38); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, score_coefs); return true; } else { return false; } }
  public string CompleteReward { get { int o = __p.__offset(40); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCompleteRewardBytes() { return __p.__vector_as_span<byte>(40, 1); }
#else
  public ArraySegment<byte>? GetCompleteRewardBytes() { return __p.__vector_as_arraysegment(40); }
#endif
  public byte[] GetCompleteRewardArray() { return __p.__vector_as_array<byte>(40); }
  public string GoodExtraGoals(int j) { int o = __p.__offset(42); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int GoodExtraGoalsLength { get { int o = __p.__offset(42); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string BetterExtraGoals(int j) { int o = __p.__offset(44); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int BetterExtraGoalsLength { get { int o = __p.__offset(44); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string BestExtraGoals(int j) { int o = __p.__offset(46); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int BestExtraGoalsLength { get { int o = __p.__offset(46); return o != 0 ? __p.__vector_len(o) : 0; } }
  public float SortOrder { get { int o = __p.__offset(48); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateSortOrder(float sort_order) { int o = __p.__offset(48); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, sort_order); return true; } else { return false; } }
  public float ReportingLevelNum { get { int o = __p.__offset(50); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateReportingLevelNum(float reporting_level_num) { int o = __p.__offset(50); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, reporting_level_num); return true; } else { return false; } }
  public int TargetWinRate { get { int o = __p.__offset(52); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTargetWinRate(int target_win_rate) { int o = __p.__offset(52); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, target_win_rate); return true; } else { return false; } }
  public int TargetWinRateT2 { get { int o = __p.__offset(54); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTargetWinRateT2(int target_win_rate_t2) { int o = __p.__offset(54); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, target_win_rate_t2); return true; } else { return false; } }
  public int TargetWinRateT3 { get { int o = __p.__offset(56); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTargetWinRateT3(int target_win_rate_t3) { int o = __p.__offset(56); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, target_win_rate_t3); return true; } else { return false; } }
  public int Difficulty { get { int o = __p.__offset(58); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDifficulty(int difficulty) { int o = __p.__offset(58); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, difficulty); return true; } else { return false; } }
  public string AssistSystemUid { get { int o = __p.__offset(60); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAssistSystemUidBytes() { return __p.__vector_as_span<byte>(60, 1); }
#else
  public ArraySegment<byte>? GetAssistSystemUidBytes() { return __p.__vector_as_arraysegment(60); }
#endif
  public byte[] GetAssistSystemUidArray() { return __p.__vector_as_array<byte>(60); }
  public string Hash { get { int o = __p.__offset(62); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHashBytes() { return __p.__vector_as_span<byte>(62, 1); }
#else
  public ArraySegment<byte>? GetHashBytes() { return __p.__vector_as_arraysegment(62); }
#endif
  public byte[] GetHashArray() { return __p.__vector_as_array<byte>(62); }
  public string FileNames(int j) { int o = __p.__offset(64); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int FileNamesLength { get { int o = __p.__offset(64); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string AssistUidByLossLevel(int j) { int o = __p.__offset(66); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int AssistUidByLossLevelLength { get { int o = __p.__offset(66); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int AssistLossLevel(int j) { int o = __p.__offset(68); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int AssistLossLevelLength { get { int o = __p.__offset(68); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetAssistLossLevelBytes() { return __p.__vector_as_span<int>(68, 4); }
#else
  public ArraySegment<byte>? GetAssistLossLevelBytes() { return __p.__vector_as_arraysegment(68); }
#endif
  public int[] GetAssistLossLevelArray() { return __p.__vector_as_array<int>(68); }
  public bool MutateAssistLossLevel(int j, int assist_loss_level) { int o = __p.__offset(68); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, assist_loss_level); return true; } else { return false; } }

  public static Offset<FBConfig.ProgressionLevelConfig> CreateProgressionLevelConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset location_uidOffset = default(StringOffset),
      VectorOffset startup_boostsOffset = default(VectorOffset),
      VectorOffset eligible_boostsOffset = default(VectorOffset),
      int good_turns = 0,
      int better_turns = 0,
      int best_turns = 0,
      int good_score = 0,
      int better_score = 0,
      int best_score = 0,
      VectorOffset good_tile_kind_goals_fbOffset = default(VectorOffset),
      VectorOffset better_tile_kind_goals_fbOffset = default(VectorOffset),
      VectorOffset best_tile_kind_goals_fbOffset = default(VectorOffset),
      StringOffset good_rewardOffset = default(StringOffset),
      StringOffset better_rewardOffset = default(StringOffset),
      StringOffset best_rewardOffset = default(StringOffset),
      VectorOffset score_coefsOffset = default(VectorOffset),
      StringOffset complete_rewardOffset = default(StringOffset),
      VectorOffset good_extra_goalsOffset = default(VectorOffset),
      VectorOffset better_extra_goalsOffset = default(VectorOffset),
      VectorOffset best_extra_goalsOffset = default(VectorOffset),
      float sort_order = 0.0f,
      float reporting_level_num = 0.0f,
      int target_win_rate = 0,
      int target_win_rate_t2 = 0,
      int target_win_rate_t3 = 0,
      int difficulty = 0,
      StringOffset assist_system_uidOffset = default(StringOffset),
      StringOffset hashOffset = default(StringOffset),
      VectorOffset file_namesOffset = default(VectorOffset),
      VectorOffset assist_uid_by_loss_levelOffset = default(VectorOffset),
      VectorOffset assist_loss_levelOffset = default(VectorOffset)) {
    builder.StartTable(33);
    ProgressionLevelConfig.AddAssistLossLevel(builder, assist_loss_levelOffset);
    ProgressionLevelConfig.AddAssistUidByLossLevel(builder, assist_uid_by_loss_levelOffset);
    ProgressionLevelConfig.AddFileNames(builder, file_namesOffset);
    ProgressionLevelConfig.AddHash(builder, hashOffset);
    ProgressionLevelConfig.AddAssistSystemUid(builder, assist_system_uidOffset);
    ProgressionLevelConfig.AddDifficulty(builder, difficulty);
    ProgressionLevelConfig.AddTargetWinRateT3(builder, target_win_rate_t3);
    ProgressionLevelConfig.AddTargetWinRateT2(builder, target_win_rate_t2);
    ProgressionLevelConfig.AddTargetWinRate(builder, target_win_rate);
    ProgressionLevelConfig.AddReportingLevelNum(builder, reporting_level_num);
    ProgressionLevelConfig.AddSortOrder(builder, sort_order);
    ProgressionLevelConfig.AddBestExtraGoals(builder, best_extra_goalsOffset);
    ProgressionLevelConfig.AddBetterExtraGoals(builder, better_extra_goalsOffset);
    ProgressionLevelConfig.AddGoodExtraGoals(builder, good_extra_goalsOffset);
    ProgressionLevelConfig.AddCompleteReward(builder, complete_rewardOffset);
    ProgressionLevelConfig.AddScoreCoefs(builder, score_coefsOffset);
    ProgressionLevelConfig.AddBestReward(builder, best_rewardOffset);
    ProgressionLevelConfig.AddBetterReward(builder, better_rewardOffset);
    ProgressionLevelConfig.AddGoodReward(builder, good_rewardOffset);
    ProgressionLevelConfig.AddBestTileKindGoalsFb(builder, best_tile_kind_goals_fbOffset);
    ProgressionLevelConfig.AddBetterTileKindGoalsFb(builder, better_tile_kind_goals_fbOffset);
    ProgressionLevelConfig.AddGoodTileKindGoalsFb(builder, good_tile_kind_goals_fbOffset);
    ProgressionLevelConfig.AddBestScore(builder, best_score);
    ProgressionLevelConfig.AddBetterScore(builder, better_score);
    ProgressionLevelConfig.AddGoodScore(builder, good_score);
    ProgressionLevelConfig.AddBestTurns(builder, best_turns);
    ProgressionLevelConfig.AddBetterTurns(builder, better_turns);
    ProgressionLevelConfig.AddGoodTurns(builder, good_turns);
    ProgressionLevelConfig.AddEligibleBoosts(builder, eligible_boostsOffset);
    ProgressionLevelConfig.AddStartupBoosts(builder, startup_boostsOffset);
    ProgressionLevelConfig.AddLocationUid(builder, location_uidOffset);
    ProgressionLevelConfig.AddName(builder, nameOffset);
    ProgressionLevelConfig.AddUid(builder, uidOffset);
    return ProgressionLevelConfig.EndProgressionLevelConfig(builder);
  }

  public static void StartProgressionLevelConfig(FlatBufferBuilder builder) { builder.StartTable(33); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddLocationUid(FlatBufferBuilder builder, StringOffset locationUidOffset) { builder.AddOffset(2, locationUidOffset.Value, 0); }
  public static void AddStartupBoosts(FlatBufferBuilder builder, VectorOffset startupBoostsOffset) { builder.AddOffset(3, startupBoostsOffset.Value, 0); }
  public static VectorOffset CreateStartupBoostsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateStartupBoostsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStartupBoostsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStartupBoostsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartStartupBoostsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddEligibleBoosts(FlatBufferBuilder builder, VectorOffset eligibleBoostsOffset) { builder.AddOffset(4, eligibleBoostsOffset.Value, 0); }
  public static VectorOffset CreateEligibleBoostsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateEligibleBoostsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEligibleBoostsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEligibleBoostsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartEligibleBoostsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddGoodTurns(FlatBufferBuilder builder, int goodTurns) { builder.AddInt(5, goodTurns, 0); }
  public static void AddBetterTurns(FlatBufferBuilder builder, int betterTurns) { builder.AddInt(6, betterTurns, 0); }
  public static void AddBestTurns(FlatBufferBuilder builder, int bestTurns) { builder.AddInt(7, bestTurns, 0); }
  public static void AddGoodScore(FlatBufferBuilder builder, int goodScore) { builder.AddInt(8, goodScore, 0); }
  public static void AddBetterScore(FlatBufferBuilder builder, int betterScore) { builder.AddInt(9, betterScore, 0); }
  public static void AddBestScore(FlatBufferBuilder builder, int bestScore) { builder.AddInt(10, bestScore, 0); }
  public static void AddGoodTileKindGoalsFb(FlatBufferBuilder builder, VectorOffset goodTileKindGoalsFbOffset) { builder.AddOffset(11, goodTileKindGoalsFbOffset.Value, 0); }
  public static VectorOffset CreateGoodTileKindGoalsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGoodTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGoodTileKindGoalsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBetterTileKindGoalsFb(FlatBufferBuilder builder, VectorOffset betterTileKindGoalsFbOffset) { builder.AddOffset(12, betterTileKindGoalsFbOffset.Value, 0); }
  public static VectorOffset CreateBetterTileKindGoalsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBetterTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBetterTileKindGoalsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBestTileKindGoalsFb(FlatBufferBuilder builder, VectorOffset bestTileKindGoalsFbOffset) { builder.AddOffset(13, bestTileKindGoalsFbOffset.Value, 0); }
  public static VectorOffset CreateBestTileKindGoalsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBestTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestTileKindGoalsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBestTileKindGoalsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddGoodReward(FlatBufferBuilder builder, StringOffset goodRewardOffset) { builder.AddOffset(14, goodRewardOffset.Value, 0); }
  public static void AddBetterReward(FlatBufferBuilder builder, StringOffset betterRewardOffset) { builder.AddOffset(15, betterRewardOffset.Value, 0); }
  public static void AddBestReward(FlatBufferBuilder builder, StringOffset bestRewardOffset) { builder.AddOffset(16, bestRewardOffset.Value, 0); }
  public static void AddScoreCoefs(FlatBufferBuilder builder, VectorOffset scoreCoefsOffset) { builder.AddOffset(17, scoreCoefsOffset.Value, 0); }
  public static VectorOffset CreateScoreCoefsVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateScoreCoefsVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreCoefsVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScoreCoefsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartScoreCoefsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddCompleteReward(FlatBufferBuilder builder, StringOffset completeRewardOffset) { builder.AddOffset(18, completeRewardOffset.Value, 0); }
  public static void AddGoodExtraGoals(FlatBufferBuilder builder, VectorOffset goodExtraGoalsOffset) { builder.AddOffset(19, goodExtraGoalsOffset.Value, 0); }
  public static VectorOffset CreateGoodExtraGoalsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGoodExtraGoalsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodExtraGoalsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoodExtraGoalsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGoodExtraGoalsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBetterExtraGoals(FlatBufferBuilder builder, VectorOffset betterExtraGoalsOffset) { builder.AddOffset(20, betterExtraGoalsOffset.Value, 0); }
  public static VectorOffset CreateBetterExtraGoalsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBetterExtraGoalsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterExtraGoalsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBetterExtraGoalsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBetterExtraGoalsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBestExtraGoals(FlatBufferBuilder builder, VectorOffset bestExtraGoalsOffset) { builder.AddOffset(21, bestExtraGoalsOffset.Value, 0); }
  public static VectorOffset CreateBestExtraGoalsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBestExtraGoalsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestExtraGoalsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBestExtraGoalsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBestExtraGoalsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSortOrder(FlatBufferBuilder builder, float sortOrder) { builder.AddFloat(22, sortOrder, 0.0f); }
  public static void AddReportingLevelNum(FlatBufferBuilder builder, float reportingLevelNum) { builder.AddFloat(23, reportingLevelNum, 0.0f); }
  public static void AddTargetWinRate(FlatBufferBuilder builder, int targetWinRate) { builder.AddInt(24, targetWinRate, 0); }
  public static void AddTargetWinRateT2(FlatBufferBuilder builder, int targetWinRateT2) { builder.AddInt(25, targetWinRateT2, 0); }
  public static void AddTargetWinRateT3(FlatBufferBuilder builder, int targetWinRateT3) { builder.AddInt(26, targetWinRateT3, 0); }
  public static void AddDifficulty(FlatBufferBuilder builder, int difficulty) { builder.AddInt(27, difficulty, 0); }
  public static void AddAssistSystemUid(FlatBufferBuilder builder, StringOffset assistSystemUidOffset) { builder.AddOffset(28, assistSystemUidOffset.Value, 0); }
  public static void AddHash(FlatBufferBuilder builder, StringOffset hashOffset) { builder.AddOffset(29, hashOffset.Value, 0); }
  public static void AddFileNames(FlatBufferBuilder builder, VectorOffset fileNamesOffset) { builder.AddOffset(30, fileNamesOffset.Value, 0); }
  public static VectorOffset CreateFileNamesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateFileNamesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFileNamesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFileNamesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartFileNamesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAssistUidByLossLevel(FlatBufferBuilder builder, VectorOffset assistUidByLossLevelOffset) { builder.AddOffset(31, assistUidByLossLevelOffset.Value, 0); }
  public static VectorOffset CreateAssistUidByLossLevelVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAssistUidByLossLevelVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssistUidByLossLevelVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssistUidByLossLevelVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAssistUidByLossLevelVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAssistLossLevel(FlatBufferBuilder builder, VectorOffset assistLossLevelOffset) { builder.AddOffset(32, assistLossLevelOffset.Value, 0); }
  public static VectorOffset CreateAssistLossLevelVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateAssistLossLevelVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssistLossLevelVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAssistLossLevelVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAssistLossLevelVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ProgressionLevelConfig> EndProgressionLevelConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ProgressionLevelConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfProgressionLevelConfig(FlatBufferBuilder builder, Offset<ProgressionLevelConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ProgressionLevelConfig> o1, Offset<ProgressionLevelConfig> o2) =>
        new ProgressionLevelConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ProgressionLevelConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ProgressionLevelConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ProgressionLevelConfig obj_ = new ProgressionLevelConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ProgressionLevelConfigT UnPack() {
    var _o = new ProgressionLevelConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ProgressionLevelConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.LocationUid = this.LocationUid;
    _o.StartupBoosts = new List<string>();
    for (var _j = 0; _j < this.StartupBoostsLength; ++_j) {_o.StartupBoosts.Add(this.StartupBoosts(_j));}
    _o.EligibleBoosts = new List<string>();
    for (var _j = 0; _j < this.EligibleBoostsLength; ++_j) {_o.EligibleBoosts.Add(this.EligibleBoosts(_j));}
    _o.GoodTurns = this.GoodTurns;
    _o.BetterTurns = this.BetterTurns;
    _o.BestTurns = this.BestTurns;
    _o.GoodScore = this.GoodScore;
    _o.BetterScore = this.BetterScore;
    _o.BestScore = this.BestScore;
    _o.GoodTileKindGoalsFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.GoodTileKindGoalsFbLength; ++_j) {_o.GoodTileKindGoalsFb.Add(this.GoodTileKindGoalsFb(_j).HasValue ? this.GoodTileKindGoalsFb(_j).Value.UnPack() : null);}
    _o.BetterTileKindGoalsFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.BetterTileKindGoalsFbLength; ++_j) {_o.BetterTileKindGoalsFb.Add(this.BetterTileKindGoalsFb(_j).HasValue ? this.BetterTileKindGoalsFb(_j).Value.UnPack() : null);}
    _o.BestTileKindGoalsFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.BestTileKindGoalsFbLength; ++_j) {_o.BestTileKindGoalsFb.Add(this.BestTileKindGoalsFb(_j).HasValue ? this.BestTileKindGoalsFb(_j).Value.UnPack() : null);}
    _o.GoodReward = this.GoodReward;
    _o.BetterReward = this.BetterReward;
    _o.BestReward = this.BestReward;
    _o.ScoreCoefs = new List<float>();
    for (var _j = 0; _j < this.ScoreCoefsLength; ++_j) {_o.ScoreCoefs.Add(this.ScoreCoefs(_j));}
    _o.CompleteReward = this.CompleteReward;
    _o.GoodExtraGoals = new List<string>();
    for (var _j = 0; _j < this.GoodExtraGoalsLength; ++_j) {_o.GoodExtraGoals.Add(this.GoodExtraGoals(_j));}
    _o.BetterExtraGoals = new List<string>();
    for (var _j = 0; _j < this.BetterExtraGoalsLength; ++_j) {_o.BetterExtraGoals.Add(this.BetterExtraGoals(_j));}
    _o.BestExtraGoals = new List<string>();
    for (var _j = 0; _j < this.BestExtraGoalsLength; ++_j) {_o.BestExtraGoals.Add(this.BestExtraGoals(_j));}
    _o.SortOrder = this.SortOrder;
    _o.ReportingLevelNum = this.ReportingLevelNum;
    _o.TargetWinRate = this.TargetWinRate;
    _o.TargetWinRateT2 = this.TargetWinRateT2;
    _o.TargetWinRateT3 = this.TargetWinRateT3;
    _o.Difficulty = this.Difficulty;
    _o.AssistSystemUid = this.AssistSystemUid;
    _o.Hash = this.Hash;
    _o.FileNames = new List<string>();
    for (var _j = 0; _j < this.FileNamesLength; ++_j) {_o.FileNames.Add(this.FileNames(_j));}
    _o.AssistUidByLossLevel = new List<string>();
    for (var _j = 0; _j < this.AssistUidByLossLevelLength; ++_j) {_o.AssistUidByLossLevel.Add(this.AssistUidByLossLevel(_j));}
    _o.AssistLossLevel = new List<int>();
    for (var _j = 0; _j < this.AssistLossLevelLength; ++_j) {_o.AssistLossLevel.Add(this.AssistLossLevel(_j));}
  }
  public static Offset<FBConfig.ProgressionLevelConfig> Pack(FlatBufferBuilder builder, ProgressionLevelConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ProgressionLevelConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _location_uid = _o.LocationUid == null ? default(StringOffset) : builder.CreateString(_o.LocationUid);
    var _startup_boosts = default(VectorOffset);
    if (_o.StartupBoosts != null) {
      var __startup_boosts = new StringOffset[_o.StartupBoosts.Count];
      for (var _j = 0; _j < __startup_boosts.Length; ++_j) { __startup_boosts[_j] = builder.CreateString(_o.StartupBoosts[_j]); }
      _startup_boosts = CreateStartupBoostsVector(builder, __startup_boosts);
    }
    var _eligible_boosts = default(VectorOffset);
    if (_o.EligibleBoosts != null) {
      var __eligible_boosts = new StringOffset[_o.EligibleBoosts.Count];
      for (var _j = 0; _j < __eligible_boosts.Length; ++_j) { __eligible_boosts[_j] = builder.CreateString(_o.EligibleBoosts[_j]); }
      _eligible_boosts = CreateEligibleBoostsVector(builder, __eligible_boosts);
    }
    var _good_tile_kind_goals_fb = default(VectorOffset);
    if (_o.GoodTileKindGoalsFb != null) {
      var __good_tile_kind_goals_fb = new Offset<FBConfig.DictStringInt>[_o.GoodTileKindGoalsFb.Count];
      for (var _j = 0; _j < __good_tile_kind_goals_fb.Length; ++_j) { __good_tile_kind_goals_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.GoodTileKindGoalsFb[_j]); }
      _good_tile_kind_goals_fb = CreateGoodTileKindGoalsFbVector(builder, __good_tile_kind_goals_fb);
    }
    var _better_tile_kind_goals_fb = default(VectorOffset);
    if (_o.BetterTileKindGoalsFb != null) {
      var __better_tile_kind_goals_fb = new Offset<FBConfig.DictStringInt>[_o.BetterTileKindGoalsFb.Count];
      for (var _j = 0; _j < __better_tile_kind_goals_fb.Length; ++_j) { __better_tile_kind_goals_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.BetterTileKindGoalsFb[_j]); }
      _better_tile_kind_goals_fb = CreateBetterTileKindGoalsFbVector(builder, __better_tile_kind_goals_fb);
    }
    var _best_tile_kind_goals_fb = default(VectorOffset);
    if (_o.BestTileKindGoalsFb != null) {
      var __best_tile_kind_goals_fb = new Offset<FBConfig.DictStringInt>[_o.BestTileKindGoalsFb.Count];
      for (var _j = 0; _j < __best_tile_kind_goals_fb.Length; ++_j) { __best_tile_kind_goals_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.BestTileKindGoalsFb[_j]); }
      _best_tile_kind_goals_fb = CreateBestTileKindGoalsFbVector(builder, __best_tile_kind_goals_fb);
    }
    var _good_reward = _o.GoodReward == null ? default(StringOffset) : builder.CreateString(_o.GoodReward);
    var _better_reward = _o.BetterReward == null ? default(StringOffset) : builder.CreateString(_o.BetterReward);
    var _best_reward = _o.BestReward == null ? default(StringOffset) : builder.CreateString(_o.BestReward);
    var _score_coefs = default(VectorOffset);
    if (_o.ScoreCoefs != null) {
      var __score_coefs = _o.ScoreCoefs.ToArray();
      _score_coefs = CreateScoreCoefsVector(builder, __score_coefs);
    }
    var _complete_reward = _o.CompleteReward == null ? default(StringOffset) : builder.CreateString(_o.CompleteReward);
    var _good_extra_goals = default(VectorOffset);
    if (_o.GoodExtraGoals != null) {
      var __good_extra_goals = new StringOffset[_o.GoodExtraGoals.Count];
      for (var _j = 0; _j < __good_extra_goals.Length; ++_j) { __good_extra_goals[_j] = builder.CreateString(_o.GoodExtraGoals[_j]); }
      _good_extra_goals = CreateGoodExtraGoalsVector(builder, __good_extra_goals);
    }
    var _better_extra_goals = default(VectorOffset);
    if (_o.BetterExtraGoals != null) {
      var __better_extra_goals = new StringOffset[_o.BetterExtraGoals.Count];
      for (var _j = 0; _j < __better_extra_goals.Length; ++_j) { __better_extra_goals[_j] = builder.CreateString(_o.BetterExtraGoals[_j]); }
      _better_extra_goals = CreateBetterExtraGoalsVector(builder, __better_extra_goals);
    }
    var _best_extra_goals = default(VectorOffset);
    if (_o.BestExtraGoals != null) {
      var __best_extra_goals = new StringOffset[_o.BestExtraGoals.Count];
      for (var _j = 0; _j < __best_extra_goals.Length; ++_j) { __best_extra_goals[_j] = builder.CreateString(_o.BestExtraGoals[_j]); }
      _best_extra_goals = CreateBestExtraGoalsVector(builder, __best_extra_goals);
    }
    var _assist_system_uid = _o.AssistSystemUid == null ? default(StringOffset) : builder.CreateString(_o.AssistSystemUid);
    var _hash = _o.Hash == null ? default(StringOffset) : builder.CreateString(_o.Hash);
    var _file_names = default(VectorOffset);
    if (_o.FileNames != null) {
      var __file_names = new StringOffset[_o.FileNames.Count];
      for (var _j = 0; _j < __file_names.Length; ++_j) { __file_names[_j] = builder.CreateString(_o.FileNames[_j]); }
      _file_names = CreateFileNamesVector(builder, __file_names);
    }
    var _assist_uid_by_loss_level = default(VectorOffset);
    if (_o.AssistUidByLossLevel != null) {
      var __assist_uid_by_loss_level = new StringOffset[_o.AssistUidByLossLevel.Count];
      for (var _j = 0; _j < __assist_uid_by_loss_level.Length; ++_j) { __assist_uid_by_loss_level[_j] = builder.CreateString(_o.AssistUidByLossLevel[_j]); }
      _assist_uid_by_loss_level = CreateAssistUidByLossLevelVector(builder, __assist_uid_by_loss_level);
    }
    var _assist_loss_level = default(VectorOffset);
    if (_o.AssistLossLevel != null) {
      var __assist_loss_level = _o.AssistLossLevel.ToArray();
      _assist_loss_level = CreateAssistLossLevelVector(builder, __assist_loss_level);
    }
    return CreateProgressionLevelConfig(
      builder,
      _uid,
      _name,
      _location_uid,
      _startup_boosts,
      _eligible_boosts,
      _o.GoodTurns,
      _o.BetterTurns,
      _o.BestTurns,
      _o.GoodScore,
      _o.BetterScore,
      _o.BestScore,
      _good_tile_kind_goals_fb,
      _better_tile_kind_goals_fb,
      _best_tile_kind_goals_fb,
      _good_reward,
      _better_reward,
      _best_reward,
      _score_coefs,
      _complete_reward,
      _good_extra_goals,
      _better_extra_goals,
      _best_extra_goals,
      _o.SortOrder,
      _o.ReportingLevelNum,
      _o.TargetWinRate,
      _o.TargetWinRateT2,
      _o.TargetWinRateT3,
      _o.Difficulty,
      _assist_system_uid,
      _hash,
      _file_names,
      _assist_uid_by_loss_level,
      _assist_loss_level);
  }
}

public class ProgressionLevelConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string LocationUid { get; set; }
  public List<string> StartupBoosts { get; set; }
  public List<string> EligibleBoosts { get; set; }
  public int GoodTurns { get; set; }
  public int BetterTurns { get; set; }
  public int BestTurns { get; set; }
  public int GoodScore { get; set; }
  public int BetterScore { get; set; }
  public int BestScore { get; set; }
  public List<FBConfig.DictStringIntT> GoodTileKindGoalsFb { get; set; }
  public List<FBConfig.DictStringIntT> BetterTileKindGoalsFb { get; set; }
  public List<FBConfig.DictStringIntT> BestTileKindGoalsFb { get; set; }
  public string GoodReward { get; set; }
  public string BetterReward { get; set; }
  public string BestReward { get; set; }
  public List<float> ScoreCoefs { get; set; }
  public string CompleteReward { get; set; }
  public List<string> GoodExtraGoals { get; set; }
  public List<string> BetterExtraGoals { get; set; }
  public List<string> BestExtraGoals { get; set; }
  public float SortOrder { get; set; }
  public float ReportingLevelNum { get; set; }
  public int TargetWinRate { get; set; }
  public int TargetWinRateT2 { get; set; }
  public int TargetWinRateT3 { get; set; }
  public int Difficulty { get; set; }
  public string AssistSystemUid { get; set; }
  public string Hash { get; set; }
  public List<string> FileNames { get; set; }
  public List<string> AssistUidByLossLevel { get; set; }
  public List<int> AssistLossLevel { get; set; }

  public ProgressionLevelConfigT() {
    this.Uid = null;
    this.Name = null;
    this.LocationUid = null;
    this.StartupBoosts = null;
    this.EligibleBoosts = null;
    this.GoodTurns = 0;
    this.BetterTurns = 0;
    this.BestTurns = 0;
    this.GoodScore = 0;
    this.BetterScore = 0;
    this.BestScore = 0;
    this.GoodTileKindGoalsFb = null;
    this.BetterTileKindGoalsFb = null;
    this.BestTileKindGoalsFb = null;
    this.GoodReward = null;
    this.BetterReward = null;
    this.BestReward = null;
    this.ScoreCoefs = null;
    this.CompleteReward = null;
    this.GoodExtraGoals = null;
    this.BetterExtraGoals = null;
    this.BestExtraGoals = null;
    this.SortOrder = 0.0f;
    this.ReportingLevelNum = 0.0f;
    this.TargetWinRate = 0;
    this.TargetWinRateT2 = 0;
    this.TargetWinRateT3 = 0;
    this.Difficulty = 0;
    this.AssistSystemUid = null;
    this.Hash = null;
    this.FileNames = null;
    this.AssistUidByLossLevel = null;
    this.AssistLossLevel = null;
  }
}

public struct ProgressionLevelConfigDict : IFlatbufferConfigDict<ProgressionLevelConfig, ProgressionLevelConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ProgressionLevelConfigDict GetRootAsProgressionLevelConfigDict(ByteBuffer _bb) { return GetRootAsProgressionLevelConfigDict(_bb, new ProgressionLevelConfigDict()); }
  public static ProgressionLevelConfigDict GetRootAsProgressionLevelConfigDict(ByteBuffer _bb, ProgressionLevelConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ProgressionLevelConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ProgressionLevelConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ProgressionLevelConfig?)(new FBConfig.ProgressionLevelConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ProgressionLevelConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ProgressionLevelConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ProgressionLevelConfigDict> CreateProgressionLevelConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ProgressionLevelConfigDict.AddValues(builder, valuesOffset);
    return ProgressionLevelConfigDict.EndProgressionLevelConfigDict(builder);
  }

  public static void StartProgressionLevelConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ProgressionLevelConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ProgressionLevelConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ProgressionLevelConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ProgressionLevelConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ProgressionLevelConfigDict> EndProgressionLevelConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ProgressionLevelConfigDict>(o);
  }
  public static void FinishProgressionLevelConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ProgressionLevelConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedProgressionLevelConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ProgressionLevelConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ProgressionLevelConfigDictT UnPack() {
    var _o = new ProgressionLevelConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ProgressionLevelConfigDictT _o) {
    _o.Values = new List<FBConfig.ProgressionLevelConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ProgressionLevelConfigDict> Pack(FlatBufferBuilder builder, ProgressionLevelConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ProgressionLevelConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ProgressionLevelConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ProgressionLevelConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateProgressionLevelConfigDict(
      builder,
      _values);
  }
}

public class ProgressionLevelConfigDictT
{
  public List<FBConfig.ProgressionLevelConfigT> Values { get; set; }

  public ProgressionLevelConfigDictT() {
    this.Values = null;
  }
  public static ProgressionLevelConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ProgressionLevelConfigDict.GetRootAsProgressionLevelConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ProgressionLevelConfigDict.FinishProgressionLevelConfigDictBuffer(fbb, ProgressionLevelConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
