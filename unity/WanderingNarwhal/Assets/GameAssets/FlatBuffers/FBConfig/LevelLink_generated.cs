// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LevelLink : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelLink GetRootAsLevelLink(ByteBuffer _bb) { return GetRootAsLevelLink(_bb, new LevelLink()); }
  public static LevelLink GetRootAsLevelLink(ByteBuffer _bb, LevelLink obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelLink __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.Point? Center { get { int o = __p.__offset(4); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.Point? Points(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PointsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.LevelLink> CreateLevelLink(FlatBufferBuilder builder,
      Offset<FBConfig.Point> centerOffset = default(Offset<FBConfig.Point>),
      VectorOffset pointsOffset = default(VectorOffset)) {
    builder.StartTable(2);
    LevelLink.AddPoints(builder, pointsOffset);
    LevelLink.AddCenter(builder, centerOffset);
    return LevelLink.EndLevelLink(builder);
  }

  public static void StartLevelLink(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddCenter(FlatBufferBuilder builder, Offset<FBConfig.Point> centerOffset) { builder.AddOffset(0, centerOffset.Value, 0); }
  public static void AddPoints(FlatBufferBuilder builder, VectorOffset pointsOffset) { builder.AddOffset(1, pointsOffset.Value, 0); }
  public static VectorOffset CreatePointsVector(FlatBufferBuilder builder, Offset<FBConfig.Point>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePointsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.Point>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePointsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.Point>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePointsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.Point>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPointsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelLink> EndLevelLink(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LevelLink>(o);
  }
  public LevelLinkT UnPack() {
    var _o = new LevelLinkT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelLinkT _o) {
    _o.Center = this.Center.HasValue ? this.Center.Value.UnPack() : null;
    _o.Points = new List<FBConfig.PointT>();
    for (var _j = 0; _j < this.PointsLength; ++_j) {_o.Points.Add(this.Points(_j).HasValue ? this.Points(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LevelLink> Pack(FlatBufferBuilder builder, LevelLinkT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelLink>);
    var _center = _o.Center == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Center);
    var _points = default(VectorOffset);
    if (_o.Points != null) {
      var __points = new Offset<FBConfig.Point>[_o.Points.Count];
      for (var _j = 0; _j < __points.Length; ++_j) { __points[_j] = FBConfig.Point.Pack(builder, _o.Points[_j]); }
      _points = CreatePointsVector(builder, __points);
    }
    return CreateLevelLink(
      builder,
      _center,
      _points);
  }
}

public class LevelLinkT
{
  public FBConfig.PointT Center { get; set; }
  public List<FBConfig.PointT> Points { get; set; }

  public LevelLinkT() {
    this.Center = null;
    this.Points = null;
  }
}


}
