// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ConfigMetadata : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ConfigMetadata GetRootAsConfigMetadata(ByteBuffer _bb) { return GetRootAsConfigMetadata(_bb, new ConfigMetadata()); }
  public static ConfigMetadata GetRootAsConfigMetadata(ByteBuffer _bb, ConfigMetadata obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ConfigMetadata __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Hash { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHashBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetHashBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetHashArray() { return __p.__vector_as_array<byte>(6); }
  public int ConfigType { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateConfigType(int config_type) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, config_type); return true; } else { return false; } }

  public static Offset<FBConfig.ConfigMetadata> CreateConfigMetadata(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset hashOffset = default(StringOffset),
      int config_type = 0) {
    builder.StartTable(3);
    ConfigMetadata.AddConfigType(builder, config_type);
    ConfigMetadata.AddHash(builder, hashOffset);
    ConfigMetadata.AddUid(builder, uidOffset);
    return ConfigMetadata.EndConfigMetadata(builder);
  }

  public static void StartConfigMetadata(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddHash(FlatBufferBuilder builder, StringOffset hashOffset) { builder.AddOffset(1, hashOffset.Value, 0); }
  public static void AddConfigType(FlatBufferBuilder builder, int configType) { builder.AddInt(2, configType, 0); }
  public static Offset<FBConfig.ConfigMetadata> EndConfigMetadata(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ConfigMetadata>(o);
  }

  public static VectorOffset CreateSortedVectorOfConfigMetadata(FlatBufferBuilder builder, Offset<ConfigMetadata>[] offsets) {
    Array.Sort(offsets,
      (Offset<ConfigMetadata> o1, Offset<ConfigMetadata> o2) =>
        new ConfigMetadata().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ConfigMetadata().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ConfigMetadata? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ConfigMetadata obj_ = new ConfigMetadata();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ConfigMetadataT UnPack() {
    var _o = new ConfigMetadataT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ConfigMetadataT _o) {
    _o.Uid = this.Uid;
    _o.Hash = this.Hash;
    _o.ConfigType = this.ConfigType;
  }
  public static Offset<FBConfig.ConfigMetadata> Pack(FlatBufferBuilder builder, ConfigMetadataT _o) {
    if (_o == null) return default(Offset<FBConfig.ConfigMetadata>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _hash = _o.Hash == null ? default(StringOffset) : builder.CreateString(_o.Hash);
    return CreateConfigMetadata(
      builder,
      _uid,
      _hash,
      _o.ConfigType);
  }
}

public class ConfigMetadataT
{
  public string Uid { get; set; }
  public string Hash { get; set; }
  public int ConfigType { get; set; }

  public ConfigMetadataT() {
    this.Uid = null;
    this.Hash = null;
    this.ConfigType = 0;
  }
}

public struct ConfigsManifest : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ConfigsManifest GetRootAsConfigsManifest(ByteBuffer _bb) { return GetRootAsConfigsManifest(_bb, new ConfigsManifest()); }
  public static ConfigsManifest GetRootAsConfigsManifest(ByteBuffer _bb, ConfigsManifest obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ConfigsManifest __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Version { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetVersionBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetVersionBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetVersionArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.ConfigMetadata? Metadata(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.ConfigMetadata?)(new FBConfig.ConfigMetadata()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int MetadataLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ConfigMetadata? MetadataByKey(string key) { int o = __p.__offset(6); return o != 0 ? FBConfig.ConfigMetadata.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }
  public int VersionCode { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateVersionCode(int version_code) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, version_code); return true; } else { return false; } }

  public static Offset<FBConfig.ConfigsManifest> CreateConfigsManifest(FlatBufferBuilder builder,
      StringOffset versionOffset = default(StringOffset),
      VectorOffset metadataOffset = default(VectorOffset),
      int version_code = 0) {
    builder.StartTable(3);
    ConfigsManifest.AddVersionCode(builder, version_code);
    ConfigsManifest.AddMetadata(builder, metadataOffset);
    ConfigsManifest.AddVersion(builder, versionOffset);
    return ConfigsManifest.EndConfigsManifest(builder);
  }

  public static void StartConfigsManifest(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddVersion(FlatBufferBuilder builder, StringOffset versionOffset) { builder.AddOffset(0, versionOffset.Value, 0); }
  public static void AddMetadata(FlatBufferBuilder builder, VectorOffset metadataOffset) { builder.AddOffset(1, metadataOffset.Value, 0); }
  public static VectorOffset CreateMetadataVector(FlatBufferBuilder builder, Offset<FBConfig.ConfigMetadata>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateMetadataVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ConfigMetadata>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateMetadataVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ConfigMetadata>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateMetadataVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ConfigMetadata>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartMetadataVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddVersionCode(FlatBufferBuilder builder, int versionCode) { builder.AddInt(2, versionCode, 0); }
  public static Offset<FBConfig.ConfigsManifest> EndConfigsManifest(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ConfigsManifest>(o);
  }
  public static void FinishConfigsManifestBuffer(FlatBufferBuilder builder, Offset<FBConfig.ConfigsManifest> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedConfigsManifestBuffer(FlatBufferBuilder builder, Offset<FBConfig.ConfigsManifest> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ConfigsManifestT UnPack() {
    var _o = new ConfigsManifestT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ConfigsManifestT _o) {
    _o.Version = this.Version;
    _o.Metadata = new List<FBConfig.ConfigMetadataT>();
    for (var _j = 0; _j < this.MetadataLength; ++_j) {_o.Metadata.Add(this.Metadata(_j).HasValue ? this.Metadata(_j).Value.UnPack() : null);}
    _o.VersionCode = this.VersionCode;
  }
  public static Offset<FBConfig.ConfigsManifest> Pack(FlatBufferBuilder builder, ConfigsManifestT _o) {
    if (_o == null) return default(Offset<FBConfig.ConfigsManifest>);
    var _version = _o.Version == null ? default(StringOffset) : builder.CreateString(_o.Version);
    var _metadata = default(VectorOffset);
    if (_o.Metadata != null) {
      var __metadata = new Offset<FBConfig.ConfigMetadata>[_o.Metadata.Count];
      for (var _j = 0; _j < __metadata.Length; ++_j) { __metadata[_j] = FBConfig.ConfigMetadata.Pack(builder, _o.Metadata[_j]); }
      _metadata = CreateMetadataVector(builder, __metadata);
    }
    return CreateConfigsManifest(
      builder,
      _version,
      _metadata,
      _o.VersionCode);
  }
}

public class ConfigsManifestT
{
  public string Version { get; set; }
  public List<FBConfig.ConfigMetadataT> Metadata { get; set; }
  public int VersionCode { get; set; }

  public ConfigsManifestT() {
    this.Version = null;
    this.Metadata = null;
    this.VersionCode = 0;
  }
  public static ConfigsManifestT DeserializeFromBinary(byte[] fbBuffer) {
    return ConfigsManifest.GetRootAsConfigsManifest(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ConfigsManifest.FinishConfigsManifestBuffer(fbb, ConfigsManifest.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
