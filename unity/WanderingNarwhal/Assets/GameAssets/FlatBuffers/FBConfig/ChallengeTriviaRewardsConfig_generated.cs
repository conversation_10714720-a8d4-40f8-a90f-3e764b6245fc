// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ChallengeTriviaRewardsConfig : IFlatbufferConfig<ChallengeTriviaRewardsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeTriviaRewardsConfig GetRootAsChallengeTriviaRewardsConfig(ByteBuffer _bb) { return GetRootAsChallengeTriviaRewardsConfig(_bb, new ChallengeTriviaRewardsConfig()); }
  public static ChallengeTriviaRewardsConfig GetRootAsChallengeTriviaRewardsConfig(ByteBuffer _bb, ChallengeTriviaRewardsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeTriviaRewardsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Question { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateQuestion(int question) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, question); return true; } else { return false; } }
  public FBConfig.DictStringInt? Rewards(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RewardsLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.ChallengeTriviaRewardsConfig> CreateChallengeTriviaRewardsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int question = 0,
      VectorOffset rewardsOffset = default(VectorOffset)) {
    builder.StartTable(3);
    ChallengeTriviaRewardsConfig.AddRewards(builder, rewardsOffset);
    ChallengeTriviaRewardsConfig.AddQuestion(builder, question);
    ChallengeTriviaRewardsConfig.AddUid(builder, uidOffset);
    return ChallengeTriviaRewardsConfig.EndChallengeTriviaRewardsConfig(builder);
  }

  public static void StartChallengeTriviaRewardsConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddQuestion(FlatBufferBuilder builder, int question) { builder.AddInt(1, question, 0); }
  public static void AddRewards(FlatBufferBuilder builder, VectorOffset rewardsOffset) { builder.AddOffset(2, rewardsOffset.Value, 0); }
  public static VectorOffset CreateRewardsVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRewardsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ChallengeTriviaRewardsConfig> EndChallengeTriviaRewardsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ChallengeTriviaRewardsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfChallengeTriviaRewardsConfig(FlatBufferBuilder builder, Offset<ChallengeTriviaRewardsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ChallengeTriviaRewardsConfig> o1, Offset<ChallengeTriviaRewardsConfig> o2) =>
        new ChallengeTriviaRewardsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ChallengeTriviaRewardsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ChallengeTriviaRewardsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ChallengeTriviaRewardsConfig obj_ = new ChallengeTriviaRewardsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ChallengeTriviaRewardsConfigT UnPack() {
    var _o = new ChallengeTriviaRewardsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeTriviaRewardsConfigT _o) {
    _o.Uid = this.Uid;
    _o.Question = this.Question;
    _o.Rewards = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.RewardsLength; ++_j) {_o.Rewards.Add(this.Rewards(_j).HasValue ? this.Rewards(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ChallengeTriviaRewardsConfig> Pack(FlatBufferBuilder builder, ChallengeTriviaRewardsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeTriviaRewardsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _rewards = default(VectorOffset);
    if (_o.Rewards != null) {
      var __rewards = new Offset<FBConfig.DictStringInt>[_o.Rewards.Count];
      for (var _j = 0; _j < __rewards.Length; ++_j) { __rewards[_j] = FBConfig.DictStringInt.Pack(builder, _o.Rewards[_j]); }
      _rewards = CreateRewardsVector(builder, __rewards);
    }
    return CreateChallengeTriviaRewardsConfig(
      builder,
      _uid,
      _o.Question,
      _rewards);
  }
}

public class ChallengeTriviaRewardsConfigT
{
  public string Uid { get; set; }
  public int Question { get; set; }
  public List<FBConfig.DictStringIntT> Rewards { get; set; }

  public ChallengeTriviaRewardsConfigT() {
    this.Uid = null;
    this.Question = 0;
    this.Rewards = null;
  }
}

public struct ChallengeTriviaRewardsConfigDict : IFlatbufferConfigDict<ChallengeTriviaRewardsConfig, ChallengeTriviaRewardsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ChallengeTriviaRewardsConfigDict GetRootAsChallengeTriviaRewardsConfigDict(ByteBuffer _bb) { return GetRootAsChallengeTriviaRewardsConfigDict(_bb, new ChallengeTriviaRewardsConfigDict()); }
  public static ChallengeTriviaRewardsConfigDict GetRootAsChallengeTriviaRewardsConfigDict(ByteBuffer _bb, ChallengeTriviaRewardsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ChallengeTriviaRewardsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ChallengeTriviaRewardsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ChallengeTriviaRewardsConfig?)(new FBConfig.ChallengeTriviaRewardsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ChallengeTriviaRewardsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ChallengeTriviaRewardsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ChallengeTriviaRewardsConfigDict> CreateChallengeTriviaRewardsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ChallengeTriviaRewardsConfigDict.AddValues(builder, valuesOffset);
    return ChallengeTriviaRewardsConfigDict.EndChallengeTriviaRewardsConfigDict(builder);
  }

  public static void StartChallengeTriviaRewardsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaRewardsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaRewardsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ChallengeTriviaRewardsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ChallengeTriviaRewardsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ChallengeTriviaRewardsConfigDict> EndChallengeTriviaRewardsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ChallengeTriviaRewardsConfigDict>(o);
  }
  public static void FinishChallengeTriviaRewardsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaRewardsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedChallengeTriviaRewardsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ChallengeTriviaRewardsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ChallengeTriviaRewardsConfigDictT UnPack() {
    var _o = new ChallengeTriviaRewardsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ChallengeTriviaRewardsConfigDictT _o) {
    _o.Values = new List<FBConfig.ChallengeTriviaRewardsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ChallengeTriviaRewardsConfigDict> Pack(FlatBufferBuilder builder, ChallengeTriviaRewardsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ChallengeTriviaRewardsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ChallengeTriviaRewardsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ChallengeTriviaRewardsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateChallengeTriviaRewardsConfigDict(
      builder,
      _values);
  }
}

public class ChallengeTriviaRewardsConfigDictT
{
  public List<FBConfig.ChallengeTriviaRewardsConfigT> Values { get; set; }

  public ChallengeTriviaRewardsConfigDictT() {
    this.Values = null;
  }
  public static ChallengeTriviaRewardsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ChallengeTriviaRewardsConfigDict.GetRootAsChallengeTriviaRewardsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ChallengeTriviaRewardsConfigDict.FinishChallengeTriviaRewardsConfigDictBuffer(fbb, ChallengeTriviaRewardsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
