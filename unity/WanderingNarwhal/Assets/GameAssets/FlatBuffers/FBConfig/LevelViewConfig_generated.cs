// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LevelViewConfig : IFlatbufferConfig<LevelViewConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelViewConfig GetRootAsLevelViewConfig(ByteBuffer _bb) { return GetRootAsLevelViewConfig(_bb, new LevelViewConfig()); }
  public static LevelViewConfig GetRootAsLevelViewConfig(ByteBuffer _bb, LevelViewConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelViewConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.Point? Position { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.LevelLink? Links(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.LevelLink?)(new FBConfig.LevelLink()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int LinksLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Point? Size { get { int o = __p.__offset(10); return o != 0 ? (FBConfig.Point?)(new FBConfig.Point()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string PoiEntityUid { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPoiEntityUidBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetPoiEntityUidBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetPoiEntityUidArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.LevelViewConfig> CreateLevelViewConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      Offset<FBConfig.Point> positionOffset = default(Offset<FBConfig.Point>),
      VectorOffset linksOffset = default(VectorOffset),
      Offset<FBConfig.Point> sizeOffset = default(Offset<FBConfig.Point>),
      StringOffset poi_entity_uidOffset = default(StringOffset)) {
    builder.StartTable(5);
    LevelViewConfig.AddPoiEntityUid(builder, poi_entity_uidOffset);
    LevelViewConfig.AddSize(builder, sizeOffset);
    LevelViewConfig.AddLinks(builder, linksOffset);
    LevelViewConfig.AddPosition(builder, positionOffset);
    LevelViewConfig.AddUid(builder, uidOffset);
    return LevelViewConfig.EndLevelViewConfig(builder);
  }

  public static void StartLevelViewConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddPosition(FlatBufferBuilder builder, Offset<FBConfig.Point> positionOffset) { builder.AddOffset(1, positionOffset.Value, 0); }
  public static void AddLinks(FlatBufferBuilder builder, VectorOffset linksOffset) { builder.AddOffset(2, linksOffset.Value, 0); }
  public static VectorOffset CreateLinksVector(FlatBufferBuilder builder, Offset<FBConfig.LevelLink>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLinksVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LevelLink>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLinksVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LevelLink>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLinksVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LevelLink>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLinksVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSize(FlatBufferBuilder builder, Offset<FBConfig.Point> sizeOffset) { builder.AddOffset(3, sizeOffset.Value, 0); }
  public static void AddPoiEntityUid(FlatBufferBuilder builder, StringOffset poiEntityUidOffset) { builder.AddOffset(4, poiEntityUidOffset.Value, 0); }
  public static Offset<FBConfig.LevelViewConfig> EndLevelViewConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LevelViewConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLevelViewConfig(FlatBufferBuilder builder, Offset<LevelViewConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LevelViewConfig> o1, Offset<LevelViewConfig> o2) =>
        new LevelViewConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LevelViewConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LevelViewConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LevelViewConfig obj_ = new LevelViewConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LevelViewConfigT UnPack() {
    var _o = new LevelViewConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelViewConfigT _o) {
    _o.Uid = this.Uid;
    _o.Position = this.Position.HasValue ? this.Position.Value.UnPack() : null;
    _o.Links = new List<FBConfig.LevelLinkT>();
    for (var _j = 0; _j < this.LinksLength; ++_j) {_o.Links.Add(this.Links(_j).HasValue ? this.Links(_j).Value.UnPack() : null);}
    _o.Size = this.Size.HasValue ? this.Size.Value.UnPack() : null;
    _o.PoiEntityUid = this.PoiEntityUid;
  }
  public static Offset<FBConfig.LevelViewConfig> Pack(FlatBufferBuilder builder, LevelViewConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelViewConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _position = _o.Position == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Position);
    var _links = default(VectorOffset);
    if (_o.Links != null) {
      var __links = new Offset<FBConfig.LevelLink>[_o.Links.Count];
      for (var _j = 0; _j < __links.Length; ++_j) { __links[_j] = FBConfig.LevelLink.Pack(builder, _o.Links[_j]); }
      _links = CreateLinksVector(builder, __links);
    }
    var _size = _o.Size == null ? default(Offset<FBConfig.Point>) : FBConfig.Point.Pack(builder, _o.Size);
    var _poi_entity_uid = _o.PoiEntityUid == null ? default(StringOffset) : builder.CreateString(_o.PoiEntityUid);
    return CreateLevelViewConfig(
      builder,
      _uid,
      _position,
      _links,
      _size,
      _poi_entity_uid);
  }
}

public class LevelViewConfigT
{
  public string Uid { get; set; }
  public FBConfig.PointT Position { get; set; }
  public List<FBConfig.LevelLinkT> Links { get; set; }
  public FBConfig.PointT Size { get; set; }
  public string PoiEntityUid { get; set; }

  public LevelViewConfigT() {
    this.Uid = null;
    this.Position = null;
    this.Links = null;
    this.Size = null;
    this.PoiEntityUid = null;
  }
}

public struct LevelViewConfigDict : IFlatbufferConfigDict<LevelViewConfig, LevelViewConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelViewConfigDict GetRootAsLevelViewConfigDict(ByteBuffer _bb) { return GetRootAsLevelViewConfigDict(_bb, new LevelViewConfigDict()); }
  public static LevelViewConfigDict GetRootAsLevelViewConfigDict(ByteBuffer _bb, LevelViewConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelViewConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LevelViewConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LevelViewConfig?)(new FBConfig.LevelViewConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LevelViewConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LevelViewConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LevelViewConfigDict> CreateLevelViewConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LevelViewConfigDict.AddValues(builder, valuesOffset);
    return LevelViewConfigDict.EndLevelViewConfigDict(builder);
  }

  public static void StartLevelViewConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LevelViewConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LevelViewConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LevelViewConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LevelViewConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelViewConfigDict> EndLevelViewConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LevelViewConfigDict>(o);
  }
  public static void FinishLevelViewConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelViewConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLevelViewConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelViewConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LevelViewConfigDictT UnPack() {
    var _o = new LevelViewConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelViewConfigDictT _o) {
    _o.Values = new List<FBConfig.LevelViewConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LevelViewConfigDict> Pack(FlatBufferBuilder builder, LevelViewConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelViewConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LevelViewConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LevelViewConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLevelViewConfigDict(
      builder,
      _values);
  }
}

public class LevelViewConfigDictT
{
  public List<FBConfig.LevelViewConfigT> Values { get; set; }

  public LevelViewConfigDictT() {
    this.Values = null;
  }
  public static LevelViewConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LevelViewConfigDict.GetRootAsLevelViewConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LevelViewConfigDict.FinishLevelViewConfigDictBuffer(fbb, LevelViewConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
