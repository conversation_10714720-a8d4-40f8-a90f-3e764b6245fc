// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct AdPlacementConfig : IFlatbufferConfig<AdPlacementConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AdPlacementConfig GetRootAsAdPlacementConfig(ByteBuffer _bb) { return GetRootAsAdPlacementConfig(_bb, new AdPlacementConfig()); }
  public static AdPlacementConfig GetRootAsAdPlacementConfig(ByteBuffer _bb, AdPlacementConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public AdPlacementConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string PlacementId { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPlacementIdBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetPlacementIdBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetPlacementIdArray() { return __p.__vector_as_array<byte>(6); }
  public string UnlockLevel { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUnlockLevelBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetUnlockLevelBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetUnlockLevelArray() { return __p.__vector_as_array<byte>(8); }
  public int PayerCap { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePayerCap(int payer_cap) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, payer_cap); return true; } else { return false; } }
  public int NonPayerCap { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateNonPayerCap(int non_payer_cap) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, non_payer_cap); return true; } else { return false; } }
  public int CooldownTime { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateCooldownTime(int cooldown_time) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, cooldown_time); return true; } else { return false; } }
  public int MaxFailedAttempts { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxFailedAttempts(int max_failed_attempts) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_failed_attempts); return true; } else { return false; } }
  public int FailCooldownTime { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateFailCooldownTime(int fail_cooldown_time) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, fail_cooldown_time); return true; } else { return false; } }
  public string DescriptionText { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDescriptionTextBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetDescriptionTextBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetDescriptionTextArray() { return __p.__vector_as_array<byte>(20); }

  public static Offset<FBConfig.AdPlacementConfig> CreateAdPlacementConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset placement_idOffset = default(StringOffset),
      StringOffset unlock_levelOffset = default(StringOffset),
      int payer_cap = 0,
      int non_payer_cap = 0,
      int cooldown_time = 0,
      int max_failed_attempts = 0,
      int fail_cooldown_time = 0,
      StringOffset description_textOffset = default(StringOffset)) {
    builder.StartTable(9);
    AdPlacementConfig.AddDescriptionText(builder, description_textOffset);
    AdPlacementConfig.AddFailCooldownTime(builder, fail_cooldown_time);
    AdPlacementConfig.AddMaxFailedAttempts(builder, max_failed_attempts);
    AdPlacementConfig.AddCooldownTime(builder, cooldown_time);
    AdPlacementConfig.AddNonPayerCap(builder, non_payer_cap);
    AdPlacementConfig.AddPayerCap(builder, payer_cap);
    AdPlacementConfig.AddUnlockLevel(builder, unlock_levelOffset);
    AdPlacementConfig.AddPlacementId(builder, placement_idOffset);
    AdPlacementConfig.AddUid(builder, uidOffset);
    return AdPlacementConfig.EndAdPlacementConfig(builder);
  }

  public static void StartAdPlacementConfig(FlatBufferBuilder builder) { builder.StartTable(9); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddPlacementId(FlatBufferBuilder builder, StringOffset placementIdOffset) { builder.AddOffset(1, placementIdOffset.Value, 0); }
  public static void AddUnlockLevel(FlatBufferBuilder builder, StringOffset unlockLevelOffset) { builder.AddOffset(2, unlockLevelOffset.Value, 0); }
  public static void AddPayerCap(FlatBufferBuilder builder, int payerCap) { builder.AddInt(3, payerCap, 0); }
  public static void AddNonPayerCap(FlatBufferBuilder builder, int nonPayerCap) { builder.AddInt(4, nonPayerCap, 0); }
  public static void AddCooldownTime(FlatBufferBuilder builder, int cooldownTime) { builder.AddInt(5, cooldownTime, 0); }
  public static void AddMaxFailedAttempts(FlatBufferBuilder builder, int maxFailedAttempts) { builder.AddInt(6, maxFailedAttempts, 0); }
  public static void AddFailCooldownTime(FlatBufferBuilder builder, int failCooldownTime) { builder.AddInt(7, failCooldownTime, 0); }
  public static void AddDescriptionText(FlatBufferBuilder builder, StringOffset descriptionTextOffset) { builder.AddOffset(8, descriptionTextOffset.Value, 0); }
  public static Offset<FBConfig.AdPlacementConfig> EndAdPlacementConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.AdPlacementConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfAdPlacementConfig(FlatBufferBuilder builder, Offset<AdPlacementConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<AdPlacementConfig> o1, Offset<AdPlacementConfig> o2) =>
        new AdPlacementConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new AdPlacementConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static AdPlacementConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    AdPlacementConfig obj_ = new AdPlacementConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public AdPlacementConfigT UnPack() {
    var _o = new AdPlacementConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AdPlacementConfigT _o) {
    _o.Uid = this.Uid;
    _o.PlacementId = this.PlacementId;
    _o.UnlockLevel = this.UnlockLevel;
    _o.PayerCap = this.PayerCap;
    _o.NonPayerCap = this.NonPayerCap;
    _o.CooldownTime = this.CooldownTime;
    _o.MaxFailedAttempts = this.MaxFailedAttempts;
    _o.FailCooldownTime = this.FailCooldownTime;
    _o.DescriptionText = this.DescriptionText;
  }
  public static Offset<FBConfig.AdPlacementConfig> Pack(FlatBufferBuilder builder, AdPlacementConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.AdPlacementConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _placement_id = _o.PlacementId == null ? default(StringOffset) : builder.CreateString(_o.PlacementId);
    var _unlock_level = _o.UnlockLevel == null ? default(StringOffset) : builder.CreateString(_o.UnlockLevel);
    var _description_text = _o.DescriptionText == null ? default(StringOffset) : builder.CreateString(_o.DescriptionText);
    return CreateAdPlacementConfig(
      builder,
      _uid,
      _placement_id,
      _unlock_level,
      _o.PayerCap,
      _o.NonPayerCap,
      _o.CooldownTime,
      _o.MaxFailedAttempts,
      _o.FailCooldownTime,
      _description_text);
  }
}

public class AdPlacementConfigT
{
  public string Uid { get; set; }
  public string PlacementId { get; set; }
  public string UnlockLevel { get; set; }
  public int PayerCap { get; set; }
  public int NonPayerCap { get; set; }
  public int CooldownTime { get; set; }
  public int MaxFailedAttempts { get; set; }
  public int FailCooldownTime { get; set; }
  public string DescriptionText { get; set; }

  public AdPlacementConfigT() {
    this.Uid = null;
    this.PlacementId = null;
    this.UnlockLevel = null;
    this.PayerCap = 0;
    this.NonPayerCap = 0;
    this.CooldownTime = 0;
    this.MaxFailedAttempts = 0;
    this.FailCooldownTime = 0;
    this.DescriptionText = null;
  }
}

public struct AdPlacementConfigDict : IFlatbufferConfigDict<AdPlacementConfig, AdPlacementConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AdPlacementConfigDict GetRootAsAdPlacementConfigDict(ByteBuffer _bb) { return GetRootAsAdPlacementConfigDict(_bb, new AdPlacementConfigDict()); }
  public static AdPlacementConfigDict GetRootAsAdPlacementConfigDict(ByteBuffer _bb, AdPlacementConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AdPlacementConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.AdPlacementConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.AdPlacementConfig?)(new FBConfig.AdPlacementConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.AdPlacementConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.AdPlacementConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.AdPlacementConfigDict> CreateAdPlacementConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    AdPlacementConfigDict.AddValues(builder, valuesOffset);
    return AdPlacementConfigDict.EndAdPlacementConfigDict(builder);
  }

  public static void StartAdPlacementConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.AdPlacementConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.AdPlacementConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.AdPlacementConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.AdPlacementConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.AdPlacementConfigDict> EndAdPlacementConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AdPlacementConfigDict>(o);
  }
  public static void FinishAdPlacementConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AdPlacementConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedAdPlacementConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AdPlacementConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public AdPlacementConfigDictT UnPack() {
    var _o = new AdPlacementConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AdPlacementConfigDictT _o) {
    _o.Values = new List<FBConfig.AdPlacementConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.AdPlacementConfigDict> Pack(FlatBufferBuilder builder, AdPlacementConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.AdPlacementConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.AdPlacementConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.AdPlacementConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateAdPlacementConfigDict(
      builder,
      _values);
  }
}

public class AdPlacementConfigDictT
{
  public List<FBConfig.AdPlacementConfigT> Values { get; set; }

  public AdPlacementConfigDictT() {
    this.Values = null;
  }
  public static AdPlacementConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return AdPlacementConfigDict.GetRootAsAdPlacementConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    AdPlacementConfigDict.FinishAdPlacementConfigDictBuffer(fbb, AdPlacementConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
