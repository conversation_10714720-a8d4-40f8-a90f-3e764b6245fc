// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct CompetitionLeaderboardConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CompetitionLeaderboardConfig GetRootAsCompetitionLeaderboardConfig(ByteBuffer _bb) { return GetRootAsCompetitionLeaderboardConfig(_bb, new CompetitionLeaderboardConfig()); }
  public static CompetitionLeaderboardConfig GetRootAsCompetitionLeaderboardConfig(ByteBuffer _bb, CompetitionLeaderboardConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CompetitionLeaderboardConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float RealPlayersLimitRatio { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateRealPlayersLimitRatio(float real_players_limit_ratio) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, real_players_limit_ratio); return true; } else { return false; } }
  public int ShowPlayersPerLeague { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateShowPlayersPerLeague(int show_players_per_league) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, show_players_per_league); return true; } else { return false; } }
  public FBConfig.DictStringInt? LeagueScoreLimitsFb(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringInt?)(new FBConfig.DictStringInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int LeagueScoreLimitsFbLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int TopCut { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateTopCut(int top_cut) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, top_cut); return true; } else { return false; } }
  public bool LocalBotsEnabled { get { int o = __p.__offset(12); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateLocalBotsEnabled(bool local_bots_enabled) { int o = __p.__offset(12); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(local_bots_enabled ? 1 : 0)); return true; } else { return false; } }
  public bool SaveLastLeaderboardLeague { get { int o = __p.__offset(14); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateSaveLastLeaderboardLeague(bool save_last_leaderboard_league) { int o = __p.__offset(14); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(save_last_leaderboard_league ? 1 : 0)); return true; } else { return false; } }
  public int BotsUpdateSteps { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateBotsUpdateSteps(int bots_update_steps) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, bots_update_steps); return true; } else { return false; } }

  public static Offset<FBConfig.CompetitionLeaderboardConfig> CreateCompetitionLeaderboardConfig(FlatBufferBuilder builder,
      float real_players_limit_ratio = 0.0f,
      int show_players_per_league = 0,
      VectorOffset league_score_limits_fbOffset = default(VectorOffset),
      int top_cut = 0,
      bool local_bots_enabled = false,
      bool save_last_leaderboard_league = false,
      int bots_update_steps = 0) {
    builder.StartTable(7);
    CompetitionLeaderboardConfig.AddBotsUpdateSteps(builder, bots_update_steps);
    CompetitionLeaderboardConfig.AddTopCut(builder, top_cut);
    CompetitionLeaderboardConfig.AddLeagueScoreLimitsFb(builder, league_score_limits_fbOffset);
    CompetitionLeaderboardConfig.AddShowPlayersPerLeague(builder, show_players_per_league);
    CompetitionLeaderboardConfig.AddRealPlayersLimitRatio(builder, real_players_limit_ratio);
    CompetitionLeaderboardConfig.AddSaveLastLeaderboardLeague(builder, save_last_leaderboard_league);
    CompetitionLeaderboardConfig.AddLocalBotsEnabled(builder, local_bots_enabled);
    return CompetitionLeaderboardConfig.EndCompetitionLeaderboardConfig(builder);
  }

  public static void StartCompetitionLeaderboardConfig(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddRealPlayersLimitRatio(FlatBufferBuilder builder, float realPlayersLimitRatio) { builder.AddFloat(0, realPlayersLimitRatio, 0.0f); }
  public static void AddShowPlayersPerLeague(FlatBufferBuilder builder, int showPlayersPerLeague) { builder.AddInt(1, showPlayersPerLeague, 0); }
  public static void AddLeagueScoreLimitsFb(FlatBufferBuilder builder, VectorOffset leagueScoreLimitsFbOffset) { builder.AddOffset(2, leagueScoreLimitsFbOffset.Value, 0); }
  public static VectorOffset CreateLeagueScoreLimitsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLeagueScoreLimitsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLeagueScoreLimitsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLeagueScoreLimitsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLeagueScoreLimitsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTopCut(FlatBufferBuilder builder, int topCut) { builder.AddInt(3, topCut, 0); }
  public static void AddLocalBotsEnabled(FlatBufferBuilder builder, bool localBotsEnabled) { builder.AddBool(4, localBotsEnabled, false); }
  public static void AddSaveLastLeaderboardLeague(FlatBufferBuilder builder, bool saveLastLeaderboardLeague) { builder.AddBool(5, saveLastLeaderboardLeague, false); }
  public static void AddBotsUpdateSteps(FlatBufferBuilder builder, int botsUpdateSteps) { builder.AddInt(6, botsUpdateSteps, 0); }
  public static Offset<FBConfig.CompetitionLeaderboardConfig> EndCompetitionLeaderboardConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CompetitionLeaderboardConfig>(o);
  }
  public CompetitionLeaderboardConfigT UnPack() {
    var _o = new CompetitionLeaderboardConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CompetitionLeaderboardConfigT _o) {
    _o.RealPlayersLimitRatio = this.RealPlayersLimitRatio;
    _o.ShowPlayersPerLeague = this.ShowPlayersPerLeague;
    _o.LeagueScoreLimitsFb = new List<FBConfig.DictStringIntT>();
    for (var _j = 0; _j < this.LeagueScoreLimitsFbLength; ++_j) {_o.LeagueScoreLimitsFb.Add(this.LeagueScoreLimitsFb(_j).HasValue ? this.LeagueScoreLimitsFb(_j).Value.UnPack() : null);}
    _o.TopCut = this.TopCut;
    _o.LocalBotsEnabled = this.LocalBotsEnabled;
    _o.SaveLastLeaderboardLeague = this.SaveLastLeaderboardLeague;
    _o.BotsUpdateSteps = this.BotsUpdateSteps;
  }
  public static Offset<FBConfig.CompetitionLeaderboardConfig> Pack(FlatBufferBuilder builder, CompetitionLeaderboardConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.CompetitionLeaderboardConfig>);
    var _league_score_limits_fb = default(VectorOffset);
    if (_o.LeagueScoreLimitsFb != null) {
      var __league_score_limits_fb = new Offset<FBConfig.DictStringInt>[_o.LeagueScoreLimitsFb.Count];
      for (var _j = 0; _j < __league_score_limits_fb.Length; ++_j) { __league_score_limits_fb[_j] = FBConfig.DictStringInt.Pack(builder, _o.LeagueScoreLimitsFb[_j]); }
      _league_score_limits_fb = CreateLeagueScoreLimitsFbVector(builder, __league_score_limits_fb);
    }
    return CreateCompetitionLeaderboardConfig(
      builder,
      _o.RealPlayersLimitRatio,
      _o.ShowPlayersPerLeague,
      _league_score_limits_fb,
      _o.TopCut,
      _o.LocalBotsEnabled,
      _o.SaveLastLeaderboardLeague,
      _o.BotsUpdateSteps);
  }
}

public class CompetitionLeaderboardConfigT
{
  public float RealPlayersLimitRatio { get; set; }
  public int ShowPlayersPerLeague { get; set; }
  public List<FBConfig.DictStringIntT> LeagueScoreLimitsFb { get; set; }
  public int TopCut { get; set; }
  public bool LocalBotsEnabled { get; set; }
  public bool SaveLastLeaderboardLeague { get; set; }
  public int BotsUpdateSteps { get; set; }

  public CompetitionLeaderboardConfigT() {
    this.RealPlayersLimitRatio = 0.0f;
    this.ShowPlayersPerLeague = 0;
    this.LeagueScoreLimitsFb = null;
    this.TopCut = 0;
    this.LocalBotsEnabled = false;
    this.SaveLastLeaderboardLeague = false;
    this.BotsUpdateSteps = 0;
  }
}

public struct BotClaimConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static BotClaimConfig GetRootAsBotClaimConfig(ByteBuffer _bb) { return GetRootAsBotClaimConfig(_bb, new BotClaimConfig()); }
  public static BotClaimConfig GetRootAsBotClaimConfig(ByteBuffer _bb, BotClaimConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public BotClaimConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int FinalStageDuration { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateFinalStageDuration(int final_stage_duration) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, final_stage_duration); return true; } else { return false; } }
  public int MaxAdditionalScore { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxAdditionalScore(int max_additional_score) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_additional_score); return true; } else { return false; } }
  public int MinAdditionalScore { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMinAdditionalScore(int min_additional_score) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, min_additional_score); return true; } else { return false; } }
  public int MinTimeToClaim { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMinTimeToClaim(int min_time_to_claim) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, min_time_to_claim); return true; } else { return false; } }
  public int MaxTimeToClaim { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxTimeToClaim(int max_time_to_claim) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_time_to_claim); return true; } else { return false; } }
  public int MaxScoreCap { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxScoreCap(int max_score_cap) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_score_cap); return true; } else { return false; } }

  public static Offset<FBConfig.BotClaimConfig> CreateBotClaimConfig(FlatBufferBuilder builder,
      int final_stage_duration = 0,
      int max_additional_score = 0,
      int min_additional_score = 0,
      int min_time_to_claim = 0,
      int max_time_to_claim = 0,
      int max_score_cap = 0) {
    builder.StartTable(6);
    BotClaimConfig.AddMaxScoreCap(builder, max_score_cap);
    BotClaimConfig.AddMaxTimeToClaim(builder, max_time_to_claim);
    BotClaimConfig.AddMinTimeToClaim(builder, min_time_to_claim);
    BotClaimConfig.AddMinAdditionalScore(builder, min_additional_score);
    BotClaimConfig.AddMaxAdditionalScore(builder, max_additional_score);
    BotClaimConfig.AddFinalStageDuration(builder, final_stage_duration);
    return BotClaimConfig.EndBotClaimConfig(builder);
  }

  public static void StartBotClaimConfig(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddFinalStageDuration(FlatBufferBuilder builder, int finalStageDuration) { builder.AddInt(0, finalStageDuration, 0); }
  public static void AddMaxAdditionalScore(FlatBufferBuilder builder, int maxAdditionalScore) { builder.AddInt(1, maxAdditionalScore, 0); }
  public static void AddMinAdditionalScore(FlatBufferBuilder builder, int minAdditionalScore) { builder.AddInt(2, minAdditionalScore, 0); }
  public static void AddMinTimeToClaim(FlatBufferBuilder builder, int minTimeToClaim) { builder.AddInt(3, minTimeToClaim, 0); }
  public static void AddMaxTimeToClaim(FlatBufferBuilder builder, int maxTimeToClaim) { builder.AddInt(4, maxTimeToClaim, 0); }
  public static void AddMaxScoreCap(FlatBufferBuilder builder, int maxScoreCap) { builder.AddInt(5, maxScoreCap, 0); }
  public static Offset<FBConfig.BotClaimConfig> EndBotClaimConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.BotClaimConfig>(o);
  }
  public BotClaimConfigT UnPack() {
    var _o = new BotClaimConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(BotClaimConfigT _o) {
    _o.FinalStageDuration = this.FinalStageDuration;
    _o.MaxAdditionalScore = this.MaxAdditionalScore;
    _o.MinAdditionalScore = this.MinAdditionalScore;
    _o.MinTimeToClaim = this.MinTimeToClaim;
    _o.MaxTimeToClaim = this.MaxTimeToClaim;
    _o.MaxScoreCap = this.MaxScoreCap;
  }
  public static Offset<FBConfig.BotClaimConfig> Pack(FlatBufferBuilder builder, BotClaimConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.BotClaimConfig>);
    return CreateBotClaimConfig(
      builder,
      _o.FinalStageDuration,
      _o.MaxAdditionalScore,
      _o.MinAdditionalScore,
      _o.MinTimeToClaim,
      _o.MaxTimeToClaim,
      _o.MaxScoreCap);
  }
}

public class BotClaimConfigT
{
  public int FinalStageDuration { get; set; }
  public int MaxAdditionalScore { get; set; }
  public int MinAdditionalScore { get; set; }
  public int MinTimeToClaim { get; set; }
  public int MaxTimeToClaim { get; set; }
  public int MaxScoreCap { get; set; }

  public BotClaimConfigT() {
    this.FinalStageDuration = 0;
    this.MaxAdditionalScore = 0;
    this.MinAdditionalScore = 0;
    this.MinTimeToClaim = 0;
    this.MaxTimeToClaim = 0;
    this.MaxScoreCap = 0;
  }
}

public struct CompetitionGameEventConfig : IFlatbufferConfig<CompetitionGameEventConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CompetitionGameEventConfig GetRootAsCompetitionGameEventConfig(ByteBuffer _bb) { return GetRootAsCompetitionGameEventConfig(_bb, new CompetitionGameEventConfig()); }
  public static CompetitionGameEventConfig GetRootAsCompetitionGameEventConfig(ByteBuffer _bb, CompetitionGameEventConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CompetitionGameEventConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.CompetitionLeaderboardConfig? CompetitionLeaderboardConfig { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.CompetitionLeaderboardConfig?)(new FBConfig.CompetitionLeaderboardConfig()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int QualificationTarget { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateQualificationTarget(int qualification_target) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, qualification_target); return true; } else { return false; } }
  public string StartNarrativeId { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetStartNarrativeIdBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetStartNarrativeIdBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetStartNarrativeIdArray() { return __p.__vector_as_array<byte>(10); }
  public string StoneLeagueRewards(int j) { int o = __p.__offset(12); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int StoneLeagueRewardsLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string BronzeLeagueRewards(int j) { int o = __p.__offset(14); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int BronzeLeagueRewardsLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string SilverLeagueRewards(int j) { int o = __p.__offset(16); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int SilverLeagueRewardsLength { get { int o = __p.__offset(16); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string GoldenLeagueRewards(int j) { int o = __p.__offset(18); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int GoldenLeagueRewardsLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string FireLeagueRewards(int j) { int o = __p.__offset(20); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int FireLeagueRewardsLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string QualificationReward { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetQualificationRewardBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetQualificationRewardBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetQualificationRewardArray() { return __p.__vector_as_array<byte>(22); }
  public string FailureReward { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFailureRewardBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetFailureRewardBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetFailureRewardArray() { return __p.__vector_as_array<byte>(24); }
  public string FailureRewards(int j) { int o = __p.__offset(26); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int FailureRewardsLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringLeagueRatio? LeagueRatiosFb(int j) { int o = __p.__offset(28); return o != 0 ? (FBConfig.DictStringLeagueRatio?)(new FBConfig.DictStringLeagueRatio()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int LeagueRatiosFbLength { get { int o = __p.__offset(28); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.BotClaimConfig? BotClaimConfig { get { int o = __p.__offset(30); return o != 0 ? (FBConfig.BotClaimConfig?)(new FBConfig.BotClaimConfig()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string AdditionalSubsetGroups(int j) { int o = __p.__offset(32); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int AdditionalSubsetGroupsLength { get { int o = __p.__offset(32); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string SpecialType { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSpecialTypeBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetSpecialTypeBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetSpecialTypeArray() { return __p.__vector_as_array<byte>(34); }

  public static Offset<FBConfig.CompetitionGameEventConfig> CreateCompetitionGameEventConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      Offset<FBConfig.CompetitionLeaderboardConfig> competition_leaderboard_configOffset = default(Offset<FBConfig.CompetitionLeaderboardConfig>),
      int qualification_target = 0,
      StringOffset start_narrative_idOffset = default(StringOffset),
      VectorOffset stone_league_rewardsOffset = default(VectorOffset),
      VectorOffset bronze_league_rewardsOffset = default(VectorOffset),
      VectorOffset silver_league_rewardsOffset = default(VectorOffset),
      VectorOffset golden_league_rewardsOffset = default(VectorOffset),
      VectorOffset fire_league_rewardsOffset = default(VectorOffset),
      StringOffset qualification_rewardOffset = default(StringOffset),
      StringOffset failure_rewardOffset = default(StringOffset),
      VectorOffset failure_rewardsOffset = default(VectorOffset),
      VectorOffset league_ratios_fbOffset = default(VectorOffset),
      Offset<FBConfig.BotClaimConfig> bot_claim_configOffset = default(Offset<FBConfig.BotClaimConfig>),
      VectorOffset additional_subset_groupsOffset = default(VectorOffset),
      StringOffset special_typeOffset = default(StringOffset)) {
    builder.StartTable(16);
    CompetitionGameEventConfig.AddSpecialType(builder, special_typeOffset);
    CompetitionGameEventConfig.AddAdditionalSubsetGroups(builder, additional_subset_groupsOffset);
    CompetitionGameEventConfig.AddBotClaimConfig(builder, bot_claim_configOffset);
    CompetitionGameEventConfig.AddLeagueRatiosFb(builder, league_ratios_fbOffset);
    CompetitionGameEventConfig.AddFailureRewards(builder, failure_rewardsOffset);
    CompetitionGameEventConfig.AddFailureReward(builder, failure_rewardOffset);
    CompetitionGameEventConfig.AddQualificationReward(builder, qualification_rewardOffset);
    CompetitionGameEventConfig.AddFireLeagueRewards(builder, fire_league_rewardsOffset);
    CompetitionGameEventConfig.AddGoldenLeagueRewards(builder, golden_league_rewardsOffset);
    CompetitionGameEventConfig.AddSilverLeagueRewards(builder, silver_league_rewardsOffset);
    CompetitionGameEventConfig.AddBronzeLeagueRewards(builder, bronze_league_rewardsOffset);
    CompetitionGameEventConfig.AddStoneLeagueRewards(builder, stone_league_rewardsOffset);
    CompetitionGameEventConfig.AddStartNarrativeId(builder, start_narrative_idOffset);
    CompetitionGameEventConfig.AddQualificationTarget(builder, qualification_target);
    CompetitionGameEventConfig.AddCompetitionLeaderboardConfig(builder, competition_leaderboard_configOffset);
    CompetitionGameEventConfig.AddUid(builder, uidOffset);
    return CompetitionGameEventConfig.EndCompetitionGameEventConfig(builder);
  }

  public static void StartCompetitionGameEventConfig(FlatBufferBuilder builder) { builder.StartTable(16); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCompetitionLeaderboardConfig(FlatBufferBuilder builder, Offset<FBConfig.CompetitionLeaderboardConfig> competitionLeaderboardConfigOffset) { builder.AddOffset(1, competitionLeaderboardConfigOffset.Value, 0); }
  public static void AddQualificationTarget(FlatBufferBuilder builder, int qualificationTarget) { builder.AddInt(2, qualificationTarget, 0); }
  public static void AddStartNarrativeId(FlatBufferBuilder builder, StringOffset startNarrativeIdOffset) { builder.AddOffset(3, startNarrativeIdOffset.Value, 0); }
  public static void AddStoneLeagueRewards(FlatBufferBuilder builder, VectorOffset stoneLeagueRewardsOffset) { builder.AddOffset(4, stoneLeagueRewardsOffset.Value, 0); }
  public static VectorOffset CreateStoneLeagueRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateStoneLeagueRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStoneLeagueRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStoneLeagueRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartStoneLeagueRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBronzeLeagueRewards(FlatBufferBuilder builder, VectorOffset bronzeLeagueRewardsOffset) { builder.AddOffset(5, bronzeLeagueRewardsOffset.Value, 0); }
  public static VectorOffset CreateBronzeLeagueRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBronzeLeagueRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBronzeLeagueRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBronzeLeagueRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBronzeLeagueRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSilverLeagueRewards(FlatBufferBuilder builder, VectorOffset silverLeagueRewardsOffset) { builder.AddOffset(6, silverLeagueRewardsOffset.Value, 0); }
  public static VectorOffset CreateSilverLeagueRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSilverLeagueRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSilverLeagueRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSilverLeagueRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSilverLeagueRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddGoldenLeagueRewards(FlatBufferBuilder builder, VectorOffset goldenLeagueRewardsOffset) { builder.AddOffset(7, goldenLeagueRewardsOffset.Value, 0); }
  public static VectorOffset CreateGoldenLeagueRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGoldenLeagueRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoldenLeagueRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoldenLeagueRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGoldenLeagueRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddFireLeagueRewards(FlatBufferBuilder builder, VectorOffset fireLeagueRewardsOffset) { builder.AddOffset(8, fireLeagueRewardsOffset.Value, 0); }
  public static VectorOffset CreateFireLeagueRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateFireLeagueRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFireLeagueRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFireLeagueRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartFireLeagueRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddQualificationReward(FlatBufferBuilder builder, StringOffset qualificationRewardOffset) { builder.AddOffset(9, qualificationRewardOffset.Value, 0); }
  public static void AddFailureReward(FlatBufferBuilder builder, StringOffset failureRewardOffset) { builder.AddOffset(10, failureRewardOffset.Value, 0); }
  public static void AddFailureRewards(FlatBufferBuilder builder, VectorOffset failureRewardsOffset) { builder.AddOffset(11, failureRewardsOffset.Value, 0); }
  public static VectorOffset CreateFailureRewardsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateFailureRewardsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFailureRewardsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateFailureRewardsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartFailureRewardsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLeagueRatiosFb(FlatBufferBuilder builder, VectorOffset leagueRatiosFbOffset) { builder.AddOffset(12, leagueRatiosFbOffset.Value, 0); }
  public static VectorOffset CreateLeagueRatiosFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringLeagueRatio>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLeagueRatiosFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringLeagueRatio>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLeagueRatiosFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringLeagueRatio>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLeagueRatiosFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringLeagueRatio>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLeagueRatiosFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBotClaimConfig(FlatBufferBuilder builder, Offset<FBConfig.BotClaimConfig> botClaimConfigOffset) { builder.AddOffset(13, botClaimConfigOffset.Value, 0); }
  public static void AddAdditionalSubsetGroups(FlatBufferBuilder builder, VectorOffset additionalSubsetGroupsOffset) { builder.AddOffset(14, additionalSubsetGroupsOffset.Value, 0); }
  public static VectorOffset CreateAdditionalSubsetGroupsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAdditionalSubsetGroupsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAdditionalSubsetGroupsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAdditionalSubsetGroupsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAdditionalSubsetGroupsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSpecialType(FlatBufferBuilder builder, StringOffset specialTypeOffset) { builder.AddOffset(15, specialTypeOffset.Value, 0); }
  public static Offset<FBConfig.CompetitionGameEventConfig> EndCompetitionGameEventConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.CompetitionGameEventConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfCompetitionGameEventConfig(FlatBufferBuilder builder, Offset<CompetitionGameEventConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<CompetitionGameEventConfig> o1, Offset<CompetitionGameEventConfig> o2) =>
        new CompetitionGameEventConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new CompetitionGameEventConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static CompetitionGameEventConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    CompetitionGameEventConfig obj_ = new CompetitionGameEventConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public CompetitionGameEventConfigT UnPack() {
    var _o = new CompetitionGameEventConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CompetitionGameEventConfigT _o) {
    _o.Uid = this.Uid;
    _o.CompetitionLeaderboardConfig = this.CompetitionLeaderboardConfig.HasValue ? this.CompetitionLeaderboardConfig.Value.UnPack() : null;
    _o.QualificationTarget = this.QualificationTarget;
    _o.StartNarrativeId = this.StartNarrativeId;
    _o.StoneLeagueRewards = new List<string>();
    for (var _j = 0; _j < this.StoneLeagueRewardsLength; ++_j) {_o.StoneLeagueRewards.Add(this.StoneLeagueRewards(_j));}
    _o.BronzeLeagueRewards = new List<string>();
    for (var _j = 0; _j < this.BronzeLeagueRewardsLength; ++_j) {_o.BronzeLeagueRewards.Add(this.BronzeLeagueRewards(_j));}
    _o.SilverLeagueRewards = new List<string>();
    for (var _j = 0; _j < this.SilverLeagueRewardsLength; ++_j) {_o.SilverLeagueRewards.Add(this.SilverLeagueRewards(_j));}
    _o.GoldenLeagueRewards = new List<string>();
    for (var _j = 0; _j < this.GoldenLeagueRewardsLength; ++_j) {_o.GoldenLeagueRewards.Add(this.GoldenLeagueRewards(_j));}
    _o.FireLeagueRewards = new List<string>();
    for (var _j = 0; _j < this.FireLeagueRewardsLength; ++_j) {_o.FireLeagueRewards.Add(this.FireLeagueRewards(_j));}
    _o.QualificationReward = this.QualificationReward;
    _o.FailureReward = this.FailureReward;
    _o.FailureRewards = new List<string>();
    for (var _j = 0; _j < this.FailureRewardsLength; ++_j) {_o.FailureRewards.Add(this.FailureRewards(_j));}
    _o.LeagueRatiosFb = new List<FBConfig.DictStringLeagueRatioT>();
    for (var _j = 0; _j < this.LeagueRatiosFbLength; ++_j) {_o.LeagueRatiosFb.Add(this.LeagueRatiosFb(_j).HasValue ? this.LeagueRatiosFb(_j).Value.UnPack() : null);}
    _o.BotClaimConfig = this.BotClaimConfig.HasValue ? this.BotClaimConfig.Value.UnPack() : null;
    _o.AdditionalSubsetGroups = new List<string>();
    for (var _j = 0; _j < this.AdditionalSubsetGroupsLength; ++_j) {_o.AdditionalSubsetGroups.Add(this.AdditionalSubsetGroups(_j));}
    _o.SpecialType = this.SpecialType;
  }
  public static Offset<FBConfig.CompetitionGameEventConfig> Pack(FlatBufferBuilder builder, CompetitionGameEventConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.CompetitionGameEventConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _competition_leaderboard_config = _o.CompetitionLeaderboardConfig == null ? default(Offset<FBConfig.CompetitionLeaderboardConfig>) : FBConfig.CompetitionLeaderboardConfig.Pack(builder, _o.CompetitionLeaderboardConfig);
    var _start_narrative_id = _o.StartNarrativeId == null ? default(StringOffset) : builder.CreateString(_o.StartNarrativeId);
    var _stone_league_rewards = default(VectorOffset);
    if (_o.StoneLeagueRewards != null) {
      var __stone_league_rewards = new StringOffset[_o.StoneLeagueRewards.Count];
      for (var _j = 0; _j < __stone_league_rewards.Length; ++_j) { __stone_league_rewards[_j] = builder.CreateString(_o.StoneLeagueRewards[_j]); }
      _stone_league_rewards = CreateStoneLeagueRewardsVector(builder, __stone_league_rewards);
    }
    var _bronze_league_rewards = default(VectorOffset);
    if (_o.BronzeLeagueRewards != null) {
      var __bronze_league_rewards = new StringOffset[_o.BronzeLeagueRewards.Count];
      for (var _j = 0; _j < __bronze_league_rewards.Length; ++_j) { __bronze_league_rewards[_j] = builder.CreateString(_o.BronzeLeagueRewards[_j]); }
      _bronze_league_rewards = CreateBronzeLeagueRewardsVector(builder, __bronze_league_rewards);
    }
    var _silver_league_rewards = default(VectorOffset);
    if (_o.SilverLeagueRewards != null) {
      var __silver_league_rewards = new StringOffset[_o.SilverLeagueRewards.Count];
      for (var _j = 0; _j < __silver_league_rewards.Length; ++_j) { __silver_league_rewards[_j] = builder.CreateString(_o.SilverLeagueRewards[_j]); }
      _silver_league_rewards = CreateSilverLeagueRewardsVector(builder, __silver_league_rewards);
    }
    var _golden_league_rewards = default(VectorOffset);
    if (_o.GoldenLeagueRewards != null) {
      var __golden_league_rewards = new StringOffset[_o.GoldenLeagueRewards.Count];
      for (var _j = 0; _j < __golden_league_rewards.Length; ++_j) { __golden_league_rewards[_j] = builder.CreateString(_o.GoldenLeagueRewards[_j]); }
      _golden_league_rewards = CreateGoldenLeagueRewardsVector(builder, __golden_league_rewards);
    }
    var _fire_league_rewards = default(VectorOffset);
    if (_o.FireLeagueRewards != null) {
      var __fire_league_rewards = new StringOffset[_o.FireLeagueRewards.Count];
      for (var _j = 0; _j < __fire_league_rewards.Length; ++_j) { __fire_league_rewards[_j] = builder.CreateString(_o.FireLeagueRewards[_j]); }
      _fire_league_rewards = CreateFireLeagueRewardsVector(builder, __fire_league_rewards);
    }
    var _qualification_reward = _o.QualificationReward == null ? default(StringOffset) : builder.CreateString(_o.QualificationReward);
    var _failure_reward = _o.FailureReward == null ? default(StringOffset) : builder.CreateString(_o.FailureReward);
    var _failure_rewards = default(VectorOffset);
    if (_o.FailureRewards != null) {
      var __failure_rewards = new StringOffset[_o.FailureRewards.Count];
      for (var _j = 0; _j < __failure_rewards.Length; ++_j) { __failure_rewards[_j] = builder.CreateString(_o.FailureRewards[_j]); }
      _failure_rewards = CreateFailureRewardsVector(builder, __failure_rewards);
    }
    var _league_ratios_fb = default(VectorOffset);
    if (_o.LeagueRatiosFb != null) {
      var __league_ratios_fb = new Offset<FBConfig.DictStringLeagueRatio>[_o.LeagueRatiosFb.Count];
      for (var _j = 0; _j < __league_ratios_fb.Length; ++_j) { __league_ratios_fb[_j] = FBConfig.DictStringLeagueRatio.Pack(builder, _o.LeagueRatiosFb[_j]); }
      _league_ratios_fb = CreateLeagueRatiosFbVector(builder, __league_ratios_fb);
    }
    var _bot_claim_config = _o.BotClaimConfig == null ? default(Offset<FBConfig.BotClaimConfig>) : FBConfig.BotClaimConfig.Pack(builder, _o.BotClaimConfig);
    var _additional_subset_groups = default(VectorOffset);
    if (_o.AdditionalSubsetGroups != null) {
      var __additional_subset_groups = new StringOffset[_o.AdditionalSubsetGroups.Count];
      for (var _j = 0; _j < __additional_subset_groups.Length; ++_j) { __additional_subset_groups[_j] = builder.CreateString(_o.AdditionalSubsetGroups[_j]); }
      _additional_subset_groups = CreateAdditionalSubsetGroupsVector(builder, __additional_subset_groups);
    }
    var _special_type = _o.SpecialType == null ? default(StringOffset) : builder.CreateString(_o.SpecialType);
    return CreateCompetitionGameEventConfig(
      builder,
      _uid,
      _competition_leaderboard_config,
      _o.QualificationTarget,
      _start_narrative_id,
      _stone_league_rewards,
      _bronze_league_rewards,
      _silver_league_rewards,
      _golden_league_rewards,
      _fire_league_rewards,
      _qualification_reward,
      _failure_reward,
      _failure_rewards,
      _league_ratios_fb,
      _bot_claim_config,
      _additional_subset_groups,
      _special_type);
  }
}

public class CompetitionGameEventConfigT
{
  public string Uid { get; set; }
  public FBConfig.CompetitionLeaderboardConfigT CompetitionLeaderboardConfig { get; set; }
  public int QualificationTarget { get; set; }
  public string StartNarrativeId { get; set; }
  public List<string> StoneLeagueRewards { get; set; }
  public List<string> BronzeLeagueRewards { get; set; }
  public List<string> SilverLeagueRewards { get; set; }
  public List<string> GoldenLeagueRewards { get; set; }
  public List<string> FireLeagueRewards { get; set; }
  public string QualificationReward { get; set; }
  public string FailureReward { get; set; }
  public List<string> FailureRewards { get; set; }
  public List<FBConfig.DictStringLeagueRatioT> LeagueRatiosFb { get; set; }
  public FBConfig.BotClaimConfigT BotClaimConfig { get; set; }
  public List<string> AdditionalSubsetGroups { get; set; }
  public string SpecialType { get; set; }

  public CompetitionGameEventConfigT() {
    this.Uid = null;
    this.CompetitionLeaderboardConfig = null;
    this.QualificationTarget = 0;
    this.StartNarrativeId = null;
    this.StoneLeagueRewards = null;
    this.BronzeLeagueRewards = null;
    this.SilverLeagueRewards = null;
    this.GoldenLeagueRewards = null;
    this.FireLeagueRewards = null;
    this.QualificationReward = null;
    this.FailureReward = null;
    this.FailureRewards = null;
    this.LeagueRatiosFb = null;
    this.BotClaimConfig = null;
    this.AdditionalSubsetGroups = null;
    this.SpecialType = null;
  }
}

public struct CompetitionGameEventConfigDict : IFlatbufferConfigDict<CompetitionGameEventConfig, CompetitionGameEventConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CompetitionGameEventConfigDict GetRootAsCompetitionGameEventConfigDict(ByteBuffer _bb) { return GetRootAsCompetitionGameEventConfigDict(_bb, new CompetitionGameEventConfigDict()); }
  public static CompetitionGameEventConfigDict GetRootAsCompetitionGameEventConfigDict(ByteBuffer _bb, CompetitionGameEventConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CompetitionGameEventConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.CompetitionGameEventConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.CompetitionGameEventConfig?)(new FBConfig.CompetitionGameEventConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.CompetitionGameEventConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.CompetitionGameEventConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.CompetitionGameEventConfigDict> CreateCompetitionGameEventConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    CompetitionGameEventConfigDict.AddValues(builder, valuesOffset);
    return CompetitionGameEventConfigDict.EndCompetitionGameEventConfigDict(builder);
  }

  public static void StartCompetitionGameEventConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.CompetitionGameEventConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.CompetitionGameEventConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.CompetitionGameEventConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.CompetitionGameEventConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.CompetitionGameEventConfigDict> EndCompetitionGameEventConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CompetitionGameEventConfigDict>(o);
  }
  public static void FinishCompetitionGameEventConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CompetitionGameEventConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedCompetitionGameEventConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.CompetitionGameEventConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public CompetitionGameEventConfigDictT UnPack() {
    var _o = new CompetitionGameEventConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CompetitionGameEventConfigDictT _o) {
    _o.Values = new List<FBConfig.CompetitionGameEventConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.CompetitionGameEventConfigDict> Pack(FlatBufferBuilder builder, CompetitionGameEventConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.CompetitionGameEventConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.CompetitionGameEventConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.CompetitionGameEventConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateCompetitionGameEventConfigDict(
      builder,
      _values);
  }
}

public class CompetitionGameEventConfigDictT
{
  public List<FBConfig.CompetitionGameEventConfigT> Values { get; set; }

  public CompetitionGameEventConfigDictT() {
    this.Values = null;
  }
  public static CompetitionGameEventConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return CompetitionGameEventConfigDict.GetRootAsCompetitionGameEventConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    CompetitionGameEventConfigDict.FinishCompetitionGameEventConfigDictBuffer(fbb, CompetitionGameEventConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}

public struct DictStringLeagueRatio : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DictStringLeagueRatio GetRootAsDictStringLeagueRatio(ByteBuffer _bb) { return GetRootAsDictStringLeagueRatio(_bb, new DictStringLeagueRatio()); }
  public static DictStringLeagueRatio GetRootAsDictStringLeagueRatio(ByteBuffer _bb, DictStringLeagueRatio obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DictStringLeagueRatio __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Key { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetKeyBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetKeyBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetKeyArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.LeagueRatio? Value { get { int o = __p.__offset(6); return o != 0 ? (FBConfig.LeagueRatio?)(new FBConfig.LeagueRatio()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }

  public static Offset<FBConfig.DictStringLeagueRatio> CreateDictStringLeagueRatio(FlatBufferBuilder builder,
      StringOffset keyOffset = default(StringOffset),
      Offset<FBConfig.LeagueRatio> valueOffset = default(Offset<FBConfig.LeagueRatio>)) {
    builder.StartTable(2);
    DictStringLeagueRatio.AddValue(builder, valueOffset);
    DictStringLeagueRatio.AddKey(builder, keyOffset);
    return DictStringLeagueRatio.EndDictStringLeagueRatio(builder);
  }

  public static void StartDictStringLeagueRatio(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddKey(FlatBufferBuilder builder, StringOffset keyOffset) { builder.AddOffset(0, keyOffset.Value, 0); }
  public static void AddValue(FlatBufferBuilder builder, Offset<FBConfig.LeagueRatio> valueOffset) { builder.AddOffset(1, valueOffset.Value, 0); }
  public static Offset<FBConfig.DictStringLeagueRatio> EndDictStringLeagueRatio(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DictStringLeagueRatio>(o);
  }
  public DictStringLeagueRatioT UnPack() {
    var _o = new DictStringLeagueRatioT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DictStringLeagueRatioT _o) {
    _o.Key = this.Key;
    _o.Value = this.Value.HasValue ? this.Value.Value.UnPack() : null;
  }
  public static Offset<FBConfig.DictStringLeagueRatio> Pack(FlatBufferBuilder builder, DictStringLeagueRatioT _o) {
    if (_o == null) return default(Offset<FBConfig.DictStringLeagueRatio>);
    var _key = _o.Key == null ? default(StringOffset) : builder.CreateString(_o.Key);
    var _value = _o.Value == null ? default(Offset<FBConfig.LeagueRatio>) : FBConfig.LeagueRatio.Pack(builder, _o.Value);
    return CreateDictStringLeagueRatio(
      builder,
      _key,
      _value);
  }
}

public class DictStringLeagueRatioT
{
  public string Key { get; set; }
  public FBConfig.LeagueRatioT Value { get; set; }

  public DictStringLeagueRatioT() {
    this.Key = null;
    this.Value = null;
  }
}

public struct LeagueRatio : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LeagueRatio GetRootAsLeagueRatio(ByteBuffer _bb) { return GetRootAsLeagueRatio(_bb, new LeagueRatio()); }
  public static LeagueRatio GetRootAsLeagueRatio(ByteBuffer _bb, LeagueRatio obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LeagueRatio __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int DemotionRatio { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDemotionRatio(int demotion_ratio) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, demotion_ratio); return true; } else { return false; } }
  public int PromotionRatio { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePromotionRatio(int promotion_ratio) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, promotion_ratio); return true; } else { return false; } }

  public static Offset<FBConfig.LeagueRatio> CreateLeagueRatio(FlatBufferBuilder builder,
      int demotion_ratio = 0,
      int promotion_ratio = 0) {
    builder.StartTable(2);
    LeagueRatio.AddPromotionRatio(builder, promotion_ratio);
    LeagueRatio.AddDemotionRatio(builder, demotion_ratio);
    return LeagueRatio.EndLeagueRatio(builder);
  }

  public static void StartLeagueRatio(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddDemotionRatio(FlatBufferBuilder builder, int demotionRatio) { builder.AddInt(0, demotionRatio, 0); }
  public static void AddPromotionRatio(FlatBufferBuilder builder, int promotionRatio) { builder.AddInt(1, promotionRatio, 0); }
  public static Offset<FBConfig.LeagueRatio> EndLeagueRatio(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LeagueRatio>(o);
  }
  public LeagueRatioT UnPack() {
    var _o = new LeagueRatioT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LeagueRatioT _o) {
    _o.DemotionRatio = this.DemotionRatio;
    _o.PromotionRatio = this.PromotionRatio;
  }
  public static Offset<FBConfig.LeagueRatio> Pack(FlatBufferBuilder builder, LeagueRatioT _o) {
    if (_o == null) return default(Offset<FBConfig.LeagueRatio>);
    return CreateLeagueRatio(
      builder,
      _o.DemotionRatio,
      _o.PromotionRatio);
  }
}

public class LeagueRatioT
{
  public int DemotionRatio { get; set; }
  public int PromotionRatio { get; set; }

  public LeagueRatioT() {
    this.DemotionRatio = 0;
    this.PromotionRatio = 0;
  }
}


}
