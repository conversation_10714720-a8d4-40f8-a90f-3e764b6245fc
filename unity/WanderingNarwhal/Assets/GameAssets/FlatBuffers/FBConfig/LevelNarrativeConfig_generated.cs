// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct LevelNarrativeConfig : IFlatbufferConfig<LevelNarrativeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelNarrativeConfig GetRootAsLevelNarrativeConfig(ByteBuffer _bb) { return GetRootAsLevelNarrativeConfig(_bb, new LevelNarrativeConfig()); }
  public static LevelNarrativeConfig GetRootAsLevelNarrativeConfig(ByteBuffer _bb, LevelNarrativeConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelNarrativeConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string LevelUid { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLevelUidBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetLevelUidBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetLevelUidArray() { return __p.__vector_as_array<byte>(6); }
  public string Case { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCaseBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetCaseBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetCaseArray() { return __p.__vector_as_array<byte>(8); }
  public string Dialog { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetDialogBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetDialogBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetDialogArray() { return __p.__vector_as_array<byte>(10); }
  public string TakeMeUid { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTakeMeUidBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetTakeMeUidBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetTakeMeUidArray() { return __p.__vector_as_array<byte>(12); }
  public FBConfig.DictStringString? TakeMeParamsFb(int j) { int o = __p.__offset(14); return o != 0 ? (FBConfig.DictStringString?)(new FBConfig.DictStringString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int TakeMeParamsFbLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.LevelNarrativeConfig> CreateLevelNarrativeConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset level_uidOffset = default(StringOffset),
      StringOffset @caseOffset = default(StringOffset),
      StringOffset dialogOffset = default(StringOffset),
      StringOffset take_me_uidOffset = default(StringOffset),
      VectorOffset take_me_params_fbOffset = default(VectorOffset)) {
    builder.StartTable(6);
    LevelNarrativeConfig.AddTakeMeParamsFb(builder, take_me_params_fbOffset);
    LevelNarrativeConfig.AddTakeMeUid(builder, take_me_uidOffset);
    LevelNarrativeConfig.AddDialog(builder, dialogOffset);
    LevelNarrativeConfig.AddCase(builder, @caseOffset);
    LevelNarrativeConfig.AddLevelUid(builder, level_uidOffset);
    LevelNarrativeConfig.AddUid(builder, uidOffset);
    return LevelNarrativeConfig.EndLevelNarrativeConfig(builder);
  }

  public static void StartLevelNarrativeConfig(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddLevelUid(FlatBufferBuilder builder, StringOffset levelUidOffset) { builder.AddOffset(1, levelUidOffset.Value, 0); }
  public static void AddCase(FlatBufferBuilder builder, StringOffset caseOffset) { builder.AddOffset(2, caseOffset.Value, 0); }
  public static void AddDialog(FlatBufferBuilder builder, StringOffset dialogOffset) { builder.AddOffset(3, dialogOffset.Value, 0); }
  public static void AddTakeMeUid(FlatBufferBuilder builder, StringOffset takeMeUidOffset) { builder.AddOffset(4, takeMeUidOffset.Value, 0); }
  public static void AddTakeMeParamsFb(FlatBufferBuilder builder, VectorOffset takeMeParamsFbOffset) { builder.AddOffset(5, takeMeParamsFbOffset.Value, 0); }
  public static VectorOffset CreateTakeMeParamsFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTakeMeParamsFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTakeMeParamsFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelNarrativeConfig> EndLevelNarrativeConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.LevelNarrativeConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfLevelNarrativeConfig(FlatBufferBuilder builder, Offset<LevelNarrativeConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<LevelNarrativeConfig> o1, Offset<LevelNarrativeConfig> o2) =>
        new LevelNarrativeConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new LevelNarrativeConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static LevelNarrativeConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    LevelNarrativeConfig obj_ = new LevelNarrativeConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public LevelNarrativeConfigT UnPack() {
    var _o = new LevelNarrativeConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelNarrativeConfigT _o) {
    _o.Uid = this.Uid;
    _o.LevelUid = this.LevelUid;
    _o.Case = this.Case;
    _o.Dialog = this.Dialog;
    _o.TakeMeUid = this.TakeMeUid;
    _o.TakeMeParamsFb = new List<FBConfig.DictStringStringT>();
    for (var _j = 0; _j < this.TakeMeParamsFbLength; ++_j) {_o.TakeMeParamsFb.Add(this.TakeMeParamsFb(_j).HasValue ? this.TakeMeParamsFb(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LevelNarrativeConfig> Pack(FlatBufferBuilder builder, LevelNarrativeConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelNarrativeConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _level_uid = _o.LevelUid == null ? default(StringOffset) : builder.CreateString(_o.LevelUid);
    var _case = _o.Case == null ? default(StringOffset) : builder.CreateString(_o.Case);
    var _dialog = _o.Dialog == null ? default(StringOffset) : builder.CreateString(_o.Dialog);
    var _take_me_uid = _o.TakeMeUid == null ? default(StringOffset) : builder.CreateString(_o.TakeMeUid);
    var _take_me_params_fb = default(VectorOffset);
    if (_o.TakeMeParamsFb != null) {
      var __take_me_params_fb = new Offset<FBConfig.DictStringString>[_o.TakeMeParamsFb.Count];
      for (var _j = 0; _j < __take_me_params_fb.Length; ++_j) { __take_me_params_fb[_j] = FBConfig.DictStringString.Pack(builder, _o.TakeMeParamsFb[_j]); }
      _take_me_params_fb = CreateTakeMeParamsFbVector(builder, __take_me_params_fb);
    }
    return CreateLevelNarrativeConfig(
      builder,
      _uid,
      _level_uid,
      _case,
      _dialog,
      _take_me_uid,
      _take_me_params_fb);
  }
}

public class LevelNarrativeConfigT
{
  public string Uid { get; set; }
  public string LevelUid { get; set; }
  public string Case { get; set; }
  public string Dialog { get; set; }
  public string TakeMeUid { get; set; }
  public List<FBConfig.DictStringStringT> TakeMeParamsFb { get; set; }

  public LevelNarrativeConfigT() {
    this.Uid = null;
    this.LevelUid = null;
    this.Case = null;
    this.Dialog = null;
    this.TakeMeUid = null;
    this.TakeMeParamsFb = null;
  }
}

public struct LevelNarrativeConfigDict : IFlatbufferConfigDict<LevelNarrativeConfig, LevelNarrativeConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static LevelNarrativeConfigDict GetRootAsLevelNarrativeConfigDict(ByteBuffer _bb) { return GetRootAsLevelNarrativeConfigDict(_bb, new LevelNarrativeConfigDict()); }
  public static LevelNarrativeConfigDict GetRootAsLevelNarrativeConfigDict(ByteBuffer _bb, LevelNarrativeConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public LevelNarrativeConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.LevelNarrativeConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.LevelNarrativeConfig?)(new FBConfig.LevelNarrativeConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.LevelNarrativeConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.LevelNarrativeConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.LevelNarrativeConfigDict> CreateLevelNarrativeConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    LevelNarrativeConfigDict.AddValues(builder, valuesOffset);
    return LevelNarrativeConfigDict.EndLevelNarrativeConfigDict(builder);
  }

  public static void StartLevelNarrativeConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.LevelNarrativeConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.LevelNarrativeConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.LevelNarrativeConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.LevelNarrativeConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.LevelNarrativeConfigDict> EndLevelNarrativeConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.LevelNarrativeConfigDict>(o);
  }
  public static void FinishLevelNarrativeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelNarrativeConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedLevelNarrativeConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.LevelNarrativeConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public LevelNarrativeConfigDictT UnPack() {
    var _o = new LevelNarrativeConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(LevelNarrativeConfigDictT _o) {
    _o.Values = new List<FBConfig.LevelNarrativeConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.LevelNarrativeConfigDict> Pack(FlatBufferBuilder builder, LevelNarrativeConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.LevelNarrativeConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.LevelNarrativeConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.LevelNarrativeConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateLevelNarrativeConfigDict(
      builder,
      _values);
  }
}

public class LevelNarrativeConfigDictT
{
  public List<FBConfig.LevelNarrativeConfigT> Values { get; set; }

  public LevelNarrativeConfigDictT() {
    this.Values = null;
  }
  public static LevelNarrativeConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return LevelNarrativeConfigDict.GetRootAsLevelNarrativeConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    LevelNarrativeConfigDict.FinishLevelNarrativeConfigDictBuffer(fbb, LevelNarrativeConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
