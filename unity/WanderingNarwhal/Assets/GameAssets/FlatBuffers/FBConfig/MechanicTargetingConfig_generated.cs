// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct MechanicTargetingConfig : IFlatbufferConfig<MechanicTargetingConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static MechanicTargetingConfig GetRootAsMechanicTargetingConfig(ByteBuffer _bb) { return GetRootAsMechanicTargetingConfig(_bb, new MechanicTargetingConfig()); }
  public static MechanicTargetingConfig GetRootAsMechanicTargetingConfig(ByteBuffer _bb, MechanicTargetingConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public MechanicTargetingConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.DictStringFloat? WeightTable(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.DictStringFloat?)(new FBConfig.DictStringFloat()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int WeightTableLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DictStringFloat? GoalWeightTable(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.DictStringFloat?)(new FBConfig.DictStringFloat()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int GoalWeightTableLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public float WeightUnderDropItem { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateWeightUnderDropItem(float weight_under_drop_item) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, weight_under_drop_item); return true; } else { return false; } }
  public float DropItemWeightDiminishStep { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateDropItemWeightDiminishStep(float drop_item_weight_diminish_step) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, drop_item_weight_diminish_step); return true; } else { return false; } }
  public float TntGoalWeight { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateTntGoalWeight(float tnt_goal_weight) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, tnt_goal_weight); return true; } else { return false; } }
  public float TukTukGoalWeight { get { int o = __p.__offset(16); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateTukTukGoalWeight(float tuk_tuk_goal_weight) { int o = __p.__offset(16); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, tuk_tuk_goal_weight); return true; } else { return false; } }

  public static Offset<FBConfig.MechanicTargetingConfig> CreateMechanicTargetingConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset weight_tableOffset = default(VectorOffset),
      VectorOffset goal_weight_tableOffset = default(VectorOffset),
      float weight_under_drop_item = 0.0f,
      float drop_item_weight_diminish_step = 0.0f,
      float tnt_goal_weight = 0.0f,
      float tuk_tuk_goal_weight = 0.0f) {
    builder.StartTable(7);
    MechanicTargetingConfig.AddTukTukGoalWeight(builder, tuk_tuk_goal_weight);
    MechanicTargetingConfig.AddTntGoalWeight(builder, tnt_goal_weight);
    MechanicTargetingConfig.AddDropItemWeightDiminishStep(builder, drop_item_weight_diminish_step);
    MechanicTargetingConfig.AddWeightUnderDropItem(builder, weight_under_drop_item);
    MechanicTargetingConfig.AddGoalWeightTable(builder, goal_weight_tableOffset);
    MechanicTargetingConfig.AddWeightTable(builder, weight_tableOffset);
    MechanicTargetingConfig.AddUid(builder, uidOffset);
    return MechanicTargetingConfig.EndMechanicTargetingConfig(builder);
  }

  public static void StartMechanicTargetingConfig(FlatBufferBuilder builder) { builder.StartTable(7); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddWeightTable(FlatBufferBuilder builder, VectorOffset weightTableOffset) { builder.AddOffset(1, weightTableOffset.Value, 0); }
  public static VectorOffset CreateWeightTableVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringFloat>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateWeightTableVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringFloat>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWeightTableVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringFloat>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateWeightTableVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringFloat>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartWeightTableVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddGoalWeightTable(FlatBufferBuilder builder, VectorOffset goalWeightTableOffset) { builder.AddOffset(2, goalWeightTableOffset.Value, 0); }
  public static VectorOffset CreateGoalWeightTableVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringFloat>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateGoalWeightTableVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringFloat>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoalWeightTableVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringFloat>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateGoalWeightTableVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringFloat>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartGoalWeightTableVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddWeightUnderDropItem(FlatBufferBuilder builder, float weightUnderDropItem) { builder.AddFloat(3, weightUnderDropItem, 0.0f); }
  public static void AddDropItemWeightDiminishStep(FlatBufferBuilder builder, float dropItemWeightDiminishStep) { builder.AddFloat(4, dropItemWeightDiminishStep, 0.0f); }
  public static void AddTntGoalWeight(FlatBufferBuilder builder, float tntGoalWeight) { builder.AddFloat(5, tntGoalWeight, 0.0f); }
  public static void AddTukTukGoalWeight(FlatBufferBuilder builder, float tukTukGoalWeight) { builder.AddFloat(6, tukTukGoalWeight, 0.0f); }
  public static Offset<FBConfig.MechanicTargetingConfig> EndMechanicTargetingConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.MechanicTargetingConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfMechanicTargetingConfig(FlatBufferBuilder builder, Offset<MechanicTargetingConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<MechanicTargetingConfig> o1, Offset<MechanicTargetingConfig> o2) =>
        new MechanicTargetingConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new MechanicTargetingConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static MechanicTargetingConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    MechanicTargetingConfig obj_ = new MechanicTargetingConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public MechanicTargetingConfigT UnPack() {
    var _o = new MechanicTargetingConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(MechanicTargetingConfigT _o) {
    _o.Uid = this.Uid;
    _o.WeightTable = new List<FBConfig.DictStringFloatT>();
    for (var _j = 0; _j < this.WeightTableLength; ++_j) {_o.WeightTable.Add(this.WeightTable(_j).HasValue ? this.WeightTable(_j).Value.UnPack() : null);}
    _o.GoalWeightTable = new List<FBConfig.DictStringFloatT>();
    for (var _j = 0; _j < this.GoalWeightTableLength; ++_j) {_o.GoalWeightTable.Add(this.GoalWeightTable(_j).HasValue ? this.GoalWeightTable(_j).Value.UnPack() : null);}
    _o.WeightUnderDropItem = this.WeightUnderDropItem;
    _o.DropItemWeightDiminishStep = this.DropItemWeightDiminishStep;
    _o.TntGoalWeight = this.TntGoalWeight;
    _o.TukTukGoalWeight = this.TukTukGoalWeight;
  }
  public static Offset<FBConfig.MechanicTargetingConfig> Pack(FlatBufferBuilder builder, MechanicTargetingConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.MechanicTargetingConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _weight_table = default(VectorOffset);
    if (_o.WeightTable != null) {
      var __weight_table = new Offset<FBConfig.DictStringFloat>[_o.WeightTable.Count];
      for (var _j = 0; _j < __weight_table.Length; ++_j) { __weight_table[_j] = FBConfig.DictStringFloat.Pack(builder, _o.WeightTable[_j]); }
      _weight_table = CreateWeightTableVector(builder, __weight_table);
    }
    var _goal_weight_table = default(VectorOffset);
    if (_o.GoalWeightTable != null) {
      var __goal_weight_table = new Offset<FBConfig.DictStringFloat>[_o.GoalWeightTable.Count];
      for (var _j = 0; _j < __goal_weight_table.Length; ++_j) { __goal_weight_table[_j] = FBConfig.DictStringFloat.Pack(builder, _o.GoalWeightTable[_j]); }
      _goal_weight_table = CreateGoalWeightTableVector(builder, __goal_weight_table);
    }
    return CreateMechanicTargetingConfig(
      builder,
      _uid,
      _weight_table,
      _goal_weight_table,
      _o.WeightUnderDropItem,
      _o.DropItemWeightDiminishStep,
      _o.TntGoalWeight,
      _o.TukTukGoalWeight);
  }
}

public class MechanicTargetingConfigT
{
  public string Uid { get; set; }
  public List<FBConfig.DictStringFloatT> WeightTable { get; set; }
  public List<FBConfig.DictStringFloatT> GoalWeightTable { get; set; }
  public float WeightUnderDropItem { get; set; }
  public float DropItemWeightDiminishStep { get; set; }
  public float TntGoalWeight { get; set; }
  public float TukTukGoalWeight { get; set; }

  public MechanicTargetingConfigT() {
    this.Uid = null;
    this.WeightTable = null;
    this.GoalWeightTable = null;
    this.WeightUnderDropItem = 0.0f;
    this.DropItemWeightDiminishStep = 0.0f;
    this.TntGoalWeight = 0.0f;
    this.TukTukGoalWeight = 0.0f;
  }
}

public struct MechanicTargetingConfigDict : IFlatbufferConfigDict<MechanicTargetingConfig, MechanicTargetingConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static MechanicTargetingConfigDict GetRootAsMechanicTargetingConfigDict(ByteBuffer _bb) { return GetRootAsMechanicTargetingConfigDict(_bb, new MechanicTargetingConfigDict()); }
  public static MechanicTargetingConfigDict GetRootAsMechanicTargetingConfigDict(ByteBuffer _bb, MechanicTargetingConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public MechanicTargetingConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.MechanicTargetingConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.MechanicTargetingConfig?)(new FBConfig.MechanicTargetingConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.MechanicTargetingConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.MechanicTargetingConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.MechanicTargetingConfigDict> CreateMechanicTargetingConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    MechanicTargetingConfigDict.AddValues(builder, valuesOffset);
    return MechanicTargetingConfigDict.EndMechanicTargetingConfigDict(builder);
  }

  public static void StartMechanicTargetingConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.MechanicTargetingConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.MechanicTargetingConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.MechanicTargetingConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.MechanicTargetingConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.MechanicTargetingConfigDict> EndMechanicTargetingConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.MechanicTargetingConfigDict>(o);
  }
  public static void FinishMechanicTargetingConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.MechanicTargetingConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedMechanicTargetingConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.MechanicTargetingConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public MechanicTargetingConfigDictT UnPack() {
    var _o = new MechanicTargetingConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(MechanicTargetingConfigDictT _o) {
    _o.Values = new List<FBConfig.MechanicTargetingConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.MechanicTargetingConfigDict> Pack(FlatBufferBuilder builder, MechanicTargetingConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.MechanicTargetingConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.MechanicTargetingConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.MechanicTargetingConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateMechanicTargetingConfigDict(
      builder,
      _values);
  }
}

public class MechanicTargetingConfigDictT
{
  public List<FBConfig.MechanicTargetingConfigT> Values { get; set; }

  public MechanicTargetingConfigDictT() {
    this.Values = null;
  }
  public static MechanicTargetingConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return MechanicTargetingConfigDict.GetRootAsMechanicTargetingConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    MechanicTargetingConfigDict.FinishMechanicTargetingConfigDictBuffer(fbb, MechanicTargetingConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
