// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ArtSelfieSubjectConfig : IFlatbufferConfig<ArtSelfieSubjectConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ArtSelfieSubjectConfig GetRootAsArtSelfieSubjectConfig(ByteBuffer _bb) { return GetRootAsArtSelfieSubjectConfig(_bb, new ArtSelfieSubjectConfig()); }
  public static ArtSelfieSubjectConfig GetRootAsArtSelfieSubjectConfig(ByteBuffer _bb, ArtSelfieSubjectConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ArtSelfieSubjectConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string ImageUrl { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetImageUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetImageUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetImageUrlArray() { return __p.__vector_as_array<byte>(8); }
  public string Birthday { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBirthdayBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetBirthdayBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetBirthdayArray() { return __p.__vector_as_array<byte>(10); }
  public string Nationality { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNationalityBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetNationalityBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetNationalityArray() { return __p.__vector_as_array<byte>(12); }
  public string Profession { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetProfessionBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetProfessionBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetProfessionArray() { return __p.__vector_as_array<byte>(14); }
  public string ArticleUrl { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetArticleUrlBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetArticleUrlBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetArticleUrlArray() { return __p.__vector_as_array<byte>(16); }
  public string Alt { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAltBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetAltBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetAltArray() { return __p.__vector_as_array<byte>(18); }

  public static Offset<FBConfig.ArtSelfieSubjectConfig> CreateArtSelfieSubjectConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset image_urlOffset = default(StringOffset),
      StringOffset birthdayOffset = default(StringOffset),
      StringOffset nationalityOffset = default(StringOffset),
      StringOffset professionOffset = default(StringOffset),
      StringOffset article_urlOffset = default(StringOffset),
      StringOffset altOffset = default(StringOffset)) {
    builder.StartTable(8);
    ArtSelfieSubjectConfig.AddAlt(builder, altOffset);
    ArtSelfieSubjectConfig.AddArticleUrl(builder, article_urlOffset);
    ArtSelfieSubjectConfig.AddProfession(builder, professionOffset);
    ArtSelfieSubjectConfig.AddNationality(builder, nationalityOffset);
    ArtSelfieSubjectConfig.AddBirthday(builder, birthdayOffset);
    ArtSelfieSubjectConfig.AddImageUrl(builder, image_urlOffset);
    ArtSelfieSubjectConfig.AddName(builder, nameOffset);
    ArtSelfieSubjectConfig.AddUid(builder, uidOffset);
    return ArtSelfieSubjectConfig.EndArtSelfieSubjectConfig(builder);
  }

  public static void StartArtSelfieSubjectConfig(FlatBufferBuilder builder) { builder.StartTable(8); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddImageUrl(FlatBufferBuilder builder, StringOffset imageUrlOffset) { builder.AddOffset(2, imageUrlOffset.Value, 0); }
  public static void AddBirthday(FlatBufferBuilder builder, StringOffset birthdayOffset) { builder.AddOffset(3, birthdayOffset.Value, 0); }
  public static void AddNationality(FlatBufferBuilder builder, StringOffset nationalityOffset) { builder.AddOffset(4, nationalityOffset.Value, 0); }
  public static void AddProfession(FlatBufferBuilder builder, StringOffset professionOffset) { builder.AddOffset(5, professionOffset.Value, 0); }
  public static void AddArticleUrl(FlatBufferBuilder builder, StringOffset articleUrlOffset) { builder.AddOffset(6, articleUrlOffset.Value, 0); }
  public static void AddAlt(FlatBufferBuilder builder, StringOffset altOffset) { builder.AddOffset(7, altOffset.Value, 0); }
  public static Offset<FBConfig.ArtSelfieSubjectConfig> EndArtSelfieSubjectConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ArtSelfieSubjectConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfArtSelfieSubjectConfig(FlatBufferBuilder builder, Offset<ArtSelfieSubjectConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ArtSelfieSubjectConfig> o1, Offset<ArtSelfieSubjectConfig> o2) =>
        new ArtSelfieSubjectConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ArtSelfieSubjectConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ArtSelfieSubjectConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ArtSelfieSubjectConfig obj_ = new ArtSelfieSubjectConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ArtSelfieSubjectConfigT UnPack() {
    var _o = new ArtSelfieSubjectConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ArtSelfieSubjectConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.ImageUrl = this.ImageUrl;
    _o.Birthday = this.Birthday;
    _o.Nationality = this.Nationality;
    _o.Profession = this.Profession;
    _o.ArticleUrl = this.ArticleUrl;
    _o.Alt = this.Alt;
  }
  public static Offset<FBConfig.ArtSelfieSubjectConfig> Pack(FlatBufferBuilder builder, ArtSelfieSubjectConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ArtSelfieSubjectConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _image_url = _o.ImageUrl == null ? default(StringOffset) : builder.CreateString(_o.ImageUrl);
    var _birthday = _o.Birthday == null ? default(StringOffset) : builder.CreateString(_o.Birthday);
    var _nationality = _o.Nationality == null ? default(StringOffset) : builder.CreateString(_o.Nationality);
    var _profession = _o.Profession == null ? default(StringOffset) : builder.CreateString(_o.Profession);
    var _article_url = _o.ArticleUrl == null ? default(StringOffset) : builder.CreateString(_o.ArticleUrl);
    var _alt = _o.Alt == null ? default(StringOffset) : builder.CreateString(_o.Alt);
    return CreateArtSelfieSubjectConfig(
      builder,
      _uid,
      _name,
      _image_url,
      _birthday,
      _nationality,
      _profession,
      _article_url,
      _alt);
  }
}

public class ArtSelfieSubjectConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string ImageUrl { get; set; }
  public string Birthday { get; set; }
  public string Nationality { get; set; }
  public string Profession { get; set; }
  public string ArticleUrl { get; set; }
  public string Alt { get; set; }

  public ArtSelfieSubjectConfigT() {
    this.Uid = null;
    this.Name = null;
    this.ImageUrl = null;
    this.Birthday = null;
    this.Nationality = null;
    this.Profession = null;
    this.ArticleUrl = null;
    this.Alt = null;
  }
}

public struct ArtSelfieSubjectConfigDict : IFlatbufferConfigDict<ArtSelfieSubjectConfig, ArtSelfieSubjectConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ArtSelfieSubjectConfigDict GetRootAsArtSelfieSubjectConfigDict(ByteBuffer _bb) { return GetRootAsArtSelfieSubjectConfigDict(_bb, new ArtSelfieSubjectConfigDict()); }
  public static ArtSelfieSubjectConfigDict GetRootAsArtSelfieSubjectConfigDict(ByteBuffer _bb, ArtSelfieSubjectConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ArtSelfieSubjectConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ArtSelfieSubjectConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ArtSelfieSubjectConfig?)(new FBConfig.ArtSelfieSubjectConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ArtSelfieSubjectConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ArtSelfieSubjectConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ArtSelfieSubjectConfigDict> CreateArtSelfieSubjectConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ArtSelfieSubjectConfigDict.AddValues(builder, valuesOffset);
    return ArtSelfieSubjectConfigDict.EndArtSelfieSubjectConfigDict(builder);
  }

  public static void StartArtSelfieSubjectConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ArtSelfieSubjectConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ArtSelfieSubjectConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ArtSelfieSubjectConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ArtSelfieSubjectConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ArtSelfieSubjectConfigDict> EndArtSelfieSubjectConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ArtSelfieSubjectConfigDict>(o);
  }
  public static void FinishArtSelfieSubjectConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ArtSelfieSubjectConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedArtSelfieSubjectConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ArtSelfieSubjectConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ArtSelfieSubjectConfigDictT UnPack() {
    var _o = new ArtSelfieSubjectConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ArtSelfieSubjectConfigDictT _o) {
    _o.Values = new List<FBConfig.ArtSelfieSubjectConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ArtSelfieSubjectConfigDict> Pack(FlatBufferBuilder builder, ArtSelfieSubjectConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ArtSelfieSubjectConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ArtSelfieSubjectConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ArtSelfieSubjectConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateArtSelfieSubjectConfigDict(
      builder,
      _values);
  }
}

public class ArtSelfieSubjectConfigDictT
{
  public List<FBConfig.ArtSelfieSubjectConfigT> Values { get; set; }

  public ArtSelfieSubjectConfigDictT() {
    this.Values = null;
  }
  public static ArtSelfieSubjectConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ArtSelfieSubjectConfigDict.GetRootAsArtSelfieSubjectConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ArtSelfieSubjectConfigDict.FinishArtSelfieSubjectConfigDictBuffer(fbb, ArtSelfieSubjectConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
