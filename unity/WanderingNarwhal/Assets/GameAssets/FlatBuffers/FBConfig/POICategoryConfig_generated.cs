// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct POICategoryConfig : IFlatbufferConfig<POICategoryConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POICategoryConfig GetRootAsPOICategoryConfig(ByteBuffer _bb) { return GetRootAsPOICategoryConfig(_bb, new POICategoryConfig()); }
  public static POICategoryConfig GetRootAsPOICategoryConfig(ByteBuffer _bb, POICategoryConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POICategoryConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string MapPrefab { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMapPrefabBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetMapPrefabBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetMapPrefabArray() { return __p.__vector_as_array<byte>(8); }
  public string MapPrefabContent { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMapPrefabContentBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetMapPrefabContentBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetMapPrefabContentArray() { return __p.__vector_as_array<byte>(10); }

  public static Offset<FBConfig.POICategoryConfig> CreatePOICategoryConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset map_prefabOffset = default(StringOffset),
      StringOffset map_prefab_contentOffset = default(StringOffset)) {
    builder.StartTable(4);
    POICategoryConfig.AddMapPrefabContent(builder, map_prefab_contentOffset);
    POICategoryConfig.AddMapPrefab(builder, map_prefabOffset);
    POICategoryConfig.AddName(builder, nameOffset);
    POICategoryConfig.AddUid(builder, uidOffset);
    return POICategoryConfig.EndPOICategoryConfig(builder);
  }

  public static void StartPOICategoryConfig(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddMapPrefab(FlatBufferBuilder builder, StringOffset mapPrefabOffset) { builder.AddOffset(2, mapPrefabOffset.Value, 0); }
  public static void AddMapPrefabContent(FlatBufferBuilder builder, StringOffset mapPrefabContentOffset) { builder.AddOffset(3, mapPrefabContentOffset.Value, 0); }
  public static Offset<FBConfig.POICategoryConfig> EndPOICategoryConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.POICategoryConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfPOICategoryConfig(FlatBufferBuilder builder, Offset<POICategoryConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<POICategoryConfig> o1, Offset<POICategoryConfig> o2) =>
        new POICategoryConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new POICategoryConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static POICategoryConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    POICategoryConfig obj_ = new POICategoryConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public POICategoryConfigT UnPack() {
    var _o = new POICategoryConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POICategoryConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.MapPrefab = this.MapPrefab;
    _o.MapPrefabContent = this.MapPrefabContent;
  }
  public static Offset<FBConfig.POICategoryConfig> Pack(FlatBufferBuilder builder, POICategoryConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.POICategoryConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _map_prefab = _o.MapPrefab == null ? default(StringOffset) : builder.CreateString(_o.MapPrefab);
    var _map_prefab_content = _o.MapPrefabContent == null ? default(StringOffset) : builder.CreateString(_o.MapPrefabContent);
    return CreatePOICategoryConfig(
      builder,
      _uid,
      _name,
      _map_prefab,
      _map_prefab_content);
  }
}

public class POICategoryConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string MapPrefab { get; set; }
  public string MapPrefabContent { get; set; }

  public POICategoryConfigT() {
    this.Uid = null;
    this.Name = null;
    this.MapPrefab = null;
    this.MapPrefabContent = null;
  }
}

public struct POICategoryConfigDict : IFlatbufferConfigDict<POICategoryConfig, POICategoryConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POICategoryConfigDict GetRootAsPOICategoryConfigDict(ByteBuffer _bb) { return GetRootAsPOICategoryConfigDict(_bb, new POICategoryConfigDict()); }
  public static POICategoryConfigDict GetRootAsPOICategoryConfigDict(ByteBuffer _bb, POICategoryConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POICategoryConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.POICategoryConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.POICategoryConfig?)(new FBConfig.POICategoryConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.POICategoryConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.POICategoryConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.POICategoryConfigDict> CreatePOICategoryConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    POICategoryConfigDict.AddValues(builder, valuesOffset);
    return POICategoryConfigDict.EndPOICategoryConfigDict(builder);
  }

  public static void StartPOICategoryConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.POICategoryConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.POICategoryConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.POICategoryConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.POICategoryConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.POICategoryConfigDict> EndPOICategoryConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.POICategoryConfigDict>(o);
  }
  public static void FinishPOICategoryConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POICategoryConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedPOICategoryConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POICategoryConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public POICategoryConfigDictT UnPack() {
    var _o = new POICategoryConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POICategoryConfigDictT _o) {
    _o.Values = new List<FBConfig.POICategoryConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.POICategoryConfigDict> Pack(FlatBufferBuilder builder, POICategoryConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.POICategoryConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.POICategoryConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.POICategoryConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreatePOICategoryConfigDict(
      builder,
      _values);
  }
}

public class POICategoryConfigDictT
{
  public List<FBConfig.POICategoryConfigT> Values { get; set; }

  public POICategoryConfigDictT() {
    this.Values = null;
  }
  public static POICategoryConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return POICategoryConfigDict.GetRootAsPOICategoryConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    POICategoryConfigDict.FinishPOICategoryConfigDictBuffer(fbb, POICategoryConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
