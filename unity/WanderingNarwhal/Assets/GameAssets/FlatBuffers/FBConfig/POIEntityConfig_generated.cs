// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct POIEntityConfig : IFlatbufferConfig<POIEntityConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POIEntityConfig GetRootAsPOIEntityConfig(ByteBuffer _bb) { return GetRootAsPOIEntityConfig(_bb, new POIEntityConfig()); }
  public static POIEntityConfig GetRootAsPOIEntityConfig(ByteBuffer _bb, POIEntityConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POIEntityConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Name { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(6); }
  public string Category { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(8); }
  public int Type { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateType(int type) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, type); return true; } else { return false; } }
  public string Text { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTextBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetTextBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetTextArray() { return __p.__vector_as_array<byte>(12); }
  public string Icon { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIconBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetIconBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetIconArray() { return __p.__vector_as_array<byte>(14); }
  public string MapIcon { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMapIconBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetMapIconBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetMapIconArray() { return __p.__vector_as_array<byte>(16); }
  public string SubName { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubNameBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetSubNameBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetSubNameArray() { return __p.__vector_as_array<byte>(18); }
  public int SortOrder { get { int o = __p.__offset(20); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateSortOrder(int sort_order) { int o = __p.__offset(20); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, sort_order); return true; } else { return false; } }
  public string PoiLocationUid { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPoiLocationUidBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetPoiLocationUidBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetPoiLocationUidArray() { return __p.__vector_as_array<byte>(22); }
  public string Thumbnail { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(24); }

  public static Offset<FBConfig.POIEntityConfig> CreatePOIEntityConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset nameOffset = default(StringOffset),
      StringOffset categoryOffset = default(StringOffset),
      int type = 0,
      StringOffset textOffset = default(StringOffset),
      StringOffset iconOffset = default(StringOffset),
      StringOffset map_iconOffset = default(StringOffset),
      StringOffset sub_nameOffset = default(StringOffset),
      int sort_order = 0,
      StringOffset poi_location_uidOffset = default(StringOffset),
      StringOffset thumbnailOffset = default(StringOffset)) {
    builder.StartTable(11);
    POIEntityConfig.AddThumbnail(builder, thumbnailOffset);
    POIEntityConfig.AddPoiLocationUid(builder, poi_location_uidOffset);
    POIEntityConfig.AddSortOrder(builder, sort_order);
    POIEntityConfig.AddSubName(builder, sub_nameOffset);
    POIEntityConfig.AddMapIcon(builder, map_iconOffset);
    POIEntityConfig.AddIcon(builder, iconOffset);
    POIEntityConfig.AddText(builder, textOffset);
    POIEntityConfig.AddType(builder, type);
    POIEntityConfig.AddCategory(builder, categoryOffset);
    POIEntityConfig.AddName(builder, nameOffset);
    POIEntityConfig.AddUid(builder, uidOffset);
    return POIEntityConfig.EndPOIEntityConfig(builder);
  }

  public static void StartPOIEntityConfig(FlatBufferBuilder builder) { builder.StartTable(11); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(1, nameOffset.Value, 0); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(2, categoryOffset.Value, 0); }
  public static void AddType(FlatBufferBuilder builder, int type) { builder.AddInt(3, type, 0); }
  public static void AddText(FlatBufferBuilder builder, StringOffset textOffset) { builder.AddOffset(4, textOffset.Value, 0); }
  public static void AddIcon(FlatBufferBuilder builder, StringOffset iconOffset) { builder.AddOffset(5, iconOffset.Value, 0); }
  public static void AddMapIcon(FlatBufferBuilder builder, StringOffset mapIconOffset) { builder.AddOffset(6, mapIconOffset.Value, 0); }
  public static void AddSubName(FlatBufferBuilder builder, StringOffset subNameOffset) { builder.AddOffset(7, subNameOffset.Value, 0); }
  public static void AddSortOrder(FlatBufferBuilder builder, int sortOrder) { builder.AddInt(8, sortOrder, 0); }
  public static void AddPoiLocationUid(FlatBufferBuilder builder, StringOffset poiLocationUidOffset) { builder.AddOffset(9, poiLocationUidOffset.Value, 0); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(10, thumbnailOffset.Value, 0); }
  public static Offset<FBConfig.POIEntityConfig> EndPOIEntityConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.POIEntityConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfPOIEntityConfig(FlatBufferBuilder builder, Offset<POIEntityConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<POIEntityConfig> o1, Offset<POIEntityConfig> o2) =>
        new POIEntityConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new POIEntityConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static POIEntityConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    POIEntityConfig obj_ = new POIEntityConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public POIEntityConfigT UnPack() {
    var _o = new POIEntityConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POIEntityConfigT _o) {
    _o.Uid = this.Uid;
    _o.Name = this.Name;
    _o.Category = this.Category;
    _o.Type = this.Type;
    _o.Text = this.Text;
    _o.Icon = this.Icon;
    _o.MapIcon = this.MapIcon;
    _o.SubName = this.SubName;
    _o.SortOrder = this.SortOrder;
    _o.PoiLocationUid = this.PoiLocationUid;
    _o.Thumbnail = this.Thumbnail;
  }
  public static Offset<FBConfig.POIEntityConfig> Pack(FlatBufferBuilder builder, POIEntityConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.POIEntityConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    var _text = _o.Text == null ? default(StringOffset) : builder.CreateString(_o.Text);
    var _icon = _o.Icon == null ? default(StringOffset) : builder.CreateString(_o.Icon);
    var _map_icon = _o.MapIcon == null ? default(StringOffset) : builder.CreateString(_o.MapIcon);
    var _sub_name = _o.SubName == null ? default(StringOffset) : builder.CreateString(_o.SubName);
    var _poi_location_uid = _o.PoiLocationUid == null ? default(StringOffset) : builder.CreateString(_o.PoiLocationUid);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    return CreatePOIEntityConfig(
      builder,
      _uid,
      _name,
      _category,
      _o.Type,
      _text,
      _icon,
      _map_icon,
      _sub_name,
      _o.SortOrder,
      _poi_location_uid,
      _thumbnail);
  }
}

public class POIEntityConfigT
{
  public string Uid { get; set; }
  public string Name { get; set; }
  public string Category { get; set; }
  public int Type { get; set; }
  public string Text { get; set; }
  public string Icon { get; set; }
  public string MapIcon { get; set; }
  public string SubName { get; set; }
  public int SortOrder { get; set; }
  public string PoiLocationUid { get; set; }
  public string Thumbnail { get; set; }

  public POIEntityConfigT() {
    this.Uid = null;
    this.Name = null;
    this.Category = null;
    this.Type = 0;
    this.Text = null;
    this.Icon = null;
    this.MapIcon = null;
    this.SubName = null;
    this.SortOrder = 0;
    this.PoiLocationUid = null;
    this.Thumbnail = null;
  }
}

public struct POIEntityConfigDict : IFlatbufferConfigDict<POIEntityConfig, POIEntityConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static POIEntityConfigDict GetRootAsPOIEntityConfigDict(ByteBuffer _bb) { return GetRootAsPOIEntityConfigDict(_bb, new POIEntityConfigDict()); }
  public static POIEntityConfigDict GetRootAsPOIEntityConfigDict(ByteBuffer _bb, POIEntityConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public POIEntityConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.POIEntityConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.POIEntityConfig?)(new FBConfig.POIEntityConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.POIEntityConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.POIEntityConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.POIEntityConfigDict> CreatePOIEntityConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    POIEntityConfigDict.AddValues(builder, valuesOffset);
    return POIEntityConfigDict.EndPOIEntityConfigDict(builder);
  }

  public static void StartPOIEntityConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.POIEntityConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.POIEntityConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.POIEntityConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.POIEntityConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.POIEntityConfigDict> EndPOIEntityConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.POIEntityConfigDict>(o);
  }
  public static void FinishPOIEntityConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POIEntityConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedPOIEntityConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.POIEntityConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public POIEntityConfigDictT UnPack() {
    var _o = new POIEntityConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(POIEntityConfigDictT _o) {
    _o.Values = new List<FBConfig.POIEntityConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.POIEntityConfigDict> Pack(FlatBufferBuilder builder, POIEntityConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.POIEntityConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.POIEntityConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.POIEntityConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreatePOIEntityConfigDict(
      builder,
      _values);
  }
}

public class POIEntityConfigDictT
{
  public List<FBConfig.POIEntityConfigT> Values { get; set; }

  public POIEntityConfigDictT() {
    this.Values = null;
  }
  public static POIEntityConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return POIEntityConfigDict.GetRootAsPOIEntityConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    POIEntityConfigDict.FinishPOIEntityConfigDictBuffer(fbb, POIEntityConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
