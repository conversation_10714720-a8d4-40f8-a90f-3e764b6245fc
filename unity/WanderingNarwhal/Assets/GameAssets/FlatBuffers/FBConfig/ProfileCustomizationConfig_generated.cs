// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ProfileCustomizationConfigItem : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ProfileCustomizationConfigItem GetRootAsProfileCustomizationConfigItem(ByteBuffer _bb) { return GetRootAsProfileCustomizationConfigItem(_bb, new ProfileCustomizationConfigItem()); }
  public static ProfileCustomizationConfigItem GetRootAsProfileCustomizationConfigItem(ByteBuffer _bb, ProfileCustomizationConfigItem obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ProfileCustomizationConfigItem __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string PrefabUid { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPrefabUidBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetPrefabUidBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetPrefabUidArray() { return __p.__vector_as_array<byte>(6); }
  public bool ShouldShowLockState { get { int o = __p.__offset(8); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateShouldShowLockState(bool should_show_lock_state) { int o = __p.__offset(8); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(should_show_lock_state ? 1 : 0)); return true; } else { return false; } }
  public string UnlockConditionId { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUnlockConditionIdBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetUnlockConditionIdBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetUnlockConditionIdArray() { return __p.__vector_as_array<byte>(10); }

  public static Offset<FBConfig.ProfileCustomizationConfigItem> CreateProfileCustomizationConfigItem(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset prefab_uidOffset = default(StringOffset),
      bool should_show_lock_state = false,
      StringOffset unlock_condition_idOffset = default(StringOffset)) {
    builder.StartTable(4);
    ProfileCustomizationConfigItem.AddUnlockConditionId(builder, unlock_condition_idOffset);
    ProfileCustomizationConfigItem.AddPrefabUid(builder, prefab_uidOffset);
    ProfileCustomizationConfigItem.AddUid(builder, uidOffset);
    ProfileCustomizationConfigItem.AddShouldShowLockState(builder, should_show_lock_state);
    return ProfileCustomizationConfigItem.EndProfileCustomizationConfigItem(builder);
  }

  public static void StartProfileCustomizationConfigItem(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddPrefabUid(FlatBufferBuilder builder, StringOffset prefabUidOffset) { builder.AddOffset(1, prefabUidOffset.Value, 0); }
  public static void AddShouldShowLockState(FlatBufferBuilder builder, bool shouldShowLockState) { builder.AddBool(2, shouldShowLockState, false); }
  public static void AddUnlockConditionId(FlatBufferBuilder builder, StringOffset unlockConditionIdOffset) { builder.AddOffset(3, unlockConditionIdOffset.Value, 0); }
  public static Offset<FBConfig.ProfileCustomizationConfigItem> EndProfileCustomizationConfigItem(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ProfileCustomizationConfigItem>(o);
  }

  public static VectorOffset CreateSortedVectorOfProfileCustomizationConfigItem(FlatBufferBuilder builder, Offset<ProfileCustomizationConfigItem>[] offsets) {
    Array.Sort(offsets,
      (Offset<ProfileCustomizationConfigItem> o1, Offset<ProfileCustomizationConfigItem> o2) =>
        new ProfileCustomizationConfigItem().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ProfileCustomizationConfigItem().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ProfileCustomizationConfigItem? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ProfileCustomizationConfigItem obj_ = new ProfileCustomizationConfigItem();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ProfileCustomizationConfigItemT UnPack() {
    var _o = new ProfileCustomizationConfigItemT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ProfileCustomizationConfigItemT _o) {
    _o.Uid = this.Uid;
    _o.PrefabUid = this.PrefabUid;
    _o.ShouldShowLockState = this.ShouldShowLockState;
    _o.UnlockConditionId = this.UnlockConditionId;
  }
  public static Offset<FBConfig.ProfileCustomizationConfigItem> Pack(FlatBufferBuilder builder, ProfileCustomizationConfigItemT _o) {
    if (_o == null) return default(Offset<FBConfig.ProfileCustomizationConfigItem>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _prefab_uid = _o.PrefabUid == null ? default(StringOffset) : builder.CreateString(_o.PrefabUid);
    var _unlock_condition_id = _o.UnlockConditionId == null ? default(StringOffset) : builder.CreateString(_o.UnlockConditionId);
    return CreateProfileCustomizationConfigItem(
      builder,
      _uid,
      _prefab_uid,
      _o.ShouldShowLockState,
      _unlock_condition_id);
  }
}

public class ProfileCustomizationConfigItemT
{
  public string Uid { get; set; }
  public string PrefabUid { get; set; }
  public bool ShouldShowLockState { get; set; }
  public string UnlockConditionId { get; set; }

  public ProfileCustomizationConfigItemT() {
    this.Uid = null;
    this.PrefabUid = null;
    this.ShouldShowLockState = false;
    this.UnlockConditionId = null;
  }
}

public struct ProfileCustomizationConfig : IFlatbufferConfig<ProfileCustomizationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ProfileCustomizationConfig GetRootAsProfileCustomizationConfig(ByteBuffer _bb) { return GetRootAsProfileCustomizationConfig(_bb, new ProfileCustomizationConfig()); }
  public static ProfileCustomizationConfig GetRootAsProfileCustomizationConfig(ByteBuffer _bb, ProfileCustomizationConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ProfileCustomizationConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.ProfileCustomizationConfigItem? CustomizationsList(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.ProfileCustomizationConfigItem?)(new FBConfig.ProfileCustomizationConfigItem()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int CustomizationsListLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ProfileCustomizationConfigItem? CustomizationsListByKey(string key) { int o = __p.__offset(6); return o != 0 ? FBConfig.ProfileCustomizationConfigItem.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ProfileCustomizationConfig> CreateProfileCustomizationConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset customizations_listOffset = default(VectorOffset)) {
    builder.StartTable(2);
    ProfileCustomizationConfig.AddCustomizationsList(builder, customizations_listOffset);
    ProfileCustomizationConfig.AddUid(builder, uidOffset);
    return ProfileCustomizationConfig.EndProfileCustomizationConfig(builder);
  }

  public static void StartProfileCustomizationConfig(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCustomizationsList(FlatBufferBuilder builder, VectorOffset customizationsListOffset) { builder.AddOffset(1, customizationsListOffset.Value, 0); }
  public static VectorOffset CreateCustomizationsListVector(FlatBufferBuilder builder, Offset<FBConfig.ProfileCustomizationConfigItem>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateCustomizationsListVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ProfileCustomizationConfigItem>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCustomizationsListVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ProfileCustomizationConfigItem>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateCustomizationsListVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ProfileCustomizationConfigItem>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartCustomizationsListVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ProfileCustomizationConfig> EndProfileCustomizationConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.ProfileCustomizationConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfProfileCustomizationConfig(FlatBufferBuilder builder, Offset<ProfileCustomizationConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<ProfileCustomizationConfig> o1, Offset<ProfileCustomizationConfig> o2) =>
        new ProfileCustomizationConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new ProfileCustomizationConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static ProfileCustomizationConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    ProfileCustomizationConfig obj_ = new ProfileCustomizationConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public ProfileCustomizationConfigT UnPack() {
    var _o = new ProfileCustomizationConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ProfileCustomizationConfigT _o) {
    _o.Uid = this.Uid;
    _o.CustomizationsList = new List<FBConfig.ProfileCustomizationConfigItemT>();
    for (var _j = 0; _j < this.CustomizationsListLength; ++_j) {_o.CustomizationsList.Add(this.CustomizationsList(_j).HasValue ? this.CustomizationsList(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ProfileCustomizationConfig> Pack(FlatBufferBuilder builder, ProfileCustomizationConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.ProfileCustomizationConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _customizations_list = default(VectorOffset);
    if (_o.CustomizationsList != null) {
      var __customizations_list = new Offset<FBConfig.ProfileCustomizationConfigItem>[_o.CustomizationsList.Count];
      for (var _j = 0; _j < __customizations_list.Length; ++_j) { __customizations_list[_j] = FBConfig.ProfileCustomizationConfigItem.Pack(builder, _o.CustomizationsList[_j]); }
      _customizations_list = CreateCustomizationsListVector(builder, __customizations_list);
    }
    return CreateProfileCustomizationConfig(
      builder,
      _uid,
      _customizations_list);
  }
}

public class ProfileCustomizationConfigT
{
  public string Uid { get; set; }
  public List<FBConfig.ProfileCustomizationConfigItemT> CustomizationsList { get; set; }

  public ProfileCustomizationConfigT() {
    this.Uid = null;
    this.CustomizationsList = null;
  }
}

public struct ProfileCustomizationConfigDict : IFlatbufferConfigDict<ProfileCustomizationConfig, ProfileCustomizationConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ProfileCustomizationConfigDict GetRootAsProfileCustomizationConfigDict(ByteBuffer _bb) { return GetRootAsProfileCustomizationConfigDict(_bb, new ProfileCustomizationConfigDict()); }
  public static ProfileCustomizationConfigDict GetRootAsProfileCustomizationConfigDict(ByteBuffer _bb, ProfileCustomizationConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ProfileCustomizationConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.ProfileCustomizationConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.ProfileCustomizationConfig?)(new FBConfig.ProfileCustomizationConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ProfileCustomizationConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.ProfileCustomizationConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.ProfileCustomizationConfigDict> CreateProfileCustomizationConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    ProfileCustomizationConfigDict.AddValues(builder, valuesOffset);
    return ProfileCustomizationConfigDict.EndProfileCustomizationConfigDict(builder);
  }

  public static void StartProfileCustomizationConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.ProfileCustomizationConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ProfileCustomizationConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ProfileCustomizationConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ProfileCustomizationConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ProfileCustomizationConfigDict> EndProfileCustomizationConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ProfileCustomizationConfigDict>(o);
  }
  public static void FinishProfileCustomizationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ProfileCustomizationConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedProfileCustomizationConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.ProfileCustomizationConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public ProfileCustomizationConfigDictT UnPack() {
    var _o = new ProfileCustomizationConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ProfileCustomizationConfigDictT _o) {
    _o.Values = new List<FBConfig.ProfileCustomizationConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ProfileCustomizationConfigDict> Pack(FlatBufferBuilder builder, ProfileCustomizationConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.ProfileCustomizationConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.ProfileCustomizationConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.ProfileCustomizationConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateProfileCustomizationConfigDict(
      builder,
      _values);
  }
}

public class ProfileCustomizationConfigDictT
{
  public List<FBConfig.ProfileCustomizationConfigT> Values { get; set; }

  public ProfileCustomizationConfigDictT() {
    this.Values = null;
  }
  public static ProfileCustomizationConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return ProfileCustomizationConfigDict.GetRootAsProfileCustomizationConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    ProfileCustomizationConfigDict.FinishProfileCustomizationConfigDictBuffer(fbb, ProfileCustomizationConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
