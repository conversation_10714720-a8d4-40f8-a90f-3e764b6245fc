// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct GameUpdateConfig : IFlatbufferConfig<GameUpdateConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GameUpdateConfig GetRootAsGameUpdateConfig(ByteBuffer _bb) { return GetRootAsGameUpdateConfig(_bb, new GameUpdateConfig()); }
  public static GameUpdateConfig GetRootAsGameUpdateConfig(ByteBuffer _bb, GameUpdateConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GameUpdateConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string MinRequiredVersion { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMinRequiredVersionBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetMinRequiredVersionBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetMinRequiredVersionArray() { return __p.__vector_as_array<byte>(6); }
  public string LastVersion { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLastVersionBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetLastVersionBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetLastVersionArray() { return __p.__vector_as_array<byte>(8); }
  public string ForceUpdateMessage { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetForceUpdateMessageBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetForceUpdateMessageBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetForceUpdateMessageArray() { return __p.__vector_as_array<byte>(10); }
  public string SimpleUpdateMessage { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSimpleUpdateMessageBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetSimpleUpdateMessageBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetSimpleUpdateMessageArray() { return __p.__vector_as_array<byte>(12); }
  public string Updateurl { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUpdateurlBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetUpdateurlBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetUpdateurlArray() { return __p.__vector_as_array<byte>(14); }
  public string Reviewurl { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetReviewurlBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetReviewurlBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetReviewurlArray() { return __p.__vector_as_array<byte>(16); }
  public string LoadingScreens(int j) { int o = __p.__offset(18); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int LoadingScreensLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string ContactUsurl { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetContactUsurlBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetContactUsurlBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetContactUsurlArray() { return __p.__vector_as_array<byte>(20); }
  public string Faq { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetFaqBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetFaqBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetFaqArray() { return __p.__vector_as_array<byte>(22); }

  public static Offset<FBConfig.GameUpdateConfig> CreateGameUpdateConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset min_required_versionOffset = default(StringOffset),
      StringOffset last_versionOffset = default(StringOffset),
      StringOffset force_update_messageOffset = default(StringOffset),
      StringOffset simple_update_messageOffset = default(StringOffset),
      StringOffset updateurlOffset = default(StringOffset),
      StringOffset reviewurlOffset = default(StringOffset),
      VectorOffset loading_screensOffset = default(VectorOffset),
      StringOffset contact_usurlOffset = default(StringOffset),
      StringOffset faqOffset = default(StringOffset)) {
    builder.StartTable(10);
    GameUpdateConfig.AddFaq(builder, faqOffset);
    GameUpdateConfig.AddContactUsurl(builder, contact_usurlOffset);
    GameUpdateConfig.AddLoadingScreens(builder, loading_screensOffset);
    GameUpdateConfig.AddReviewurl(builder, reviewurlOffset);
    GameUpdateConfig.AddUpdateurl(builder, updateurlOffset);
    GameUpdateConfig.AddSimpleUpdateMessage(builder, simple_update_messageOffset);
    GameUpdateConfig.AddForceUpdateMessage(builder, force_update_messageOffset);
    GameUpdateConfig.AddLastVersion(builder, last_versionOffset);
    GameUpdateConfig.AddMinRequiredVersion(builder, min_required_versionOffset);
    GameUpdateConfig.AddUid(builder, uidOffset);
    return GameUpdateConfig.EndGameUpdateConfig(builder);
  }

  public static void StartGameUpdateConfig(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddMinRequiredVersion(FlatBufferBuilder builder, StringOffset minRequiredVersionOffset) { builder.AddOffset(1, minRequiredVersionOffset.Value, 0); }
  public static void AddLastVersion(FlatBufferBuilder builder, StringOffset lastVersionOffset) { builder.AddOffset(2, lastVersionOffset.Value, 0); }
  public static void AddForceUpdateMessage(FlatBufferBuilder builder, StringOffset forceUpdateMessageOffset) { builder.AddOffset(3, forceUpdateMessageOffset.Value, 0); }
  public static void AddSimpleUpdateMessage(FlatBufferBuilder builder, StringOffset simpleUpdateMessageOffset) { builder.AddOffset(4, simpleUpdateMessageOffset.Value, 0); }
  public static void AddUpdateurl(FlatBufferBuilder builder, StringOffset updateurlOffset) { builder.AddOffset(5, updateurlOffset.Value, 0); }
  public static void AddReviewurl(FlatBufferBuilder builder, StringOffset reviewurlOffset) { builder.AddOffset(6, reviewurlOffset.Value, 0); }
  public static void AddLoadingScreens(FlatBufferBuilder builder, VectorOffset loadingScreensOffset) { builder.AddOffset(7, loadingScreensOffset.Value, 0); }
  public static VectorOffset CreateLoadingScreensVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateLoadingScreensVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLoadingScreensVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLoadingScreensVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLoadingScreensVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddContactUsurl(FlatBufferBuilder builder, StringOffset contactUsurlOffset) { builder.AddOffset(8, contactUsurlOffset.Value, 0); }
  public static void AddFaq(FlatBufferBuilder builder, StringOffset faqOffset) { builder.AddOffset(9, faqOffset.Value, 0); }
  public static Offset<FBConfig.GameUpdateConfig> EndGameUpdateConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.GameUpdateConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfGameUpdateConfig(FlatBufferBuilder builder, Offset<GameUpdateConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<GameUpdateConfig> o1, Offset<GameUpdateConfig> o2) =>
        new GameUpdateConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new GameUpdateConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static GameUpdateConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    GameUpdateConfig obj_ = new GameUpdateConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public GameUpdateConfigT UnPack() {
    var _o = new GameUpdateConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GameUpdateConfigT _o) {
    _o.Uid = this.Uid;
    _o.MinRequiredVersion = this.MinRequiredVersion;
    _o.LastVersion = this.LastVersion;
    _o.ForceUpdateMessage = this.ForceUpdateMessage;
    _o.SimpleUpdateMessage = this.SimpleUpdateMessage;
    _o.Updateurl = this.Updateurl;
    _o.Reviewurl = this.Reviewurl;
    _o.LoadingScreens = new List<string>();
    for (var _j = 0; _j < this.LoadingScreensLength; ++_j) {_o.LoadingScreens.Add(this.LoadingScreens(_j));}
    _o.ContactUsurl = this.ContactUsurl;
    _o.Faq = this.Faq;
  }
  public static Offset<FBConfig.GameUpdateConfig> Pack(FlatBufferBuilder builder, GameUpdateConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.GameUpdateConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _min_required_version = _o.MinRequiredVersion == null ? default(StringOffset) : builder.CreateString(_o.MinRequiredVersion);
    var _last_version = _o.LastVersion == null ? default(StringOffset) : builder.CreateString(_o.LastVersion);
    var _force_update_message = _o.ForceUpdateMessage == null ? default(StringOffset) : builder.CreateString(_o.ForceUpdateMessage);
    var _simple_update_message = _o.SimpleUpdateMessage == null ? default(StringOffset) : builder.CreateString(_o.SimpleUpdateMessage);
    var _updateurl = _o.Updateurl == null ? default(StringOffset) : builder.CreateString(_o.Updateurl);
    var _reviewurl = _o.Reviewurl == null ? default(StringOffset) : builder.CreateString(_o.Reviewurl);
    var _loading_screens = default(VectorOffset);
    if (_o.LoadingScreens != null) {
      var __loading_screens = new StringOffset[_o.LoadingScreens.Count];
      for (var _j = 0; _j < __loading_screens.Length; ++_j) { __loading_screens[_j] = builder.CreateString(_o.LoadingScreens[_j]); }
      _loading_screens = CreateLoadingScreensVector(builder, __loading_screens);
    }
    var _contact_usurl = _o.ContactUsurl == null ? default(StringOffset) : builder.CreateString(_o.ContactUsurl);
    var _faq = _o.Faq == null ? default(StringOffset) : builder.CreateString(_o.Faq);
    return CreateGameUpdateConfig(
      builder,
      _uid,
      _min_required_version,
      _last_version,
      _force_update_message,
      _simple_update_message,
      _updateurl,
      _reviewurl,
      _loading_screens,
      _contact_usurl,
      _faq);
  }
}

public class GameUpdateConfigT
{
  public string Uid { get; set; }
  public string MinRequiredVersion { get; set; }
  public string LastVersion { get; set; }
  public string ForceUpdateMessage { get; set; }
  public string SimpleUpdateMessage { get; set; }
  public string Updateurl { get; set; }
  public string Reviewurl { get; set; }
  public List<string> LoadingScreens { get; set; }
  public string ContactUsurl { get; set; }
  public string Faq { get; set; }

  public GameUpdateConfigT() {
    this.Uid = null;
    this.MinRequiredVersion = null;
    this.LastVersion = null;
    this.ForceUpdateMessage = null;
    this.SimpleUpdateMessage = null;
    this.Updateurl = null;
    this.Reviewurl = null;
    this.LoadingScreens = null;
    this.ContactUsurl = null;
    this.Faq = null;
  }
}

public struct GameUpdateConfigDict : IFlatbufferConfigDict<GameUpdateConfig, GameUpdateConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GameUpdateConfigDict GetRootAsGameUpdateConfigDict(ByteBuffer _bb) { return GetRootAsGameUpdateConfigDict(_bb, new GameUpdateConfigDict()); }
  public static GameUpdateConfigDict GetRootAsGameUpdateConfigDict(ByteBuffer _bb, GameUpdateConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GameUpdateConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.GameUpdateConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.GameUpdateConfig?)(new FBConfig.GameUpdateConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.GameUpdateConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.GameUpdateConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.GameUpdateConfigDict> CreateGameUpdateConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    GameUpdateConfigDict.AddValues(builder, valuesOffset);
    return GameUpdateConfigDict.EndGameUpdateConfigDict(builder);
  }

  public static void StartGameUpdateConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.GameUpdateConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.GameUpdateConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.GameUpdateConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.GameUpdateConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.GameUpdateConfigDict> EndGameUpdateConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GameUpdateConfigDict>(o);
  }
  public static void FinishGameUpdateConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GameUpdateConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedGameUpdateConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GameUpdateConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public GameUpdateConfigDictT UnPack() {
    var _o = new GameUpdateConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GameUpdateConfigDictT _o) {
    _o.Values = new List<FBConfig.GameUpdateConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.GameUpdateConfigDict> Pack(FlatBufferBuilder builder, GameUpdateConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.GameUpdateConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.GameUpdateConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.GameUpdateConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateGameUpdateConfigDict(
      builder,
      _values);
  }
}

public class GameUpdateConfigDictT
{
  public List<FBConfig.GameUpdateConfigT> Values { get; set; }

  public GameUpdateConfigDictT() {
    this.Values = null;
  }
  public static GameUpdateConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return GameUpdateConfigDict.GetRootAsGameUpdateConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    GameUpdateConfigDict.FinishGameUpdateConfigDictBuffer(fbb, GameUpdateConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
