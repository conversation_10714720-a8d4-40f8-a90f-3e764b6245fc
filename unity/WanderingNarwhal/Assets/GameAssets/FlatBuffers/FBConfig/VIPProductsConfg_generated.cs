// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct ProductOptions : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ProductOptions GetRootAsProductOptions(ByteBuffer _bb) { return GetRootAsProductOptions(_bb, new ProductOptions()); }
  public static ProductOptions GetRootAsProductOptions(ByteBuffer _bb, ProductOptions obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ProductOptions __assign(int _i, By<PERSON><PERSON>uff<PERSON> _bb) { __init(_i, _bb); return this; }

  public string Name { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(4); }
  public string Options(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int OptionsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.ProductOptions> CreateProductOptions(FlatBufferBuilder builder,
      StringOffset nameOffset = default(StringOffset),
      VectorOffset optionsOffset = default(VectorOffset)) {
    builder.StartTable(2);
    ProductOptions.AddOptions(builder, optionsOffset);
    ProductOptions.AddName(builder, nameOffset);
    return ProductOptions.EndProductOptions(builder);
  }

  public static void StartProductOptions(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(0, nameOffset.Value, 0); }
  public static void AddOptions(FlatBufferBuilder builder, VectorOffset optionsOffset) { builder.AddOffset(1, optionsOffset.Value, 0); }
  public static VectorOffset CreateOptionsVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateOptionsVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOptionsVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOptionsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartOptionsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ProductOptions> EndProductOptions(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ProductOptions>(o);
  }
  public ProductOptionsT UnPack() {
    var _o = new ProductOptionsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ProductOptionsT _o) {
    _o.Name = this.Name;
    _o.Options = new List<string>();
    for (var _j = 0; _j < this.OptionsLength; ++_j) {_o.Options.Add(this.Options(_j));}
  }
  public static Offset<FBConfig.ProductOptions> Pack(FlatBufferBuilder builder, ProductOptionsT _o) {
    if (_o == null) return default(Offset<FBConfig.ProductOptions>);
    var _name = _o.Name == null ? default(StringOffset) : builder.CreateString(_o.Name);
    var _options = default(VectorOffset);
    if (_o.Options != null) {
      var __options = new StringOffset[_o.Options.Count];
      for (var _j = 0; _j < __options.Length; ++_j) { __options[_j] = builder.CreateString(_o.Options[_j]); }
      _options = CreateOptionsVector(builder, __options);
    }
    return CreateProductOptions(
      builder,
      _name,
      _options);
  }
}

public class ProductOptionsT
{
  public string Name { get; set; }
  public List<string> Options { get; set; }

  public ProductOptionsT() {
    this.Name = null;
    this.Options = null;
  }
}

public struct VIPProductsConfg : IFlatbufferConfig<VIPProductsConfgT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static VIPProductsConfg GetRootAsVIPProductsConfg(ByteBuffer _bb) { return GetRootAsVIPProductsConfg(_bb, new VIPProductsConfg()); }
  public static VIPProductsConfg GetRootAsVIPProductsConfg(ByteBuffer _bb, VIPProductsConfg obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public VIPProductsConfg __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int Order { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }
  public string Title { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetTitleBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetTitleArray() { return __p.__vector_as_array<byte>(8); }
  public string ShortDescription { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetShortDescriptionBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetShortDescriptionBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetShortDescriptionArray() { return __p.__vector_as_array<byte>(10); }
  public string LongDescription { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetLongDescriptionBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetLongDescriptionBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetLongDescriptionArray() { return __p.__vector_as_array<byte>(12); }
  public string MonetizrTag { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMonetizrTagBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetMonetizrTagBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetMonetizrTagArray() { return __p.__vector_as_array<byte>(14); }
  public string Thumbnail { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(16); }
  public int Price { get { int o = __p.__offset(18); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePrice(int price) { int o = __p.__offset(18); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, price); return true; } else { return false; } }
  public bool ComingSoon { get { int o = __p.__offset(20); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateComingSoon(bool coming_soon) { int o = __p.__offset(20); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(coming_soon ? 1 : 0)); return true; } else { return false; } }
  public bool RealPurchaseAllowed { get { int o = __p.__offset(22); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateRealPurchaseAllowed(bool real_purchase_allowed) { int o = __p.__offset(22); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(real_purchase_allowed ? 1 : 0)); return true; } else { return false; } }
  public string AllowedCountries(int j) { int o = __p.__offset(24); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int AllowedCountriesLength { get { int o = __p.__offset(24); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ProductOptions? ProductOptions(int j) { int o = __p.__offset(26); return o != 0 ? (FBConfig.ProductOptions?)(new FBConfig.ProductOptions()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ProductOptionsLength { get { int o = __p.__offset(26); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.VIPProductsConfg> CreateVIPProductsConfg(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int order = 0,
      StringOffset titleOffset = default(StringOffset),
      StringOffset short_descriptionOffset = default(StringOffset),
      StringOffset long_descriptionOffset = default(StringOffset),
      StringOffset monetizr_tagOffset = default(StringOffset),
      StringOffset thumbnailOffset = default(StringOffset),
      int price = 0,
      bool coming_soon = false,
      bool real_purchase_allowed = false,
      VectorOffset allowed_countriesOffset = default(VectorOffset),
      VectorOffset product_optionsOffset = default(VectorOffset)) {
    builder.StartTable(12);
    VIPProductsConfg.AddProductOptions(builder, product_optionsOffset);
    VIPProductsConfg.AddAllowedCountries(builder, allowed_countriesOffset);
    VIPProductsConfg.AddPrice(builder, price);
    VIPProductsConfg.AddThumbnail(builder, thumbnailOffset);
    VIPProductsConfg.AddMonetizrTag(builder, monetizr_tagOffset);
    VIPProductsConfg.AddLongDescription(builder, long_descriptionOffset);
    VIPProductsConfg.AddShortDescription(builder, short_descriptionOffset);
    VIPProductsConfg.AddTitle(builder, titleOffset);
    VIPProductsConfg.AddOrder(builder, order);
    VIPProductsConfg.AddUid(builder, uidOffset);
    VIPProductsConfg.AddRealPurchaseAllowed(builder, real_purchase_allowed);
    VIPProductsConfg.AddComingSoon(builder, coming_soon);
    return VIPProductsConfg.EndVIPProductsConfg(builder);
  }

  public static void StartVIPProductsConfg(FlatBufferBuilder builder) { builder.StartTable(12); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(1, order, 0); }
  public static void AddTitle(FlatBufferBuilder builder, StringOffset titleOffset) { builder.AddOffset(2, titleOffset.Value, 0); }
  public static void AddShortDescription(FlatBufferBuilder builder, StringOffset shortDescriptionOffset) { builder.AddOffset(3, shortDescriptionOffset.Value, 0); }
  public static void AddLongDescription(FlatBufferBuilder builder, StringOffset longDescriptionOffset) { builder.AddOffset(4, longDescriptionOffset.Value, 0); }
  public static void AddMonetizrTag(FlatBufferBuilder builder, StringOffset monetizrTagOffset) { builder.AddOffset(5, monetizrTagOffset.Value, 0); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(6, thumbnailOffset.Value, 0); }
  public static void AddPrice(FlatBufferBuilder builder, int price) { builder.AddInt(7, price, 0); }
  public static void AddComingSoon(FlatBufferBuilder builder, bool comingSoon) { builder.AddBool(8, comingSoon, false); }
  public static void AddRealPurchaseAllowed(FlatBufferBuilder builder, bool realPurchaseAllowed) { builder.AddBool(9, realPurchaseAllowed, false); }
  public static void AddAllowedCountries(FlatBufferBuilder builder, VectorOffset allowedCountriesOffset) { builder.AddOffset(10, allowedCountriesOffset.Value, 0); }
  public static VectorOffset CreateAllowedCountriesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAllowedCountriesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAllowedCountriesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAllowedCountriesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAllowedCountriesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddProductOptions(FlatBufferBuilder builder, VectorOffset productOptionsOffset) { builder.AddOffset(11, productOptionsOffset.Value, 0); }
  public static VectorOffset CreateProductOptionsVector(FlatBufferBuilder builder, Offset<FBConfig.ProductOptions>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateProductOptionsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ProductOptions>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateProductOptionsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ProductOptions>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateProductOptionsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ProductOptions>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartProductOptionsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.VIPProductsConfg> EndVIPProductsConfg(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.VIPProductsConfg>(o);
  }

  public static VectorOffset CreateSortedVectorOfVIPProductsConfg(FlatBufferBuilder builder, Offset<VIPProductsConfg>[] offsets) {
    Array.Sort(offsets,
      (Offset<VIPProductsConfg> o1, Offset<VIPProductsConfg> o2) =>
        new VIPProductsConfg().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new VIPProductsConfg().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static VIPProductsConfg? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    VIPProductsConfg obj_ = new VIPProductsConfg();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public VIPProductsConfgT UnPack() {
    var _o = new VIPProductsConfgT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(VIPProductsConfgT _o) {
    _o.Uid = this.Uid;
    _o.Order = this.Order;
    _o.Title = this.Title;
    _o.ShortDescription = this.ShortDescription;
    _o.LongDescription = this.LongDescription;
    _o.MonetizrTag = this.MonetizrTag;
    _o.Thumbnail = this.Thumbnail;
    _o.Price = this.Price;
    _o.ComingSoon = this.ComingSoon;
    _o.RealPurchaseAllowed = this.RealPurchaseAllowed;
    _o.AllowedCountries = new List<string>();
    for (var _j = 0; _j < this.AllowedCountriesLength; ++_j) {_o.AllowedCountries.Add(this.AllowedCountries(_j));}
    _o.ProductOptions = new List<FBConfig.ProductOptionsT>();
    for (var _j = 0; _j < this.ProductOptionsLength; ++_j) {_o.ProductOptions.Add(this.ProductOptions(_j).HasValue ? this.ProductOptions(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.VIPProductsConfg> Pack(FlatBufferBuilder builder, VIPProductsConfgT _o) {
    if (_o == null) return default(Offset<FBConfig.VIPProductsConfg>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _title = _o.Title == null ? default(StringOffset) : builder.CreateString(_o.Title);
    var _short_description = _o.ShortDescription == null ? default(StringOffset) : builder.CreateString(_o.ShortDescription);
    var _long_description = _o.LongDescription == null ? default(StringOffset) : builder.CreateString(_o.LongDescription);
    var _monetizr_tag = _o.MonetizrTag == null ? default(StringOffset) : builder.CreateString(_o.MonetizrTag);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    var _allowed_countries = default(VectorOffset);
    if (_o.AllowedCountries != null) {
      var __allowed_countries = new StringOffset[_o.AllowedCountries.Count];
      for (var _j = 0; _j < __allowed_countries.Length; ++_j) { __allowed_countries[_j] = builder.CreateString(_o.AllowedCountries[_j]); }
      _allowed_countries = CreateAllowedCountriesVector(builder, __allowed_countries);
    }
    var _product_options = default(VectorOffset);
    if (_o.ProductOptions != null) {
      var __product_options = new Offset<FBConfig.ProductOptions>[_o.ProductOptions.Count];
      for (var _j = 0; _j < __product_options.Length; ++_j) { __product_options[_j] = FBConfig.ProductOptions.Pack(builder, _o.ProductOptions[_j]); }
      _product_options = CreateProductOptionsVector(builder, __product_options);
    }
    return CreateVIPProductsConfg(
      builder,
      _uid,
      _o.Order,
      _title,
      _short_description,
      _long_description,
      _monetizr_tag,
      _thumbnail,
      _o.Price,
      _o.ComingSoon,
      _o.RealPurchaseAllowed,
      _allowed_countries,
      _product_options);
  }
}

public class VIPProductsConfgT
{
  public string Uid { get; set; }
  public int Order { get; set; }
  public string Title { get; set; }
  public string ShortDescription { get; set; }
  public string LongDescription { get; set; }
  public string MonetizrTag { get; set; }
  public string Thumbnail { get; set; }
  public int Price { get; set; }
  public bool ComingSoon { get; set; }
  public bool RealPurchaseAllowed { get; set; }
  public List<string> AllowedCountries { get; set; }
  public List<FBConfig.ProductOptionsT> ProductOptions { get; set; }

  public VIPProductsConfgT() {
    this.Uid = null;
    this.Order = 0;
    this.Title = null;
    this.ShortDescription = null;
    this.LongDescription = null;
    this.MonetizrTag = null;
    this.Thumbnail = null;
    this.Price = 0;
    this.ComingSoon = false;
    this.RealPurchaseAllowed = false;
    this.AllowedCountries = null;
    this.ProductOptions = null;
  }
}

public struct VIPProductsConfgDict : IFlatbufferConfigDict<VIPProductsConfg, VIPProductsConfgT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static VIPProductsConfgDict GetRootAsVIPProductsConfgDict(ByteBuffer _bb) { return GetRootAsVIPProductsConfgDict(_bb, new VIPProductsConfgDict()); }
  public static VIPProductsConfgDict GetRootAsVIPProductsConfgDict(ByteBuffer _bb, VIPProductsConfgDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public VIPProductsConfgDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.VIPProductsConfg? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.VIPProductsConfg?)(new FBConfig.VIPProductsConfg()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.VIPProductsConfg? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.VIPProductsConfg.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.VIPProductsConfgDict> CreateVIPProductsConfgDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    VIPProductsConfgDict.AddValues(builder, valuesOffset);
    return VIPProductsConfgDict.EndVIPProductsConfgDict(builder);
  }

  public static void StartVIPProductsConfgDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsConfg>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsConfg>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.VIPProductsConfg>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.VIPProductsConfg>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.VIPProductsConfgDict> EndVIPProductsConfgDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.VIPProductsConfgDict>(o);
  }
  public static void FinishVIPProductsConfgDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsConfgDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedVIPProductsConfgDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.VIPProductsConfgDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public VIPProductsConfgDictT UnPack() {
    var _o = new VIPProductsConfgDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(VIPProductsConfgDictT _o) {
    _o.Values = new List<FBConfig.VIPProductsConfgT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.VIPProductsConfgDict> Pack(FlatBufferBuilder builder, VIPProductsConfgDictT _o) {
    if (_o == null) return default(Offset<FBConfig.VIPProductsConfgDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.VIPProductsConfg>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.VIPProductsConfg.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateVIPProductsConfgDict(
      builder,
      _values);
  }
}

public class VIPProductsConfgDictT
{
  public List<FBConfig.VIPProductsConfgT> Values { get; set; }

  public VIPProductsConfgDictT() {
    this.Values = null;
  }
  public static VIPProductsConfgDictT DeserializeFromBinary(byte[] fbBuffer) {
    return VIPProductsConfgDict.GetRootAsVIPProductsConfgDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    VIPProductsConfgDict.FinishVIPProductsConfgDictBuffer(fbb, VIPProductsConfgDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
