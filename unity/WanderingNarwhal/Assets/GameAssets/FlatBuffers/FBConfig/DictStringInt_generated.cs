// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DictStringInt : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DictStringInt GetRootAsDictStringInt(ByteBuffer _bb) { return GetRootAsDictStringInt(_bb, new DictStringInt()); }
  public static DictStringInt GetRootAsDictStringInt(ByteBuffer _bb, DictStringInt obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DictStringInt __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string Key { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetKeyBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetKeyBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetKeyArray() { return __p.__vector_as_array<byte>(4); }
  public int Value { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateValue(int value) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, value); return true; } else { return false; } }

  public static Offset<FBConfig.DictStringInt> CreateDictStringInt(FlatBufferBuilder builder,
      StringOffset keyOffset = default(StringOffset),
      int value = 0) {
    builder.StartTable(2);
    DictStringInt.AddValue(builder, value);
    DictStringInt.AddKey(builder, keyOffset);
    return DictStringInt.EndDictStringInt(builder);
  }

  public static void StartDictStringInt(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddKey(FlatBufferBuilder builder, StringOffset keyOffset) { builder.AddOffset(0, keyOffset.Value, 0); }
  public static void AddValue(FlatBufferBuilder builder, int value) { builder.AddInt(1, value, 0); }
  public static Offset<FBConfig.DictStringInt> EndDictStringInt(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DictStringInt>(o);
  }
  public DictStringIntT UnPack() {
    var _o = new DictStringIntT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DictStringIntT _o) {
    _o.Key = this.Key;
    _o.Value = this.Value;
  }
  public static Offset<FBConfig.DictStringInt> Pack(FlatBufferBuilder builder, DictStringIntT _o) {
    if (_o == null) return default(Offset<FBConfig.DictStringInt>);
    var _key = _o.Key == null ? default(StringOffset) : builder.CreateString(_o.Key);
    return CreateDictStringInt(
      builder,
      _key,
      _o.Value);
  }
}

public class DictStringIntT
{
  public string Key { get; set; }
  public int Value { get; set; }

  public DictStringIntT() {
    this.Key = null;
    this.Value = 0;
  }
}


}
