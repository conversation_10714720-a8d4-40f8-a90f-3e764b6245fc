// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct DayMonthYear : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static DayMonthYear GetRootAsDayMonthYear(ByteBuffer _bb) { return GetRootAsDayMonthYear(_bb, new DayMonthYear()); }
  public static DayMonthYear GetRootAsDayMonthYear(ByteBuffer _bb, DayMonthYear obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public DayMonthYear __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public int Day { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateDay(int day) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, day); return true; } else { return false; } }
  public int Month { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMonth(int month) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, month); return true; } else { return false; } }
  public int Year { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateYear(int year) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, year); return true; } else { return false; } }
  public int Hour { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateHour(int hour) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, hour); return true; } else { return false; } }
  public int Minute { get { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMinute(int minute) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, minute); return true; } else { return false; } }
  public bool UseLocalTime { get { int o = __p.__offset(14); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateUseLocalTime(bool use_local_time) { int o = __p.__offset(14); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(use_local_time ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.DayMonthYear> CreateDayMonthYear(FlatBufferBuilder builder,
      int day = 0,
      int month = 0,
      int year = 0,
      int hour = 0,
      int minute = 0,
      bool use_local_time = false) {
    builder.StartTable(6);
    DayMonthYear.AddMinute(builder, minute);
    DayMonthYear.AddHour(builder, hour);
    DayMonthYear.AddYear(builder, year);
    DayMonthYear.AddMonth(builder, month);
    DayMonthYear.AddDay(builder, day);
    DayMonthYear.AddUseLocalTime(builder, use_local_time);
    return DayMonthYear.EndDayMonthYear(builder);
  }

  public static void StartDayMonthYear(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddDay(FlatBufferBuilder builder, int day) { builder.AddInt(0, day, 0); }
  public static void AddMonth(FlatBufferBuilder builder, int month) { builder.AddInt(1, month, 0); }
  public static void AddYear(FlatBufferBuilder builder, int year) { builder.AddInt(2, year, 0); }
  public static void AddHour(FlatBufferBuilder builder, int hour) { builder.AddInt(3, hour, 0); }
  public static void AddMinute(FlatBufferBuilder builder, int minute) { builder.AddInt(4, minute, 0); }
  public static void AddUseLocalTime(FlatBufferBuilder builder, bool useLocalTime) { builder.AddBool(5, useLocalTime, false); }
  public static Offset<FBConfig.DayMonthYear> EndDayMonthYear(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.DayMonthYear>(o);
  }
  public DayMonthYearT UnPack() {
    var _o = new DayMonthYearT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(DayMonthYearT _o) {
    _o.Day = this.Day;
    _o.Month = this.Month;
    _o.Year = this.Year;
    _o.Hour = this.Hour;
    _o.Minute = this.Minute;
    _o.UseLocalTime = this.UseLocalTime;
  }
  public static Offset<FBConfig.DayMonthYear> Pack(FlatBufferBuilder builder, DayMonthYearT _o) {
    if (_o == null) return default(Offset<FBConfig.DayMonthYear>);
    return CreateDayMonthYear(
      builder,
      _o.Day,
      _o.Month,
      _o.Year,
      _o.Hour,
      _o.Minute,
      _o.UseLocalTime);
  }
}

public class DayMonthYearT
{
  public int Day { get; set; }
  public int Month { get; set; }
  public int Year { get; set; }
  public int Hour { get; set; }
  public int Minute { get; set; }
  public bool UseLocalTime { get; set; }

  public DayMonthYearT() {
    this.Day = 0;
    this.Month = 0;
    this.Year = 0;
    this.Hour = 0;
    this.Minute = 0;
    this.UseLocalTime = false;
  }
}


}
