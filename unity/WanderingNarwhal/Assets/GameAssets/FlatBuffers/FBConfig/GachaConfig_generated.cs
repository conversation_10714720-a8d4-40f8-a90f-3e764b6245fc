// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct PrizeProb : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static PrizeProb GetRootAsPrizeProb(ByteBuffer _bb) { return GetRootAsPrizeProb(_bb, new PrizeProb()); }
  public static PrizeProb GetRootAsPrizeProb(ByteBuffer _bb, PrizeProb obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public PrizeProb __assign(int _i, <PERSON>te<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public int Range(int j) { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int RangeLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetRangeBytes() { return __p.__vector_as_span<int>(4, 4); }
#else
  public ArraySegment<byte>? GetRangeBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public int[] GetRangeArray() { return __p.__vector_as_array<int>(4); }
  public bool MutateRange(int j, int range) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, range); return true; } else { return false; } }
  public int Weight { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateWeight(int weight) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, weight); return true; } else { return false; } }
  public int WinWeight { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateWinWeight(int win_weight) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, win_weight); return true; } else { return false; } }
  public string Uid { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(10); }

  public static Offset<FBConfig.PrizeProb> CreatePrizeProb(FlatBufferBuilder builder,
      VectorOffset rangeOffset = default(VectorOffset),
      int weight = 0,
      int win_weight = 0,
      StringOffset uidOffset = default(StringOffset)) {
    builder.StartTable(4);
    PrizeProb.AddUid(builder, uidOffset);
    PrizeProb.AddWinWeight(builder, win_weight);
    PrizeProb.AddWeight(builder, weight);
    PrizeProb.AddRange(builder, rangeOffset);
    return PrizeProb.EndPrizeProb(builder);
  }

  public static void StartPrizeProb(FlatBufferBuilder builder) { builder.StartTable(4); }
  public static void AddRange(FlatBufferBuilder builder, VectorOffset rangeOffset) { builder.AddOffset(0, rangeOffset.Value, 0); }
  public static VectorOffset CreateRangeVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateRangeVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRangeVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRangeVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRangeVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddWeight(FlatBufferBuilder builder, int weight) { builder.AddInt(1, weight, 0); }
  public static void AddWinWeight(FlatBufferBuilder builder, int winWeight) { builder.AddInt(2, winWeight, 0); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(3, uidOffset.Value, 0); }
  public static Offset<FBConfig.PrizeProb> EndPrizeProb(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.PrizeProb>(o);
  }
  public PrizeProbT UnPack() {
    var _o = new PrizeProbT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(PrizeProbT _o) {
    _o.Range = new List<int>();
    for (var _j = 0; _j < this.RangeLength; ++_j) {_o.Range.Add(this.Range(_j));}
    _o.Weight = this.Weight;
    _o.WinWeight = this.WinWeight;
    _o.Uid = this.Uid;
  }
  public static Offset<FBConfig.PrizeProb> Pack(FlatBufferBuilder builder, PrizeProbT _o) {
    if (_o == null) return default(Offset<FBConfig.PrizeProb>);
    var _range = default(VectorOffset);
    if (_o.Range != null) {
      var __range = _o.Range.ToArray();
      _range = CreateRangeVector(builder, __range);
    }
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreatePrizeProb(
      builder,
      _range,
      _o.Weight,
      _o.WinWeight,
      _uid);
  }
}

public class PrizeProbT
{
  public List<int> Range { get; set; }
  public int Weight { get; set; }
  public int WinWeight { get; set; }
  public string Uid { get; set; }

  public PrizeProbT() {
    this.Range = null;
    this.Weight = 0;
    this.WinWeight = 0;
    this.Uid = null;
  }
}

public struct GachaSlotPrizesConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GachaSlotPrizesConfig GetRootAsGachaSlotPrizesConfig(ByteBuffer _bb) { return GetRootAsGachaSlotPrizesConfig(_bb, new GachaSlotPrizesConfig()); }
  public static GachaSlotPrizesConfig GetRootAsGachaSlotPrizesConfig(ByteBuffer _bb, GachaSlotPrizesConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GachaSlotPrizesConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.PrizeProb? Regular(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.PrizeProb?)(new FBConfig.PrizeProb()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int RegularLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.PrizeProb? Premium(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.PrizeProb?)(new FBConfig.PrizeProb()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PremiumLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int Life(int j) { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int LifeLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetLifeBytes() { return __p.__vector_as_span<int>(10, 4); }
#else
  public ArraySegment<byte>? GetLifeBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public int[] GetLifeArray() { return __p.__vector_as_array<int>(10); }
  public bool MutateLife(int j, int life) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, life); return true; } else { return false; } }
  public int Moves(int j) { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int MovesLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetMovesBytes() { return __p.__vector_as_span<int>(12, 4); }
#else
  public ArraySegment<byte>? GetMovesBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public int[] GetMovesArray() { return __p.__vector_as_array<int>(12); }
  public bool MutateMoves(int j, int moves) { int o = __p.__offset(12); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, moves); return true; } else { return false; } }
  public int NoPrize(int j) { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int NoPrizeLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetNoPrizeBytes() { return __p.__vector_as_span<int>(14, 4); }
#else
  public ArraySegment<byte>? GetNoPrizeBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public int[] GetNoPrizeArray() { return __p.__vector_as_array<int>(14); }
  public bool MutateNoPrize(int j, int no_prize) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(__p.__vector(o) + j * 4, no_prize); return true; } else { return false; } }
  public FBConfig.DictStringListInt? Other(int j) { int o = __p.__offset(16); return o != 0 ? (FBConfig.DictStringListInt?)(new FBConfig.DictStringListInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int OtherLength { get { int o = __p.__offset(16); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.PrizeProb? ExtraMoves(int j) { int o = __p.__offset(18); return o != 0 ? (FBConfig.PrizeProb?)(new FBConfig.PrizeProb()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ExtraMovesLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.PrizeProb? ExtraLife(int j) { int o = __p.__offset(20); return o != 0 ? (FBConfig.PrizeProb?)(new FBConfig.PrizeProb()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ExtraLifeLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.PrizeProb? BoosterExtended(int j) { int o = __p.__offset(22); return o != 0 ? (FBConfig.PrizeProb?)(new FBConfig.PrizeProb()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int BoosterExtendedLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.GachaSlotPrizesConfig> CreateGachaSlotPrizesConfig(FlatBufferBuilder builder,
      VectorOffset regularOffset = default(VectorOffset),
      VectorOffset premiumOffset = default(VectorOffset),
      VectorOffset lifeOffset = default(VectorOffset),
      VectorOffset movesOffset = default(VectorOffset),
      VectorOffset no_prizeOffset = default(VectorOffset),
      VectorOffset otherOffset = default(VectorOffset),
      VectorOffset extra_movesOffset = default(VectorOffset),
      VectorOffset extra_lifeOffset = default(VectorOffset),
      VectorOffset booster_extendedOffset = default(VectorOffset)) {
    builder.StartTable(10);
    GachaSlotPrizesConfig.AddBoosterExtended(builder, booster_extendedOffset);
    GachaSlotPrizesConfig.AddExtraLife(builder, extra_lifeOffset);
    GachaSlotPrizesConfig.AddExtraMoves(builder, extra_movesOffset);
    GachaSlotPrizesConfig.AddOther(builder, otherOffset);
    GachaSlotPrizesConfig.AddNoPrize(builder, no_prizeOffset);
    GachaSlotPrizesConfig.AddMoves(builder, movesOffset);
    GachaSlotPrizesConfig.AddLife(builder, lifeOffset);
    GachaSlotPrizesConfig.AddPremium(builder, premiumOffset);
    GachaSlotPrizesConfig.AddRegular(builder, regularOffset);
    return GachaSlotPrizesConfig.EndGachaSlotPrizesConfig(builder);
  }

  public static void StartGachaSlotPrizesConfig(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddRegular(FlatBufferBuilder builder, VectorOffset regularOffset) { builder.AddOffset(0, regularOffset.Value, 0); }
  public static VectorOffset CreateRegularVector(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateRegularVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRegularVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.PrizeProb>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateRegularVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.PrizeProb>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartRegularVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPremium(FlatBufferBuilder builder, VectorOffset premiumOffset) { builder.AddOffset(1, premiumOffset.Value, 0); }
  public static VectorOffset CreatePremiumVector(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePremiumVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePremiumVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.PrizeProb>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePremiumVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.PrizeProb>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPremiumVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddLife(FlatBufferBuilder builder, VectorOffset lifeOffset) { builder.AddOffset(3, lifeOffset.Value, 0); }
  public static VectorOffset CreateLifeVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateLifeVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLifeVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateLifeVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartLifeVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddMoves(FlatBufferBuilder builder, VectorOffset movesOffset) { builder.AddOffset(4, movesOffset.Value, 0); }
  public static VectorOffset CreateMovesVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateMovesVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateMovesVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateMovesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartMovesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddNoPrize(FlatBufferBuilder builder, VectorOffset noPrizeOffset) { builder.AddOffset(5, noPrizeOffset.Value, 0); }
  public static VectorOffset CreateNoPrizeVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateNoPrizeVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateNoPrizeVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateNoPrizeVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartNoPrizeVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddOther(FlatBufferBuilder builder, VectorOffset otherOffset) { builder.AddOffset(6, otherOffset.Value, 0); }
  public static VectorOffset CreateOtherVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringListInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateOtherVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringListInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOtherVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringListInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOtherVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringListInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartOtherVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddExtraMoves(FlatBufferBuilder builder, VectorOffset extraMovesOffset) { builder.AddOffset(7, extraMovesOffset.Value, 0); }
  public static VectorOffset CreateExtraMovesVector(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateExtraMovesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateExtraMovesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.PrizeProb>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateExtraMovesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.PrizeProb>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartExtraMovesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddExtraLife(FlatBufferBuilder builder, VectorOffset extraLifeOffset) { builder.AddOffset(8, extraLifeOffset.Value, 0); }
  public static VectorOffset CreateExtraLifeVector(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateExtraLifeVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateExtraLifeVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.PrizeProb>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateExtraLifeVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.PrizeProb>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartExtraLifeVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBoosterExtended(FlatBufferBuilder builder, VectorOffset boosterExtendedOffset) { builder.AddOffset(9, boosterExtendedOffset.Value, 0); }
  public static VectorOffset CreateBoosterExtendedVector(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBoosterExtendedVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.PrizeProb>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBoosterExtendedVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.PrizeProb>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBoosterExtendedVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.PrizeProb>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBoosterExtendedVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.GachaSlotPrizesConfig> EndGachaSlotPrizesConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GachaSlotPrizesConfig>(o);
  }
  public GachaSlotPrizesConfigT UnPack() {
    var _o = new GachaSlotPrizesConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GachaSlotPrizesConfigT _o) {
    _o.Regular = new List<FBConfig.PrizeProbT>();
    for (var _j = 0; _j < this.RegularLength; ++_j) {_o.Regular.Add(this.Regular(_j).HasValue ? this.Regular(_j).Value.UnPack() : null);}
    _o.Premium = new List<FBConfig.PrizeProbT>();
    for (var _j = 0; _j < this.PremiumLength; ++_j) {_o.Premium.Add(this.Premium(_j).HasValue ? this.Premium(_j).Value.UnPack() : null);}
    _o.Life = new List<int>();
    for (var _j = 0; _j < this.LifeLength; ++_j) {_o.Life.Add(this.Life(_j));}
    _o.Moves = new List<int>();
    for (var _j = 0; _j < this.MovesLength; ++_j) {_o.Moves.Add(this.Moves(_j));}
    _o.NoPrize = new List<int>();
    for (var _j = 0; _j < this.NoPrizeLength; ++_j) {_o.NoPrize.Add(this.NoPrize(_j));}
    _o.Other = new List<FBConfig.DictStringListIntT>();
    for (var _j = 0; _j < this.OtherLength; ++_j) {_o.Other.Add(this.Other(_j).HasValue ? this.Other(_j).Value.UnPack() : null);}
    _o.ExtraMoves = new List<FBConfig.PrizeProbT>();
    for (var _j = 0; _j < this.ExtraMovesLength; ++_j) {_o.ExtraMoves.Add(this.ExtraMoves(_j).HasValue ? this.ExtraMoves(_j).Value.UnPack() : null);}
    _o.ExtraLife = new List<FBConfig.PrizeProbT>();
    for (var _j = 0; _j < this.ExtraLifeLength; ++_j) {_o.ExtraLife.Add(this.ExtraLife(_j).HasValue ? this.ExtraLife(_j).Value.UnPack() : null);}
    _o.BoosterExtended = new List<FBConfig.PrizeProbT>();
    for (var _j = 0; _j < this.BoosterExtendedLength; ++_j) {_o.BoosterExtended.Add(this.BoosterExtended(_j).HasValue ? this.BoosterExtended(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.GachaSlotPrizesConfig> Pack(FlatBufferBuilder builder, GachaSlotPrizesConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.GachaSlotPrizesConfig>);
    var _regular = default(VectorOffset);
    if (_o.Regular != null) {
      var __regular = new Offset<FBConfig.PrizeProb>[_o.Regular.Count];
      for (var _j = 0; _j < __regular.Length; ++_j) { __regular[_j] = FBConfig.PrizeProb.Pack(builder, _o.Regular[_j]); }
      _regular = CreateRegularVector(builder, __regular);
    }
    var _premium = default(VectorOffset);
    if (_o.Premium != null) {
      var __premium = new Offset<FBConfig.PrizeProb>[_o.Premium.Count];
      for (var _j = 0; _j < __premium.Length; ++_j) { __premium[_j] = FBConfig.PrizeProb.Pack(builder, _o.Premium[_j]); }
      _premium = CreatePremiumVector(builder, __premium);
    }
    var _life = default(VectorOffset);
    if (_o.Life != null) {
      var __life = _o.Life.ToArray();
      _life = CreateLifeVector(builder, __life);
    }
    var _moves = default(VectorOffset);
    if (_o.Moves != null) {
      var __moves = _o.Moves.ToArray();
      _moves = CreateMovesVector(builder, __moves);
    }
    var _no_prize = default(VectorOffset);
    if (_o.NoPrize != null) {
      var __no_prize = _o.NoPrize.ToArray();
      _no_prize = CreateNoPrizeVector(builder, __no_prize);
    }
    var _other = default(VectorOffset);
    if (_o.Other != null) {
      var __other = new Offset<FBConfig.DictStringListInt>[_o.Other.Count];
      for (var _j = 0; _j < __other.Length; ++_j) { __other[_j] = FBConfig.DictStringListInt.Pack(builder, _o.Other[_j]); }
      _other = CreateOtherVector(builder, __other);
    }
    var _extra_moves = default(VectorOffset);
    if (_o.ExtraMoves != null) {
      var __extra_moves = new Offset<FBConfig.PrizeProb>[_o.ExtraMoves.Count];
      for (var _j = 0; _j < __extra_moves.Length; ++_j) { __extra_moves[_j] = FBConfig.PrizeProb.Pack(builder, _o.ExtraMoves[_j]); }
      _extra_moves = CreateExtraMovesVector(builder, __extra_moves);
    }
    var _extra_life = default(VectorOffset);
    if (_o.ExtraLife != null) {
      var __extra_life = new Offset<FBConfig.PrizeProb>[_o.ExtraLife.Count];
      for (var _j = 0; _j < __extra_life.Length; ++_j) { __extra_life[_j] = FBConfig.PrizeProb.Pack(builder, _o.ExtraLife[_j]); }
      _extra_life = CreateExtraLifeVector(builder, __extra_life);
    }
    var _booster_extended = default(VectorOffset);
    if (_o.BoosterExtended != null) {
      var __booster_extended = new Offset<FBConfig.PrizeProb>[_o.BoosterExtended.Count];
      for (var _j = 0; _j < __booster_extended.Length; ++_j) { __booster_extended[_j] = FBConfig.PrizeProb.Pack(builder, _o.BoosterExtended[_j]); }
      _booster_extended = CreateBoosterExtendedVector(builder, __booster_extended);
    }
    return CreateGachaSlotPrizesConfig(
      builder,
      _regular,
      _premium,
      _life,
      _moves,
      _no_prize,
      _other,
      _extra_moves,
      _extra_life,
      _booster_extended);
  }
}

public class GachaSlotPrizesConfigT
{
  public List<FBConfig.PrizeProbT> Regular { get; set; }
  public List<FBConfig.PrizeProbT> Premium { get; set; }
  public List<int> Life { get; set; }
  public List<int> Moves { get; set; }
  public List<int> NoPrize { get; set; }
  public List<FBConfig.DictStringListIntT> Other { get; set; }
  public List<FBConfig.PrizeProbT> ExtraMoves { get; set; }
  public List<FBConfig.PrizeProbT> ExtraLife { get; set; }
  public List<FBConfig.PrizeProbT> BoosterExtended { get; set; }

  public GachaSlotPrizesConfigT() {
    this.Regular = null;
    this.Premium = null;
    this.Life = null;
    this.Moves = null;
    this.NoPrize = null;
    this.Other = null;
    this.ExtraMoves = null;
    this.ExtraLife = null;
    this.BoosterExtended = null;
  }
}

public struct GachaPrizeConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GachaPrizeConfig GetRootAsGachaPrizeConfig(ByteBuffer _bb) { return GetRootAsGachaPrizeConfig(_bb, new GachaPrizeConfig()); }
  public static GachaPrizeConfig GetRootAsGachaPrizeConfig(ByteBuffer _bb, GachaPrizeConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GachaPrizeConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int PlayerLevel { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePlayerLevel(int player_level) { int o = __p.__offset(4); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, player_level); return true; } else { return false; } }
  public FBConfig.GachaSlotPrizesConfig? Slots(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.GachaSlotPrizesConfig?)(new FBConfig.GachaSlotPrizesConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int SlotsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string Predicate { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetPredicateBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetPredicateBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetPredicateArray() { return __p.__vector_as_array<byte>(8); }

  public static Offset<FBConfig.GachaPrizeConfig> CreateGachaPrizeConfig(FlatBufferBuilder builder,
      int player_level = 0,
      VectorOffset slotsOffset = default(VectorOffset),
      StringOffset predicateOffset = default(StringOffset)) {
    builder.StartTable(3);
    GachaPrizeConfig.AddPredicate(builder, predicateOffset);
    GachaPrizeConfig.AddSlots(builder, slotsOffset);
    GachaPrizeConfig.AddPlayerLevel(builder, player_level);
    return GachaPrizeConfig.EndGachaPrizeConfig(builder);
  }

  public static void StartGachaPrizeConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddPlayerLevel(FlatBufferBuilder builder, int playerLevel) { builder.AddInt(0, playerLevel, 0); }
  public static void AddSlots(FlatBufferBuilder builder, VectorOffset slotsOffset) { builder.AddOffset(1, slotsOffset.Value, 0); }
  public static VectorOffset CreateSlotsVector(FlatBufferBuilder builder, Offset<FBConfig.GachaSlotPrizesConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSlotsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.GachaSlotPrizesConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSlotsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.GachaSlotPrizesConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSlotsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.GachaSlotPrizesConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSlotsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPredicate(FlatBufferBuilder builder, StringOffset predicateOffset) { builder.AddOffset(2, predicateOffset.Value, 0); }
  public static Offset<FBConfig.GachaPrizeConfig> EndGachaPrizeConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GachaPrizeConfig>(o);
  }
  public GachaPrizeConfigT UnPack() {
    var _o = new GachaPrizeConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GachaPrizeConfigT _o) {
    _o.PlayerLevel = this.PlayerLevel;
    _o.Slots = new List<FBConfig.GachaSlotPrizesConfigT>();
    for (var _j = 0; _j < this.SlotsLength; ++_j) {_o.Slots.Add(this.Slots(_j).HasValue ? this.Slots(_j).Value.UnPack() : null);}
    _o.Predicate = this.Predicate;
  }
  public static Offset<FBConfig.GachaPrizeConfig> Pack(FlatBufferBuilder builder, GachaPrizeConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.GachaPrizeConfig>);
    var _slots = default(VectorOffset);
    if (_o.Slots != null) {
      var __slots = new Offset<FBConfig.GachaSlotPrizesConfig>[_o.Slots.Count];
      for (var _j = 0; _j < __slots.Length; ++_j) { __slots[_j] = FBConfig.GachaSlotPrizesConfig.Pack(builder, _o.Slots[_j]); }
      _slots = CreateSlotsVector(builder, __slots);
    }
    var _predicate = _o.Predicate == null ? default(StringOffset) : builder.CreateString(_o.Predicate);
    return CreateGachaPrizeConfig(
      builder,
      _o.PlayerLevel,
      _slots,
      _predicate);
  }
}

public class GachaPrizeConfigT
{
  public int PlayerLevel { get; set; }
  public List<FBConfig.GachaSlotPrizesConfigT> Slots { get; set; }
  public string Predicate { get; set; }

  public GachaPrizeConfigT() {
    this.PlayerLevel = 0;
    this.Slots = null;
    this.Predicate = null;
  }
}

public struct GachaConfig : IFlatbufferConfig<GachaConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GachaConfig GetRootAsGachaConfig(ByteBuffer _bb) { return GetRootAsGachaConfig(_bb, new GachaConfig()); }
  public static GachaConfig GetRootAsGachaConfig(ByteBuffer _bb, GachaConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GachaConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.GachaPrizeConfig? PrizesConfigFb(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.GachaPrizeConfig?)(new FBConfig.GachaPrizeConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PrizesConfigFbLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int FreeRollTimer { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateFreeRollTimer(int free_roll_timer) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, free_roll_timer); return true; } else { return false; } }
  public FBConfig.Price? SpinPriceFb { get { int o = __p.__offset(10); return o != 0 ? (FBConfig.Price?)(new FBConfig.Price()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public bool FixedPrice { get { int o = __p.__offset(12); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateFixedPrice(bool fixed_price) { int o = __p.__offset(12); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(fixed_price ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.GachaConfig> CreateGachaConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset prizes_config_fbOffset = default(VectorOffset),
      int free_roll_timer = 0,
      Offset<FBConfig.Price> spin_price_fbOffset = default(Offset<FBConfig.Price>),
      bool fixed_price = false) {
    builder.StartTable(5);
    GachaConfig.AddSpinPriceFb(builder, spin_price_fbOffset);
    GachaConfig.AddFreeRollTimer(builder, free_roll_timer);
    GachaConfig.AddPrizesConfigFb(builder, prizes_config_fbOffset);
    GachaConfig.AddUid(builder, uidOffset);
    GachaConfig.AddFixedPrice(builder, fixed_price);
    return GachaConfig.EndGachaConfig(builder);
  }

  public static void StartGachaConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddPrizesConfigFb(FlatBufferBuilder builder, VectorOffset prizesConfigFbOffset) { builder.AddOffset(1, prizesConfigFbOffset.Value, 0); }
  public static VectorOffset CreatePrizesConfigFbVector(FlatBufferBuilder builder, Offset<FBConfig.GachaPrizeConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePrizesConfigFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.GachaPrizeConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePrizesConfigFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.GachaPrizeConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePrizesConfigFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.GachaPrizeConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPrizesConfigFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddFreeRollTimer(FlatBufferBuilder builder, int freeRollTimer) { builder.AddInt(2, freeRollTimer, 0); }
  public static void AddSpinPriceFb(FlatBufferBuilder builder, Offset<FBConfig.Price> spinPriceFbOffset) { builder.AddOffset(3, spinPriceFbOffset.Value, 0); }
  public static void AddFixedPrice(FlatBufferBuilder builder, bool fixedPrice) { builder.AddBool(4, fixedPrice, false); }
  public static Offset<FBConfig.GachaConfig> EndGachaConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.GachaConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfGachaConfig(FlatBufferBuilder builder, Offset<GachaConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<GachaConfig> o1, Offset<GachaConfig> o2) =>
        new GachaConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new GachaConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static GachaConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    GachaConfig obj_ = new GachaConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public GachaConfigT UnPack() {
    var _o = new GachaConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GachaConfigT _o) {
    _o.Uid = this.Uid;
    _o.PrizesConfigFb = new List<FBConfig.GachaPrizeConfigT>();
    for (var _j = 0; _j < this.PrizesConfigFbLength; ++_j) {_o.PrizesConfigFb.Add(this.PrizesConfigFb(_j).HasValue ? this.PrizesConfigFb(_j).Value.UnPack() : null);}
    _o.FreeRollTimer = this.FreeRollTimer;
    _o.SpinPriceFb = this.SpinPriceFb.HasValue ? this.SpinPriceFb.Value.UnPack() : null;
    _o.FixedPrice = this.FixedPrice;
  }
  public static Offset<FBConfig.GachaConfig> Pack(FlatBufferBuilder builder, GachaConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.GachaConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _prizes_config_fb = default(VectorOffset);
    if (_o.PrizesConfigFb != null) {
      var __prizes_config_fb = new Offset<FBConfig.GachaPrizeConfig>[_o.PrizesConfigFb.Count];
      for (var _j = 0; _j < __prizes_config_fb.Length; ++_j) { __prizes_config_fb[_j] = FBConfig.GachaPrizeConfig.Pack(builder, _o.PrizesConfigFb[_j]); }
      _prizes_config_fb = CreatePrizesConfigFbVector(builder, __prizes_config_fb);
    }
    var _spin_price_fb = _o.SpinPriceFb == null ? default(Offset<FBConfig.Price>) : FBConfig.Price.Pack(builder, _o.SpinPriceFb);
    return CreateGachaConfig(
      builder,
      _uid,
      _prizes_config_fb,
      _o.FreeRollTimer,
      _spin_price_fb,
      _o.FixedPrice);
  }
}

public class GachaConfigT
{
  public string Uid { get; set; }
  public List<FBConfig.GachaPrizeConfigT> PrizesConfigFb { get; set; }
  public int FreeRollTimer { get; set; }
  public FBConfig.PriceT SpinPriceFb { get; set; }
  public bool FixedPrice { get; set; }

  public GachaConfigT() {
    this.Uid = null;
    this.PrizesConfigFb = null;
    this.FreeRollTimer = 0;
    this.SpinPriceFb = null;
    this.FixedPrice = false;
  }
}

public struct GachaConfigDict : IFlatbufferConfigDict<GachaConfig, GachaConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GachaConfigDict GetRootAsGachaConfigDict(ByteBuffer _bb) { return GetRootAsGachaConfigDict(_bb, new GachaConfigDict()); }
  public static GachaConfigDict GetRootAsGachaConfigDict(ByteBuffer _bb, GachaConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GachaConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.GachaConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.GachaConfig?)(new FBConfig.GachaConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.GachaConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.GachaConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.GachaConfigDict> CreateGachaConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    GachaConfigDict.AddValues(builder, valuesOffset);
    return GachaConfigDict.EndGachaConfigDict(builder);
  }

  public static void StartGachaConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.GachaConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.GachaConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.GachaConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.GachaConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.GachaConfigDict> EndGachaConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GachaConfigDict>(o);
  }
  public static void FinishGachaConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GachaConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedGachaConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.GachaConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public GachaConfigDictT UnPack() {
    var _o = new GachaConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GachaConfigDictT _o) {
    _o.Values = new List<FBConfig.GachaConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.GachaConfigDict> Pack(FlatBufferBuilder builder, GachaConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.GachaConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.GachaConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.GachaConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateGachaConfigDict(
      builder,
      _values);
  }
}

public class GachaConfigDictT
{
  public List<FBConfig.GachaConfigT> Values { get; set; }

  public GachaConfigDictT() {
    this.Values = null;
  }
  public static GachaConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return GachaConfigDict.GetRootAsGachaConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    GachaConfigDict.FinishGachaConfigDictBuffer(fbb, GachaConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
