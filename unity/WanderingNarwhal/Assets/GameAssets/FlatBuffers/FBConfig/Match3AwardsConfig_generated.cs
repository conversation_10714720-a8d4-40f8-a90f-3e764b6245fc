// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct Match3AwardsConfig : IFlatbufferConfig<Match3AwardsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Match3AwardsConfig GetRootAsMatch3AwardsConfig(ByteBuffer _bb) { return GetRootAsMatch3AwardsConfig(_bb, new Match3AwardsConfig()); }
  public static Match3AwardsConfig GetRootAsMatch3AwardsConfig(ByteBuffer _bb, Match3AwardsConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Match3AwardsConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public int MatchesComboCount { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMatchesComboCount(int matches_combo_count) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, matches_combo_count); return true; } else { return false; } }
  public float ChanceToPlay { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateChanceToPlay(float chance_to_play) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, chance_to_play); return true; } else { return false; } }

  public static Offset<FBConfig.Match3AwardsConfig> CreateMatch3AwardsConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      int matches_combo_count = 0,
      float chance_to_play = 0.0f) {
    builder.StartTable(3);
    Match3AwardsConfig.AddChanceToPlay(builder, chance_to_play);
    Match3AwardsConfig.AddMatchesComboCount(builder, matches_combo_count);
    Match3AwardsConfig.AddUid(builder, uidOffset);
    return Match3AwardsConfig.EndMatch3AwardsConfig(builder);
  }

  public static void StartMatch3AwardsConfig(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddMatchesComboCount(FlatBufferBuilder builder, int matchesComboCount) { builder.AddInt(1, matchesComboCount, 0); }
  public static void AddChanceToPlay(FlatBufferBuilder builder, float chanceToPlay) { builder.AddFloat(2, chanceToPlay, 0.0f); }
  public static Offset<FBConfig.Match3AwardsConfig> EndMatch3AwardsConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.Match3AwardsConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfMatch3AwardsConfig(FlatBufferBuilder builder, Offset<Match3AwardsConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<Match3AwardsConfig> o1, Offset<Match3AwardsConfig> o2) =>
        new Match3AwardsConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new Match3AwardsConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static Match3AwardsConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    Match3AwardsConfig obj_ = new Match3AwardsConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public Match3AwardsConfigT UnPack() {
    var _o = new Match3AwardsConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(Match3AwardsConfigT _o) {
    _o.Uid = this.Uid;
    _o.MatchesComboCount = this.MatchesComboCount;
    _o.ChanceToPlay = this.ChanceToPlay;
  }
  public static Offset<FBConfig.Match3AwardsConfig> Pack(FlatBufferBuilder builder, Match3AwardsConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.Match3AwardsConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    return CreateMatch3AwardsConfig(
      builder,
      _uid,
      _o.MatchesComboCount,
      _o.ChanceToPlay);
  }
}

public class Match3AwardsConfigT
{
  public string Uid { get; set; }
  public int MatchesComboCount { get; set; }
  public float ChanceToPlay { get; set; }

  public Match3AwardsConfigT() {
    this.Uid = null;
    this.MatchesComboCount = 0;
    this.ChanceToPlay = 0.0f;
  }
}

public struct Match3AwardsConfigDict : IFlatbufferConfigDict<Match3AwardsConfig, Match3AwardsConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static Match3AwardsConfigDict GetRootAsMatch3AwardsConfigDict(ByteBuffer _bb) { return GetRootAsMatch3AwardsConfigDict(_bb, new Match3AwardsConfigDict()); }
  public static Match3AwardsConfigDict GetRootAsMatch3AwardsConfigDict(ByteBuffer _bb, Match3AwardsConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public Match3AwardsConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.Match3AwardsConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.Match3AwardsConfig?)(new FBConfig.Match3AwardsConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.Match3AwardsConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.Match3AwardsConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.Match3AwardsConfigDict> CreateMatch3AwardsConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    Match3AwardsConfigDict.AddValues(builder, valuesOffset);
    return Match3AwardsConfigDict.EndMatch3AwardsConfigDict(builder);
  }

  public static void StartMatch3AwardsConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.Match3AwardsConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.Match3AwardsConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.Match3AwardsConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.Match3AwardsConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.Match3AwardsConfigDict> EndMatch3AwardsConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.Match3AwardsConfigDict>(o);
  }
  public static void FinishMatch3AwardsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.Match3AwardsConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedMatch3AwardsConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.Match3AwardsConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public Match3AwardsConfigDictT UnPack() {
    var _o = new Match3AwardsConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(Match3AwardsConfigDictT _o) {
    _o.Values = new List<FBConfig.Match3AwardsConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.Match3AwardsConfigDict> Pack(FlatBufferBuilder builder, Match3AwardsConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.Match3AwardsConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.Match3AwardsConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.Match3AwardsConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateMatch3AwardsConfigDict(
      builder,
      _values);
  }
}

public class Match3AwardsConfigDictT
{
  public List<FBConfig.Match3AwardsConfigT> Values { get; set; }

  public Match3AwardsConfigDictT() {
    this.Values = null;
  }
  public static Match3AwardsConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return Match3AwardsConfigDict.GetRootAsMatch3AwardsConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    Match3AwardsConfigDict.FinishMatch3AwardsConfigDictBuffer(fbb, Match3AwardsConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
