// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct OfferConfig : IFlatbufferConfig<OfferConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static OfferConfig GetRootAsOfferConfig(ByteBuffer _bb) { return GetRootAsOfferConfig(_bb, new OfferConfig()); }
  public static OfferConfig GetRootAsOfferConfig(ByteBuffer _bb, OfferConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public OfferConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Category { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(6); }
  public string Thumbnail { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetThumbnailBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetThumbnailBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetThumbnailArray() { return __p.__vector_as_array<byte>(8); }
  public int Order { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateOrder(int order) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, order); return true; } else { return false; } }
  public FBConfig.Price? PriceFb { get { int o = __p.__offset(12); return o != 0 ? (FBConfig.Price?)(new FBConfig.Price()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public int Amount { get { int o = __p.__offset(14); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateAmount(int amount) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, amount); return true; } else { return false; } }
  public bool IsCurrency { get { int o = __p.__offset(16); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateIsCurrency(bool is_currency) { int o = __p.__offset(16); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(is_currency ? 1 : 0)); return true; } else { return false; } }
  public string ExtraReward { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetExtraRewardBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetExtraRewardBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetExtraRewardArray() { return __p.__vector_as_array<byte>(18); }
  public FBConfig.DictIntInt? EscalatingMovesFb(int j) { int o = __p.__offset(20); return o != 0 ? (FBConfig.DictIntInt?)(new FBConfig.DictIntInt()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int EscalatingMovesFbLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.OfferConfig> CreateOfferConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      StringOffset categoryOffset = default(StringOffset),
      StringOffset thumbnailOffset = default(StringOffset),
      int order = 0,
      Offset<FBConfig.Price> price_fbOffset = default(Offset<FBConfig.Price>),
      int amount = 0,
      bool is_currency = false,
      StringOffset extra_rewardOffset = default(StringOffset),
      VectorOffset escalating_moves_fbOffset = default(VectorOffset)) {
    builder.StartTable(9);
    OfferConfig.AddEscalatingMovesFb(builder, escalating_moves_fbOffset);
    OfferConfig.AddExtraReward(builder, extra_rewardOffset);
    OfferConfig.AddAmount(builder, amount);
    OfferConfig.AddPriceFb(builder, price_fbOffset);
    OfferConfig.AddOrder(builder, order);
    OfferConfig.AddThumbnail(builder, thumbnailOffset);
    OfferConfig.AddCategory(builder, categoryOffset);
    OfferConfig.AddUid(builder, uidOffset);
    OfferConfig.AddIsCurrency(builder, is_currency);
    return OfferConfig.EndOfferConfig(builder);
  }

  public static void StartOfferConfig(FlatBufferBuilder builder) { builder.StartTable(9); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(1, categoryOffset.Value, 0); }
  public static void AddThumbnail(FlatBufferBuilder builder, StringOffset thumbnailOffset) { builder.AddOffset(2, thumbnailOffset.Value, 0); }
  public static void AddOrder(FlatBufferBuilder builder, int order) { builder.AddInt(3, order, 0); }
  public static void AddPriceFb(FlatBufferBuilder builder, Offset<FBConfig.Price> priceFbOffset) { builder.AddOffset(4, priceFbOffset.Value, 0); }
  public static void AddAmount(FlatBufferBuilder builder, int amount) { builder.AddInt(5, amount, 0); }
  public static void AddIsCurrency(FlatBufferBuilder builder, bool isCurrency) { builder.AddBool(6, isCurrency, false); }
  public static void AddExtraReward(FlatBufferBuilder builder, StringOffset extraRewardOffset) { builder.AddOffset(7, extraRewardOffset.Value, 0); }
  public static void AddEscalatingMovesFb(FlatBufferBuilder builder, VectorOffset escalatingMovesFbOffset) { builder.AddOffset(8, escalatingMovesFbOffset.Value, 0); }
  public static VectorOffset CreateEscalatingMovesFbVector(FlatBufferBuilder builder, Offset<FBConfig.DictIntInt>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateEscalatingMovesFbVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictIntInt>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEscalatingMovesFbVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictIntInt>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateEscalatingMovesFbVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictIntInt>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartEscalatingMovesFbVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.OfferConfig> EndOfferConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.OfferConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfOfferConfig(FlatBufferBuilder builder, Offset<OfferConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<OfferConfig> o1, Offset<OfferConfig> o2) =>
        new OfferConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new OfferConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static OfferConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    OfferConfig obj_ = new OfferConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public OfferConfigT UnPack() {
    var _o = new OfferConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(OfferConfigT _o) {
    _o.Uid = this.Uid;
    _o.Category = this.Category;
    _o.Thumbnail = this.Thumbnail;
    _o.Order = this.Order;
    _o.PriceFb = this.PriceFb.HasValue ? this.PriceFb.Value.UnPack() : null;
    _o.Amount = this.Amount;
    _o.IsCurrency = this.IsCurrency;
    _o.ExtraReward = this.ExtraReward;
    _o.EscalatingMovesFb = new List<FBConfig.DictIntIntT>();
    for (var _j = 0; _j < this.EscalatingMovesFbLength; ++_j) {_o.EscalatingMovesFb.Add(this.EscalatingMovesFb(_j).HasValue ? this.EscalatingMovesFb(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.OfferConfig> Pack(FlatBufferBuilder builder, OfferConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.OfferConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    var _thumbnail = _o.Thumbnail == null ? default(StringOffset) : builder.CreateString(_o.Thumbnail);
    var _price_fb = _o.PriceFb == null ? default(Offset<FBConfig.Price>) : FBConfig.Price.Pack(builder, _o.PriceFb);
    var _extra_reward = _o.ExtraReward == null ? default(StringOffset) : builder.CreateString(_o.ExtraReward);
    var _escalating_moves_fb = default(VectorOffset);
    if (_o.EscalatingMovesFb != null) {
      var __escalating_moves_fb = new Offset<FBConfig.DictIntInt>[_o.EscalatingMovesFb.Count];
      for (var _j = 0; _j < __escalating_moves_fb.Length; ++_j) { __escalating_moves_fb[_j] = FBConfig.DictIntInt.Pack(builder, _o.EscalatingMovesFb[_j]); }
      _escalating_moves_fb = CreateEscalatingMovesFbVector(builder, __escalating_moves_fb);
    }
    return CreateOfferConfig(
      builder,
      _uid,
      _category,
      _thumbnail,
      _o.Order,
      _price_fb,
      _o.Amount,
      _o.IsCurrency,
      _extra_reward,
      _escalating_moves_fb);
  }
}

public class OfferConfigT
{
  public string Uid { get; set; }
  public string Category { get; set; }
  public string Thumbnail { get; set; }
  public int Order { get; set; }
  public FBConfig.PriceT PriceFb { get; set; }
  public int Amount { get; set; }
  public bool IsCurrency { get; set; }
  public string ExtraReward { get; set; }
  public List<FBConfig.DictIntIntT> EscalatingMovesFb { get; set; }

  public OfferConfigT() {
    this.Uid = null;
    this.Category = null;
    this.Thumbnail = null;
    this.Order = 0;
    this.PriceFb = null;
    this.Amount = 0;
    this.IsCurrency = false;
    this.ExtraReward = null;
    this.EscalatingMovesFb = null;
  }
}

public struct OfferConfigDict : IFlatbufferConfigDict<OfferConfig, OfferConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static OfferConfigDict GetRootAsOfferConfigDict(ByteBuffer _bb) { return GetRootAsOfferConfigDict(_bb, new OfferConfigDict()); }
  public static OfferConfigDict GetRootAsOfferConfigDict(ByteBuffer _bb, OfferConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public OfferConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.OfferConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.OfferConfig?)(new FBConfig.OfferConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.OfferConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.OfferConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.OfferConfigDict> CreateOfferConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    OfferConfigDict.AddValues(builder, valuesOffset);
    return OfferConfigDict.EndOfferConfigDict(builder);
  }

  public static void StartOfferConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.OfferConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.OfferConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.OfferConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.OfferConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.OfferConfigDict> EndOfferConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.OfferConfigDict>(o);
  }
  public static void FinishOfferConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.OfferConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedOfferConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.OfferConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public OfferConfigDictT UnPack() {
    var _o = new OfferConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(OfferConfigDictT _o) {
    _o.Values = new List<FBConfig.OfferConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.OfferConfigDict> Pack(FlatBufferBuilder builder, OfferConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.OfferConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.OfferConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.OfferConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateOfferConfigDict(
      builder,
      _values);
  }
}

public class OfferConfigDictT
{
  public List<FBConfig.OfferConfigT> Values { get; set; }

  public OfferConfigDictT() {
    this.Values = null;
  }
  public static OfferConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return OfferConfigDict.GetRootAsOfferConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    OfferConfigDict.FinishOfferConfigDictBuffer(fbb, OfferConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
