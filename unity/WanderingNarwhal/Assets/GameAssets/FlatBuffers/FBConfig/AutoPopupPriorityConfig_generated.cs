// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct PriorityConfig : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static PriorityConfig GetRootAsPriorityConfig(ByteBuffer _bb) { return GetRootAsPriorityConfig(_bb, new PriorityConfig()); }
  public static PriorityConfig GetRootAsPriorityConfig(ByteBuffer _bb, PriorityConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public PriorityConfig __assign(int _i, Byte<PERSON>uffer _bb) { __init(_i, _bb); return this; }

  public string HandlerName { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHandlerNameBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetHandlerNameBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetHandlerNameArray() { return __p.__vector_as_array<byte>(4); }
  public int Priority { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePriority(int priority) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, priority); return true; } else { return false; } }
  public int MinimumScreenVisits { get { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMinimumScreenVisits(int minimum_screen_visits) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, minimum_screen_visits); return true; } else { return false; } }
  public string AutoPopupCondition { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAutoPopupConditionBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetAutoPopupConditionBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetAutoPopupConditionArray() { return __p.__vector_as_array<byte>(10); }
  public bool CountTowardsLimit { get { int o = __p.__offset(12); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateCountTowardsLimit(bool count_towards_limit) { int o = __p.__offset(12); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(count_towards_limit ? 1 : 0)); return true; } else { return false; } }
  public bool HighPriority { get { int o = __p.__offset(14); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateHighPriority(bool high_priority) { int o = __p.__offset(14); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(high_priority ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.PriorityConfig> CreatePriorityConfig(FlatBufferBuilder builder,
      StringOffset handler_nameOffset = default(StringOffset),
      int priority = 0,
      int minimum_screen_visits = 0,
      StringOffset auto_popup_conditionOffset = default(StringOffset),
      bool count_towards_limit = false,
      bool high_priority = false) {
    builder.StartTable(6);
    PriorityConfig.AddAutoPopupCondition(builder, auto_popup_conditionOffset);
    PriorityConfig.AddMinimumScreenVisits(builder, minimum_screen_visits);
    PriorityConfig.AddPriority(builder, priority);
    PriorityConfig.AddHandlerName(builder, handler_nameOffset);
    PriorityConfig.AddHighPriority(builder, high_priority);
    PriorityConfig.AddCountTowardsLimit(builder, count_towards_limit);
    return PriorityConfig.EndPriorityConfig(builder);
  }

  public static void StartPriorityConfig(FlatBufferBuilder builder) { builder.StartTable(6); }
  public static void AddHandlerName(FlatBufferBuilder builder, StringOffset handlerNameOffset) { builder.AddOffset(0, handlerNameOffset.Value, 0); }
  public static void AddPriority(FlatBufferBuilder builder, int priority) { builder.AddInt(1, priority, 0); }
  public static void AddMinimumScreenVisits(FlatBufferBuilder builder, int minimumScreenVisits) { builder.AddInt(2, minimumScreenVisits, 0); }
  public static void AddAutoPopupCondition(FlatBufferBuilder builder, StringOffset autoPopupConditionOffset) { builder.AddOffset(3, autoPopupConditionOffset.Value, 0); }
  public static void AddCountTowardsLimit(FlatBufferBuilder builder, bool countTowardsLimit) { builder.AddBool(4, countTowardsLimit, false); }
  public static void AddHighPriority(FlatBufferBuilder builder, bool highPriority) { builder.AddBool(5, highPriority, false); }
  public static Offset<FBConfig.PriorityConfig> EndPriorityConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.PriorityConfig>(o);
  }
  public PriorityConfigT UnPack() {
    var _o = new PriorityConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(PriorityConfigT _o) {
    _o.HandlerName = this.HandlerName;
    _o.Priority = this.Priority;
    _o.MinimumScreenVisits = this.MinimumScreenVisits;
    _o.AutoPopupCondition = this.AutoPopupCondition;
    _o.CountTowardsLimit = this.CountTowardsLimit;
    _o.HighPriority = this.HighPriority;
  }
  public static Offset<FBConfig.PriorityConfig> Pack(FlatBufferBuilder builder, PriorityConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.PriorityConfig>);
    var _handler_name = _o.HandlerName == null ? default(StringOffset) : builder.CreateString(_o.HandlerName);
    var _auto_popup_condition = _o.AutoPopupCondition == null ? default(StringOffset) : builder.CreateString(_o.AutoPopupCondition);
    return CreatePriorityConfig(
      builder,
      _handler_name,
      _o.Priority,
      _o.MinimumScreenVisits,
      _auto_popup_condition,
      _o.CountTowardsLimit,
      _o.HighPriority);
  }
}

public class PriorityConfigT
{
  public string HandlerName { get; set; }
  public int Priority { get; set; }
  public int MinimumScreenVisits { get; set; }
  public string AutoPopupCondition { get; set; }
  public bool CountTowardsLimit { get; set; }
  public bool HighPriority { get; set; }

  public PriorityConfigT() {
    this.HandlerName = null;
    this.Priority = 0;
    this.MinimumScreenVisits = 0;
    this.AutoPopupCondition = null;
    this.CountTowardsLimit = false;
    this.HighPriority = false;
  }
}

public struct AutoPopupPriorityConfig : IFlatbufferConfig<AutoPopupPriorityConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AutoPopupPriorityConfig GetRootAsAutoPopupPriorityConfig(ByteBuffer _bb) { return GetRootAsAutoPopupPriorityConfig(_bb, new AutoPopupPriorityConfig()); }
  public static AutoPopupPriorityConfig GetRootAsAutoPopupPriorityConfig(ByteBuffer _bb, AutoPopupPriorityConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AutoPopupPriorityConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public string Screens(int j) { int o = __p.__offset(6); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int ScreensLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.PriorityConfig? Priorities(int j) { int o = __p.__offset(8); return o != 0 ? (FBConfig.PriorityConfig?)(new FBConfig.PriorityConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int PrioritiesLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int MaxAutoPopupsInARow { get { int o = __p.__offset(10); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateMaxAutoPopupsInARow(int max_auto_popups_in_a_row) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, max_auto_popups_in_a_row); return true; } else { return false; } }
  public bool AllowSelfDefinedPromotionsPriority { get { int o = __p.__offset(12); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateAllowSelfDefinedPromotionsPriority(bool allow_self_defined_promotions_priority) { int o = __p.__offset(12); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(allow_self_defined_promotions_priority ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.AutoPopupPriorityConfig> CreateAutoPopupPriorityConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      VectorOffset screensOffset = default(VectorOffset),
      VectorOffset prioritiesOffset = default(VectorOffset),
      int max_auto_popups_in_a_row = 0,
      bool allow_self_defined_promotions_priority = false) {
    builder.StartTable(5);
    AutoPopupPriorityConfig.AddMaxAutoPopupsInARow(builder, max_auto_popups_in_a_row);
    AutoPopupPriorityConfig.AddPriorities(builder, prioritiesOffset);
    AutoPopupPriorityConfig.AddScreens(builder, screensOffset);
    AutoPopupPriorityConfig.AddUid(builder, uidOffset);
    AutoPopupPriorityConfig.AddAllowSelfDefinedPromotionsPriority(builder, allow_self_defined_promotions_priority);
    return AutoPopupPriorityConfig.EndAutoPopupPriorityConfig(builder);
  }

  public static void StartAutoPopupPriorityConfig(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddScreens(FlatBufferBuilder builder, VectorOffset screensOffset) { builder.AddOffset(1, screensOffset.Value, 0); }
  public static VectorOffset CreateScreensVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateScreensVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScreensVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScreensVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartScreensVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddPriorities(FlatBufferBuilder builder, VectorOffset prioritiesOffset) { builder.AddOffset(2, prioritiesOffset.Value, 0); }
  public static VectorOffset CreatePrioritiesVector(FlatBufferBuilder builder, Offset<FBConfig.PriorityConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreatePrioritiesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.PriorityConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePrioritiesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.PriorityConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreatePrioritiesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.PriorityConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartPrioritiesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddMaxAutoPopupsInARow(FlatBufferBuilder builder, int maxAutoPopupsInARow) { builder.AddInt(3, maxAutoPopupsInARow, 0); }
  public static void AddAllowSelfDefinedPromotionsPriority(FlatBufferBuilder builder, bool allowSelfDefinedPromotionsPriority) { builder.AddBool(4, allowSelfDefinedPromotionsPriority, false); }
  public static Offset<FBConfig.AutoPopupPriorityConfig> EndAutoPopupPriorityConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.AutoPopupPriorityConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfAutoPopupPriorityConfig(FlatBufferBuilder builder, Offset<AutoPopupPriorityConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<AutoPopupPriorityConfig> o1, Offset<AutoPopupPriorityConfig> o2) =>
        new AutoPopupPriorityConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new AutoPopupPriorityConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static AutoPopupPriorityConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    AutoPopupPriorityConfig obj_ = new AutoPopupPriorityConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public AutoPopupPriorityConfigT UnPack() {
    var _o = new AutoPopupPriorityConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AutoPopupPriorityConfigT _o) {
    _o.Uid = this.Uid;
    _o.Screens = new List<string>();
    for (var _j = 0; _j < this.ScreensLength; ++_j) {_o.Screens.Add(this.Screens(_j));}
    _o.Priorities = new List<FBConfig.PriorityConfigT>();
    for (var _j = 0; _j < this.PrioritiesLength; ++_j) {_o.Priorities.Add(this.Priorities(_j).HasValue ? this.Priorities(_j).Value.UnPack() : null);}
    _o.MaxAutoPopupsInARow = this.MaxAutoPopupsInARow;
    _o.AllowSelfDefinedPromotionsPriority = this.AllowSelfDefinedPromotionsPriority;
  }
  public static Offset<FBConfig.AutoPopupPriorityConfig> Pack(FlatBufferBuilder builder, AutoPopupPriorityConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.AutoPopupPriorityConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _screens = default(VectorOffset);
    if (_o.Screens != null) {
      var __screens = new StringOffset[_o.Screens.Count];
      for (var _j = 0; _j < __screens.Length; ++_j) { __screens[_j] = builder.CreateString(_o.Screens[_j]); }
      _screens = CreateScreensVector(builder, __screens);
    }
    var _priorities = default(VectorOffset);
    if (_o.Priorities != null) {
      var __priorities = new Offset<FBConfig.PriorityConfig>[_o.Priorities.Count];
      for (var _j = 0; _j < __priorities.Length; ++_j) { __priorities[_j] = FBConfig.PriorityConfig.Pack(builder, _o.Priorities[_j]); }
      _priorities = CreatePrioritiesVector(builder, __priorities);
    }
    return CreateAutoPopupPriorityConfig(
      builder,
      _uid,
      _screens,
      _priorities,
      _o.MaxAutoPopupsInARow,
      _o.AllowSelfDefinedPromotionsPriority);
  }
}

public class AutoPopupPriorityConfigT
{
  public string Uid { get; set; }
  public List<string> Screens { get; set; }
  public List<FBConfig.PriorityConfigT> Priorities { get; set; }
  public int MaxAutoPopupsInARow { get; set; }
  public bool AllowSelfDefinedPromotionsPriority { get; set; }

  public AutoPopupPriorityConfigT() {
    this.Uid = null;
    this.Screens = null;
    this.Priorities = null;
    this.MaxAutoPopupsInARow = 0;
    this.AllowSelfDefinedPromotionsPriority = false;
  }
}

public struct AutoPopupPriorityConfigDict : IFlatbufferConfigDict<AutoPopupPriorityConfig, AutoPopupPriorityConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static AutoPopupPriorityConfigDict GetRootAsAutoPopupPriorityConfigDict(ByteBuffer _bb) { return GetRootAsAutoPopupPriorityConfigDict(_bb, new AutoPopupPriorityConfigDict()); }
  public static AutoPopupPriorityConfigDict GetRootAsAutoPopupPriorityConfigDict(ByteBuffer _bb, AutoPopupPriorityConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public AutoPopupPriorityConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.AutoPopupPriorityConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.AutoPopupPriorityConfig?)(new FBConfig.AutoPopupPriorityConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.AutoPopupPriorityConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.AutoPopupPriorityConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.AutoPopupPriorityConfigDict> CreateAutoPopupPriorityConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    AutoPopupPriorityConfigDict.AddValues(builder, valuesOffset);
    return AutoPopupPriorityConfigDict.EndAutoPopupPriorityConfigDict(builder);
  }

  public static void StartAutoPopupPriorityConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.AutoPopupPriorityConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.AutoPopupPriorityConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.AutoPopupPriorityConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.AutoPopupPriorityConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.AutoPopupPriorityConfigDict> EndAutoPopupPriorityConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.AutoPopupPriorityConfigDict>(o);
  }
  public static void FinishAutoPopupPriorityConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AutoPopupPriorityConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedAutoPopupPriorityConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.AutoPopupPriorityConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public AutoPopupPriorityConfigDictT UnPack() {
    var _o = new AutoPopupPriorityConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(AutoPopupPriorityConfigDictT _o) {
    _o.Values = new List<FBConfig.AutoPopupPriorityConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.AutoPopupPriorityConfigDict> Pack(FlatBufferBuilder builder, AutoPopupPriorityConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.AutoPopupPriorityConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.AutoPopupPriorityConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.AutoPopupPriorityConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateAutoPopupPriorityConfigDict(
      builder,
      _values);
  }
}

public class AutoPopupPriorityConfigDictT
{
  public List<FBConfig.AutoPopupPriorityConfigT> Values { get; set; }

  public AutoPopupPriorityConfigDictT() {
    this.Values = null;
  }
  public static AutoPopupPriorityConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return AutoPopupPriorityConfigDict.GetRootAsAutoPopupPriorityConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    AutoPopupPriorityConfigDict.FinishAutoPopupPriorityConfigDictBuffer(fbb, AutoPopupPriorityConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
