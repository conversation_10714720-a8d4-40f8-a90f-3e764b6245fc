// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

namespace FBConfig
{

using global::System;
using global::System.Collections.Generic;
using global::Google.FlatBuffers;

public struct CategoryParams : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static CategoryParams GetRootAsCategoryParams(ByteBuffer _bb) { return GetRootAsCategoryParams(_bb, new CategoryParams()); }
  public static CategoryParams GetRootAsCategoryParams(ByteBuffer _bb, CategoryParams obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public CategoryParams __assign(int _i, Byte<PERSON>uff<PERSON> _bb) { __init(_i, _bb); return this; }

  public string Category { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetCategoryBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetCategoryBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetCategoryArray() { return __p.__vector_as_array<byte>(4); }
  public int Priority { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePriority(int priority) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, priority); return true; } else { return false; } }
  public bool AllowMultiple { get { int o = __p.__offset(8); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateAllowMultiple(bool allow_multiple) { int o = __p.__offset(8); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(allow_multiple ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.CategoryParams> CreateCategoryParams(FlatBufferBuilder builder,
      StringOffset categoryOffset = default(StringOffset),
      int priority = 0,
      bool allow_multiple = false) {
    builder.StartTable(3);
    CategoryParams.AddPriority(builder, priority);
    CategoryParams.AddCategory(builder, categoryOffset);
    CategoryParams.AddAllowMultiple(builder, allow_multiple);
    return CategoryParams.EndCategoryParams(builder);
  }

  public static void StartCategoryParams(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddCategory(FlatBufferBuilder builder, StringOffset categoryOffset) { builder.AddOffset(0, categoryOffset.Value, 0); }
  public static void AddPriority(FlatBufferBuilder builder, int priority) { builder.AddInt(1, priority, 0); }
  public static void AddAllowMultiple(FlatBufferBuilder builder, bool allowMultiple) { builder.AddBool(2, allowMultiple, false); }
  public static Offset<FBConfig.CategoryParams> EndCategoryParams(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.CategoryParams>(o);
  }
  public CategoryParamsT UnPack() {
    var _o = new CategoryParamsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(CategoryParamsT _o) {
    _o.Category = this.Category;
    _o.Priority = this.Priority;
    _o.AllowMultiple = this.AllowMultiple;
  }
  public static Offset<FBConfig.CategoryParams> Pack(FlatBufferBuilder builder, CategoryParamsT _o) {
    if (_o == null) return default(Offset<FBConfig.CategoryParams>);
    var _category = _o.Category == null ? default(StringOffset) : builder.CreateString(_o.Category);
    return CreateCategoryParams(
      builder,
      _category,
      _o.Priority,
      _o.AllowMultiple);
  }
}

public class CategoryParamsT
{
  public string Category { get; set; }
  public int Priority { get; set; }
  public bool AllowMultiple { get; set; }

  public CategoryParamsT() {
    this.Category = null;
    this.Priority = 0;
    this.AllowMultiple = false;
  }
}

public struct ActionTuple : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ActionTuple GetRootAsActionTuple(ByteBuffer _bb) { return GetRootAsActionTuple(_bb, new ActionTuple()); }
  public static ActionTuple GetRootAsActionTuple(ByteBuffer _bb, ActionTuple obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ActionTuple __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Action { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetActionBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetActionBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetActionArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.DictStringString? Params(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.DictStringString?)(new FBConfig.DictStringString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ParamsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public bool ExecuteViaHud { get { int o = __p.__offset(8); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateExecuteViaHud(bool execute_via_hud) { int o = __p.__offset(8); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(execute_via_hud ? 1 : 0)); return true; } else { return false; } }

  public static Offset<FBConfig.ActionTuple> CreateActionTuple(FlatBufferBuilder builder,
      StringOffset actionOffset = default(StringOffset),
      VectorOffset @paramsOffset = default(VectorOffset),
      bool execute_via_hud = false) {
    builder.StartTable(3);
    ActionTuple.AddParams(builder, @paramsOffset);
    ActionTuple.AddAction(builder, actionOffset);
    ActionTuple.AddExecuteViaHud(builder, execute_via_hud);
    return ActionTuple.EndActionTuple(builder);
  }

  public static void StartActionTuple(FlatBufferBuilder builder) { builder.StartTable(3); }
  public static void AddAction(FlatBufferBuilder builder, StringOffset actionOffset) { builder.AddOffset(0, actionOffset.Value, 0); }
  public static void AddParams(FlatBufferBuilder builder, VectorOffset paramsOffset) { builder.AddOffset(1, paramsOffset.Value, 0); }
  public static VectorOffset CreateParamsVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateParamsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateParamsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateParamsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartParamsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddExecuteViaHud(FlatBufferBuilder builder, bool executeViaHud) { builder.AddBool(2, executeViaHud, false); }
  public static Offset<FBConfig.ActionTuple> EndActionTuple(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ActionTuple>(o);
  }
  public ActionTupleT UnPack() {
    var _o = new ActionTupleT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ActionTupleT _o) {
    _o.Action = this.Action;
    _o.Params = new List<FBConfig.DictStringStringT>();
    for (var _j = 0; _j < this.ParamsLength; ++_j) {_o.Params.Add(this.Params(_j).HasValue ? this.Params(_j).Value.UnPack() : null);}
    _o.ExecuteViaHud = this.ExecuteViaHud;
  }
  public static Offset<FBConfig.ActionTuple> Pack(FlatBufferBuilder builder, ActionTupleT _o) {
    if (_o == null) return default(Offset<FBConfig.ActionTuple>);
    var _action = _o.Action == null ? default(StringOffset) : builder.CreateString(_o.Action);
    var _params = default(VectorOffset);
    if (_o.Params != null) {
      var __params = new Offset<FBConfig.DictStringString>[_o.Params.Count];
      for (var _j = 0; _j < __params.Length; ++_j) { __params[_j] = FBConfig.DictStringString.Pack(builder, _o.Params[_j]); }
      _params = CreateParamsVector(builder, __params);
    }
    return CreateActionTuple(
      builder,
      _action,
      _params,
      _o.ExecuteViaHud);
  }
}

public class ActionTupleT
{
  public string Action { get; set; }
  public List<FBConfig.DictStringStringT> Params { get; set; }
  public bool ExecuteViaHud { get; set; }

  public ActionTupleT() {
    this.Action = null;
    this.Params = null;
    this.ExecuteViaHud = false;
  }
}

public struct ActionItem : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static ActionItem GetRootAsActionItem(ByteBuffer _bb) { return GetRootAsActionItem(_bb, new ActionItem()); }
  public static ActionItem GetRootAsActionItem(ByteBuffer _bb, ActionItem obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ActionItem __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Action { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetActionBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetActionBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetActionArray() { return __p.__vector_as_array<byte>(4); }
  public FBConfig.DictStringString? Params(int j) { int o = __p.__offset(6); return o != 0 ? (FBConfig.DictStringString?)(new FBConfig.DictStringString()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ParamsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<FBConfig.ActionItem> CreateActionItem(FlatBufferBuilder builder,
      StringOffset actionOffset = default(StringOffset),
      VectorOffset @paramsOffset = default(VectorOffset)) {
    builder.StartTable(2);
    ActionItem.AddParams(builder, @paramsOffset);
    ActionItem.AddAction(builder, actionOffset);
    return ActionItem.EndActionItem(builder);
  }

  public static void StartActionItem(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddAction(FlatBufferBuilder builder, StringOffset actionOffset) { builder.AddOffset(0, actionOffset.Value, 0); }
  public static void AddParams(FlatBufferBuilder builder, VectorOffset paramsOffset) { builder.AddOffset(1, paramsOffset.Value, 0); }
  public static VectorOffset CreateParamsVector(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateParamsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.DictStringString>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateParamsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.DictStringString>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateParamsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.DictStringString>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartParamsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.ActionItem> EndActionItem(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.ActionItem>(o);
  }
  public ActionItemT UnPack() {
    var _o = new ActionItemT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(ActionItemT _o) {
    _o.Action = this.Action;
    _o.Params = new List<FBConfig.DictStringStringT>();
    for (var _j = 0; _j < this.ParamsLength; ++_j) {_o.Params.Add(this.Params(_j).HasValue ? this.Params(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.ActionItem> Pack(FlatBufferBuilder builder, ActionItemT _o) {
    if (_o == null) return default(Offset<FBConfig.ActionItem>);
    var _action = _o.Action == null ? default(StringOffset) : builder.CreateString(_o.Action);
    var _params = default(VectorOffset);
    if (_o.Params != null) {
      var __params = new Offset<FBConfig.DictStringString>[_o.Params.Count];
      for (var _j = 0; _j < __params.Length; ++_j) { __params[_j] = FBConfig.DictStringString.Pack(builder, _o.Params[_j]); }
      _params = CreateParamsVector(builder, __params);
    }
    return CreateActionItem(
      builder,
      _action,
      _params);
  }
}

public class ActionItemT
{
  public string Action { get; set; }
  public List<FBConfig.DictStringStringT> Params { get; set; }

  public ActionItemT() {
    this.Action = null;
    this.Params = null;
  }
}

public struct HudSettings : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static HudSettings GetRootAsHudSettings(ByteBuffer _bb) { return GetRootAsHudSettings(_bb, new HudSettings()); }
  public static HudSettings GetRootAsHudSettings(ByteBuffer _bb, HudSettings obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public HudSettings __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string ForegroundImageUrl { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetForegroundImageUrlBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetForegroundImageUrlBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetForegroundImageUrlArray() { return __p.__vector_as_array<byte>(4); }
  public string ForegroundLocalImage { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetForegroundLocalImageBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetForegroundLocalImageBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetForegroundLocalImageArray() { return __p.__vector_as_array<byte>(6); }
  public float ForegroundAnchors(int j) { int o = __p.__offset(8); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int ForegroundAnchorsLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetForegroundAnchorsBytes() { return __p.__vector_as_span<float>(8, 4); }
#else
  public ArraySegment<byte>? GetForegroundAnchorsBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public float[] GetForegroundAnchorsArray() { return __p.__vector_as_array<float>(8); }
  public bool MutateForegroundAnchors(int j, float foreground_anchors) { int o = __p.__offset(8); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, foreground_anchors); return true; } else { return false; } }
  public string BackgroundImageUrl { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBackgroundImageUrlBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetBackgroundImageUrlBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetBackgroundImageUrlArray() { return __p.__vector_as_array<byte>(10); }
  public string BackgroundLocalImage { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBackgroundLocalImageBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetBackgroundLocalImageBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetBackgroundLocalImageArray() { return __p.__vector_as_array<byte>(12); }
  public float BackgroundAnchors(int j) { int o = __p.__offset(14); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int BackgroundAnchorsLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetBackgroundAnchorsBytes() { return __p.__vector_as_span<float>(14, 4); }
#else
  public ArraySegment<byte>? GetBackgroundAnchorsBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public float[] GetBackgroundAnchorsArray() { return __p.__vector_as_array<float>(14); }
  public bool MutateBackgroundAnchors(int j, float background_anchors) { int o = __p.__offset(14); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, background_anchors); return true; } else { return false; } }
  public string SubtitleLocalizationUid { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubtitleLocalizationUidBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetSubtitleLocalizationUidBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetSubtitleLocalizationUidArray() { return __p.__vector_as_array<byte>(16); }
  public bool HideTimer { get { int o = __p.__offset(18); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateHideTimer(bool hide_timer) { int o = __p.__offset(18); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(hide_timer ? 1 : 0)); return true; } else { return false; } }
  public string Screens(int j) { int o = __p.__offset(20); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int ScreensLength { get { int o = __p.__offset(20); return o != 0 ? __p.__vector_len(o) : 0; } }
  public bool HideButton { get { int o = __p.__offset(22); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateHideButton(bool hide_button) { int o = __p.__offset(22); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(hide_button ? 1 : 0)); return true; } else { return false; } }
  public float TimerAnchoring(int j) { int o = __p.__offset(24); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int TimerAnchoringLength { get { int o = __p.__offset(24); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetTimerAnchoringBytes() { return __p.__vector_as_span<float>(24, 4); }
#else
  public ArraySegment<byte>? GetTimerAnchoringBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public float[] GetTimerAnchoringArray() { return __p.__vector_as_array<float>(24); }
  public bool MutateTimerAnchoring(int j, float timer_anchoring) { int o = __p.__offset(24); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, timer_anchoring); return true; } else { return false; } }
  public string TimerColor { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTimerColorBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetTimerColorBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetTimerColorArray() { return __p.__vector_as_array<byte>(26); }
  public float OverridenHeight { get { int o = __p.__offset(28); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateOverridenHeight(float overriden_height) { int o = __p.__offset(28); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, overriden_height); return true; } else { return false; } }
  public bool HideHudIcon { get { int o = __p.__offset(30); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateHideHudIcon(bool hide_hud_icon) { int o = __p.__offset(30); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(hide_hud_icon ? 1 : 0)); return true; } else { return false; } }
  public string SubtitleBgColor { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubtitleBgColorBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetSubtitleBgColorBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetSubtitleBgColorArray() { return __p.__vector_as_array<byte>(32); }
  public string MainBadgeLocalizationUid { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMainBadgeLocalizationUidBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetMainBadgeLocalizationUidBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetMainBadgeLocalizationUidArray() { return __p.__vector_as_array<byte>(34); }

  public static Offset<FBConfig.HudSettings> CreateHudSettings(FlatBufferBuilder builder,
      StringOffset foreground_image_urlOffset = default(StringOffset),
      StringOffset foreground_local_imageOffset = default(StringOffset),
      VectorOffset foreground_anchorsOffset = default(VectorOffset),
      StringOffset background_image_urlOffset = default(StringOffset),
      StringOffset background_local_imageOffset = default(StringOffset),
      VectorOffset background_anchorsOffset = default(VectorOffset),
      StringOffset subtitle_localization_uidOffset = default(StringOffset),
      bool hide_timer = false,
      VectorOffset screensOffset = default(VectorOffset),
      bool hide_button = false,
      VectorOffset timer_anchoringOffset = default(VectorOffset),
      StringOffset timer_colorOffset = default(StringOffset),
      float overriden_height = 0.0f,
      bool hide_hud_icon = false,
      StringOffset subtitle_bg_colorOffset = default(StringOffset),
      StringOffset main_badge_localization_uidOffset = default(StringOffset)) {
    builder.StartTable(16);
    HudSettings.AddMainBadgeLocalizationUid(builder, main_badge_localization_uidOffset);
    HudSettings.AddSubtitleBgColor(builder, subtitle_bg_colorOffset);
    HudSettings.AddOverridenHeight(builder, overriden_height);
    HudSettings.AddTimerColor(builder, timer_colorOffset);
    HudSettings.AddTimerAnchoring(builder, timer_anchoringOffset);
    HudSettings.AddScreens(builder, screensOffset);
    HudSettings.AddSubtitleLocalizationUid(builder, subtitle_localization_uidOffset);
    HudSettings.AddBackgroundAnchors(builder, background_anchorsOffset);
    HudSettings.AddBackgroundLocalImage(builder, background_local_imageOffset);
    HudSettings.AddBackgroundImageUrl(builder, background_image_urlOffset);
    HudSettings.AddForegroundAnchors(builder, foreground_anchorsOffset);
    HudSettings.AddForegroundLocalImage(builder, foreground_local_imageOffset);
    HudSettings.AddForegroundImageUrl(builder, foreground_image_urlOffset);
    HudSettings.AddHideHudIcon(builder, hide_hud_icon);
    HudSettings.AddHideButton(builder, hide_button);
    HudSettings.AddHideTimer(builder, hide_timer);
    return HudSettings.EndHudSettings(builder);
  }

  public static void StartHudSettings(FlatBufferBuilder builder) { builder.StartTable(16); }
  public static void AddForegroundImageUrl(FlatBufferBuilder builder, StringOffset foregroundImageUrlOffset) { builder.AddOffset(0, foregroundImageUrlOffset.Value, 0); }
  public static void AddForegroundLocalImage(FlatBufferBuilder builder, StringOffset foregroundLocalImageOffset) { builder.AddOffset(1, foregroundLocalImageOffset.Value, 0); }
  public static void AddForegroundAnchors(FlatBufferBuilder builder, VectorOffset foregroundAnchorsOffset) { builder.AddOffset(2, foregroundAnchorsOffset.Value, 0); }
  public static VectorOffset CreateForegroundAnchorsVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateForegroundAnchorsVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateForegroundAnchorsVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateForegroundAnchorsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartForegroundAnchorsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBackgroundImageUrl(FlatBufferBuilder builder, StringOffset backgroundImageUrlOffset) { builder.AddOffset(3, backgroundImageUrlOffset.Value, 0); }
  public static void AddBackgroundLocalImage(FlatBufferBuilder builder, StringOffset backgroundLocalImageOffset) { builder.AddOffset(4, backgroundLocalImageOffset.Value, 0); }
  public static void AddBackgroundAnchors(FlatBufferBuilder builder, VectorOffset backgroundAnchorsOffset) { builder.AddOffset(5, backgroundAnchorsOffset.Value, 0); }
  public static VectorOffset CreateBackgroundAnchorsVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateBackgroundAnchorsVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBackgroundAnchorsVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBackgroundAnchorsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBackgroundAnchorsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddSubtitleLocalizationUid(FlatBufferBuilder builder, StringOffset subtitleLocalizationUidOffset) { builder.AddOffset(6, subtitleLocalizationUidOffset.Value, 0); }
  public static void AddHideTimer(FlatBufferBuilder builder, bool hideTimer) { builder.AddBool(7, hideTimer, false); }
  public static void AddScreens(FlatBufferBuilder builder, VectorOffset screensOffset) { builder.AddOffset(8, screensOffset.Value, 0); }
  public static VectorOffset CreateScreensVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateScreensVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScreensVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateScreensVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartScreensVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddHideButton(FlatBufferBuilder builder, bool hideButton) { builder.AddBool(9, hideButton, false); }
  public static void AddTimerAnchoring(FlatBufferBuilder builder, VectorOffset timerAnchoringOffset) { builder.AddOffset(10, timerAnchoringOffset.Value, 0); }
  public static VectorOffset CreateTimerAnchoringVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateTimerAnchoringVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTimerAnchoringVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateTimerAnchoringVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartTimerAnchoringVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddTimerColor(FlatBufferBuilder builder, StringOffset timerColorOffset) { builder.AddOffset(11, timerColorOffset.Value, 0); }
  public static void AddOverridenHeight(FlatBufferBuilder builder, float overridenHeight) { builder.AddFloat(12, overridenHeight, 0.0f); }
  public static void AddHideHudIcon(FlatBufferBuilder builder, bool hideHudIcon) { builder.AddBool(13, hideHudIcon, false); }
  public static void AddSubtitleBgColor(FlatBufferBuilder builder, StringOffset subtitleBgColorOffset) { builder.AddOffset(14, subtitleBgColorOffset.Value, 0); }
  public static void AddMainBadgeLocalizationUid(FlatBufferBuilder builder, StringOffset mainBadgeLocalizationUidOffset) { builder.AddOffset(15, mainBadgeLocalizationUidOffset.Value, 0); }
  public static Offset<FBConfig.HudSettings> EndHudSettings(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.HudSettings>(o);
  }
  public HudSettingsT UnPack() {
    var _o = new HudSettingsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(HudSettingsT _o) {
    _o.ForegroundImageUrl = this.ForegroundImageUrl;
    _o.ForegroundLocalImage = this.ForegroundLocalImage;
    _o.ForegroundAnchors = new List<float>();
    for (var _j = 0; _j < this.ForegroundAnchorsLength; ++_j) {_o.ForegroundAnchors.Add(this.ForegroundAnchors(_j));}
    _o.BackgroundImageUrl = this.BackgroundImageUrl;
    _o.BackgroundLocalImage = this.BackgroundLocalImage;
    _o.BackgroundAnchors = new List<float>();
    for (var _j = 0; _j < this.BackgroundAnchorsLength; ++_j) {_o.BackgroundAnchors.Add(this.BackgroundAnchors(_j));}
    _o.SubtitleLocalizationUid = this.SubtitleLocalizationUid;
    _o.HideTimer = this.HideTimer;
    _o.Screens = new List<string>();
    for (var _j = 0; _j < this.ScreensLength; ++_j) {_o.Screens.Add(this.Screens(_j));}
    _o.HideButton = this.HideButton;
    _o.TimerAnchoring = new List<float>();
    for (var _j = 0; _j < this.TimerAnchoringLength; ++_j) {_o.TimerAnchoring.Add(this.TimerAnchoring(_j));}
    _o.TimerColor = this.TimerColor;
    _o.OverridenHeight = this.OverridenHeight;
    _o.HideHudIcon = this.HideHudIcon;
    _o.SubtitleBgColor = this.SubtitleBgColor;
    _o.MainBadgeLocalizationUid = this.MainBadgeLocalizationUid;
  }
  public static Offset<FBConfig.HudSettings> Pack(FlatBufferBuilder builder, HudSettingsT _o) {
    if (_o == null) return default(Offset<FBConfig.HudSettings>);
    var _foreground_image_url = _o.ForegroundImageUrl == null ? default(StringOffset) : builder.CreateString(_o.ForegroundImageUrl);
    var _foreground_local_image = _o.ForegroundLocalImage == null ? default(StringOffset) : builder.CreateString(_o.ForegroundLocalImage);
    var _foreground_anchors = default(VectorOffset);
    if (_o.ForegroundAnchors != null) {
      var __foreground_anchors = _o.ForegroundAnchors.ToArray();
      _foreground_anchors = CreateForegroundAnchorsVector(builder, __foreground_anchors);
    }
    var _background_image_url = _o.BackgroundImageUrl == null ? default(StringOffset) : builder.CreateString(_o.BackgroundImageUrl);
    var _background_local_image = _o.BackgroundLocalImage == null ? default(StringOffset) : builder.CreateString(_o.BackgroundLocalImage);
    var _background_anchors = default(VectorOffset);
    if (_o.BackgroundAnchors != null) {
      var __background_anchors = _o.BackgroundAnchors.ToArray();
      _background_anchors = CreateBackgroundAnchorsVector(builder, __background_anchors);
    }
    var _subtitle_localization_uid = _o.SubtitleLocalizationUid == null ? default(StringOffset) : builder.CreateString(_o.SubtitleLocalizationUid);
    var _screens = default(VectorOffset);
    if (_o.Screens != null) {
      var __screens = new StringOffset[_o.Screens.Count];
      for (var _j = 0; _j < __screens.Length; ++_j) { __screens[_j] = builder.CreateString(_o.Screens[_j]); }
      _screens = CreateScreensVector(builder, __screens);
    }
    var _timer_anchoring = default(VectorOffset);
    if (_o.TimerAnchoring != null) {
      var __timer_anchoring = _o.TimerAnchoring.ToArray();
      _timer_anchoring = CreateTimerAnchoringVector(builder, __timer_anchoring);
    }
    var _timer_color = _o.TimerColor == null ? default(StringOffset) : builder.CreateString(_o.TimerColor);
    var _subtitle_bg_color = _o.SubtitleBgColor == null ? default(StringOffset) : builder.CreateString(_o.SubtitleBgColor);
    var _main_badge_localization_uid = _o.MainBadgeLocalizationUid == null ? default(StringOffset) : builder.CreateString(_o.MainBadgeLocalizationUid);
    return CreateHudSettings(
      builder,
      _foreground_image_url,
      _foreground_local_image,
      _foreground_anchors,
      _background_image_url,
      _background_local_image,
      _background_anchors,
      _subtitle_localization_uid,
      _o.HideTimer,
      _screens,
      _o.HideButton,
      _timer_anchoring,
      _timer_color,
      _o.OverridenHeight,
      _o.HideHudIcon,
      _subtitle_bg_color,
      _main_badge_localization_uid);
  }
}

public class HudSettingsT
{
  public string ForegroundImageUrl { get; set; }
  public string ForegroundLocalImage { get; set; }
  public List<float> ForegroundAnchors { get; set; }
  public string BackgroundImageUrl { get; set; }
  public string BackgroundLocalImage { get; set; }
  public List<float> BackgroundAnchors { get; set; }
  public string SubtitleLocalizationUid { get; set; }
  public bool HideTimer { get; set; }
  public List<string> Screens { get; set; }
  public bool HideButton { get; set; }
  public List<float> TimerAnchoring { get; set; }
  public string TimerColor { get; set; }
  public float OverridenHeight { get; set; }
  public bool HideHudIcon { get; set; }
  public string SubtitleBgColor { get; set; }
  public string MainBadgeLocalizationUid { get; set; }

  public HudSettingsT() {
    this.ForegroundImageUrl = null;
    this.ForegroundLocalImage = null;
    this.ForegroundAnchors = null;
    this.BackgroundImageUrl = null;
    this.BackgroundLocalImage = null;
    this.BackgroundAnchors = null;
    this.SubtitleLocalizationUid = null;
    this.HideTimer = false;
    this.Screens = null;
    this.HideButton = false;
    this.TimerAnchoring = null;
    this.TimerColor = null;
    this.OverridenHeight = 0.0f;
    this.HideHudIcon = false;
    this.SubtitleBgColor = null;
    this.MainBadgeLocalizationUid = null;
  }
}

public struct RewardSettings : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static RewardSettings GetRootAsRewardSettings(ByteBuffer _bb) { return GetRootAsRewardSettings(_bb, new RewardSettings()); }
  public static RewardSettings GetRootAsRewardSettings(ByteBuffer _bb, RewardSettings obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public RewardSettings __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Reward { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRewardBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetRewardBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetRewardArray() { return __p.__vector_as_array<byte>(4); }
  public int RewardingNumberLimit { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutateRewardingNumberLimit(int rewarding_number_limit) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, rewarding_number_limit); return true; } else { return false; } }
  public bool ShowSeparateRewardModal { get { int o = __p.__offset(8); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateShowSeparateRewardModal(bool show_separate_reward_modal) { int o = __p.__offset(8); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(show_separate_reward_modal ? 1 : 0)); return true; } else { return false; } }
  public string RewardModalTitle { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRewardModalTitleBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetRewardModalTitleBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetRewardModalTitleArray() { return __p.__vector_as_array<byte>(10); }
  public string RewardModalSubtitle { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetRewardModalSubtitleBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetRewardModalSubtitleBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetRewardModalSubtitleArray() { return __p.__vector_as_array<byte>(12); }

  public static Offset<FBConfig.RewardSettings> CreateRewardSettings(FlatBufferBuilder builder,
      StringOffset rewardOffset = default(StringOffset),
      int rewarding_number_limit = 0,
      bool show_separate_reward_modal = false,
      StringOffset reward_modal_titleOffset = default(StringOffset),
      StringOffset reward_modal_subtitleOffset = default(StringOffset)) {
    builder.StartTable(5);
    RewardSettings.AddRewardModalSubtitle(builder, reward_modal_subtitleOffset);
    RewardSettings.AddRewardModalTitle(builder, reward_modal_titleOffset);
    RewardSettings.AddRewardingNumberLimit(builder, rewarding_number_limit);
    RewardSettings.AddReward(builder, rewardOffset);
    RewardSettings.AddShowSeparateRewardModal(builder, show_separate_reward_modal);
    return RewardSettings.EndRewardSettings(builder);
  }

  public static void StartRewardSettings(FlatBufferBuilder builder) { builder.StartTable(5); }
  public static void AddReward(FlatBufferBuilder builder, StringOffset rewardOffset) { builder.AddOffset(0, rewardOffset.Value, 0); }
  public static void AddRewardingNumberLimit(FlatBufferBuilder builder, int rewardingNumberLimit) { builder.AddInt(1, rewardingNumberLimit, 0); }
  public static void AddShowSeparateRewardModal(FlatBufferBuilder builder, bool showSeparateRewardModal) { builder.AddBool(2, showSeparateRewardModal, false); }
  public static void AddRewardModalTitle(FlatBufferBuilder builder, StringOffset rewardModalTitleOffset) { builder.AddOffset(3, rewardModalTitleOffset.Value, 0); }
  public static void AddRewardModalSubtitle(FlatBufferBuilder builder, StringOffset rewardModalSubtitleOffset) { builder.AddOffset(4, rewardModalSubtitleOffset.Value, 0); }
  public static Offset<FBConfig.RewardSettings> EndRewardSettings(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.RewardSettings>(o);
  }
  public RewardSettingsT UnPack() {
    var _o = new RewardSettingsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(RewardSettingsT _o) {
    _o.Reward = this.Reward;
    _o.RewardingNumberLimit = this.RewardingNumberLimit;
    _o.ShowSeparateRewardModal = this.ShowSeparateRewardModal;
    _o.RewardModalTitle = this.RewardModalTitle;
    _o.RewardModalSubtitle = this.RewardModalSubtitle;
  }
  public static Offset<FBConfig.RewardSettings> Pack(FlatBufferBuilder builder, RewardSettingsT _o) {
    if (_o == null) return default(Offset<FBConfig.RewardSettings>);
    var _reward = _o.Reward == null ? default(StringOffset) : builder.CreateString(_o.Reward);
    var _reward_modal_title = _o.RewardModalTitle == null ? default(StringOffset) : builder.CreateString(_o.RewardModalTitle);
    var _reward_modal_subtitle = _o.RewardModalSubtitle == null ? default(StringOffset) : builder.CreateString(_o.RewardModalSubtitle);
    return CreateRewardSettings(
      builder,
      _reward,
      _o.RewardingNumberLimit,
      _o.ShowSeparateRewardModal,
      _reward_modal_title,
      _reward_modal_subtitle);
  }
}

public class RewardSettingsT
{
  public string Reward { get; set; }
  public int RewardingNumberLimit { get; set; }
  public bool ShowSeparateRewardModal { get; set; }
  public string RewardModalTitle { get; set; }
  public string RewardModalSubtitle { get; set; }

  public RewardSettingsT() {
    this.Reward = null;
    this.RewardingNumberLimit = 0;
    this.ShowSeparateRewardModal = false;
    this.RewardModalTitle = null;
    this.RewardModalSubtitle = null;
  }
}

public struct GenericPromoSettings : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static GenericPromoSettings GetRootAsGenericPromoSettings(ByteBuffer _bb) { return GetRootAsGenericPromoSettings(_bb, new GenericPromoSettings()); }
  public static GenericPromoSettings GetRootAsGenericPromoSettings(ByteBuffer _bb, GenericPromoSettings obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public GenericPromoSettings __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string HeaderLeftImageUrl { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHeaderLeftImageUrlBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetHeaderLeftImageUrlBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetHeaderLeftImageUrlArray() { return __p.__vector_as_array<byte>(4); }
  public float HeaderLeftImageAnchors(int j) { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int HeaderLeftImageAnchorsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetHeaderLeftImageAnchorsBytes() { return __p.__vector_as_span<float>(6, 4); }
#else
  public ArraySegment<byte>? GetHeaderLeftImageAnchorsBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public float[] GetHeaderLeftImageAnchorsArray() { return __p.__vector_as_array<float>(6); }
  public bool MutateHeaderLeftImageAnchors(int j, float header_left_image_anchors) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, header_left_image_anchors); return true; } else { return false; } }
  public string HeaderRightImageUrl { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHeaderRightImageUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetHeaderRightImageUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetHeaderRightImageUrlArray() { return __p.__vector_as_array<byte>(8); }
  public float HeaderRightImageAnchors(int j) { int o = __p.__offset(10); return o != 0 ? __p.bb.GetFloat(__p.__vector(o) + j * 4) : (float)0; }
  public int HeaderRightImageAnchorsLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<float> GetHeaderRightImageAnchorsBytes() { return __p.__vector_as_span<float>(10, 4); }
#else
  public ArraySegment<byte>? GetHeaderRightImageAnchorsBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public float[] GetHeaderRightImageAnchorsArray() { return __p.__vector_as_array<float>(10); }
  public bool MutateHeaderRightImageAnchors(int j, float header_right_image_anchors) { int o = __p.__offset(10); if (o != 0) { __p.bb.PutFloat(__p.__vector(o) + j * 4, header_right_image_anchors); return true; } else { return false; } }
  public string MainImageUrl { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMainImageUrlBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetMainImageUrlBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetMainImageUrlArray() { return __p.__vector_as_array<byte>(12); }
  public string TitleLocalizationUid { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleLocalizationUidBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetTitleLocalizationUidBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetTitleLocalizationUidArray() { return __p.__vector_as_array<byte>(14); }
  public string SubtitleLocalizationUid { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetSubtitleLocalizationUidBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetSubtitleLocalizationUidBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetSubtitleLocalizationUidArray() { return __p.__vector_as_array<byte>(16); }
  public string ButtonLocalizationUid { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetButtonLocalizationUidBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetButtonLocalizationUidBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetButtonLocalizationUidArray() { return __p.__vector_as_array<byte>(18); }
  public bool ShowViaHud { get { int o = __p.__offset(20); return o != 0 ? 0!=__p.bb.Get(o + __p.bb_pos) : (bool)false; } }
  public bool MutateShowViaHud(bool show_via_hud) { int o = __p.__offset(20); if (o != 0) { __p.bb.Put(o + __p.bb_pos, (byte)(show_via_hud ? 1 : 0)); return true; } else { return false; } }
  public string MainLocalImage { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetMainLocalImageBytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetMainLocalImageBytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetMainLocalImageArray() { return __p.__vector_as_array<byte>(22); }
  public string HeaderLeftLocalImage { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHeaderLeftLocalImageBytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetHeaderLeftLocalImageBytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetHeaderLeftLocalImageArray() { return __p.__vector_as_array<byte>(24); }
  public string HeaderRightLocalImage { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHeaderRightLocalImageBytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetHeaderRightLocalImageBytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetHeaderRightLocalImageArray() { return __p.__vector_as_array<byte>(26); }

  public static Offset<FBConfig.GenericPromoSettings> CreateGenericPromoSettings(FlatBufferBuilder builder,
      StringOffset header_left_image_urlOffset = default(StringOffset),
      VectorOffset header_left_image_anchorsOffset = default(VectorOffset),
      StringOffset header_right_image_urlOffset = default(StringOffset),
      VectorOffset header_right_image_anchorsOffset = default(VectorOffset),
      StringOffset main_image_urlOffset = default(StringOffset),
      StringOffset title_localization_uidOffset = default(StringOffset),
      StringOffset subtitle_localization_uidOffset = default(StringOffset),
      StringOffset button_localization_uidOffset = default(StringOffset),
      bool show_via_hud = false,
      StringOffset main_local_imageOffset = default(StringOffset),
      StringOffset header_left_local_imageOffset = default(StringOffset),
      StringOffset header_right_local_imageOffset = default(StringOffset)) {
    builder.StartTable(12);
    GenericPromoSettings.AddHeaderRightLocalImage(builder, header_right_local_imageOffset);
    GenericPromoSettings.AddHeaderLeftLocalImage(builder, header_left_local_imageOffset);
    GenericPromoSettings.AddMainLocalImage(builder, main_local_imageOffset);
    GenericPromoSettings.AddButtonLocalizationUid(builder, button_localization_uidOffset);
    GenericPromoSettings.AddSubtitleLocalizationUid(builder, subtitle_localization_uidOffset);
    GenericPromoSettings.AddTitleLocalizationUid(builder, title_localization_uidOffset);
    GenericPromoSettings.AddMainImageUrl(builder, main_image_urlOffset);
    GenericPromoSettings.AddHeaderRightImageAnchors(builder, header_right_image_anchorsOffset);
    GenericPromoSettings.AddHeaderRightImageUrl(builder, header_right_image_urlOffset);
    GenericPromoSettings.AddHeaderLeftImageAnchors(builder, header_left_image_anchorsOffset);
    GenericPromoSettings.AddHeaderLeftImageUrl(builder, header_left_image_urlOffset);
    GenericPromoSettings.AddShowViaHud(builder, show_via_hud);
    return GenericPromoSettings.EndGenericPromoSettings(builder);
  }

  public static void StartGenericPromoSettings(FlatBufferBuilder builder) { builder.StartTable(12); }
  public static void AddHeaderLeftImageUrl(FlatBufferBuilder builder, StringOffset headerLeftImageUrlOffset) { builder.AddOffset(0, headerLeftImageUrlOffset.Value, 0); }
  public static void AddHeaderLeftImageAnchors(FlatBufferBuilder builder, VectorOffset headerLeftImageAnchorsOffset) { builder.AddOffset(1, headerLeftImageAnchorsOffset.Value, 0); }
  public static VectorOffset CreateHeaderLeftImageAnchorsVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateHeaderLeftImageAnchorsVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHeaderLeftImageAnchorsVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHeaderLeftImageAnchorsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartHeaderLeftImageAnchorsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddHeaderRightImageUrl(FlatBufferBuilder builder, StringOffset headerRightImageUrlOffset) { builder.AddOffset(2, headerRightImageUrlOffset.Value, 0); }
  public static void AddHeaderRightImageAnchors(FlatBufferBuilder builder, VectorOffset headerRightImageAnchorsOffset) { builder.AddOffset(3, headerRightImageAnchorsOffset.Value, 0); }
  public static VectorOffset CreateHeaderRightImageAnchorsVector(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddFloat(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateHeaderRightImageAnchorsVectorBlock(FlatBufferBuilder builder, float[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHeaderRightImageAnchorsVectorBlock(FlatBufferBuilder builder, ArraySegment<float> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHeaderRightImageAnchorsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<float>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartHeaderRightImageAnchorsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddMainImageUrl(FlatBufferBuilder builder, StringOffset mainImageUrlOffset) { builder.AddOffset(4, mainImageUrlOffset.Value, 0); }
  public static void AddTitleLocalizationUid(FlatBufferBuilder builder, StringOffset titleLocalizationUidOffset) { builder.AddOffset(5, titleLocalizationUidOffset.Value, 0); }
  public static void AddSubtitleLocalizationUid(FlatBufferBuilder builder, StringOffset subtitleLocalizationUidOffset) { builder.AddOffset(6, subtitleLocalizationUidOffset.Value, 0); }
  public static void AddButtonLocalizationUid(FlatBufferBuilder builder, StringOffset buttonLocalizationUidOffset) { builder.AddOffset(7, buttonLocalizationUidOffset.Value, 0); }
  public static void AddShowViaHud(FlatBufferBuilder builder, bool showViaHud) { builder.AddBool(8, showViaHud, false); }
  public static void AddMainLocalImage(FlatBufferBuilder builder, StringOffset mainLocalImageOffset) { builder.AddOffset(9, mainLocalImageOffset.Value, 0); }
  public static void AddHeaderLeftLocalImage(FlatBufferBuilder builder, StringOffset headerLeftLocalImageOffset) { builder.AddOffset(10, headerLeftLocalImageOffset.Value, 0); }
  public static void AddHeaderRightLocalImage(FlatBufferBuilder builder, StringOffset headerRightLocalImageOffset) { builder.AddOffset(11, headerRightLocalImageOffset.Value, 0); }
  public static Offset<FBConfig.GenericPromoSettings> EndGenericPromoSettings(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.GenericPromoSettings>(o);
  }
  public GenericPromoSettingsT UnPack() {
    var _o = new GenericPromoSettingsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(GenericPromoSettingsT _o) {
    _o.HeaderLeftImageUrl = this.HeaderLeftImageUrl;
    _o.HeaderLeftImageAnchors = new List<float>();
    for (var _j = 0; _j < this.HeaderLeftImageAnchorsLength; ++_j) {_o.HeaderLeftImageAnchors.Add(this.HeaderLeftImageAnchors(_j));}
    _o.HeaderRightImageUrl = this.HeaderRightImageUrl;
    _o.HeaderRightImageAnchors = new List<float>();
    for (var _j = 0; _j < this.HeaderRightImageAnchorsLength; ++_j) {_o.HeaderRightImageAnchors.Add(this.HeaderRightImageAnchors(_j));}
    _o.MainImageUrl = this.MainImageUrl;
    _o.TitleLocalizationUid = this.TitleLocalizationUid;
    _o.SubtitleLocalizationUid = this.SubtitleLocalizationUid;
    _o.ButtonLocalizationUid = this.ButtonLocalizationUid;
    _o.ShowViaHud = this.ShowViaHud;
    _o.MainLocalImage = this.MainLocalImage;
    _o.HeaderLeftLocalImage = this.HeaderLeftLocalImage;
    _o.HeaderRightLocalImage = this.HeaderRightLocalImage;
  }
  public static Offset<FBConfig.GenericPromoSettings> Pack(FlatBufferBuilder builder, GenericPromoSettingsT _o) {
    if (_o == null) return default(Offset<FBConfig.GenericPromoSettings>);
    var _header_left_image_url = _o.HeaderLeftImageUrl == null ? default(StringOffset) : builder.CreateString(_o.HeaderLeftImageUrl);
    var _header_left_image_anchors = default(VectorOffset);
    if (_o.HeaderLeftImageAnchors != null) {
      var __header_left_image_anchors = _o.HeaderLeftImageAnchors.ToArray();
      _header_left_image_anchors = CreateHeaderLeftImageAnchorsVector(builder, __header_left_image_anchors);
    }
    var _header_right_image_url = _o.HeaderRightImageUrl == null ? default(StringOffset) : builder.CreateString(_o.HeaderRightImageUrl);
    var _header_right_image_anchors = default(VectorOffset);
    if (_o.HeaderRightImageAnchors != null) {
      var __header_right_image_anchors = _o.HeaderRightImageAnchors.ToArray();
      _header_right_image_anchors = CreateHeaderRightImageAnchorsVector(builder, __header_right_image_anchors);
    }
    var _main_image_url = _o.MainImageUrl == null ? default(StringOffset) : builder.CreateString(_o.MainImageUrl);
    var _title_localization_uid = _o.TitleLocalizationUid == null ? default(StringOffset) : builder.CreateString(_o.TitleLocalizationUid);
    var _subtitle_localization_uid = _o.SubtitleLocalizationUid == null ? default(StringOffset) : builder.CreateString(_o.SubtitleLocalizationUid);
    var _button_localization_uid = _o.ButtonLocalizationUid == null ? default(StringOffset) : builder.CreateString(_o.ButtonLocalizationUid);
    var _main_local_image = _o.MainLocalImage == null ? default(StringOffset) : builder.CreateString(_o.MainLocalImage);
    var _header_left_local_image = _o.HeaderLeftLocalImage == null ? default(StringOffset) : builder.CreateString(_o.HeaderLeftLocalImage);
    var _header_right_local_image = _o.HeaderRightLocalImage == null ? default(StringOffset) : builder.CreateString(_o.HeaderRightLocalImage);
    return CreateGenericPromoSettings(
      builder,
      _header_left_image_url,
      _header_left_image_anchors,
      _header_right_image_url,
      _header_right_image_anchors,
      _main_image_url,
      _title_localization_uid,
      _subtitle_localization_uid,
      _button_localization_uid,
      _o.ShowViaHud,
      _main_local_image,
      _header_left_local_image,
      _header_right_local_image);
  }
}

public class GenericPromoSettingsT
{
  public string HeaderLeftImageUrl { get; set; }
  public List<float> HeaderLeftImageAnchors { get; set; }
  public string HeaderRightImageUrl { get; set; }
  public List<float> HeaderRightImageAnchors { get; set; }
  public string MainImageUrl { get; set; }
  public string TitleLocalizationUid { get; set; }
  public string SubtitleLocalizationUid { get; set; }
  public string ButtonLocalizationUid { get; set; }
  public bool ShowViaHud { get; set; }
  public string MainLocalImage { get; set; }
  public string HeaderLeftLocalImage { get; set; }
  public string HeaderRightLocalImage { get; set; }

  public GenericPromoSettingsT() {
    this.HeaderLeftImageUrl = null;
    this.HeaderLeftImageAnchors = null;
    this.HeaderRightImageUrl = null;
    this.HeaderRightImageAnchors = null;
    this.MainImageUrl = null;
    this.TitleLocalizationUid = null;
    this.SubtitleLocalizationUid = null;
    this.ButtonLocalizationUid = null;
    this.ShowViaHud = false;
    this.MainLocalImage = null;
    this.HeaderLeftLocalImage = null;
    this.HeaderRightLocalImage = null;
  }
}

public struct PromoBannerSettings : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static PromoBannerSettings GetRootAsPromoBannerSettings(ByteBuffer _bb) { return GetRootAsPromoBannerSettings(_bb, new PromoBannerSettings()); }
  public static PromoBannerSettings GetRootAsPromoBannerSettings(ByteBuffer _bb, PromoBannerSettings obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public PromoBannerSettings __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string EventIconImageUrl { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetEventIconImageUrlBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetEventIconImageUrlBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetEventIconImageUrlArray() { return __p.__vector_as_array<byte>(4); }
  public string EventIconLocalImage { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetEventIconLocalImageBytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetEventIconLocalImageBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetEventIconLocalImageArray() { return __p.__vector_as_array<byte>(6); }
  public string BackgroundImageUrl { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBackgroundImageUrlBytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetBackgroundImageUrlBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetBackgroundImageUrlArray() { return __p.__vector_as_array<byte>(8); }
  public string BackgroundLocalImage { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBackgroundLocalImageBytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetBackgroundLocalImageBytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetBackgroundLocalImageArray() { return __p.__vector_as_array<byte>(10); }
  public string TitleLocalizationUid { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetTitleLocalizationUidBytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetTitleLocalizationUidBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetTitleLocalizationUidArray() { return __p.__vector_as_array<byte>(12); }
  public string BoosterLeftIconLocalImage { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBoosterLeftIconLocalImageBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetBoosterLeftIconLocalImageBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetBoosterLeftIconLocalImageArray() { return __p.__vector_as_array<byte>(14); }
  public string BoosterLeftCount { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBoosterLeftCountBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetBoosterLeftCountBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetBoosterLeftCountArray() { return __p.__vector_as_array<byte>(16); }
  public string BoosterRightIconLocalImage { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBoosterRightIconLocalImageBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetBoosterRightIconLocalImageBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetBoosterRightIconLocalImageArray() { return __p.__vector_as_array<byte>(18); }
  public string BoosterRightCount { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBoosterRightCountBytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetBoosterRightCountBytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetBoosterRightCountArray() { return __p.__vector_as_array<byte>(20); }
  public int PriorityOrder { get { int o = __p.__offset(22); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public bool MutatePriorityOrder(int priority_order) { int o = __p.__offset(22); if (o != 0) { __p.bb.PutInt(o + __p.bb_pos, priority_order); return true; } else { return false; } }

  public static Offset<FBConfig.PromoBannerSettings> CreatePromoBannerSettings(FlatBufferBuilder builder,
      StringOffset event_icon_image_urlOffset = default(StringOffset),
      StringOffset event_icon_local_imageOffset = default(StringOffset),
      StringOffset background_image_urlOffset = default(StringOffset),
      StringOffset background_local_imageOffset = default(StringOffset),
      StringOffset title_localization_uidOffset = default(StringOffset),
      StringOffset booster_left_icon_local_imageOffset = default(StringOffset),
      StringOffset booster_left_countOffset = default(StringOffset),
      StringOffset booster_right_icon_local_imageOffset = default(StringOffset),
      StringOffset booster_right_countOffset = default(StringOffset),
      int priority_order = 0) {
    builder.StartTable(10);
    PromoBannerSettings.AddPriorityOrder(builder, priority_order);
    PromoBannerSettings.AddBoosterRightCount(builder, booster_right_countOffset);
    PromoBannerSettings.AddBoosterRightIconLocalImage(builder, booster_right_icon_local_imageOffset);
    PromoBannerSettings.AddBoosterLeftCount(builder, booster_left_countOffset);
    PromoBannerSettings.AddBoosterLeftIconLocalImage(builder, booster_left_icon_local_imageOffset);
    PromoBannerSettings.AddTitleLocalizationUid(builder, title_localization_uidOffset);
    PromoBannerSettings.AddBackgroundLocalImage(builder, background_local_imageOffset);
    PromoBannerSettings.AddBackgroundImageUrl(builder, background_image_urlOffset);
    PromoBannerSettings.AddEventIconLocalImage(builder, event_icon_local_imageOffset);
    PromoBannerSettings.AddEventIconImageUrl(builder, event_icon_image_urlOffset);
    return PromoBannerSettings.EndPromoBannerSettings(builder);
  }

  public static void StartPromoBannerSettings(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddEventIconImageUrl(FlatBufferBuilder builder, StringOffset eventIconImageUrlOffset) { builder.AddOffset(0, eventIconImageUrlOffset.Value, 0); }
  public static void AddEventIconLocalImage(FlatBufferBuilder builder, StringOffset eventIconLocalImageOffset) { builder.AddOffset(1, eventIconLocalImageOffset.Value, 0); }
  public static void AddBackgroundImageUrl(FlatBufferBuilder builder, StringOffset backgroundImageUrlOffset) { builder.AddOffset(2, backgroundImageUrlOffset.Value, 0); }
  public static void AddBackgroundLocalImage(FlatBufferBuilder builder, StringOffset backgroundLocalImageOffset) { builder.AddOffset(3, backgroundLocalImageOffset.Value, 0); }
  public static void AddTitleLocalizationUid(FlatBufferBuilder builder, StringOffset titleLocalizationUidOffset) { builder.AddOffset(4, titleLocalizationUidOffset.Value, 0); }
  public static void AddBoosterLeftIconLocalImage(FlatBufferBuilder builder, StringOffset boosterLeftIconLocalImageOffset) { builder.AddOffset(5, boosterLeftIconLocalImageOffset.Value, 0); }
  public static void AddBoosterLeftCount(FlatBufferBuilder builder, StringOffset boosterLeftCountOffset) { builder.AddOffset(6, boosterLeftCountOffset.Value, 0); }
  public static void AddBoosterRightIconLocalImage(FlatBufferBuilder builder, StringOffset boosterRightIconLocalImageOffset) { builder.AddOffset(7, boosterRightIconLocalImageOffset.Value, 0); }
  public static void AddBoosterRightCount(FlatBufferBuilder builder, StringOffset boosterRightCountOffset) { builder.AddOffset(8, boosterRightCountOffset.Value, 0); }
  public static void AddPriorityOrder(FlatBufferBuilder builder, int priorityOrder) { builder.AddInt(9, priorityOrder, 0); }
  public static Offset<FBConfig.PromoBannerSettings> EndPromoBannerSettings(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.PromoBannerSettings>(o);
  }
  public PromoBannerSettingsT UnPack() {
    var _o = new PromoBannerSettingsT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(PromoBannerSettingsT _o) {
    _o.EventIconImageUrl = this.EventIconImageUrl;
    _o.EventIconLocalImage = this.EventIconLocalImage;
    _o.BackgroundImageUrl = this.BackgroundImageUrl;
    _o.BackgroundLocalImage = this.BackgroundLocalImage;
    _o.TitleLocalizationUid = this.TitleLocalizationUid;
    _o.BoosterLeftIconLocalImage = this.BoosterLeftIconLocalImage;
    _o.BoosterLeftCount = this.BoosterLeftCount;
    _o.BoosterRightIconLocalImage = this.BoosterRightIconLocalImage;
    _o.BoosterRightCount = this.BoosterRightCount;
    _o.PriorityOrder = this.PriorityOrder;
  }
  public static Offset<FBConfig.PromoBannerSettings> Pack(FlatBufferBuilder builder, PromoBannerSettingsT _o) {
    if (_o == null) return default(Offset<FBConfig.PromoBannerSettings>);
    var _event_icon_image_url = _o.EventIconImageUrl == null ? default(StringOffset) : builder.CreateString(_o.EventIconImageUrl);
    var _event_icon_local_image = _o.EventIconLocalImage == null ? default(StringOffset) : builder.CreateString(_o.EventIconLocalImage);
    var _background_image_url = _o.BackgroundImageUrl == null ? default(StringOffset) : builder.CreateString(_o.BackgroundImageUrl);
    var _background_local_image = _o.BackgroundLocalImage == null ? default(StringOffset) : builder.CreateString(_o.BackgroundLocalImage);
    var _title_localization_uid = _o.TitleLocalizationUid == null ? default(StringOffset) : builder.CreateString(_o.TitleLocalizationUid);
    var _booster_left_icon_local_image = _o.BoosterLeftIconLocalImage == null ? default(StringOffset) : builder.CreateString(_o.BoosterLeftIconLocalImage);
    var _booster_left_count = _o.BoosterLeftCount == null ? default(StringOffset) : builder.CreateString(_o.BoosterLeftCount);
    var _booster_right_icon_local_image = _o.BoosterRightIconLocalImage == null ? default(StringOffset) : builder.CreateString(_o.BoosterRightIconLocalImage);
    var _booster_right_count = _o.BoosterRightCount == null ? default(StringOffset) : builder.CreateString(_o.BoosterRightCount);
    return CreatePromoBannerSettings(
      builder,
      _event_icon_image_url,
      _event_icon_local_image,
      _background_image_url,
      _background_local_image,
      _title_localization_uid,
      _booster_left_icon_local_image,
      _booster_left_count,
      _booster_right_icon_local_image,
      _booster_right_count,
      _o.PriorityOrder);
  }
}

public class PromoBannerSettingsT
{
  public string EventIconImageUrl { get; set; }
  public string EventIconLocalImage { get; set; }
  public string BackgroundImageUrl { get; set; }
  public string BackgroundLocalImage { get; set; }
  public string TitleLocalizationUid { get; set; }
  public string BoosterLeftIconLocalImage { get; set; }
  public string BoosterLeftCount { get; set; }
  public string BoosterRightIconLocalImage { get; set; }
  public string BoosterRightCount { get; set; }
  public int PriorityOrder { get; set; }

  public PromoBannerSettingsT() {
    this.EventIconImageUrl = null;
    this.EventIconLocalImage = null;
    this.BackgroundImageUrl = null;
    this.BackgroundLocalImage = null;
    this.TitleLocalizationUid = null;
    this.BoosterLeftIconLocalImage = null;
    this.BoosterLeftCount = null;
    this.BoosterRightIconLocalImage = null;
    this.BoosterRightCount = null;
    this.PriorityOrder = 0;
  }
}

public struct UnifiedPromotionConfig : IFlatbufferConfig<UnifiedPromotionConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static UnifiedPromotionConfig GetRootAsUnifiedPromotionConfig(ByteBuffer _bb) { return GetRootAsUnifiedPromotionConfig(_bb, new UnifiedPromotionConfig()); }
  public static UnifiedPromotionConfig GetRootAsUnifiedPromotionConfig(ByteBuffer _bb, UnifiedPromotionConfig obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public UnifiedPromotionConfig __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Uid { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetUidBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetUidBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetUidArray() { return __p.__vector_as_array<byte>(4); }
  public float Duration { get { int o = __p.__offset(6); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }
  public bool MutateDuration(float duration) { int o = __p.__offset(6); if (o != 0) { __p.bb.PutFloat(o + __p.bb_pos, duration); return true; } else { return false; } }
  public FBConfig.CategoryParams? CategoryParams { get { int o = __p.__offset(12); return o != 0 ? (FBConfig.CategoryParams?)(new FBConfig.CategoryParams()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string StartCondition { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetStartConditionBytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetStartConditionBytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetStartConditionArray() { return __p.__vector_as_array<byte>(14); }
  public string EndCondition { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetEndConditionBytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetEndConditionBytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetEndConditionArray() { return __p.__vector_as_array<byte>(16); }
  public string AutoshowCondition { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAutoshowConditionBytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetAutoshowConditionBytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetAutoshowConditionArray() { return __p.__vector_as_array<byte>(18); }
  public string StartLevelCondition(int j) { int o = __p.__offset(30); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int StartLevelConditionLength { get { int o = __p.__offset(30); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string WeeklySchedule { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetWeeklyScheduleBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetWeeklyScheduleBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetWeeklyScheduleArray() { return __p.__vector_as_array<byte>(32); }
  public string ActivationCall { get { int o = __p.__offset(34); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetActivationCallBytes() { return __p.__vector_as_span<byte>(34, 1); }
#else
  public ArraySegment<byte>? GetActivationCallBytes() { return __p.__vector_as_arraysegment(34); }
#endif
  public byte[] GetActivationCallArray() { return __p.__vector_as_array<byte>(34); }
  public FBConfig.PromoBannerSettings? PromoBannerSettings { get { int o = __p.__offset(36); return o != 0 ? (FBConfig.PromoBannerSettings?)(new FBConfig.PromoBannerSettings()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string HudPrefab { get { int o = __p.__offset(38); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetHudPrefabBytes() { return __p.__vector_as_span<byte>(38, 1); }
#else
  public ArraySegment<byte>? GetHudPrefabBytes() { return __p.__vector_as_arraysegment(38); }
#endif
  public byte[] GetHudPrefabArray() { return __p.__vector_as_array<byte>(38); }
  public string ModalPrefab { get { int o = __p.__offset(40); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetModalPrefabBytes() { return __p.__vector_as_span<byte>(40, 1); }
#else
  public ArraySegment<byte>? GetModalPrefabBytes() { return __p.__vector_as_arraysegment(40); }
#endif
  public byte[] GetModalPrefabArray() { return __p.__vector_as_array<byte>(40); }
  public string BannerPrefab { get { int o = __p.__offset(42); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetBannerPrefabBytes() { return __p.__vector_as_span<byte>(42, 1); }
#else
  public ArraySegment<byte>? GetBannerPrefabBytes() { return __p.__vector_as_arraysegment(42); }
#endif
  public byte[] GetBannerPrefabArray() { return __p.__vector_as_array<byte>(42); }
  public FBConfig.ActionItem? AutoshowActions(int j) { int o = __p.__offset(44); return o != 0 ? (FBConfig.ActionItem?)(new FBConfig.ActionItem()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int AutoshowActionsLength { get { int o = __p.__offset(44); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ActionItem? HudActions(int j) { int o = __p.__offset(46); return o != 0 ? (FBConfig.ActionItem?)(new FBConfig.ActionItem()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int HudActionsLength { get { int o = __p.__offset(46); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.ActionItem? BannerActions(int j) { int o = __p.__offset(48); return o != 0 ? (FBConfig.ActionItem?)(new FBConfig.ActionItem()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int BannerActionsLength { get { int o = __p.__offset(48); return o != 0 ? __p.__vector_len(o) : 0; } }
  public string AutoshowPriority { get { int o = __p.__offset(50); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAutoshowPriorityBytes() { return __p.__vector_as_span<byte>(50, 1); }
#else
  public ArraySegment<byte>? GetAutoshowPriorityBytes() { return __p.__vector_as_arraysegment(50); }
#endif
  public byte[] GetAutoshowPriorityArray() { return __p.__vector_as_array<byte>(50); }
  public string ActiveScreens(int j) { int o = __p.__offset(52); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int ActiveScreensLength { get { int o = __p.__offset(52); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.DayMonthYear? StartTime { get { int o = __p.__offset(54); return o != 0 ? (FBConfig.DayMonthYear?)(new FBConfig.DayMonthYear()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public FBConfig.DayMonthYear? EndTime { get { int o = __p.__offset(56); return o != 0 ? (FBConfig.DayMonthYear?)(new FBConfig.DayMonthYear()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string Notification { get { int o = __p.__offset(58); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNotificationBytes() { return __p.__vector_as_span<byte>(58, 1); }
#else
  public ArraySegment<byte>? GetNotificationBytes() { return __p.__vector_as_arraysegment(58); }
#endif
  public byte[] GetNotificationArray() { return __p.__vector_as_array<byte>(58); }
  public string Analytics { get { int o = __p.__offset(60); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetAnalyticsBytes() { return __p.__vector_as_span<byte>(60, 1); }
#else
  public ArraySegment<byte>? GetAnalyticsBytes() { return __p.__vector_as_arraysegment(60); }
#endif
  public byte[] GetAnalyticsArray() { return __p.__vector_as_array<byte>(60); }
  public string IapPrefab { get { int o = __p.__offset(62); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIapPrefabBytes() { return __p.__vector_as_span<byte>(62, 1); }
#else
  public ArraySegment<byte>? GetIapPrefabBytes() { return __p.__vector_as_arraysegment(62); }
#endif
  public byte[] GetIapPrefabArray() { return __p.__vector_as_array<byte>(62); }

  public static Offset<FBConfig.UnifiedPromotionConfig> CreateUnifiedPromotionConfig(FlatBufferBuilder builder,
      StringOffset uidOffset = default(StringOffset),
      float duration = 0.0f,
      Offset<FBConfig.CategoryParams> category_paramsOffset = default(Offset<FBConfig.CategoryParams>),
      StringOffset start_conditionOffset = default(StringOffset),
      StringOffset end_conditionOffset = default(StringOffset),
      StringOffset autoshow_conditionOffset = default(StringOffset),
      VectorOffset start_level_conditionOffset = default(VectorOffset),
      StringOffset weekly_scheduleOffset = default(StringOffset),
      StringOffset activation_callOffset = default(StringOffset),
      Offset<FBConfig.PromoBannerSettings> promo_banner_settingsOffset = default(Offset<FBConfig.PromoBannerSettings>),
      StringOffset hud_prefabOffset = default(StringOffset),
      StringOffset modal_prefabOffset = default(StringOffset),
      StringOffset banner_prefabOffset = default(StringOffset),
      VectorOffset autoshow_actionsOffset = default(VectorOffset),
      VectorOffset hud_actionsOffset = default(VectorOffset),
      VectorOffset banner_actionsOffset = default(VectorOffset),
      StringOffset autoshow_priorityOffset = default(StringOffset),
      VectorOffset active_screensOffset = default(VectorOffset),
      Offset<FBConfig.DayMonthYear> start_timeOffset = default(Offset<FBConfig.DayMonthYear>),
      Offset<FBConfig.DayMonthYear> end_timeOffset = default(Offset<FBConfig.DayMonthYear>),
      StringOffset notificationOffset = default(StringOffset),
      StringOffset analyticsOffset = default(StringOffset),
      StringOffset iap_prefabOffset = default(StringOffset)) {
    builder.StartTable(30);
    UnifiedPromotionConfig.AddIapPrefab(builder, iap_prefabOffset);
    UnifiedPromotionConfig.AddAnalytics(builder, analyticsOffset);
    UnifiedPromotionConfig.AddNotification(builder, notificationOffset);
    UnifiedPromotionConfig.AddEndTime(builder, end_timeOffset);
    UnifiedPromotionConfig.AddStartTime(builder, start_timeOffset);
    UnifiedPromotionConfig.AddActiveScreens(builder, active_screensOffset);
    UnifiedPromotionConfig.AddAutoshowPriority(builder, autoshow_priorityOffset);
    UnifiedPromotionConfig.AddBannerActions(builder, banner_actionsOffset);
    UnifiedPromotionConfig.AddHudActions(builder, hud_actionsOffset);
    UnifiedPromotionConfig.AddAutoshowActions(builder, autoshow_actionsOffset);
    UnifiedPromotionConfig.AddBannerPrefab(builder, banner_prefabOffset);
    UnifiedPromotionConfig.AddModalPrefab(builder, modal_prefabOffset);
    UnifiedPromotionConfig.AddHudPrefab(builder, hud_prefabOffset);
    UnifiedPromotionConfig.AddPromoBannerSettings(builder, promo_banner_settingsOffset);
    UnifiedPromotionConfig.AddActivationCall(builder, activation_callOffset);
    UnifiedPromotionConfig.AddWeeklySchedule(builder, weekly_scheduleOffset);
    UnifiedPromotionConfig.AddStartLevelCondition(builder, start_level_conditionOffset);
    UnifiedPromotionConfig.AddAutoshowCondition(builder, autoshow_conditionOffset);
    UnifiedPromotionConfig.AddEndCondition(builder, end_conditionOffset);
    UnifiedPromotionConfig.AddStartCondition(builder, start_conditionOffset);
    UnifiedPromotionConfig.AddCategoryParams(builder, category_paramsOffset);
    UnifiedPromotionConfig.AddDuration(builder, duration);
    UnifiedPromotionConfig.AddUid(builder, uidOffset);
    return UnifiedPromotionConfig.EndUnifiedPromotionConfig(builder);
  }

  public static void StartUnifiedPromotionConfig(FlatBufferBuilder builder) { builder.StartTable(30); }
  public static void AddUid(FlatBufferBuilder builder, StringOffset uidOffset) { builder.AddOffset(0, uidOffset.Value, 0); }
  public static void AddDuration(FlatBufferBuilder builder, float duration) { builder.AddFloat(1, duration, 0.0f); }
  public static void AddCategoryParams(FlatBufferBuilder builder, Offset<FBConfig.CategoryParams> categoryParamsOffset) { builder.AddOffset(4, categoryParamsOffset.Value, 0); }
  public static void AddStartCondition(FlatBufferBuilder builder, StringOffset startConditionOffset) { builder.AddOffset(5, startConditionOffset.Value, 0); }
  public static void AddEndCondition(FlatBufferBuilder builder, StringOffset endConditionOffset) { builder.AddOffset(6, endConditionOffset.Value, 0); }
  public static void AddAutoshowCondition(FlatBufferBuilder builder, StringOffset autoshowConditionOffset) { builder.AddOffset(7, autoshowConditionOffset.Value, 0); }
  public static void AddStartLevelCondition(FlatBufferBuilder builder, VectorOffset startLevelConditionOffset) { builder.AddOffset(13, startLevelConditionOffset.Value, 0); }
  public static VectorOffset CreateStartLevelConditionVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateStartLevelConditionVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStartLevelConditionVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStartLevelConditionVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartStartLevelConditionVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddWeeklySchedule(FlatBufferBuilder builder, StringOffset weeklyScheduleOffset) { builder.AddOffset(14, weeklyScheduleOffset.Value, 0); }
  public static void AddActivationCall(FlatBufferBuilder builder, StringOffset activationCallOffset) { builder.AddOffset(15, activationCallOffset.Value, 0); }
  public static void AddPromoBannerSettings(FlatBufferBuilder builder, Offset<FBConfig.PromoBannerSettings> promoBannerSettingsOffset) { builder.AddOffset(16, promoBannerSettingsOffset.Value, 0); }
  public static void AddHudPrefab(FlatBufferBuilder builder, StringOffset hudPrefabOffset) { builder.AddOffset(17, hudPrefabOffset.Value, 0); }
  public static void AddModalPrefab(FlatBufferBuilder builder, StringOffset modalPrefabOffset) { builder.AddOffset(18, modalPrefabOffset.Value, 0); }
  public static void AddBannerPrefab(FlatBufferBuilder builder, StringOffset bannerPrefabOffset) { builder.AddOffset(19, bannerPrefabOffset.Value, 0); }
  public static void AddAutoshowActions(FlatBufferBuilder builder, VectorOffset autoshowActionsOffset) { builder.AddOffset(20, autoshowActionsOffset.Value, 0); }
  public static VectorOffset CreateAutoshowActionsVector(FlatBufferBuilder builder, Offset<FBConfig.ActionItem>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateAutoshowActionsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ActionItem>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAutoshowActionsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ActionItem>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateAutoshowActionsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ActionItem>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartAutoshowActionsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddHudActions(FlatBufferBuilder builder, VectorOffset hudActionsOffset) { builder.AddOffset(21, hudActionsOffset.Value, 0); }
  public static VectorOffset CreateHudActionsVector(FlatBufferBuilder builder, Offset<FBConfig.ActionItem>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateHudActionsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ActionItem>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHudActionsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ActionItem>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateHudActionsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ActionItem>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartHudActionsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBannerActions(FlatBufferBuilder builder, VectorOffset bannerActionsOffset) { builder.AddOffset(22, bannerActionsOffset.Value, 0); }
  public static VectorOffset CreateBannerActionsVector(FlatBufferBuilder builder, Offset<FBConfig.ActionItem>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateBannerActionsVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.ActionItem>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBannerActionsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.ActionItem>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateBannerActionsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.ActionItem>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartBannerActionsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddAutoshowPriority(FlatBufferBuilder builder, StringOffset autoshowPriorityOffset) { builder.AddOffset(23, autoshowPriorityOffset.Value, 0); }
  public static void AddActiveScreens(FlatBufferBuilder builder, VectorOffset activeScreensOffset) { builder.AddOffset(24, activeScreensOffset.Value, 0); }
  public static VectorOffset CreateActiveScreensVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateActiveScreensVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateActiveScreensVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateActiveScreensVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartActiveScreensVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddStartTime(FlatBufferBuilder builder, Offset<FBConfig.DayMonthYear> startTimeOffset) { builder.AddOffset(25, startTimeOffset.Value, 0); }
  public static void AddEndTime(FlatBufferBuilder builder, Offset<FBConfig.DayMonthYear> endTimeOffset) { builder.AddOffset(26, endTimeOffset.Value, 0); }
  public static void AddNotification(FlatBufferBuilder builder, StringOffset notificationOffset) { builder.AddOffset(27, notificationOffset.Value, 0); }
  public static void AddAnalytics(FlatBufferBuilder builder, StringOffset analyticsOffset) { builder.AddOffset(28, analyticsOffset.Value, 0); }
  public static void AddIapPrefab(FlatBufferBuilder builder, StringOffset iapPrefabOffset) { builder.AddOffset(29, iapPrefabOffset.Value, 0); }
  public static Offset<FBConfig.UnifiedPromotionConfig> EndUnifiedPromotionConfig(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // uid
    return new Offset<FBConfig.UnifiedPromotionConfig>(o);
  }

  public static VectorOffset CreateSortedVectorOfUnifiedPromotionConfig(FlatBufferBuilder builder, Offset<UnifiedPromotionConfig>[] offsets) {
    Array.Sort(offsets,
      (Offset<UnifiedPromotionConfig> o1, Offset<UnifiedPromotionConfig> o2) =>
        new UnifiedPromotionConfig().__assign(builder.DataBuffer.Length - o1.Value, builder.DataBuffer).Uid.CompareTo(new UnifiedPromotionConfig().__assign(builder.DataBuffer.Length - o2.Value, builder.DataBuffer).Uid));
    return builder.CreateVectorOfTables(offsets);
  }

  public static UnifiedPromotionConfig? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    UnifiedPromotionConfig obj_ = new UnifiedPromotionConfig();
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      obj_.__assign(tableOffset, bb);
      int comp = obj_.Uid.CompareTo(key);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return obj_;
      }
    }
    return null;
  }
  public UnifiedPromotionConfigT UnPack() {
    var _o = new UnifiedPromotionConfigT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(UnifiedPromotionConfigT _o) {
    _o.Uid = this.Uid;
    _o.Duration = this.Duration;
    _o.CategoryParams = this.CategoryParams.HasValue ? this.CategoryParams.Value.UnPack() : null;
    _o.StartCondition = this.StartCondition;
    _o.EndCondition = this.EndCondition;
    _o.AutoshowCondition = this.AutoshowCondition;
    _o.StartLevelCondition = new List<string>();
    for (var _j = 0; _j < this.StartLevelConditionLength; ++_j) {_o.StartLevelCondition.Add(this.StartLevelCondition(_j));}
    _o.WeeklySchedule = this.WeeklySchedule;
    _o.ActivationCall = this.ActivationCall;
    _o.PromoBannerSettings = this.PromoBannerSettings.HasValue ? this.PromoBannerSettings.Value.UnPack() : null;
    _o.HudPrefab = this.HudPrefab;
    _o.ModalPrefab = this.ModalPrefab;
    _o.BannerPrefab = this.BannerPrefab;
    _o.AutoshowActions = new List<FBConfig.ActionItemT>();
    for (var _j = 0; _j < this.AutoshowActionsLength; ++_j) {_o.AutoshowActions.Add(this.AutoshowActions(_j).HasValue ? this.AutoshowActions(_j).Value.UnPack() : null);}
    _o.HudActions = new List<FBConfig.ActionItemT>();
    for (var _j = 0; _j < this.HudActionsLength; ++_j) {_o.HudActions.Add(this.HudActions(_j).HasValue ? this.HudActions(_j).Value.UnPack() : null);}
    _o.BannerActions = new List<FBConfig.ActionItemT>();
    for (var _j = 0; _j < this.BannerActionsLength; ++_j) {_o.BannerActions.Add(this.BannerActions(_j).HasValue ? this.BannerActions(_j).Value.UnPack() : null);}
    _o.AutoshowPriority = this.AutoshowPriority;
    _o.ActiveScreens = new List<string>();
    for (var _j = 0; _j < this.ActiveScreensLength; ++_j) {_o.ActiveScreens.Add(this.ActiveScreens(_j));}
    _o.StartTime = this.StartTime.HasValue ? this.StartTime.Value.UnPack() : null;
    _o.EndTime = this.EndTime.HasValue ? this.EndTime.Value.UnPack() : null;
    _o.Notification = this.Notification;
    _o.Analytics = this.Analytics;
    _o.IapPrefab = this.IapPrefab;
  }
  public static Offset<FBConfig.UnifiedPromotionConfig> Pack(FlatBufferBuilder builder, UnifiedPromotionConfigT _o) {
    if (_o == null) return default(Offset<FBConfig.UnifiedPromotionConfig>);
    var _uid = _o.Uid == null ? default(StringOffset) : builder.CreateString(_o.Uid);
    var _category_params = _o.CategoryParams == null ? default(Offset<FBConfig.CategoryParams>) : FBConfig.CategoryParams.Pack(builder, _o.CategoryParams);
    var _start_condition = _o.StartCondition == null ? default(StringOffset) : builder.CreateString(_o.StartCondition);
    var _end_condition = _o.EndCondition == null ? default(StringOffset) : builder.CreateString(_o.EndCondition);
    var _autoshow_condition = _o.AutoshowCondition == null ? default(StringOffset) : builder.CreateString(_o.AutoshowCondition);
    var _start_level_condition = default(VectorOffset);
    if (_o.StartLevelCondition != null) {
      var __start_level_condition = new StringOffset[_o.StartLevelCondition.Count];
      for (var _j = 0; _j < __start_level_condition.Length; ++_j) { __start_level_condition[_j] = builder.CreateString(_o.StartLevelCondition[_j]); }
      _start_level_condition = CreateStartLevelConditionVector(builder, __start_level_condition);
    }
    var _weekly_schedule = _o.WeeklySchedule == null ? default(StringOffset) : builder.CreateString(_o.WeeklySchedule);
    var _activation_call = _o.ActivationCall == null ? default(StringOffset) : builder.CreateString(_o.ActivationCall);
    var _promo_banner_settings = _o.PromoBannerSettings == null ? default(Offset<FBConfig.PromoBannerSettings>) : FBConfig.PromoBannerSettings.Pack(builder, _o.PromoBannerSettings);
    var _hud_prefab = _o.HudPrefab == null ? default(StringOffset) : builder.CreateString(_o.HudPrefab);
    var _modal_prefab = _o.ModalPrefab == null ? default(StringOffset) : builder.CreateString(_o.ModalPrefab);
    var _banner_prefab = _o.BannerPrefab == null ? default(StringOffset) : builder.CreateString(_o.BannerPrefab);
    var _autoshow_actions = default(VectorOffset);
    if (_o.AutoshowActions != null) {
      var __autoshow_actions = new Offset<FBConfig.ActionItem>[_o.AutoshowActions.Count];
      for (var _j = 0; _j < __autoshow_actions.Length; ++_j) { __autoshow_actions[_j] = FBConfig.ActionItem.Pack(builder, _o.AutoshowActions[_j]); }
      _autoshow_actions = CreateAutoshowActionsVector(builder, __autoshow_actions);
    }
    var _hud_actions = default(VectorOffset);
    if (_o.HudActions != null) {
      var __hud_actions = new Offset<FBConfig.ActionItem>[_o.HudActions.Count];
      for (var _j = 0; _j < __hud_actions.Length; ++_j) { __hud_actions[_j] = FBConfig.ActionItem.Pack(builder, _o.HudActions[_j]); }
      _hud_actions = CreateHudActionsVector(builder, __hud_actions);
    }
    var _banner_actions = default(VectorOffset);
    if (_o.BannerActions != null) {
      var __banner_actions = new Offset<FBConfig.ActionItem>[_o.BannerActions.Count];
      for (var _j = 0; _j < __banner_actions.Length; ++_j) { __banner_actions[_j] = FBConfig.ActionItem.Pack(builder, _o.BannerActions[_j]); }
      _banner_actions = CreateBannerActionsVector(builder, __banner_actions);
    }
    var _autoshow_priority = _o.AutoshowPriority == null ? default(StringOffset) : builder.CreateString(_o.AutoshowPriority);
    var _active_screens = default(VectorOffset);
    if (_o.ActiveScreens != null) {
      var __active_screens = new StringOffset[_o.ActiveScreens.Count];
      for (var _j = 0; _j < __active_screens.Length; ++_j) { __active_screens[_j] = builder.CreateString(_o.ActiveScreens[_j]); }
      _active_screens = CreateActiveScreensVector(builder, __active_screens);
    }
    var _start_time = _o.StartTime == null ? default(Offset<FBConfig.DayMonthYear>) : FBConfig.DayMonthYear.Pack(builder, _o.StartTime);
    var _end_time = _o.EndTime == null ? default(Offset<FBConfig.DayMonthYear>) : FBConfig.DayMonthYear.Pack(builder, _o.EndTime);
    var _notification = _o.Notification == null ? default(StringOffset) : builder.CreateString(_o.Notification);
    var _analytics = _o.Analytics == null ? default(StringOffset) : builder.CreateString(_o.Analytics);
    var _iap_prefab = _o.IapPrefab == null ? default(StringOffset) : builder.CreateString(_o.IapPrefab);
    return CreateUnifiedPromotionConfig(
      builder,
      _uid,
      _o.Duration,
      _category_params,
      _start_condition,
      _end_condition,
      _autoshow_condition,
      _start_level_condition,
      _weekly_schedule,
      _activation_call,
      _promo_banner_settings,
      _hud_prefab,
      _modal_prefab,
      _banner_prefab,
      _autoshow_actions,
      _hud_actions,
      _banner_actions,
      _autoshow_priority,
      _active_screens,
      _start_time,
      _end_time,
      _notification,
      _analytics,
      _iap_prefab);
  }
}

public class UnifiedPromotionConfigT
{
  public string Uid { get; set; }
  public float Duration { get; set; }
  public FBConfig.CategoryParamsT CategoryParams { get; set; }
  public string StartCondition { get; set; }
  public string EndCondition { get; set; }
  public string AutoshowCondition { get; set; }
  public List<string> StartLevelCondition { get; set; }
  public string WeeklySchedule { get; set; }
  public string ActivationCall { get; set; }
  public FBConfig.PromoBannerSettingsT PromoBannerSettings { get; set; }
  public string HudPrefab { get; set; }
  public string ModalPrefab { get; set; }
  public string BannerPrefab { get; set; }
  public List<FBConfig.ActionItemT> AutoshowActions { get; set; }
  public List<FBConfig.ActionItemT> HudActions { get; set; }
  public List<FBConfig.ActionItemT> BannerActions { get; set; }
  public string AutoshowPriority { get; set; }
  public List<string> ActiveScreens { get; set; }
  public FBConfig.DayMonthYearT StartTime { get; set; }
  public FBConfig.DayMonthYearT EndTime { get; set; }
  public string Notification { get; set; }
  public string Analytics { get; set; }
  public string IapPrefab { get; set; }

  public UnifiedPromotionConfigT() {
    this.Uid = null;
    this.Duration = 0.0f;
    this.CategoryParams = null;
    this.StartCondition = null;
    this.EndCondition = null;
    this.AutoshowCondition = null;
    this.StartLevelCondition = null;
    this.WeeklySchedule = null;
    this.ActivationCall = null;
    this.PromoBannerSettings = null;
    this.HudPrefab = null;
    this.ModalPrefab = null;
    this.BannerPrefab = null;
    this.AutoshowActions = null;
    this.HudActions = null;
    this.BannerActions = null;
    this.AutoshowPriority = null;
    this.ActiveScreens = null;
    this.StartTime = null;
    this.EndTime = null;
    this.Notification = null;
    this.Analytics = null;
    this.IapPrefab = null;
  }
}

public struct UnifiedPromotionConfigDict : IFlatbufferConfigDict<UnifiedPromotionConfig, UnifiedPromotionConfigT>
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_1_21(); }
  public static UnifiedPromotionConfigDict GetRootAsUnifiedPromotionConfigDict(ByteBuffer _bb) { return GetRootAsUnifiedPromotionConfigDict(_bb, new UnifiedPromotionConfigDict()); }
  public static UnifiedPromotionConfigDict GetRootAsUnifiedPromotionConfigDict(ByteBuffer _bb, UnifiedPromotionConfigDict obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public UnifiedPromotionConfigDict __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public FBConfig.UnifiedPromotionConfig? Values(int j) { int o = __p.__offset(4); return o != 0 ? (FBConfig.UnifiedPromotionConfig?)(new FBConfig.UnifiedPromotionConfig()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public FBConfig.UnifiedPromotionConfig? ValuesByKey(string key) { int o = __p.__offset(4); return o != 0 ? FBConfig.UnifiedPromotionConfig.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<FBConfig.UnifiedPromotionConfigDict> CreateUnifiedPromotionConfigDict(FlatBufferBuilder builder,
      VectorOffset valuesOffset = default(VectorOffset)) {
    builder.StartTable(1);
    UnifiedPromotionConfigDict.AddValues(builder, valuesOffset);
    return UnifiedPromotionConfigDict.EndUnifiedPromotionConfigDict(builder);
  }

  public static void StartUnifiedPromotionConfigDict(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(0, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<FBConfig.UnifiedPromotionConfig>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<FBConfig.UnifiedPromotionConfig>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<FBConfig.UnifiedPromotionConfig>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<FBConfig.UnifiedPromotionConfig>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<FBConfig.UnifiedPromotionConfigDict> EndUnifiedPromotionConfigDict(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<FBConfig.UnifiedPromotionConfigDict>(o);
  }
  public static void FinishUnifiedPromotionConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.UnifiedPromotionConfigDict> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedUnifiedPromotionConfigDictBuffer(FlatBufferBuilder builder, Offset<FBConfig.UnifiedPromotionConfigDict> offset) { builder.FinishSizePrefixed(offset.Value); }
  public UnifiedPromotionConfigDictT UnPack() {
    var _o = new UnifiedPromotionConfigDictT();
    this.UnPackTo(_o);
    return _o;
  }
  public void UnPackTo(UnifiedPromotionConfigDictT _o) {
    _o.Values = new List<FBConfig.UnifiedPromotionConfigT>();
    for (var _j = 0; _j < this.ValuesLength; ++_j) {_o.Values.Add(this.Values(_j).HasValue ? this.Values(_j).Value.UnPack() : null);}
  }
  public static Offset<FBConfig.UnifiedPromotionConfigDict> Pack(FlatBufferBuilder builder, UnifiedPromotionConfigDictT _o) {
    if (_o == null) return default(Offset<FBConfig.UnifiedPromotionConfigDict>);
    var _values = default(VectorOffset);
    if (_o.Values != null) {
      var __values = new Offset<FBConfig.UnifiedPromotionConfig>[_o.Values.Count];
      for (var _j = 0; _j < __values.Length; ++_j) { __values[_j] = FBConfig.UnifiedPromotionConfig.Pack(builder, _o.Values[_j]); }
      _values = CreateValuesVector(builder, __values);
    }
    return CreateUnifiedPromotionConfigDict(
      builder,
      _values);
  }
}

public class UnifiedPromotionConfigDictT
{
  public List<FBConfig.UnifiedPromotionConfigT> Values { get; set; }

  public UnifiedPromotionConfigDictT() {
    this.Values = null;
  }
  public static UnifiedPromotionConfigDictT DeserializeFromBinary(byte[] fbBuffer) {
    return UnifiedPromotionConfigDict.GetRootAsUnifiedPromotionConfigDict(new ByteBuffer(fbBuffer)).UnPack();
  }
  public byte[] SerializeToBinary() {
    var fbb = new FlatBufferBuilder(0x10000);
    UnifiedPromotionConfigDict.FinishUnifiedPromotionConfigDictBuffer(fbb, UnifiedPromotionConfigDict.Pack(fbb, this));
    return fbb.DataBuffer.ToSizedArray();
  }
}


}
