%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: All M3 Fennec new meta_Material
  m_Shader: {fileID: 4800000, guid: 1e8a610c9e01c3648bac42585e5fc676, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _USE8NEIGHBOURHOOD_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 704d81a403fdd444485aa5800c98b4c4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Cutoff: 0.1
    - _OutlineMipLevel: 0
    - _OutlineOpaqueAlpha: 1
    - _OutlineReferenceTexWidth: 1024
    - _OutlineSmoothness: 1
    - _OutlineWidth: 3
    - _StencilComp: 8
    - _StencilRef: 1
    - _StraightAlphaInput: 0
    - _ThresholdEnd: 0.25
    - _Use8Neighbourhood: 1
    - _UseScreenSpaceOutlineWidth: 0
    m_Colors:
    - _OutlineColor: {r: 1, g: 1, b: 0, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
