%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1b3b4b945939a54ea0b23d3396115fb, type: 3}
  m_Name: Team Road trip Animation_SkeletonData
  m_EditorClassIdentifier: 
  atlasAssets:
  - {fileID: 11400000, guid: 8c9d33bc0160ed143b448f2a478d0449, type: 2}
  scale: 0.01
  skeletonJSON: {fileID: 4900000, guid: 3b6f293314086b249a93e760aecd10ac, type: 3}
  isUpgradingBlendModeMaterials: 0
  blendModeMaterials:
    requiresBlendModeMaterials: 1
    applyAdditiveMaterial: 0
    additiveMaterials: []
    multiplyMaterials:
    - pageName: Team Road trip Animation.png
      material: {fileID: 2100000, guid: 8d03d90bacf0e6742bc77f3af6f97463, type: 2}
    screenMaterials: []
  skeletonDataModifiers: []
  fromAnimation: []
  toAnimation: []
  duration: []
  defaultMix: 0.2
  controller: {fileID: 0}
