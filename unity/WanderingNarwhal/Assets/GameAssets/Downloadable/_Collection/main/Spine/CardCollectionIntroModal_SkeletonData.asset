%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1b3b4b945939a54ea0b23d3396115fb, type: 3}
  m_Name: CardCollectionIntroModal_SkeletonData
  m_EditorClassIdentifier: 
  atlasAssets:
  - {fileID: 11400000, guid: 3d9e1b82aac8d4376abd040ac2554b80, type: 2}
  scale: 0.0055
  skeletonJSON: {fileID: 4900000, guid: e56e7ea794d954859900f3e7f76bc903, type: 3}
  isUpgradingBlendModeMaterials: 0
  blendModeMaterials:
    requiresBlendModeMaterials: 1
    applyAdditiveMaterial: 0
    additiveMaterials: []
    multiplyMaterials:
    - pageName: CardCollectionIntroModal.png
      material: {fileID: 2100000, guid: 2538d7a75a9824ee396067e19e9c85b0, type: 2}
    screenMaterials: []
  skeletonDataModifiers: []
  fromAnimation: []
  toAnimation: []
  duration: []
  defaultMix: 0.2
  controller: {fileID: 0}
