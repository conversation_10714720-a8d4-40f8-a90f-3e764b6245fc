using BBB.DI;
using BBB.EndGameEvents;
using BBB.Map.Location;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core.TimeManager;
using PBGame;

namespace BBB.UI.UI.Controllers
{
    public class SideMapStartLevelController : StartLevelController
    {
        private IGameEventManager _gameEventManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private IPlayerManager _playerManager;
        private TimeManager _timeManager;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            
            LevelStarter = context.Resolve<SideMapLevelStarter>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _timeManager = context.Resolve<TimeManager>();
        }

        protected override void StartLevel()
        {
            IsStartLevelRequested = true;
            LevelStarter.StartLevel(LevelUid);
        }

        protected override LevelState GetLevelState()
        {
            var gameEvent = _gameEventManager.GetCurrentSideMapEvent() as SideMapGameEvent;
            return gameEvent?.CurrentLevelState;
        }

        protected override async UniTask SetupViewAsync()
        {
            await base.SetupViewAsync();
            if ((ScreensBuilder.CurrentScreenType & ScreenType.SideMap) > 0)
            {
                var levelConfig = Config.Get<FBConfig.ProgressionLevelConfig>()[LevelUid];
                var gameEvent = _gameEventManager.GetCurrentSideMapEvent() as SideMapGameEvent;
                View.SetupLevelDataForEvent(levelConfig, gameEvent, IsRetry);
                var eventSettings = _gameEventResourceManager.GetSettings(gameEvent?.Uid);
                View.ApplyPalette(eventSettings.Palette);

                _playerManager.Player.UpdateLastInteractionTimestamp(gameEvent.Uid, _timeManager.CurrentTimeStamp());
            }
        }
    }
}