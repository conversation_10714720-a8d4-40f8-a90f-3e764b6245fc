# Name Style System - Simple Implementation

A consolidated 2-class system for user name customization using TextAnimator with enum-based configuration.

## Core Components

### 1. `NameStyle.cs` - Enum
Defines available name animation styles:
```csharp
public enum NameStyle
{
    Default = 0,    // No animation
    Rainbow = 1,    // Rainbow colors
    Glow = 2,       // Glowing effect
    Bounce = 3,     // Bouncing animation
    Shake = 4,      // Shaking effect
    Pulse = 5,      // Pulsing scale
    Wave = 6,       // Wave movement
    Typewriter = 7, // Typewriter effect
    Fade = 8,       // Fade animation
    Rotate = 9      // Rotation effect
}
```

### 2. `NameStyleManager.cs` - Main Manager
Handles style application and user preferences:
```csharp
public class NameStyleManager : INameStyleManager
{
    // Apply current user's style
    string ApplyCurrentNameStyle(string nameText);
    
    // Apply specific style
    string ApplyNameStyle(string nameText, NameStyle style);
    
    // Set user preference
    void SetNameStyle(NameStyle style);
    
    // Check if style is unlocked
    bool IsNameStyleUnlocked(NameStyle style);
}
```

### 3. `StyledNameText.cs` - Component for Unified Name Prefabs
Component designed for your unified name prefab system:
```csharp
public class StyledNameText : MonoBehaviour
{
    // Simple method for unified prefabs
    public void SetStyledName(string nameText);
    
    // More control
    public void SetNameText(string nameText);
    public void SetStyleOverride(NameStyle style);
}
```

## Quick Setup

### 1. Register Service
```csharp
// In your DI setup
context.Register<INameStyleManager, NameStyleManager>();
```

### 2. Integration Examples

#### Simple Text Styling (without component)
```csharp
// Get the manager
var nameStyleManager = context.Resolve<INameStyleManager>();

// Apply current user's style
string styledName = nameStyleManager.ApplyCurrentNameStyle("PlayerName");
textComponent.text = styledName;

// Apply specific style
string rainbowName = nameStyleManager.ApplyNameStyle("PlayerName", NameStyle.Rainbow);
```

#### Unified Name Prefab Usage
```csharp
// On your unified name prefab, add StyledNameText component
var styledNameText = GetComponent<StyledNameText>();

// Simple usage - applies current user's style automatically
styledNameText.SetStyledName("PlayerName");

// Override with specific style
styledNameText.SetStyleOverride(NameStyle.Rainbow);
```

#### Existing Code Integration
```csharp
// BEFORE:
_nameText.text = playerName;

// AFTER:
var nameStyleManager = context.Resolve<INameStyleManager>();
_nameText.text = nameStyleManager.ApplyCurrentNameStyle(playerName);
```

## Server Integration

### Receiving Style from Server
```csharp
// Handle server response with name style
public void HandleNameStyleFromServer(string styleFromServer)
{
    // Server can send: "Rainbow", "rainbow", or "1"
    if (System.Enum.TryParse<NameStyle>(styleFromServer, true, out var style))
    {
        var manager = context.Resolve<INameStyleManager>();
        manager.SetNameStyle(style);
    }
}
```

### Server Configuration
Add to ProfileCustomizationConfig:
```json
{
  "NameStyle": {
    "customizations_list": [
      {
        "uid": "rainb",
        "prefab_uid": "rainbow_style_prefab",
        "should_show_lock_state": true,
        "unlock_condition_id": "unlock_rainbow_style"
      }
    ]
  }
}
```

## TextAnimator Integration

The system uses TextAnimator's built-in animation tags:
- `NameStyle.Rainbow` → `<rainb>text</rainb>`
- `NameStyle.Bounce` → `<bounce>text</bounce>`
- `NameStyle.Shake` → `<shake>text</shake>`
- `NameStyle.Wave` → `<wave>text</wave>`
- `NameStyle.Pulse` → `<size>text</size>`

No custom animations needed - works with TextAnimator's default effects.

## Usage Patterns

### For Unified Name Prefabs
1. Add `StyledNameText` component to your unified name prefab
2. Call `SetStyledName(playerName)` to apply current user's style
3. Component automatically finds TextMeshPro and TextAnimator in children

### For Existing Code
1. Inject `INameStyleManager` where you display names
2. Replace `text = name` with `text = manager.ApplyCurrentNameStyle(name)`
3. Minimal code changes required

### For Style Selection UI
```csharp
// Get available styles
var manager = context.Resolve<INameStyleManager>();
var availableStyles = manager.GetUnlockedNameStyles();

// Create UI for each style
foreach (var style in availableStyles)
{
    // Create button/option for style
    // On selection: manager.SetNameStyle(style);
}
```

This system provides a clean, enum-based API that integrates easily with your existing codebase and unified name prefab approach.
