using System;
using System.Collections.Generic;
using BBB.DI;
using Core.Configs;
using FBConfig;
using UnityEngine;

namespace BBB.ProfileCustomization.UI
{
    public sealed class AvatarDecorationManager : IAvatarDecorationManager, IContextInitializable, IContextReleasable
    {
        private const string FrameLayerUid = "Frame";
        private const string BadgeLayerUid = "Badge";
        private const string NameStyleUid = "NameStyle";
        private static readonly Type[] RequiredConfigs =
        {
            typeof(ProfileCustomizationConfig)
        };

        private IInventory PlayerInventory => _playerManager.PlayerInventory;
        private IPlayerManager _playerManager;
        private IDictionary<string, ProfileCustomizationConfig> _profileCustomizationConfig;
        public List<ProfileCustomizationConfigItem> FramesList { get; private set; }
        public List<ProfileCustomizationConfigItem> BadgesList { get; private set; }
        public List<ProfileCustomizationConfigItem> NameStylesList { get; private set; }

        public void InitializeByContext(IContext context)
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
            Config.OnConfigUpdated += OnConfigUpdated;
            _playerManager = context.Resolve<IPlayerManager>();
            FramesList = new List<ProfileCustomizationConfigItem>();
            BadgesList = new List<ProfileCustomizationConfigItem>();
            NameStylesList = new List<ProfileCustomizationConfigItem>();
        }

        private void OnConfigUpdated(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _profileCustomizationConfig = config.Get<ProfileCustomizationConfig>();
            
            if(_profileCustomizationConfig == null) return;
            FramesList.Clear();
            BadgesList.Clear();
            NameStylesList.Clear();
            Debug.LogError("OnConfigUpdated");

            if (_profileCustomizationConfig.TryGetValue(FrameLayerUid, out var frameConfig))
            {
                for (var i = 0; i < frameConfig.CustomizationsListLength; i++)
                {
                    var customization = frameConfig.CustomizationsList(i);
                    if (customization == null) continue;
                    FramesList.Add(customization.Value);
                }
            }
            
            if (_profileCustomizationConfig.TryGetValue(BadgeLayerUid, out var badgeConfig))
            {
                for (var i = 0; i < badgeConfig.CustomizationsListLength; i++)
                {
                    var customization = badgeConfig.CustomizationsList(i);
                    if (customization == null) continue;
                    BadgesList.Add(customization.Value);
                }
            }

            if (_profileCustomizationConfig.TryGetValue(NameStyleUid, out var nameStyleConfig))
            {
                for (var i = 0; i < nameStyleConfig.CustomizationsListLength; i++)
                {
                    var customization = nameStyleConfig.CustomizationsList(i);
                    if (customization == null) continue;
                    NameStylesList.Add(customization.Value);
                }
            }
        }

        public void AddGiftToPlayerInventory(string uid)
        {
            if (!CheckIfRewardExists(uid) || PlayerInventory.HasItem(uid)) return;
            PlayerInventory.AddItem(uid, 1);
        }

        private bool CheckIfRewardExists(string uid)
        {
             return _profileCustomizationConfig.ContainsKey(uid);
        }
        
        public void ReleaseByContext(IContext context)
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
        }
    }
}