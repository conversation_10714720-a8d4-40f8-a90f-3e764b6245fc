using BBB.DI;
using BBB.UI.Core;
using Febucci.UI;
using TMPro;
using UnityEngine;

namespace BBB.ProfileCustomization
{
    /// <summary>
    /// Component for unified name prefabs that automatically applies name styles using TextAnimator.
    /// Designed to work with a unified name prefab system with multiple layers.
    /// </summary>
    public class StyledNameText : ContextedUiBehaviour
    {
        [Header("Style Configuration")]
        [SerializeField] private bool _useCurrentUserStyle = true;
        [SerializeField] private NameStyle _overrideStyle = NameStyle.Default;
        [SerializeField] private bool _autoUpdateOnStyleChange = true;

        [Header("Text Configuration")]
        [SerializeField] private string _baseNameText = "";

        private TextMeshProUGUI _textComponent;
        private TextAnimator_TMP _textAnimator;
        private INameStyleManager _nameStyleManager;
        private string _currentStyledText = "";

        protected override void OnEnable()
        {
            base.OnEnable();
            _textComponent = GetComponent<TextMeshProUGUI>();
            _textAnimator = GetComponent<TextAnimator_TMP>();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_nameStyleManager != null && _autoUpdateOnStyleChange)
            {
                _nameStyleManager.NameStyleChanged -= OnNameStyleChanged;
            }
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _nameStyleManager = context.Resolve<INameStyleManager>();
                
            if (_autoUpdateOnStyleChange)
            {
                _nameStyleManager.NameStyleChanged += OnNameStyleChanged;
            }
            UpdateStyledText();
        }
        
        
        private void OnNameStyleChanged(NameStyle newStyle)
        {
            if (_useCurrentUserStyle)
            {
                UpdateStyledText();
            }
        }
        
        /// <summary>
        /// Sets the base name text and applies the current style
        /// </summary>
        /// <param name="nameText">The name text to display</param>
        public void SetNameText(string nameText)
        {
            _baseNameText = nameText;
            UpdateStyledText();
        }

        /// <summary>
        /// Simple method to set name text with current user's style - ideal for unified name prefabs
        /// </summary>
        /// <param name="nameText">The name text to display with styling</param>
        public void SetStyledName(string nameText)
        {
            SetNameText(nameText);
        }
        
        /// <summary>
        /// Sets a specific style override for this text component
        /// </summary>
        /// <param name="nameStyle">The style to apply</param>
        public void SetStyleOverride(NameStyle nameStyle)
        {
            _useCurrentUserStyle = false;
            _overrideStyle = nameStyle;
            UpdateStyledText();
        }
        
        /// <summary>
        /// Enables using the current user's style preference
        /// </summary>
        public void UseCurrentUserStyle()
        {
            _useCurrentUserStyle = true;
            UpdateStyledText();
        }
        
        /// <summary>
        /// Updates the displayed text with the appropriate style applied
        /// </summary>
        public void UpdateStyledText()
        {
            if (_nameStyleManager == null || string.IsNullOrEmpty(_baseNameText))
                return;
                
            string styledText;
            
            if (_useCurrentUserStyle)
            {
                styledText = _nameStyleManager.ApplyCurrentNameStyle(_baseNameText);
            }
            else
            {
                styledText = _nameStyleManager.ApplyNameStyle(_baseNameText, _overrideStyle);
            }
            
            // Only update if the text has changed to avoid unnecessary processing
            if (_currentStyledText != styledText)
            {
                _currentStyledText = styledText;
                
                if (_textAnimator != null)
                {
                    _textAnimator.SetText(styledText);
                }
                else if (_textComponent != null)
                {
                    _textComponent.text = styledText;
                }
            }
        }
        
        /// <summary>
        /// Gets the current styled text being displayed
        /// </summary>
        /// <returns>The styled text with TextAnimator tags</returns>
        public string GetStyledText()
        {
            return _currentStyledText;
        }
        
        /// <summary>
        /// Gets the base name text without styling
        /// </summary>
        /// <returns>The base name text</returns>
        public string GetBaseNameText()
        {
            return _baseNameText;
        }
        
        /// <summary>
        /// Gets the currently applied name style
        /// </summary>
        /// <returns>The current name style being used</returns>
        public NameStyle GetCurrentStyle()
        {
            return _useCurrentUserStyle ? 
                _nameStyleManager?.CurrentNameStyle ?? NameStyle.Default : 
                _overrideStyle;
        }
        
#if UNITY_EDITOR
        private void OnValidate()
        {
            // Update in editor when values change
            if (Application.isPlaying)
            {
                UpdateStyledText();
            }
        }
#endif
    }
}
