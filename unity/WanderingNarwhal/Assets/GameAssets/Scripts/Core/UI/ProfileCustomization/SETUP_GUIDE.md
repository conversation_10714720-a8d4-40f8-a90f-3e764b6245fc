# Name Style System - Quick Setup Guide

This guide shows you how to quickly set up and use the name styling system with TextAnimator.

## Quick Start (5 minutes)

### 1. Register the Service
Add this to your DI container setup:
```csharp
context.Register<INameStyleManager, NameStyleManager>();
```

### 2. Update Existing Name Displays
Replace existing name text assignments with styled versions:

**BEFORE:**
```csharp
_nameText.text = playerName;
```

**AFTER:**
```csharp
_nameText.text = NameStyleUtility.StyleName(playerName);
```

### 3. Test with Default Animations
The system works immediately with TextAnimator's built-in animations:
- `NameStyle.Rainbow` → uses "rainb" tag (rainbow effect)
- `NameStyle.Bounce` → uses "bounce" tag (bouncing animation)
- `NameStyle.Shake` → uses "shake" tag (shaking effect)
- `NameStyle.Wave` → uses "wave" tag (wave animation)
- `NameStyle.Fade` → uses "fade" tag (fade effect)

## Integration Examples

### Chat Messages
```csharp
// In your chat message setup method:
private void SetupSenderInfo(ChatMessage message)
{
    var senderName = message.Sender.Name;
    
    // Apply name styling
    _name.text = NameStyleUtility.StyleName(senderName);
}
```

### Leaderboards
```csharp
// In your leaderboard player setup:
private void Setup(string playerName, int score, int place, bool isOwn)
{
    // Apply name styling
    _nameText.text = NameStyleUtility.StyleName(playerName);
    
    // Rest of your existing code...
}
```

### Profile Display
```csharp
// In your profile update method:
private void UpdatePlayerName()
{
    var displayName = _accountManager.Profile.DisplayName;
    _nameText.text = NameStyleUtility.StyleName(displayName);
}
```

## Server Integration

### Receiving Style from Server
Handle server responses with name style data:
```csharp
// Server can send: "Rainbow", "rainbow", or "1"
public void HandleProfileUpdate(string nameStyleFromServer)
{
    if (System.Enum.TryParse<NameStyle>(nameStyleFromServer, true, out var style))
    {
        var manager = NameStyleUtility.GetNameStyleManager();
        manager?.SetNameStyle(style);
    }
}
```

### Server Configuration
Add name styles to your ProfileCustomizationConfig:
```json
{
  "NameStyle": {
    "customizations_list": [
      {
        "uid": "rainbow",
        "prefab_uid": "rainbow_style_prefab",
        "should_show_lock_state": true,
        "unlock_condition_id": "unlock_rainbow_style"
      },
      {
        "uid": "bounce",
        "prefab_uid": "bounce_style_prefab", 
        "should_show_lock_state": true,
        "unlock_condition_id": "unlock_bounce_style"
      }
    ]
  }
}
```

## Advanced Usage

### Component-Based Approach
For automatic styling, use the `StyledNameText` component:
1. Add `StyledNameText` to GameObjects with TextMeshPro + TextAnimator
2. Set the base name text
3. Component automatically applies current user's style

```csharp
// Get the component and set name
var styledText = GetComponent<StyledNameText>();
styledText.SetNameText(playerName);
styledText.UseCurrentUserStyle();
```

### Style Selection UI
Create UI for users to select their preferred style:
```csharp
// Populate dropdown with available styles
foreach (NameStyle style in System.Enum.GetValues(typeof(NameStyle)))
{
    if (NameStyleUtility.IsStyleUnlocked(style))
    {
        dropdown.options.Add(new Dropdown.OptionData(style.ToString()));
    }
}

// Handle selection
public void OnStyleSelected(int index)
{
    var style = (NameStyle)index;
    var manager = NameStyleUtility.GetNameStyleManager();
    manager?.SetNameStyle(style);
}
```

### Preview Styles
Show style previews before applying:
```csharp
public void PreviewStyle(NameStyle style)
{
    var preview = NameStyleUtility.StyleName("Preview Name", style);
    _previewText.text = preview;
}
```

## Troubleshooting

### Styles Not Appearing
1. **Check TextAnimator Setup**: Ensure TextAnimator components are properly configured
2. **Verify Animation Database**: Make sure TextAnimator has the default animations loaded
3. **Check Tags**: Verify the animation tags match what's expected (rainb, bounce, shake, etc.)

### Context Issues
1. **Missing ContextProvider**: Ensure scene has a ContextProvider with the DI container
2. **Service Registration**: Verify INameStyleManager is registered in your DI setup
3. **Clear Cache**: Call `NameStyleUtility.ClearCache()` if context changes

### Performance
- The system caches the NameStyleManager instance for performance
- Name styling is only applied when text actually changes
- Use the utility methods for best performance

## Next Steps

1. **Test Integration**: Start with one name display (like chat) and verify it works
2. **Add More Locations**: Gradually add styling to other name displays
3. **Server Setup**: Configure server to send/receive name style preferences
4. **UI Creation**: Build user interface for style selection
5. **Custom Styles**: Optionally create custom TextAnimator animations for unique effects

The system is designed to be minimally invasive - most existing code only needs a single line change to add name styling support!
