using BBB.DI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.ProfileCustomization.Examples
{
    /// <summary>
    /// Example script demonstrating how to use the name styling system.
    /// This script shows different ways to apply name styles to text components.
    /// </summary>
    public class NameStyleExample : MonoBehaviour
    {
        [Header("UI Components")]
        [SerializeField] private TextMeshProUGUI _playerNameText;
        [SerializeField] private StyledNameText _styledNameComponent;
        [SerializeField] private Button _changeStyleButton;
        [SerializeField] private Dropdown _styleDropdown;
        
        [Header("Configuration")]
        [SerializeField] private string _examplePlayerName = "TestPlayer";
        
        private INameStyleManager _nameStyleManager;
        private NameStyle _currentExampleStyle = NameStyle.Default;
        
        private void Start()
        {
            InitializeWithContext();
            SetupUI();
            UpdateDisplays();
        }
        
        private void InitializeWithContext()
        {
            var context = FindObjectOfType<ContextProvider>()?.Context;
            if (context != null)
            {
                _nameStyleManager = context.Resolve<INameStyleManager>();
            }
            else
            {
                Debug.LogWarning("[NameStyleExample] Could not find ContextProvider");
            }
        }
        
        private void SetupUI()
        {
            // Setup dropdown with available styles
            if (_styleDropdown != null)
            {
                _styleDropdown.ClearOptions();
                var options = new System.Collections.Generic.List<string>();
                
                foreach (NameStyle style in System.Enum.GetValues(typeof(NameStyle)))
                {
                    options.Add(style.ToString());
                }
                
                _styleDropdown.AddOptions(options);
                _styleDropdown.onValueChanged.AddListener(OnStyleDropdownChanged);
            }
            
            // Setup change style button
            if (_changeStyleButton != null)
            {
                _changeStyleButton.onClick.AddListener(OnChangeStyleClicked);
            }
        }
        
        private void OnStyleDropdownChanged(int index)
        {
            var styles = System.Enum.GetValues(typeof(NameStyle));
            if (index >= 0 && index < styles.Length)
            {
                _currentExampleStyle = (NameStyle)styles.GetValue(index);
                UpdateDisplays();
            }
        }
        
        private void OnChangeStyleClicked()
        {
            // Cycle through available styles
            var styles = System.Enum.GetValues(typeof(NameStyle));
            int currentIndex = System.Array.IndexOf(styles, _currentExampleStyle);
            int nextIndex = (currentIndex + 1) % styles.Length;
            _currentExampleStyle = (NameStyle)styles.GetValue(nextIndex);
            
            // Update dropdown to match
            if (_styleDropdown != null)
            {
                _styleDropdown.value = nextIndex;
            }
            
            UpdateDisplays();
        }
        
        private void UpdateDisplays()
        {
            // Example 1: Using NameStyleUtility for simple text styling
            if (_playerNameText != null)
            {
                NameStyleUtility.ApplyStyleToText(_playerNameText, _examplePlayerName, _currentExampleStyle);
            }
            
            // Example 2: Using StyledNameText component
            if (_styledNameComponent != null)
            {
                _styledNameComponent.SetNameText(_examplePlayerName);
                _styledNameComponent.SetStyleOverride(_currentExampleStyle);
            }
            
            Debug.Log($"[NameStyleExample] Applied style: {_currentExampleStyle}");
        }
        
        /// <summary>
        /// Example of how to apply the current user's style to a name
        /// </summary>
        /// <param name="nameText">The name to style</param>
        /// <returns>The styled name text</returns>
        public string GetStyledPlayerName(string nameText)
        {
            // This is how you would typically use the system in your game
            return NameStyleUtility.StyleName(nameText);
        }
        
        /// <summary>
        /// Example of how to apply a specific style to a name
        /// </summary>
        /// <param name="nameText">The name to style</param>
        /// <param name="style">The specific style to apply</param>
        /// <returns>The styled name text</returns>
        public string GetStyledPlayerName(string nameText, NameStyle style)
        {
            return NameStyleUtility.StyleName(nameText, style);
        }
        
        /// <summary>
        /// Example of how to check if a style is unlocked
        /// </summary>
        /// <param name="style">The style to check</param>
        /// <returns>True if the style is unlocked</returns>
        public bool IsStyleAvailable(NameStyle style)
        {
            return NameStyleUtility.IsStyleUnlocked(style);
        }
        
        private void OnDestroy()
        {
            if (_changeStyleButton != null)
            {
                _changeStyleButton.onClick.RemoveListener(OnChangeStyleClicked);
            }
            
            if (_styleDropdown != null)
            {
                _styleDropdown.onValueChanged.RemoveListener(OnStyleDropdownChanged);
            }
        }
    }
}
