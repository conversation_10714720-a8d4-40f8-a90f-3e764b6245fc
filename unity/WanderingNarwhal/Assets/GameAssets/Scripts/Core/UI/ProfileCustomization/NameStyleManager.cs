using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.ProfileCustomization.UI;
using UnityEngine;

namespace BBB.ProfileCustomization
{
    /// <summary>
    /// Manager responsible for handling name style customization using TextAnimator.
    /// Provides methods to apply name styles based on enum values and manage style configurations.
    /// </summary>
    public class NameStyleManager : INameStyleManager, IContextInitializable, IContextReleasable
    {
        private const string NAME_STYLE_PLAYER_PREFS_KEY = "NameStyle";

        private readonly Dictionary<NameStyle, string> _styleTagMapping = new();

        private IAvatarDecorationManager _avatarDecorationManager;

        public event Action<NameStyle> NameStyleChanged;

        /// <summary>
        /// Currently selected name style for the user
        /// </summary>
        public NameStyle CurrentNameStyle { get; private set; } = NameStyle.Default;

        public void InitializeByContext(IContext context)
        {
            _avatarDecorationManager = context.Resolve<IAvatarDecorationManager>();

            InitializeStyleTagMapping();
            LoadUserNameStyle();
        }

        public void ReleaseByContext(IContext context)
        {
            _styleTagMapping.Clear();
        }

        private void InitializeStyleTagMapping()
        {
            _styleTagMapping.Clear();

            // Map enum values to existing TextAnimator default animation tags
            _styleTagMapping[NameStyle.Default] = "";
            _styleTagMapping[NameStyle.Rainbow] = "rainb"; // Default TextAnimator rainbow effect
            _styleTagMapping[NameStyle.Glow] = "glow"; // Default glow effect if available
            _styleTagMapping[NameStyle.Bounce] = "bounce"; // Default bounce behavior
            _styleTagMapping[NameStyle.Shake] = "shake"; // Default shake behavior
            _styleTagMapping[NameStyle.Pulse] = "size"; // Use size animation for pulse effect
            _styleTagMapping[NameStyle.Wave] = "wave"; // Default wave behavior
            _styleTagMapping[NameStyle.Typewriter] = "typewriter"; // Typewriter effect
            _styleTagMapping[NameStyle.Fade] = "fade"; // Default fade effect
            _styleTagMapping[NameStyle.Rotate] = "rot"; // Default rotation behavior
        }

        private void LoadUserNameStyle()
        {
            // Load from PlayerPrefs
            var savedStyle = PlayerPrefs.GetInt(NAME_STYLE_PLAYER_PREFS_KEY, (int)NameStyle.Default);
            if (Enum.IsDefined(typeof(NameStyle), savedStyle))
            {
                CurrentNameStyle = (NameStyle)savedStyle;
            }
        }
        
        /// <summary>
        /// Gets the TextAnimator tag corresponding to the specified name style
        /// </summary>
        /// <param name="nameStyle">The name style enum value</param>
        /// <returns>The corresponding TextAnimator tag string</returns>
        public string GetStyleTag(NameStyle nameStyle)
        {
            return _styleTagMapping.TryGetValue(nameStyle, out var tag) ? tag : "";
        }
        
        /// <summary>
        /// Applies the specified name style to a text string using TextAnimator tags
        /// </summary>
        /// <param name="nameText">The name text to style</param>
        /// <param name="nameStyle">The style to apply</param>
        /// <returns>The styled text with TextAnimator tags</returns>
        public string ApplyNameStyle(string nameText, NameStyle nameStyle)
        {
            if (string.IsNullOrEmpty(nameText))
                return nameText;
                
            if (nameStyle == NameStyle.Default)
                return nameText;

            var styleTag = GetStyleTag(nameStyle);
            if (string.IsNullOrEmpty(styleTag))
                return nameText;

            return $"<{styleTag}>{nameText}</{styleTag}>";
        }
        
        /// <summary>
        /// Applies the current user's name style to a text string
        /// </summary>
        /// <param name="nameText">The name text to style</param>
        /// <returns>The styled text with the user's current name style</returns>
        public string ApplyCurrentNameStyle(string nameText)
        {
            return ApplyNameStyle(nameText, CurrentNameStyle);
        }
        
        /// <summary>
        /// Sets the user's name style preference
        /// </summary>
        /// <param name="nameStyle">The new name style to set</param>
        public void SetNameStyle(NameStyle nameStyle)
        {
            if (CurrentNameStyle == nameStyle)
                return;
                
            CurrentNameStyle = nameStyle;
            
            // Save to user profile
            SaveNameStyleToProfile();
            
            // Notify listeners
            NameStyleChanged?.Invoke(nameStyle);
        }
        
        private void SaveNameStyleToProfile()
        {
            PlayerPrefs.SetInt(NAME_STYLE_PLAYER_PREFS_KEY, (int)CurrentNameStyle);
            PlayerPrefs.Save();
        }
        
        /// <summary>
        /// Checks if the user has unlocked the specified name style
        /// </summary>
        /// <param name="nameStyle">The name style to check</param>
        /// <returns>True if the style is unlocked, false otherwise</returns>
        public bool IsNameStyleUnlocked(NameStyle nameStyle)
        {
            // Default style is always unlocked
            if (nameStyle == NameStyle.Default)
                return true;

            // Check with the avatar decoration manager for unlocked styles
            if (_avatarDecorationManager?.NameStylesList != null)
            {
                var styleTag = GetStyleTag(nameStyle);
                foreach (var item in _avatarDecorationManager.NameStylesList)
                {
                    if (item.Uid == styleTag)
                    {
                        // Check if the player has this item in their inventory
                        // This would need to be implemented based on your inventory system
                        return true; // For now, return true if the style exists in config
                    }
                }
            }

            return false;
        }
        
        /// <summary>
        /// Gets all available name styles that the user has unlocked
        /// </summary>
        /// <returns>Array of unlocked name styles</returns>
        public NameStyle[] GetUnlockedNameStyles()
        {
            var unlockedStyles = new List<NameStyle>();
            
            foreach (NameStyle style in Enum.GetValues(typeof(NameStyle)))
            {
                if (IsNameStyleUnlocked(style))
                {
                    unlockedStyles.Add(style);
                }
            }
            
            return unlockedStyles.ToArray();
        }
    }
}
