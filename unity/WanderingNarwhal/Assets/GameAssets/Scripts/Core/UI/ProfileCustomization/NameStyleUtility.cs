using BBB.DI;
using Febucci.UI;
using TMPro;
using UnityEngine;

namespace BBB.ProfileCustomization
{
    /// <summary>
    /// Utility class providing static methods for applying name styles to text components.
    /// This class offers convenient methods for styling names without requiring the StyledNameText component.
    /// </summary>
    public static class NameStyleUtility
    {
        private static INameStyleManager _cachedNameStyleManager;
        
        /// <summary>
        /// Gets the name style manager instance, caching it for performance
        /// </summary>
        /// <returns>The INameStyleManager instance or null if not available</returns>
        private static INameStyleManager GetNameStyleManager()
        {
            if (_cachedNameStyleManager == null)
            {
                var contextProvider = Object.FindObjectOfType<ContextProvider>();
                if (contextProvider?.Context != null)
                {
                    _cachedNameStyleManager = contextProvider.Context.Resolve<INameStyleManager>();
                }
            }
            
            return _cachedNameStyleManager;
        }
        
        /// <summary>
        /// Applies the current user's name style to a text string
        /// </summary>
        /// <param name="nameText">The name text to style</param>
        /// <returns>The styled text with TextAnimator tags, or original text if manager is unavailable</returns>
        public static string StyleName(string nameText)
        {
            var manager = GetNameStyleManager();
            return manager?.ApplyCurrentNameStyle(nameText) ?? nameText;
        }
        
        /// <summary>
        /// Applies a specific name style to a text string
        /// </summary>
        /// <param name="nameText">The name text to style</param>
        /// <param name="nameStyle">The style to apply</param>
        /// <returns>The styled text with TextAnimator tags, or original text if manager is unavailable</returns>
        public static string StyleName(string nameText, NameStyle nameStyle)
        {
            var manager = GetNameStyleManager();
            return manager?.ApplyNameStyle(nameText, nameStyle) ?? nameText;
        }
        
        /// <summary>
        /// Applies the current user's name style to a TextMeshPro component
        /// </summary>
        /// <param name="textComponent">The TextMeshPro component to update</param>
        /// <param name="nameText">The name text to style and set</param>
        public static void ApplyStyleToText(TextMeshProUGUI textComponent, string nameText)
        {
            if (textComponent == null) return;
            
            var styledText = StyleName(nameText);
            textComponent.text = styledText;
        }
        
        /// <summary>
        /// Applies a specific name style to a TextMeshPro component
        /// </summary>
        /// <param name="textComponent">The TextMeshPro component to update</param>
        /// <param name="nameText">The name text to style and set</param>
        /// <param name="nameStyle">The style to apply</param>
        public static void ApplyStyleToText(TextMeshProUGUI textComponent, string nameText, NameStyle nameStyle)
        {
            if (textComponent == null) return;
            
            var styledText = StyleName(nameText, nameStyle);
            textComponent.text = styledText;
        }
        
        /// <summary>
        /// Applies the current user's name style to a TextAnimator component
        /// </summary>
        /// <param name="textAnimator">The TextAnimator component to update</param>
        /// <param name="nameText">The name text to style and set</param>
        public static void ApplyStyleToTextAnimator(TextAnimator_TMP textAnimator, string nameText)
        {
            if (textAnimator == null) return;
            
            var manager = GetNameStyleManager();
            if (manager != null)
            {
                // Ensure the TextAnimator is using the name style sheet
                var nameStyleSheet = manager.GetNameStyleSheet();
                if (nameStyleSheet != null)
                {
                    textAnimator.StyleSheet = nameStyleSheet;
                }
                
                var styledText = manager.ApplyCurrentNameStyle(nameText);
                textAnimator.SetText(styledText);
            }
            else
            {
                textAnimator.SetText(nameText);
            }
        }
        
        /// <summary>
        /// Applies a specific name style to a TextAnimator component
        /// </summary>
        /// <param name="textAnimator">The TextAnimator component to update</param>
        /// <param name="nameText">The name text to style and set</param>
        /// <param name="nameStyle">The style to apply</param>
        public static void ApplyStyleToTextAnimator(TextAnimator_TMP textAnimator, string nameText, NameStyle nameStyle)
        {
            if (textAnimator == null) return;
            
            var manager = GetNameStyleManager();
            if (manager != null)
            {
                // Ensure the TextAnimator is using the name style sheet
                var nameStyleSheet = manager.GetNameStyleSheet();
                if (nameStyleSheet != null)
                {
                    textAnimator.StyleSheet = nameStyleSheet;
                }
                
                var styledText = manager.ApplyNameStyle(nameText, nameStyle);
                textAnimator.SetText(styledText);
            }
            else
            {
                textAnimator.SetText(nameText);
            }
        }
        
        /// <summary>
        /// Gets the TextAnimator tag for a specific name style
        /// </summary>
        /// <param name="nameStyle">The name style to get the tag for</param>
        /// <returns>The TextAnimator tag string, or empty string if manager is unavailable</returns>
        public static string GetStyleTag(NameStyle nameStyle)
        {
            var manager = GetNameStyleManager();
            return manager?.GetStyleTag(nameStyle) ?? string.Empty;
        }
        
        /// <summary>
        /// Checks if a name style is unlocked for the current user
        /// </summary>
        /// <param name="nameStyle">The name style to check</param>
        /// <returns>True if unlocked, false otherwise or if manager is unavailable</returns>
        public static bool IsStyleUnlocked(NameStyle nameStyle)
        {
            var manager = GetNameStyleManager();
            return manager?.IsNameStyleUnlocked(nameStyle) ?? false;
        }
        
        /// <summary>
        /// Gets the current user's selected name style
        /// </summary>
        /// <returns>The current name style, or Default if manager is unavailable</returns>
        public static NameStyle GetCurrentUserStyle()
        {
            var manager = GetNameStyleManager();
            return manager?.CurrentNameStyle ?? NameStyle.Default;
        }
        
        /// <summary>
        /// Clears the cached name style manager (useful for testing or context changes)
        /// </summary>
        public static void ClearCache()
        {
            _cachedNameStyleManager = null;
        }
    }
}
