using System;
using Febucci.UI.Styles;

namespace BBB.ProfileCustomization
{
    /// <summary>
    /// Interface for managing name style customization using TextAnimator style sheets
    /// </summary>
    public interface INameStyleManager
    {
        /// <summary>
        /// Event triggered when the user's name style changes
        /// </summary>
        event Action<NameStyle> NameStyleChanged;
        
        /// <summary>
        /// Currently selected name style for the user
        /// </summary>
        NameStyle CurrentNameStyle { get; }
        
        /// <summary>
        /// Gets the TextAnimator tag corresponding to the specified name style
        /// </summary>
        /// <param name="nameStyle">The name style enum value</param>
        /// <returns>The corresponding TextAnimator tag string</returns>
        string GetStyleTag(NameStyle nameStyle);
        
        /// <summary>
        /// Applies the specified name style to a text string using TextAnimator tags
        /// </summary>
        /// <param name="nameText">The name text to style</param>
        /// <param name="nameStyle">The style to apply</param>
        /// <returns>The styled text with TextAnimator tags</returns>
        string ApplyNameStyle(string nameText, NameStyle nameStyle);
        
        /// <summary>
        /// Applies the current user's name style to a text string
        /// </summary>
        /// <param name="nameText">The name text to style</param>
        /// <returns>The styled text with the user's current name style</returns>
        string ApplyCurrentNameStyle(string nameText);
        
        /// <summary>
        /// Sets the user's name style preference
        /// </summary>
        /// <param name="nameStyle">The new name style to set</param>
        void SetNameStyle(NameStyle nameStyle);
        
        /// <summary>
        /// Gets the StyleSheet used for name animations
        /// </summary>
        /// <returns>The StyleSheetScriptable for name styles</returns>
        StyleSheetScriptable GetNameStyleSheet();
        
        /// <summary>
        /// Checks if the user has unlocked the specified name style
        /// </summary>
        /// <param name="nameStyle">The name style to check</param>
        /// <returns>True if the style is unlocked, false otherwise</returns>
        bool IsNameStyleUnlocked(NameStyle nameStyle);
        
        /// <summary>
        /// Gets all available name styles that the user has unlocked
        /// </summary>
        /// <returns>Array of unlocked name styles</returns>
        NameStyle[] GetUnlockedNameStyles();
    }
}
