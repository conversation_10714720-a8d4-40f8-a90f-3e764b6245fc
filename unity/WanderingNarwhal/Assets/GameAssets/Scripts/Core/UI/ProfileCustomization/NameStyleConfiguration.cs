using System;
using System.Collections.Generic;
using Febucci.UI.Styles;
using UnityEngine;

namespace BBB.ProfileCustomization
{
    /// <summary>
    /// Configuration class that defines the mapping between NameStyle enum values and TextAnimator tags.
    /// This class can be used to create a StyleSheet asset with predefined name animation styles.
    /// </summary>
    [CreateAssetMenu(fileName = "NameStyleConfiguration", menuName = "BBB/ProfileCustomization/Name Style Configuration")]
    public class NameStyleConfiguration : ScriptableObject
    {
        [System.Serializable]
        public class NameStyleDefinition
        {
            [Header("Style Information")]
            public NameStyle nameStyle;
            public string displayName;
            [TextArea(2, 4)]
            public string description;
            
            [Head<PERSON>("TextAnimator Tags")]
            public string openingTag;
            public string closingTag;
            
            [Header("Preview")]
            public string previewText = "Player Name";
            
            public Style ToStyle()
            {
                var styleTag = nameStyle.ToString().ToLower();
                return new Style(styleTag, openingTag, closingTag);
            }
        }
        
        [Header("Name Style Definitions")]
        [SerializeField] private NameStyleDefinition[] _styleDefinitions = new NameStyleDefinition[]
        {
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Default,
                displayName = "Default",
                description = "Standard text with no animation",
                openingTag = "",
                closingTag = "",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Rainbow,
                displayName = "Rainbow",
                description = "Cycling rainbow colors",
                openingTag = "<rainbow>",
                closingTag = "</rainbow>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Glow,
                displayName = "Glow",
                description = "Glowing text effect",
                openingTag = "<glow>",
                closingTag = "</glow>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Bounce,
                displayName = "Bounce",
                description = "Bouncing animation",
                openingTag = "<bounce>",
                closingTag = "</bounce>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Shake,
                displayName = "Shake",
                description = "Shaking/vibrating effect",
                openingTag = "<shake>",
                closingTag = "</shake>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Pulse,
                displayName = "Pulse",
                description = "Pulsing scale animation",
                openingTag = "<pulse>",
                closingTag = "</pulse>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Wave,
                displayName = "Wave",
                description = "Wavy text movement",
                openingTag = "<wave>",
                closingTag = "</wave>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Typewriter,
                displayName = "Typewriter",
                description = "Typewriter reveal effect",
                openingTag = "<typewriter>",
                closingTag = "</typewriter>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Fade,
                displayName = "Fade",
                description = "Fade in/out animation",
                openingTag = "<fade>",
                closingTag = "</fade>",
                previewText = "Player Name"
            },
            new NameStyleDefinition
            {
                nameStyle = NameStyle.Rotate,
                displayName = "Rotate",
                description = "Rotating text animation",
                openingTag = "<rotate>",
                closingTag = "</rotate>",
                previewText = "Player Name"
            }
        };
        
        public NameStyleDefinition[] StyleDefinitions => _styleDefinitions;
        
        /// <summary>
        /// Gets the style definition for a specific name style
        /// </summary>
        /// <param name="nameStyle">The name style to get the definition for</param>
        /// <returns>The style definition, or null if not found</returns>
        public NameStyleDefinition GetStyleDefinition(NameStyle nameStyle)
        {
            foreach (var definition in _styleDefinitions)
            {
                if (definition.nameStyle == nameStyle)
                    return definition;
            }
            return null;
        }
        
        /// <summary>
        /// Creates a StyleSheetScriptable from this configuration
        /// </summary>
        /// <returns>A new StyleSheetScriptable with all the defined styles</returns>
        public StyleSheetScriptable CreateStyleSheet()
        {
            var styleSheet = CreateInstance<StyleSheetScriptable>();
            var styles = new List<Style>();
            
            foreach (var definition in _styleDefinitions)
            {
                if (definition.nameStyle != NameStyle.Default) // Skip default as it has no tags
                {
                    styles.Add(definition.ToStyle());
                }
            }
            
            styleSheet.Styles = styles.ToArray();
            return styleSheet;
        }
        
        /// <summary>
        /// Gets the display name for a name style
        /// </summary>
        /// <param name="nameStyle">The name style to get the display name for</param>
        /// <returns>The display name, or the enum name if not found</returns>
        public string GetDisplayName(NameStyle nameStyle)
        {
            var definition = GetStyleDefinition(nameStyle);
            return definition?.displayName ?? nameStyle.ToString();
        }
        
        /// <summary>
        /// Gets the description for a name style
        /// </summary>
        /// <param name="nameStyle">The name style to get the description for</param>
        /// <returns>The description, or empty string if not found</returns>
        public string GetDescription(NameStyle nameStyle)
        {
            var definition = GetStyleDefinition(nameStyle);
            return definition?.description ?? string.Empty;
        }
    }
}
