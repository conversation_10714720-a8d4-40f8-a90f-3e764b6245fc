using System;

namespace BBB.ProfileCustomization
{
    /// <summary>
    /// Enum representing different name animation styles available for user customization.
    /// Each style corresponds to a TextAnimator tag defined in the NameStyleSheet.
    /// </summary>
    [Serializable]
    public enum NameStyle
    {
        /// <summary>
        /// Default style with no special animation
        /// </summary>
        Default = 0,
        
        /// <summary>
        /// Rainbow color cycling animation
        /// </summary>
        Rainbow = 1,
        
        /// <summary>
        /// Glowing effect animation
        /// </summary>
        Glow = 2,
        
        /// <summary>
        /// Bouncing text animation
        /// </summary>
        Bounce = 3,
        
        /// <summary>
        /// Shaking/vibrating text animation
        /// </summary>
        Shake = 4,
        
        /// <summary>
        /// Pulsing scale animation
        /// </summary>
        Pulse = 5,
        
        /// <summary>
        /// Wavy text animation
        /// </summary>
        Wave = 6,
        
        /// <summary>
        /// Typewriter effect animation
        /// </summary>
        Typewriter = 7,
        
        /// <summary>
        /// Fade in/out animation
        /// </summary>
        Fade = 8,
        
        /// <summary>
        /// Rotating text animation
        /// </summary>
        Rotate = 9
    }
}
