using BBB.ProfileCustomization;
using TMPro;
using UnityEngine;

namespace BBB.ProfileCustomization.Examples
{
    /// <summary>
    /// Example showing how to integrate the name styling system with existing code.
    /// This demonstrates the simple changes needed to add name styling to current name displays.
    /// </summary>
    public class NameStyleIntegrationExample : MonoBehaviour
    {
        [Header("Example: Chat Message Integration")]
        [SerializeField] private TextMeshProUGUI _chatSenderName;
        
        [Header("Example: Leaderboard Integration")]
        [SerializeField] private TextMeshProUGUI _leaderboardPlayerName;
        
        [Header("Example: Profile Integration")]
        [SerializeField] private TextMeshProUGUI _profileDisplayName;
        
        /// <summary>
        /// Example: How to modify existing chat message setup to include name styling
        /// 
        /// BEFORE (existing code):
        /// _name.text = senderName;
        /// 
        /// AFTER (with name styling):
        /// _name.text = NameStyleUtility.StyleName(senderName);
        /// </summary>
        public void SetupChatSenderInfo_Example(string senderName, string country = null)
        {
            if (_chatSenderName == null) return;
            
            // Original logic for handling country localization
            var text = senderName;
            if (!string.IsNullOrEmpty(country))
            {
                // Your existing localization logic here
                // text = _localizationManager.getLocalizedTextWithArgs("SOCIAL_MESSAGE_SENDER_NAME", senderName, countryLocalized);
            }
            
            // SIMPLE INTEGRATION: Just wrap the final text assignment with NameStyleUtility.StyleName()
            _chatSenderName.text = NameStyleUtility.StyleName(text);
        }
        
        /// <summary>
        /// Example: How to modify existing leaderboard player setup to include name styling
        /// 
        /// BEFORE (existing code):
        /// _nameText.text = playerName;
        /// 
        /// AFTER (with name styling):
        /// _nameText.text = NameStyleUtility.StyleName(playerName);
        /// </summary>
        public void SetupLeaderboardPlayer_Example(string playerName, int score, int place, bool isOwn)
        {
            if (_leaderboardPlayerName == null) return;
            
            // Your existing setup logic here
            // _scoreText.text = score.ToString();
            // _placeText.text = place.ToString();
            // _ownPlayerAvatarBg.SetActive(isOwn);
            
            // SIMPLE INTEGRATION: Apply name styling
            _leaderboardPlayerName.text = NameStyleUtility.StyleName(playerName);
            
            // Optional: Apply different styling for own player vs others
            if (isOwn)
            {
                // Could apply current user's style preference
                _leaderboardPlayerName.text = NameStyleUtility.StyleName(playerName);
            }
            else
            {
                // Could apply a specific style for other players, or no style
                _leaderboardPlayerName.text = NameStyleUtility.StyleName(playerName, NameStyle.Default);
            }
        }
        
        /// <summary>
        /// Example: How to modify existing profile display to include name styling
        /// 
        /// BEFORE (existing code):
        /// _nameText.text = _accountManager.Profile.DisplayName;
        /// 
        /// AFTER (with name styling):
        /// _nameText.text = NameStyleUtility.StyleName(_accountManager.Profile.DisplayName);
        /// </summary>
        public void UpdateProfileDisplay_Example(string displayName)
        {
            if (_profileDisplayName == null) return;
            
            // SIMPLE INTEGRATION: Apply current user's name style preference
            _profileDisplayName.text = NameStyleUtility.StyleName(displayName);
        }
        
        /// <summary>
        /// Example: Server response handling for name style preferences
        /// This shows how to handle server responses that include name style information
        /// </summary>
        public void HandleServerResponse_Example(string nameStyleFromServer)
        {
            // Server can send name style in different formats:
            // 1. Enum name: "Rainbow", "Glow", etc.
            // 2. Lowercase: "rainbow", "glow", etc.  
            // 3. Numeric: "1", "2", etc.
            
            if (System.Enum.TryParse<NameStyle>(nameStyleFromServer, true, out var nameStyle))
            {
                // Apply the style from server
                var nameStyleManager = NameStyleUtility.GetNameStyleManager();
                nameStyleManager?.SetNameStyle(nameStyle);
            }
        }
        
        /// <summary>
        /// Example: How to check if a style is available before showing it in UI
        /// </summary>
        public void PopulateStyleSelectionUI_Example()
        {
            // Get all available styles for the user
            foreach (NameStyle style in System.Enum.GetValues(typeof(NameStyle)))
            {
                if (NameStyleUtility.IsStyleUnlocked(style))
                {
                    // Add to UI selection
                    Debug.Log($"Style available: {style}");
                    // CreateStyleButton(style);
                }
            }
        }
        
        /// <summary>
        /// Example: How to preview a style before applying it
        /// </summary>
        public void PreviewStyle_Example(NameStyle style, string previewText = "Preview Name")
        {
            if (_profileDisplayName == null) return;
            
            // Show preview of the style
            var styledPreview = NameStyleUtility.StyleName(previewText, style);
            _profileDisplayName.text = styledPreview;
        }
        
        /// <summary>
        /// Example: Integration with existing TextAnimator components
        /// If you already have TextAnimator components, you can use the TextAnimator-specific methods
        /// </summary>
        public void SetupTextAnimatorIntegration_Example(Febucci.UI.TextAnimator_TMP textAnimator, string playerName)
        {
            if (textAnimator == null) return;
            
            // Use the TextAnimator-specific utility method
            NameStyleUtility.ApplyStyleToTextAnimator(textAnimator, playerName);
        }
    }
}
