using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.AvatarCustomization.UI
{
    public class AvatarFrameView : ContextedUiBehaviour
    {
        [SerializeField] private AsyncImage _avatarImage;
        [SerializeField] private string _defaultAvatar = "avatar_fennec";
        
        private IAssetsManager _assetsManager;
        
        protected override void InitWithContextInternal(IContext context)
        {
            _assetsManager = context.Resolve<IAssetsManager>();
        }

        protected override void Awake()
        {
            base.Awake();
            if (_avatarImage == null)
            {
                BDebug.LogError(LogCat.General,$"AsyncImage is not setup for AsyncAvatar: {gameObject.name}");
            }
        }

        public async UniTaskVoid SetupAvatar(string avatarUrl)
        {
            LazyInit();

            if (_avatarImage != null)
            {
                var sprite = await _assetsManager.LoadSpriteAsync(GenericResourceProvider.FallbackOfflineAvatar);
                _avatarImage.ReplaceSprite(sprite);
            }
            else
            {
                BDebug.LogError(LogCat.General,$"AsyncImage is not setup for AsyncAvatar: {gameObject.name}");
            }
        }
        
        public void Clear()
        {
            if (_avatarImage != null)
            {
                _avatarImage.Stop();
            }
            else
            {
                BDebug.LogError(LogCat.General,$"AsyncImage is not setup for AsyncAvatar: {gameObject.name}");
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Clear();
        }
    }
}