using System;
using System.Collections.Generic;
using Assets.UltimateIsometricToolkit.Scripts.External;
using BBB.DI;
using BBB.UI;
using BBB.UI.Core;
using Core.Configs;
using FBConfig;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class PassportRanksObjectiveView : ContextedUiBehaviour
    {
        [SerializeField] private GameObject _inProgressHolder;
        [SerializeField] private GameObject _inactiveHolder;
        [SerializeField] private GameObject _readyToClaimHolder;
        [SerializeField] private GameObject _claimedHolder;
        [SerializeField] private GameObject _checkMark;
        [SerializeField] private UIRewardComponent[] _rewards;

        [SerializeField] private GameObject[] _currentRankObjects;

        [SerializeField] private LocalizedTextPro[] _titles;
        [SerializeField] private LocalizedTextPro[] _descriptions;

        [SerializeField] private Button _rankButton;
        [SerializeField] private AsyncLoadableImage _image;
        [SerializeField] private MaterialSwapper _materialSwapper;
        [SerializeField] private Transform _rewardSpeechBubbleAnchor;

        private Dictionary<string, int> _cachedRewardDictionary = new();
        public string QuestUid { get; private set; }

        private Action _claimCallback;
        private Action<Dictionary<string, int>, Vector3> _rankButtonClickedCallback;
        public ObjectiveState CachedState { get; private set; }

        protected override void InitWithContextInternal(IContext context)
        {
            foreach (var reward in _rewards)
                reward.Init(context);

            _materialSwapper.StoreOriginalValues();
            _claimCallback = null;
            _rankButtonClickedCallback = null;
            QuestUid = string.Empty;
            CachedState = ObjectiveState.Inactive;
            _cachedRewardDictionary.Clear();
        }

        private void ResetView()
        {
            _inProgressHolder.SetActive(false);
            _inactiveHolder.SetActive(false);
            _readyToClaimHolder.SetActive(false);
            _claimedHolder.SetActive(false);
            _checkMark.SetActive(false);
            _currentRankObjects.ForEach(x => x.SetActive(false));
        }

        public void Setup(QuestObjectiveConfig objectiveConfig, ObjectiveState state, Action claimCallback,
            Action<Dictionary<string, int>, Vector3> rankButtonCallback)
        {
            LazyInit();
            QuestUid = objectiveConfig.Uid;
            _image.Show(objectiveConfig.Thumbnail);
            
            _claimCallback = claimCallback;
            _rankButtonClickedCallback = rankButtonCallback;
            _rankButton.ReplaceOnClick(OnRankButtonClicked);
            
            RefreshState(state);
            foreach (var title in _titles)
                title.SetTextId(objectiveConfig.Title);

            foreach (var description in _descriptions)
                description.SetTextId(objectiveConfig.Desc);

            _cachedRewardDictionary = FlatBufferHelper.ToDict(objectiveConfig.Reward, objectiveConfig.RewardLength);
            foreach (var reward in _rewards)
                reward.SetupReward(_cachedRewardDictionary);
        }

        public void RefreshState(ObjectiveState state)
        {
            ResetView();
            CachedState = state;
            switch (state)
            {
                case ObjectiveState.Done:
                    _rankButton.interactable = true;
                    _readyToClaimHolder.SetActive(true);
                    break;
                
                case ObjectiveState.Claimed:
                    _rankButton.interactable = false;
                    _claimedHolder.SetActive(true);
                    _checkMark.SetActive(true);
                    break;
                
                case ObjectiveState.Inactive:
                default:
                    _rankButton.interactable = true;
                    _inactiveHolder.SetActive(true);
                    _materialSwapper.SetNewValue();
                    break;
            }
        }

        private void ClaimObjective()
        {
            _claimCallback?.Invoke();
        }

        private void OnRankButtonClicked()
        {
            switch (CachedState)
            {
                case ObjectiveState.Claimed:
                    return;
                case ObjectiveState.Done:
                    ClaimObjective();
                    break;
                default:
                    ShowRewardSpeechBubble();
                    break;
            }
        }
        
        private void ShowRewardSpeechBubble()
        {
            _rankButtonClickedCallback?.Invoke(_cachedRewardDictionary, _rewardSpeechBubbleAnchor.position);
        }

        public void SetAsNext()
        {
            _inProgressHolder.SetActive(true);
            _materialSwapper.SetNewValue();
        }
    }
}