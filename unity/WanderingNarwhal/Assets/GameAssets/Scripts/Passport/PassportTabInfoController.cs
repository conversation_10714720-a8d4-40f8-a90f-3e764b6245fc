using BBB.DI;
using BBB.Generic.Modal;
using BBB.Quests;
using BebopBee.Core;
using BebopBee.Core.UI;
using Core.RPC;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.DailyEvents;
using GameAssets.Scripts.SocialScreens.Teams;

namespace BBB
{
    public class PassportTabInfoController : PassportTabController<PassportTabInfoView>
    {
        private ISocialManager _socialManager;
        private IModalsBuilder _modalsBuilder;
        private ILocationManager _locationManager;
        private ChangeNameModalController _changeNameModalController;

        private GenericModalFactory _genericModalFactory;
        private ILeaderboardManager _leaderboardManager;

        protected override void OnContextInitialized(IContext context)
        {
            _socialManager = context.Resolve<ISocialManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _locationManager = context.Resolve<ILocationManager>();
            _leaderboardManager = context.Resolve<ILeaderboardManager>();

            _genericModalFactory = context.Resolve<GenericModalFactory>();
        }

        protected override void OnShow()
        {
            Subscribe();
        }

        protected override void OnHide()
        {
            Unsubscribe();
        }

        private void Unsubscribe()
        {
            InternalView.BadgesClicked -= BadgesClickedHandler;
            InternalView.ChangeNameClicked -= ChangeNameClickedHandler;
        }

        private void Subscribe()
        {
            Unsubscribe();
            InternalView.BadgesClicked += BadgesClickedHandler;
            InternalView.ChangeNameClicked += ChangeNameClickedHandler;
        }

        private void ChangeNameClickedHandler()
        {
            PassportModalController.LogTapEvent();
            
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _genericModalFactory.ShowNoConnectionModal();
                return;
            }

            _changeNameModalController = _modalsBuilder.CreateModalView<ChangeNameModalController>(ModalsType.ChangeName);

            _changeNameModalController.OnClose -= ChangeNameModalCloseHandler;
            _changeNameModalController.OnClose += ChangeNameModalCloseHandler;

            _changeNameModalController.ShowModal(ShowMode.Delayed);
            HideModal();
        }
        
        private void ChangeNameModalCloseHandler()
        {
            _changeNameModalController.OnClose -= ChangeNameModalCloseHandler;
            PassportModalController.Setup(PassportTab.Info);
            PassportModalController.ShowModal(ShowMode.Delayed);
        }

        private void BadgesClickedHandler()
        {
            PassportModalController.LogTapEvent();
            PassportModalController.ShowAchievementsTab();
        }
    }
}