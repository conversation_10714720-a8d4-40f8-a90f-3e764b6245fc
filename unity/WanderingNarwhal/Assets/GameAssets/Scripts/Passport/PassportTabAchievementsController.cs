using BBB.Audio;
using BBB.DI;
using BebopBee.Core.Audio;
using FBConfig;

namespace BBB
{
    public class PassportTabAchievementsController : PassportTabController<PassportTabAchievementsView>
    { 
        private QuestManager _questManager;

        protected override void OnContextInitialized(IContext context)
        {
            _questManager = context.Resolve<QuestManager>();
        }

        protected override void OnShow()
        {
            Subscribe();
        }

        protected override void OnHide()
        {
            Unsubscribe();
        }

        private void Unsubscribe()
        {
            InternalView.ClaimButtonClicked -= OnClaimButtonClicked;
        }
        
        private void Subscribe()
        {
            Unsubscribe();
            InternalView.ClaimButtonClicked += OnClaimButtonClicked;
        }

        private void OnClaimButtonClicked(QuestObjectiveConfig questObjectiveConfig)
        {
            AudioProxy.PlaySound(GenericSoundIds.ClaimOrRevealButtonTap);
            _questManager.ClaimObjective(questObjectiveConfig);
            InternalView.RefreshRanksQuest();
        }
    }
}