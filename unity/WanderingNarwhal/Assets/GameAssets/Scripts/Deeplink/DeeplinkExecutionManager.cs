using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using BBB;
using BBB.ProfileCustomization.UI;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core.UI;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Deeplink.Gifts;
using UnityEngine;

namespace GameAssets.Scripts.Deeplink
{
    public class DeepLinkExecutionManager : IContextInitializable, IContextReleasable
    {
        public event Action DeeplinkQueueUpdated;

        private const float TimeOutLimit = 5f;

        private AdminGifts _adminGiftsPendingToApply;

        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private IGameEventManager _gameEventManager;
        private IAccountManager _accountManager;
        private IPlayerManager _playerManager;
        private IModalsBuilder _modalsBuilder;
        private WeeklyLeaderboardManager _weeklyLeaderboardManager;
        private GenericModalFactory _genericModalFactory;
        private ILocalizationManager _localizationManager;
        private IAvatarDecorationManager _avatarDecorationManager;
        
        private DeepLinkGiftData? _deeplinkGiftData;
        private DeepLinkStatus _deepLinkStatus;
        private Action _buddyGiftCardDelegate;
        private Action _modalDeeplinkDelegate;

        public void InitializeByContext(IContext context)
        {
            _accountManager = context.Resolve<IAccountManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
        }

        public void Init(IContext context)
        {
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _weeklyLeaderboardManager = context.Resolve<WeeklyLeaderboardManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _avatarDecorationManager = context.Resolve<IAvatarDecorationManager>();
        }

        public void ReleaseByContext(IContext context)
        {
            DeeplinkQueueUpdated = null;
        }

        // Safe to do as we guarantee execution of only 1 command at a time
        public void SetDeeplinkStatus(DeepLinkStatus deepLinkStatus)
        {
            _deepLinkStatus = deepLinkStatus;
            DeeplinkQueueUpdated?.Invoke();
        }

        public void SetAdminGifts(Dictionary<string, int> newAdminGifts, string title, string message, string rewardTitle)
        {
            if (newAdminGifts == null || newAdminGifts.Count == 0)
                return;

            var currentAdminGifts = _playerManager.Player.AdminGifts;
            var gifts = GetAdminGifts(currentAdminGifts, newAdminGifts);
            if (gifts == null || gifts.Count == 0)
                return;

            _adminGiftsPendingToApply = new AdminGifts
            {
                Title = title,
                Message = message,
                RewardTitle = rewardTitle,
                Rewards = newAdminGifts
            };
        }

        public void ScheduleAwardDeeplink(Dictionary<string, int> gifts, GiftModalOptions modalOptions)
        {
            // as we invoke it now as part of the flow, we do not want to break scheduling
            modalOptions.ShowMode = ShowMode.Delayed;
            _deeplinkGiftData = new DeepLinkGiftData()
            {
                Gifts = gifts,
                ModalOptions = modalOptions,
            };
            DeeplinkQueueUpdated?.Invoke();
        }

        public void ScheduleBuddyGiftDeeplink(Action buddyGiftCardDelegate)
        {
            _buddyGiftCardDelegate = buddyGiftCardDelegate;
            DeeplinkQueueUpdated?.Invoke();
        }

        public void ScheduleModalDeeplink(Action modalDeeplinkDelegate)
        {
            _modalDeeplinkDelegate = modalDeeplinkDelegate;
            DeeplinkQueueUpdated?.Invoke();
        }

        public IEnumerator Execute(Action breakCallback)
        {
            BDebug.Log(LogCat.DeepLink, $"DeepLinkExecutionManager.Execute _deeplinkGiftData {_deeplinkGiftData}, _buddyGiftCardDelegate {_buddyGiftCardDelegate}, _modalDeeplinkDelegate {_modalDeeplinkDelegate},");
            
            if (_deeplinkGiftData != null)
            {
                AwardGifts(_deeplinkGiftData.Value.Gifts, _deeplinkGiftData.Value.ModalOptions, TransactionTag.Gift,
                    new GiftAnalyticsData { Category = CurrencyFlow.Social.Name, Family = CurrencyFlow.Social.Gifts.Name, Item = CurrencyFlow.Social.Gifts.DeepLink });

                _deeplinkGiftData = null;
                breakCallback?.Invoke();
                yield break;
            }

            if (_buddyGiftCardDelegate != null)
            {
                _buddyGiftCardDelegate.Invoke();
                _buddyGiftCardDelegate = null;
                breakCallback?.Invoke();
                yield break;
            }

            if (TryAwardAdminGifts())
            {
                BDebug.Log(LogCat.Flow, $"Flow Action: AwardAdminGifts, TryAwardAdminGifts");
                breakCallback?.Invoke();
                yield break;
            }

            if (_modalDeeplinkDelegate != null)
            {
                _modalDeeplinkDelegate.Invoke();
                _modalDeeplinkDelegate = null;
                breakCallback?.Invoke();
                yield break;
            }

            // it is possible to have command loading while other delegates is not null, so it makes sense to show instantly available
            // but if all of them null, then we actually waiting for some validation command to finish
            // if not timed out, we repeat the call without entering the branch as _deeplink status is different now
            if (_deepLinkStatus == DeepLinkStatus.Loading)
            {
                var deepLinkValidationModalController = _modalsBuilder.CreateModalView<DeepLinkValidationModalController>(ModalsType.DeepLinkValidation);
                deepLinkValidationModalController.ShowModal(ShowMode.Delayed);

                var loopStartTime = Time.realtimeSinceStartup;
                while (_deepLinkStatus == DeepLinkStatus.Loading && Time.realtimeSinceStartup < loopStartTime + TimeOutLimit)
                    yield return null;

                deepLinkValidationModalController.Hide();

                // we are in timeout scenario
                if (_deepLinkStatus == DeepLinkStatus.Loading)
                {
                    // It could have timed out by multiple reasons, if it was due connectivity then we show the no connection modal
                    if (!ConnectivityStatusManager.ConnectivityReachable)
                    {
                        _genericModalFactory.ShowNoConnectionModal(null, _localizationManager.getLocalizedText("DEEPLINK_NO_CONNECTION_ERROR_TITLE"), 
                                                                    _localizationManager.getLocalizedText("DEEPLINK_NO_CONNECTION_ERROR_DESC"));
                    }

                    yield break;
                }

                // if not timeout, we repeat checks for delegates
                // recursion is safe here as we repeating calls only on _deeplinkStatus changes during previous loop part
                // and it is guaranteed that multiple commands will not be changing it in parallel
                yield return Execute(breakCallback);
            }
        }

        private bool TryAwardAdminGifts()
        {
            if (_adminGiftsPendingToApply == null) return false;
            
            var adminGifts = _adminGiftsPendingToApply.Rewards;
            var currentAdminGifts = _playerManager.Player.AdminGifts;
            var gifts = GetAdminGifts(currentAdminGifts, adminGifts);

            if (gifts == null || gifts.Count == 0)
                return false;

            var playerName = _accountManager.Profile.Name;
            
            var giftTitle = ReplacePlayerName(_adminGiftsPendingToApply.Title, playerName, LocalizationManagerHelper.DefaultGiftTitleKey);
            var message = ReplacePlayerName(_adminGiftsPendingToApply.Message, playerName, LocalizationManagerHelper.DefaultGiftMessageKey);
            var rewardTitle = ReplacePlayerName(_adminGiftsPendingToApply.RewardTitle, playerName);

            var giftModalOptions = new GiftModalOptions
            {
                GiftTitle = giftTitle,
                Message = message,
                Title = rewardTitle,
                ShowMode = ShowMode.Delayed,
            };
            
            AwardGifts(gifts, giftModalOptions, TransactionTag.AdminGift, new GiftAnalyticsData { Category = CurrencyFlow.AdminGift.Name, Family = "", Item = "" });

            // Update local admin gift
            //TODO: Assigning Dictionary does not work with serialization??
            foreach (var kvp in adminGifts)
            {
                _playerManager.Player.AdminGifts[kvp.Key] = kvp.Value;
            }

            _playerManager.MarkDirty();

            _adminGiftsPendingToApply = null;
            return true;
        }
        
        private string ReplacePlayerName(string text, string playerName, string defaultLocalizationKey = null)
        {
            if (text.IsNullOrEmpty() && !defaultLocalizationKey.IsNullOrEmpty())
            {
                return _localizationManager.getLocalizedTextWithArgs(defaultLocalizationKey, playerName);
            }
            
            if (text.IsNullOrEmpty() || playerName.IsNullOrEmpty()) return text;
            
            const string namePlaceholder = "<name>";
            return Regex.Replace(text, namePlaceholder, playerName, RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// Checks whether there's new Admin Gifts to give to the user. Since Admin Gifts are saved in the user's profile,
        /// we know what has already been given to the them. This function will compare old/new Admin Gifts and only award
        /// the difference.
        /// </summary>
        private Dictionary<string, int> GetAdminGifts(IDictionary<string, int> currentAdminGifts, IDictionary<string, int> adminGiftsToAward)
        {
            if (adminGiftsToAward == null || adminGiftsToAward.Count == 0) return null;

            Dictionary<string, int> gifts = new();

            foreach (var adminGift in adminGiftsToAward)
            {
                var newValue = adminGift.Value;
                if (currentAdminGifts.TryGetValue(adminGift.Key, out var oldValue))
                {
                    newValue -= oldValue;
                }

                if (newValue > 0)
                {
                    gifts.Add(adminGift.Key, newValue);
                }
            }

            return gifts;
        }

        private void AwardGifts(Dictionary<string, int> gifts, GiftModalOptions modalOptions, string transactionTag, GiftAnalyticsData analyticsData)
        {
            FilterGifts(gifts);

            var transaction = new Transaction()
                .AddTag(transactionTag)
                .SetAnalyticsData(analyticsData.Category, analyticsData.Family, analyticsData.Item)
                .Earn(gifts);
            _walletManager.TransactionController.MakeTransaction(transaction);
            modalOptions.InvokeRewarded();

            AwardSpecialItems(gifts);

            if (gifts.Count == 0) //don't have any valid reward to claim
                return;

            var isRewardCollected = false;
            var giftModal = _modalsBuilder.CreateModalView<ClaimGiftModalController>(ModalsType.ClaimGift);
            giftModal.Setup(gifts, modalOptions.GiftTitle, modalOptions.Message, skippedCurrencies =>
                {
                    if (isRewardCollected) return;
                    isRewardCollected = true;

                    _uiWalletManager.VisualizeAllTransactionsWithTag(transactionTag, skippedCurrencies);
                },
                modalOptions.Title, modalOptions.Subtitle);

            giftModal.ShowModal(modalOptions.ShowMode);
        }

        public void FilterGifts(Dictionary<string, int> gifts)
        {
            foreach (var kvp in new Dictionary<string, int>(gifts))
            {
                var key = kvp.Key;
                if (InventoryItems.IsGameEventScore(key))
                {
                    if (!_gameEventManager.IsValidScore(key, out _))
                    {
                        gifts.Remove(key);
                    }
                }
                else
                {
                    switch (kvp.Key)
                    {
                        case InventoryItems.WeeklyTournament:
                        {
                            if (!_weeklyLeaderboardManager.ActiveTimeNow)
                            {
                                gifts.Remove(kvp.Key);
                            }

                            break;
                        }
                    }
                }
            }
        }

        private void AwardSpecialItems(Dictionary<string, int> rewards)
        {
            foreach (var kvp in rewards)
            {
                switch (kvp.Key)
                {
                    case InventoryItems.GameEventScore:
                    case InventoryItems.CollectionGameEventScore:
                    {
                        var gameEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                            ev.Status == GameEventStatus.Active);

                        gameEvent?.AddScore(kvp.Value);
                        if (gameEvent is CompetitionGameEvent
                            {
                                SpecialType: SpecialCompetitionEventType.Casual
                            } cGameEvent)
                        {
                            cGameEvent.EventLeaderboard.SubmitCurrentScore(null);
                        }

                        break;
                    }
                    case InventoryItems.SideMapEventScore:
                        var endGameEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                            ev.Status == GameEventStatus.Active && ev.GameplayType == GameEventGameplayType.SideMap);
                        endGameEvent?.AddScore(kvp.Value);
                        break;
                    case InventoryItems.EndOfContentEventScore:
                        var endOfContentEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                            ev.Status == GameEventStatus.Active && ev.GameplayType == GameEventGameplayType.EndOfContent);
                        endOfContentEvent?.AddScore(kvp.Value);
                        break;
                    case InventoryItems.WeeklyTournament:
                    {
                        int totalTrophies = _weeklyLeaderboardManager.TryAddWeeklyTrophy(kvp.Value);
                        if (totalTrophies > 0)
                        {
                            _weeklyLeaderboardManager.TrySubmitToWeeklyLeaderboard();
                        }

                        break;
                    }
                    
                    default:
                        _avatarDecorationManager.AddGiftToPlayerInventory(kvp.Key);
                        break;
                }
            }
        }

        public void Restart()
        {
            _buddyGiftCardDelegate = null;
            _modalDeeplinkDelegate = null;
        }

        private record AdminGifts
        {
            public string Title;
            public string Message;
            public string RewardTitle;
            public IDictionary<string, int> Rewards;
        }
    }
}