using System;
using System.Collections.Generic;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BebopBee;
using Core.Configs;
using UnityEngine;

namespace BBB
{
    public static class LoadingProcessTracker
    {
        public const string CommandNameAppControllerStart = "AppController_Start";
        public const string CommandNameAppControllerAwake = "AppController_Awake";
        
        public const string CommandNameShowScreen = "ShowScreen";
        public const string CommandNameLoadingScreens = "LoadingScreens";
        
        public static readonly string CommandNameLoadingScreensEnd = $"{CommandNameLoadingScreens}_End";
        public static readonly string CommandNameLoadingScreensConfig = $"{CommandNameLoadingScreens}_Config";
        public static readonly string CommandNameLoadingScreensCheckInProgress = $"{CommandNameLoadingScreens}_CheckInProgress";
        
        public static readonly string CommandNameLoadingScreensTransitionRoutines = $"{CommandNameLoadingScreens}_TransitionRoutines";
        public static readonly string CommandNameLoadingScreensTransitionRoutinesEnsureCityTransitionReady = $"{CommandNameLoadingScreensTransitionRoutines}_EnsureCityTransitionReady";
        public static readonly string CommandNameLoadingScreensTransitionRoutinesIsShown = $"{CommandNameLoadingScreensTransitionRoutines}_IsShown";
        public static readonly string CommandNameLoadingScreensTransitionRoutinesIsVisible = $"{CommandNameLoadingScreensTransitionRoutines}_IsVisible";
        
        public static readonly string CommandNameLoadingScreensCreateScreen = $"{CommandNameLoadingScreens}_CreateScreen";
        public static readonly string CommandNameLoadingScreensCreateScreenCmdsIsFinished = $"{CommandNameLoadingScreensCreateScreen}_CmdsIsFinished";
        public static readonly string CommandNameLoadingScreensCreateScreenTooManyLoops = $"{CommandNameLoadingScreensCreateScreen}_TooManyLoops";
        public static readonly string CommandNameLoadingScreensCreateScreenRestartGame = $"{CommandNameLoadingScreensCreateScreen}_RestartGame";
        public static readonly string CommandNameLoadingScreensCreateScreenOnScreenCreated = $"{CommandNameLoadingScreensCreateScreen}_OnScreenCreated";
        
        public static readonly string CommandNameLoadingScreensHandleResources = $"{CommandNameLoadingScreens}_HandleResources";
        public static readonly string CommandNameLoadingScreensHandleResourcesIterateResourcesMgr = $"{CommandNameLoadingScreensHandleResources}_IterateResourcesMgr";
        public static readonly string CommandNameLoadingScreensHandleResourcesIterateDone = $"{CommandNameLoadingScreensHandleResources}_IterateDone";
        
        public static readonly string CommandNameLoadingScreensOnTransitionSuccess = $"{CommandNameLoadingScreens}_OnTransitionSuccess";
        public static readonly string CommandNameLoadingScreensOnTransitionSuccessPreLoadScreen = $"{CommandNameLoadingScreensOnTransitionSuccess}_PreLoadScreen";
        public static readonly string CommandNameLoadingScreensOnTransitionSuccessWaitScrCtrlView = $"{CommandNameLoadingScreensOnTransitionSuccess}_WaitScrCtrlView";
        public static readonly string CommandNameLoadingScreensOnTransitionSuccessCacheResources = $"{CommandNameLoadingScreensOnTransitionSuccess}_CacheResources";
        public static readonly string CommandNameLoadingScreensOnTransitionSuccessViewReady = $"{CommandNameLoadingScreensOnTransitionSuccess}_ViewReady";
        public static readonly string CommandNameLoadingScreensOnTransitionSuccessWaitBar = $"{CommandNameLoadingScreensOnTransitionSuccess}_WaitBar";
        public static readonly string CommandNameLoadingScreensOnTransitionSuccessEndTransition = $"{CommandNameLoadingScreensOnTransitionSuccess}_EndTransition";
        public static readonly string CommandNameLoadingScreensOnTransitionSuccessFadeout = $"{CommandNameLoadingScreensOnTransitionSuccess}_OnTransitionSuccessFadeout";
        
        public static readonly string CommandNameLoadingScreensFinalizeTransition = $"{CommandNameLoadingScreens}_FinalizeTransition";
        public static readonly string CommandNameLoadingScreensFinalizeTransitionEndTransition = $"{CommandNameLoadingScreensFinalizeTransition}_EndTransition";
        public static readonly string CommandNameLoadingScreensFinalizeTransitionOnScreenChanged = $"{CommandNameLoadingScreensFinalizeTransition}_OnScreenChanged";
        
        public static readonly string CommandNameLoadingScreensPostFinish = $"{CommandNameLoadingScreens}_PostFinish";
        public static readonly string CommandNameLoadingScreensPostFinishWait = $"{CommandNameLoadingScreensPostFinish}_Wait";
        
        public const string ScreenNameMain = "Main";
        public const string ScreenNameSplash = "Splash";
        public const string ScreenNameLoading = "LoadingScreen";
        
        private const string TrackerEnabledKey = "PT/Enabled";
        public const string FpsTrackerEnabledKey = "PT/FpsEnabled";
        private const string TrackerUsersModLimitKey = "PT/UsersModLimit";
        private const string TrackerUsersModThresholdKey = "PT/UsersModThreshold";
        public const string FpsHitchThresholdKey = "PT/FpsHitchThreshol";
        private static int _usersModThreshold = 10;
        private static int _usersModLimit = 100;

        private static bool _initialized;
        private static bool _fpsTrackingEnabled;

        private static readonly Type[] RequiredConfigs =
        {
            typeof(FBConfig.SystemConfig),
        };

        // Dictionary to track step start times
        private static readonly Dictionary<string, DateTimeOffset> _stepStartTimes = new Dictionary<string, DateTimeOffset>();

        public static void Reset()
        {
            _initialized = false;
            _stepStartTimes.Clear();
        }
        
        public static void UpdateSettings(IConfig config)
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
            Config.OnConfigUpdated += OnConfigUpdated;

            var systemConfig = config.TryGetDefaultFromDictionary<FBConfig.SystemConfig>();

            PlayerPrefs.SetInt(TrackerEnabledKey, systemConfig.LoadingTrackingDisabled ? 0 : 1);
            PlayerPrefs.SetInt(FpsTrackerEnabledKey, systemConfig.FpsTrackingDisabled ? 0 : 1);

            if (systemConfig.LoadingTrackingLimit > 0)
            {
                PlayerPrefs.SetInt(TrackerUsersModLimitKey, systemConfig.LoadingTrackingLimit);
            }

            if (systemConfig.LoadingTrackingThreshold > 0)
            {
                PlayerPrefs.SetInt(TrackerUsersModThresholdKey, systemConfig.LoadingTrackingThreshold);
            }

            if (systemConfig.HitchFpsThreshold > 0)
            {
                PlayerPrefs.SetInt(FpsHitchThresholdKey, systemConfig.HitchFpsThreshold);
            }
            
            PlayerPrefs.Save();
        }

        private static void OnConfigUpdated(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            
            UpdateSettings(config);
        }

        private static void Initialize()
        {
            _usersModLimit = PlayerPrefs.GetInt(TrackerUsersModLimitKey, _usersModLimit);
            if (_usersModLimit <= 0)
            {
                _usersModLimit = 100;
            }

            _usersModThreshold = PlayerPrefs.GetInt(TrackerUsersModThresholdKey, _usersModThreshold);
            if (_usersModThreshold <= 0)
            {
                _usersModThreshold = 10;
            }
            
            _initialized = true;
        }

        public static void LogStep(string commandName, string screenName, string prevScreenName)
        {
            if (!_initialized)
            {
                Initialize();
            }

            if (IsInitialLoadingScreenChange(screenName, prevScreenName) && !LoadingTimeMetricsManager.InitialLoadingEnded)
            {
                LoadingTimeMetricsManager.ReportInitialLoadingStep(commandName);
            }
        }

        public static void StartStep(string commandName, string screenName, string prevScreenName)
        {
            if (!_initialized)
            {
                Initialize();
            }

            var stepKey = GetStepKey(commandName, screenName, prevScreenName);
            _stepStartTimes[stepKey] = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
        }

        public static void EndStep(string commandName, string screenName, string prevScreenName)
        {
            if (!_initialized)
            {
                Initialize();
            }

            var stepKey = GetStepKey(commandName, screenName, prevScreenName);
            
            if (_stepStartTimes.TryGetValue(stepKey, out var startTime))
            {
                var endTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
                
                // Report the step timing using LoadingTimeMetricsManager
                LoadingTimeMetricsManager.LogLoadingTimeMetric(commandName,  startTime, LoadingTimeMetricsManager.SpanKindLoading, endTime);
                // Remove the start time as it's no longer needed
                _stepStartTimes.Remove(stepKey);
            }
        }

        private static string GetStepKey(string commandName, string screenName, string prevScreenName)
        {
            return $"{commandName}_{screenName}_{prevScreenName}";
        }

        private static bool IsInitialLoadingScreenChange(string screenName, string prevScreenName) 
        {
            return IsInitialLoadingScreen(screenName) || IsInitialLoadingScreen(prevScreenName);
        }

        private static bool IsInitialLoadingScreen(string screenName)
        {
            return screenName is ScreenNameLoading or ScreenNameSplash;
        }

        public static void LogShowScreen(string screenName, string prevScreen, string context)
        {
            if (!IsInitialLoadingScreenChange(screenName, prevScreen))
            {
                LoadingTimeMetricsManager.LoadingStarted(false, screenName, prevScreen, context);
            }
            LogStep(CommandNameShowScreen, screenName, prevScreen);
        }

        public static void EndStep(string screenName, string prevScreenName)
        {
            LogStep(CommandNameLoadingScreensEnd, screenName, prevScreenName);
            
            if (IsInitialLoadingScreen(screenName))
                return;

            LoadingTimeMetricsManager.ReportLoading();
        }

        public static void Restart()
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
            _stepStartTimes.Clear();
        }
    }
}