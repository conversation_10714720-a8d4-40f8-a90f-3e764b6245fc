using System;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using BBB.Core.AssetBundles;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI;
using BBB.UI.Core;
using BBB.Modals;
using BBB.Navigation;
using BBB.Screens;
using UnityEngine;
using BebopBee.Social;
using BebopBee;
using UnityEngine.SceneManagement;
using UnityEngine.Serialization;
using UnityEngine.U2D;
using Object = UnityEngine.Object;
using BBB.Wallet;
using BebopBee.Core.Audio;
using BBB.Core.Ads;
using BBB.Ads;
using BBB.Ads.Mediator;
using BBB.ProfileCustomization.UI;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Crash;
using BBB.Core.Notifications;
using BBB.DailyTrivia;
using BBB.Generic.Modal;
using BBB.IAP;
using BBB.Match3;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.Narrative.Controllers;
using BBB.Quests;
using BBB.RaceEvents;
using BBB.Social;
using BBB.UI.Transitions;
using PBConfig;
using BBB.UI.UI.Controllers;
using BBB.ScreenRecording;
using Bebopbee.Core.Extensions.Unity;
using Bebopbee.Core.Systems.RpcCommandManager;
using BebopBee.Core;
using Bebopbee.Core.Systems.RpcCommandManager.Core;
using Core.Configs;
#if BBB_LOG || BBB_DEBUG
using BBB.Actions;
using Core.Debug;
#endif
using Cysharp.Threading.Tasks;
using Core.RPC;
using GameAssets.Scripts;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.Generic;
using GameAssets.Scripts.IAP.Baskets;
using GameAssets.Scripts.IAP.EndlessTreasure;
using GameAssets.Scripts.Lua;
using GameAssets.Scripts.Map.UI.Controllers;
using GameAssets.Scripts.Promotions;
using GameAssets.Scripts.Promotions.Banners;
using GameAssets.Scripts.SocialScreens.Teams;
using GameAssets.Scripts.SocialScreens.Teams.IceBreaker;
using GameAssets.Scripts.Theme;
using GameAssets.Scripts.Wallet.Visualizing;
using Loading.Commands;
using UI;
using Unity.Services.Core;
using UnityEngine.Profiling;
using BBB.BrainCloud;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BBB.Social.Chat;
using Newtonsoft.Json.Utilities;
using Bebopbee.Core.Systems.GamemessengerBase;
using Core.Rpc.Commands;
using BBB.Core.Systems.GameMessenger;
using BBB.TeamEvents;
using BugsnagUnityPerformance;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Collection;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Deeplink;
using GameAssets.Scripts.ModalPriorityDisplay;
using GameAssets.Scripts.Messages;
using GameAssets.Scripts.Tutorial.Core;
using BBB.Core.AssetBundles.Loaders;
using BBB.DailyLogin;
using BBB.Social.SignIn;
using GameAssets.Scripts.DailyTask;
using SoftMasking;
using GameAssets.Scripts.IAP;
using GameAssets.Scripts.Player;
using GameAssets.Scripts.BBBQuickActions;
using GameAssets.Scripts.Core.UI;
using GameAssets.Scripts.UI.OverlayDialog;
using Unity.Services.Core.Environments;
#if USE_NUNU_SDK && BBB_DEBUG
using GameAssets.Scripts.Nunu.NunuFlayerFunctions;
#endif

namespace BBB
{
    public class AppContext : UnityContext
    {
    }

    public partial class AppController : BbbMonoBehaviour, ICoroutineExecutor, IRestartable,
        IPlayerLoginDataReciever, IUrlOpener, IUnityContainer,
        IAssetsManagerReceiver, IUpdateDispatcher
    {
        public event Action OnUpdate;
        public event Action OnFixedUpdate;
        public event Action OnLateUpdate;

        private const string SessionCountKey = "user/sessionCount";

        [FormerlySerializedAs("_screensController")] [SerializeField]
        private ScreensManager _screensManager;

        [FormerlySerializedAs("_modalsController")] [SerializeField]
        private ModalsManager _modalsManager;

        [SerializeField] private TransitionManager _transitionManager;
        [SerializeField] private AnimatorShowable _curtain;
        [SerializeField] private LoadingViewPresenter _loadingScreenViewPresenter;
        [SerializeField] private OrientationTracker _orientationTracker;
        [SerializeField] private PopupManager _popupManager;
        [SerializeField] private FPSTracker _fpsTracker;
        [SerializeField] private OverlayDialogManager _overlayDialogManager;

        private ITutorialPlaybackController _tutorialPlaybackController;
        private UIWalletManager _uiWalletManager;
        private OverlayEffectRenderer _overlayEffectRenderer;
        private GenericHudManager _genericHudManager;
        private IContext _appContext;
        private IContext _globalContext;

        private RPCService _rpcService;

        private Config _config;
        private PlayerManager _playerManager;

        private UserSettings _userSettings;
        private ILeaderboardManager _leaderboardManager;
        private WeeklyLeaderboardManager _weeklyLeaderboardManager;
        private ILivesManager _livesManager;
        private IBoosterManager _boosterManager;
        private LuaManager _luaManager;
        private LocalizationManager _localizationManager;
        private ILockManager _lockManager;
        private IThemeManager _themeManager;
        private TimeManager _timeManager;
        private BundlesConfigUpdater _bundlesConfigUpdater;
        private IEventDispatcher _eventDispatcher;
        private IapManager _iapManager;
        private GachaManager _gachaManager;
        private QuestManager _questManager;
        private AssetDownloadManager _assetDownloadManager;
        private ScreensBuilder _screensBuilder;
        private ModalsBuilder _modalsBuilder;
        private PlayerObjectivesManager _pom;
        private BrainCloudManager _brainCloudManager;

        private FakeUsersManager _fakeUsersManager;
        private PlayerComEventsManager _playerEventsManager;
        private AsyncCommandManager _asyncCommandManager;
        private AsyncCommandGenerator _asyncCommandGenerator;
        private AssetBundleManager _bundleManager;
        private AssetsManager _assetsManager;
        private LocationManager _locationManager;
        private FlagsLoader _flagsLoader;

        private IWalletManager _walletManager;
        private GenericResourceProvider _genericResourceProvider;
        private INotificationManager _notificationManager;
        private LevelsOrderingManager _levelsOrderingManager;

        private AccountManager _accountManager;
        private LoginManager _loginManager;

        private AdsManager _adsManager;
        private VideoAdsPlayer _videoAdsPlayer;
        private InfoModalFactory _infoModalFactory;
        private ObjectiveProgressionTrackerListener _objectiveProgressListener;
        private GameNotificationManager _gameNotificationManager;

        private LocalNotificationsScheduler _localNotificationsScheduler;
        private bool _blockSchedulingNotifications;

        private RpcCommandManager _rpcCommandManager;
        private double _maxSleptTime = 300.0D; // 5 minutes
        private DateTime _pauseStartTime = DateTime.MinValue;
        private GenericModalFactory _genericModalFactory;
        private int _lastRpcFrameSend;
        private LevelNarrativeController _levelNarrativeController;
        private const int RpcFramesInterval = 15;
        private bool _restarting;
        private IPerformanceManager _performanceManager;
        private GameEventManager _gameEventManager;
        private RaceEventManager _raceEventManager;
        private RoyaleEventManager _royaleEventManager;
        private TeamEventManager _teamEventManager;
        private ProgressCommandsManager _loadingCommands;
        private AdLootboxManager _adLootboxManager;
        private BundlesBackgroundDownloaderManager _bundlesBackgroundDownloaderManager;
        private MMVibrationsWrapper _vibrations;
        private IPriorityLoader _priorityLoader;
        private AnalyticsPropertiesMonitor _analyticsMonitor;
        private IDeepLinkManager _deepLinkManager;
        private IDeepLinkNotifier _deepLinkNotifier;
        private INotificationService _notificationService;
        private bool _awakeCalled;
        private ProtobufSerializer _protobufSerializer;

        private static AppController _instance;

        private bool _tokenSent;
        private ErrorMonitor _errorMonitor;
        private PromotionManager _promotionManager;
        private ChallengeTriviaManager _challengeTriviaManager;
        private EndlessTreasureManager _endlessTreasureManager;
        private DailyTriviaManager _dailyTriviaManager;
        private DailyLoginManager _dailyLoginManager;
        private ConfigsLoader _configsLoader;
        private IHelpDeskManager _helpDeskManager;
        private VideoAdManager _videoAdManager;
        private ScreenRecorder _screenRecorder;
        private HudSlotsManager _hudSlotsManager;
        private IEpisodeTaskManager _episodeTaskManager;
        private IEpisodicScenesManager _episodicScenesManager;
        private IDailyTasksManager _dailyTasksManager;
        private ISignInManager _signInManager;

        private Match3LockListener _match3LockListener;
        private DeepLinkExecutionManager _deepLinkExecutionManager;
        private QuickActionsManager _quickActionsManager;

        // To avoid repeated error logs in LateUpdate
        private static bool _tickPlayerErrorReported;

        private bool _loginCompleted;

        private void Awake()
        {
            if (_awakeCalled)
                return;
            
            CultureInfo.DefaultThreadCurrentCulture = CultureInfo.InvariantCulture;
            CultureInfo.DefaultThreadCurrentUICulture = CultureInfo.InvariantCulture;

            MemoryAdviceApi.Initialize();
            MetricsManagerHelper.InitializeTechKpiManagers();
            LoadingProcessTracker.Reset();
            
            var sessionCount = GetSessionCount() + 1;
            SetSessionCount(sessionCount);
            _screenRecorder = new ScreenRecorder(sessionCount);
            _screenRecorder.SetRecordingMode();

            _awakeCalled = true;
            _instance = this;

            Application.backgroundLoadingPriority = ThreadPriority.High;

            MemoryAdviceApi.RegisterMemoryStateWarn(MemoryTracker.OnLowMemory);

            // Do not remove. This is here as a fix for deserializing HashSet<> from a json string
            // https://github.com/jilleJr/Newtonsoft.Json-for-Unity/wiki/Fix-AOT-using-AotHelper
            AotHelper.EnsureList<int>();
            AotHelper.EnsureList<string>();
            AotHelper.EnsureList<BCMessageData>();
            AotHelper.EnsureList<ChatMessageAttachement>();
            AotHelper.EnsureList<BCUserRaceData>();
            AotHelper.EnsureList<BCUserGameEventData>();
            AotHelper.EnsureList<TScheduledTime>();
            AotHelper.EnsureList<BCTeamData>();
            AotHelper.EnsureList<BCEntity>();
            AotHelper.EnsureList<BCOpponentData>();
            AotHelper.EnsureList<BCPlayerLeaderboardEntry>();
            AotHelper.EnsureList<BCGroupLeaderboardEntry>();
            AotHelper.EnsureList<BCPayoutRule>();
            AotHelper.EnsureList<BCPresenceData>();
            AotHelper.EnsureList<BCLeaderboardUserData>();
            AotHelper.EnsureList<BCTeamEventMembersData>();
            AotHelper.EnsureList<BCTeamVsTeamData>();

            //Do not Remove this, it's for Generics optimization for AOT compiler
            AOTCodeGenerationHelper.UsedOnlyForAOTCodeGeneration();

            if (AppDefinesConverter.UnityAndroid && !AppDefinesConverter.UnityEditor)
            {
                Screen.fullScreenMode = FullScreenMode.FullScreenWindow;
            }

            UniTaskScheduler.UnobservedTaskException -= UniTask_UnhandledException;
            UniTaskScheduler.UnobservedTaskException += UniTask_UnhandledException;
            Application.logMessageReceivedThreaded -= HandleLog;
            Application.logMessageReceivedThreaded += HandleLog;
            SpriteAtlasManager.atlasRequested -= RequestAtlas;
            SpriteAtlasManager.atlasRequested += RequestAtlas;

            _protobufSerializer = new ProtobufSerializer();

            BDebug.Initialize();
            CustomScreen.Init();

            // ====== IMPORTANT =======
            // NOTE: do not add any more code here, at this stage the game has everything that it needs to start
            // any new code needs to be done through startup commands to avoid delays at loading
            LoadingProcessTracker.LogStep(LoadingProcessTracker.CommandNameAppControllerAwake, LoadingProcessTracker.ScreenNameMain, LoadingProcessTracker.ScreenNameSplash);
        }
        
        private int GetSessionCount()
        {
            return PlayerPrefs.GetInt(SessionCountKey, 0);
        }

        private void SetSessionCount(int sessionCount)
        {
            PlayerPrefs.SetInt(SessionCountKey, sessionCount);
            PlayerPrefs.Save();
        }

        private void Start()
        {
            _fpsTracker.StartRecording();
            _config = new Config();
            var configTypesToLoad = GetConfigTypesToLoad();
            _configsLoader = new ConfigsLoader(_config, configTypesToLoad);
            _loadingCommands = gameObject.GetOrAddComponent<ProgressCommandsManager>();
            _assetDownloadManager = GetComponent<AssetDownloadManager>();
            _brainCloudManager = GetComponent<BrainCloudManager>();
            _errorMonitor = new ErrorMonitor();
            
            Profiler.BeginSample("RegisterGlobalContext");
            var context = RegisterGlobalContext();
            Profiler.EndSample();
           
            Profiler.BeginSample("SetupLoadingCommands");
            _globalContext = SetupLoadingCommands(context);
            Profiler.EndSample();
            
            try
            {
                InitUgs();
            }
            catch (Exception e)
            {
                Debug.LogError($"UGS init error: {e}");
            }

            LoadingProcessTracker.LogStep(LoadingProcessTracker.CommandNameAppControllerStart, LoadingProcessTracker.ScreenNameMain, LoadingProcessTracker.ScreenNameSplash);

            _loadingCommands.Start();
        }
        
        private static async void InitUgs()
        {
            try
            {
#if BBB_PROD
                const string environment = "production";
#else
                const string environment = "development";
#endif
                var options = new InitializationOptions().SetEnvironmentName(environment);
                await UnityServices.InitializeAsync(options);
            }
            catch (Exception ex)
            {
                Debug.LogError($"UGS init error: {ex}");
            }
        }

        private IncrementalContext RegisterGlobalContext()
        {
            var incrementalContext = new IncrementalContext();

            incrementalContext.AddServiceToRegister<PopupManager>(_popupManager);
            incrementalContext.AddServiceToRegister<ProtobufSerializer>(_protobufSerializer);
            incrementalContext.AddServiceToRegister<IUrlOpener>(this);
            incrementalContext.AddServiceToRegister<IConfig>(_config);
            incrementalContext.AddServiceToRegister<IConfigsLoader>(_configsLoader);
            incrementalContext.AddServiceToRegister<ICoroutineExecutor>(this);
            incrementalContext.AddServiceToRegister<IRestartable>(this);
            incrementalContext.AddServiceToRegister<IConfigLoader>(this);
            incrementalContext.AddServiceToRegister<IUnityContainer>(this);
            incrementalContext.AddServiceToRegister<IUpdateDispatcher>(this);
            incrementalContext.AddServiceToRegister<IPlayerLoginDataReciever>(this);
            _eventDispatcher = new MessageDispatcher();
            incrementalContext.AddServiceToRegister<IEventDispatcher>(_eventDispatcher);

            incrementalContext.AddServiceToRegister<IAssetsManagerReceiver>(this);
            incrementalContext.AddServiceToRegister<ProgressCommandsManager>(_loadingCommands);
            incrementalContext.AddServiceToRegister<IOrientationTracker>(_orientationTracker);

            incrementalContext.AddServiceToRegister<ErrorMonitor>(_errorMonitor);

            _bundlesBackgroundDownloaderManager = new BundlesBackgroundDownloaderManager();
            _themeManager = new ThemeManager(_bundlesBackgroundDownloaderManager);
            incrementalContext.AddServiceToRegister<IThemeManager>(_themeManager);
            _bundlesConfigUpdater = new BundlesConfigUpdater();
            incrementalContext.AddServiceToRegister<BundlesConfigUpdater>(_bundlesConfigUpdater);
            _timeManager = new TimeManager();
            incrementalContext.AddServiceToRegister<ITimeController>(_timeManager);
            incrementalContext.AddServiceToRegister<TimeManager>(_timeManager);

            Profiler.BeginSample("GameMessengerBus.Init");
            var gameMessenger = new GameMessenger();
            GameMessengerBus.Init(gameMessenger);
            Profiler.EndSample();
            incrementalContext.AddServiceToRegister<IGameMessenger>(gameMessenger);
            var rpcService = gameObject.GetOrAddComponent<RPCService>();
            incrementalContext.AddServiceToRegister<IRPCService>(rpcService);
            incrementalContext.AddServiceToRegister<IRpcComm>(new RpcComm());
            GameMessengerBus.Subscribe<RpcServiceMessage>(rpcService);
            _signInManager = new FirebaseSignInManager();
            incrementalContext.AddServiceToRegister<ISignInManager>(_signInManager);
            incrementalContext.RegisterContext();
            return incrementalContext;
        }

        private IContext SetupLoadingCommands(IncrementalContext context)
        {
            if (_orientationTracker != null)
            {
                _orientationTracker.Init(_screensManager);
            }

            LevelBinarySerializer.SetWrapper(_protobufSerializer);

            _loadingCommands.Init(context);
            _transitionManager.SetDefaultCurtain(_curtain);
            // Startup managers only, until LoadingScreen is visible
            _loadingCommands.AddCommand(new SocialAuthInitializerCommand());
            _loadingCommands.AddCommand(new LoadVitalConfigsCommand(_configsLoader));

            // Load the needed managers so that we can show the first screen. This includes, for example, everything related to asset bundles,
            // localization, views/modals, etc
            var assetsBatch = new CommandsBatch();
            assetsBatch.AddCommand(new InitialAssetBundleCommand());
            assetsBatch.AddCommand(new LoadPriorityManagersCommand(_transitionManager, _screensManager, _modalsManager));
            _loadingCommands.AddCommand(assetsBatch);

            //Register screens and modals
            var uiBatch = new CommandsBatch();
            uiBatch.AddCommand(new RegisterModalsCommand());
            uiBatch.AddCommand(new RegisterScreensCommand(_loadingScreenViewPresenter));
            _loadingCommands.AddCommand(uiBatch);

            // Preload priority assets. In particular, preload sprite atlases that are needed to show ATT pre-dialog
            _loadingCommands.AddCommand(new LoadPriorityResourcesCommand());

            // Show loading screen
            _loadingCommands.AddCommand(new ShowLoadingScreenCommand());

            // Try to show consent flow
            _loadingCommands.AddCommand(new AttConsentCommand());

            _loadingCommands.AddCommand(new RegisterAnalyticsCommand());
            _loadingCommands.AddCommand(new BootstrapCommand(NotificationEnablingRequestedhandler, OnConfigLoaded));
            _loadingCommands.AddCommand(new SetupRpcCommand());
            _loadingCommands.AddCommand(new StartConfigLoaderCommand());
            _loadingCommands.AddCommand(new SetupUserRelatedCommand());
            _loadingCommands.AddCommand(new WaitForConfigWillBeLoaded());
            _loadingCommands.AddCommand(new StartupManagersCommand(_brainCloudManager));

            // From here add any initialization, because loading screen is already visible
            if (AppDefinesConverter.BbbLog)
            {
                _loadingCommands.AddCommand(new LoadDebugConsoleCommand());
                _loadingCommands.AddCommand(new LoadDebugManagersCommand());
            }

            _loadingCommands.AddCommand(new InitialManagersCommand(_screenRecorder));

            _loadingCommands.AddCommand(new LoadStartupPrefabsCommand());
            _loadingCommands.AddCommand(new RegisterNotificationsCommand());

            _loadingCommands.AddCommand(new RegisterGlobalContextCommand());

            _loadingCommands.AddCommand(new MergeBundleConfigsCommand());
            _loadingCommands.AddCommand(new ExecuteOnConfigLoadedCommand());

            var resourcesBatch = new CommandsBatch();
            var resourceSequence = new CommandsSequence();
            resourceSequence.AddCommand(new LoadGenericResourcesCommand());
            resourceSequence.AddCommand(new AudioSetupCommand());
            resourcesBatch.AddCommand(resourceSequence);
            resourcesBatch.AddCommand(new InitThemeManagerCommand());
            resourcesBatch.AddCommand(new BundleCleanupCommand());
            _loadingCommands.AddCommand(resourcesBatch);
            _loadingCommands.AddCommand(new PostLandingScreenCommand());
            _loadingCommands.AddCommand(new CustomActionCommand(OnLoadingCommandsCompleted));
            _loadingCommands.AddCommand(new WaitingForChangeScreenCommand());

            return context;
        }

        public void SetAssetManager(IAssetsManager assetsManager)
        {
            _assetsManager = assetsManager as AssetsManager;
        }

        private void OnLoadingCommandsCompleted()
        {
            _loginManager.Login();
        }

        private async void OnConfigLoaded(ConfigLoadedEvent obj)
        {
#if BBB_LOG
            var logStopwatch = new LogStopwatch("red");
            logStopwatch.Start();
#endif
            Profiler.BeginSample("AppController.OnConfigLoaded");

            _luaManager = _globalContext.Resolve<LuaManager>();
            _screensBuilder = _globalContext.Resolve<IScreensBuilder>() as ScreensBuilder;
            _modalsBuilder = _globalContext.Resolve<IModalsBuilder>() as ModalsBuilder;
            _genericResourceProvider = _globalContext.Resolve<GenericResourceProvider>();
            _notificationManager = _globalContext.Resolve<INotificationManager>();
            _helpDeskManager = _globalContext.Resolve<IHelpDeskManager>();
            _locationManager = _globalContext.Resolve<ILocationManager>() as LocationManager;
            _bundleManager = _globalContext.Resolve<IBundleManager>() as AssetBundleManager;
            _leaderboardManager = _globalContext.Resolve<ILeaderboardManager>();
            _weeklyLeaderboardManager = _globalContext.Resolve<WeeklyLeaderboardManager>();
            _playerManager = _globalContext.Resolve<IPlayerManager>() as PlayerManager;
            _asyncCommandGenerator = _globalContext.Resolve<AsyncCommandGenerator>();
            _asyncCommandManager = _globalContext.Resolve<IAsyncCommandManager>() as AsyncCommandManager;
            _gameNotificationManager = _globalContext.Resolve<GameNotificationManager>();
            _performanceManager = _globalContext.Resolve<PerformanceManager>();
            _accountManager = _globalContext.Resolve<IAccountManager>() as AccountManager;
            _loginManager = _globalContext.Resolve<LoginManager>();
            _priorityLoader = _globalContext.Resolve<IPriorityLoader>();
            _notificationService = _globalContext.Resolve<INotificationService>();
            _localizationManager = _globalContext.Resolve<ILocalizationManager>() as LocalizationManager;
            _eventDispatcher = _globalContext.Resolve<IEventDispatcher>();
            _timeManager = _globalContext.Resolve<TimeManager>();
            _userSettings = _globalContext.Resolve<UserSettings>();
            _rpcService = _globalContext.Resolve<IRPCService>() as RPCService;
            _vibrations = _globalContext.Resolve<IVibrationsWrapper>() as MMVibrationsWrapper;
            _rpcCommandManager = _globalContext.Resolve<RpcCommandManager>();

            Profiler.BeginSample("screensManager.SetConfig");
            _screensManager.SetConfig(_config);
            Profiler.EndSample();

            Profiler.BeginSample("OnConfigLoaded: localizationManager.SetConfigs");
            if (_localizationManager != null)
            {
                _localizationManager.SetConfig(_config);
                _localizationManager.SubscribeToConfigUpdates();
                Profiler.EndSample();

                Profiler.BeginSample("OnConfigLoaded: Notifications");

                var notifTimingConfig = _config.GetFromDictionary<FBConfig.LocalPushNotificationsTimingConfig>("default");
                var notificationsFilter = new NotificationFilter(notifTimingConfig);

                notificationsFilter.LoadTimestamps();
                _notificationManager.Initialize(_rpcService, notificationsFilter, notifTimingConfig);

                Profiler.EndSample();

                _gameEventManager = new GameEventManager();

                Profiler.BeginSample("OnConfigLoaded: TransitionManager.Init");
                await _transitionManager.InitAsync(_gameEventManager, _playerManager);
                Profiler.EndSample();
                _transitionManager.SetLocalizationManager(_localizationManager);
            }

            Profiler.BeginSample("OnConfigLoaded: TransitionManager.Load");
            _transitionManager.Load(_globalContext);
            Profiler.EndSample();

            Profiler.BeginSample("OnConfigLoaded: LevelOrderingManager SetupConfig");
            _levelsOrderingManager = _globalContext.Resolve<ILevelsOrderingManager>() as LevelsOrderingManager;
            _levelsOrderingManager?.SetupConfig(_config);
            Profiler.EndSample();

            Profiler.BeginSample("OnConfigLoaded: SocialLinks SetupConfig");
            SocialLinks.InitFromConfig(_config);
            Profiler.EndSample();

#if BBB_LOG
            logStopwatch.StopLog("OnConfigLoaded method done");
#endif
            var systemConfig = _config.TryGetDefaultFromDictionary<FBConfig.SystemConfig>();
            if (!systemConfig.IsNull() && systemConfig.BackgroundRestartTime > 0f)
            {
#if UNITY_EDITOR
                _maxSleptTime = double.MaxValue;
#else
#if BBB_DEBUG
                _maxSleptTime = Mathf.Max(systemConfig.BackgroundRestartTime * 60f, 1f);
#else
                _maxSleptTime = Mathf.Max(systemConfig.BackgroundRestartTime * 60f, 300f);
#endif
#endif
            }

            AccountTypeExtensions.Initialize(systemConfig);

            Analytics.SetSessionTimeout(_maxSleptTime);

            _deepLinkManager = _globalContext.Resolve<IDeepLinkManager>();
            _deepLinkNotifier = _globalContext.Resolve<IDeepLinkNotifier>();

            if (_accountManager != null)
            {
                _accountManager.ProfileProcessingStateChanged -= OnProfileProcessingStateChanged;
                _accountManager.ProfileProcessingStateChanged += OnProfileProcessingStateChanged;

                //Load player profile
                _accountManager.ProfileUpdated -= OnProfileUpdated;
                _accountManager.ProfileUpdated += OnProfileUpdated;
            }

            Profiler.BeginSample("AppController.OnConfigLoaded - OnLoginSend");
            OnLoginSend();
            Profiler.EndSample();

            Profiler.BeginSample("AppController.OnConfigLoaded - OnLoginCompleted");
            OnLoginCompleted();
            Profiler.EndSample();

            Profiler.BeginSample("AppController.OnConfigLoaded - OnProfileLoaded");
            OnProfileLoaded();
            Profiler.EndSample();

            Profiler.EndSample();
            LoadingProcessTracker.UpdateSettings(_config);
        }

        private async void HandleLog(string condition, string stacktrace, LogType type)
        {
#if BBB_DEBUG
            if (type == LogType.Exception)
            {
                await UniTask.SwitchToMainThread();
                //Ignore UMP errors
                if (!(AppDefinesConverter.UnityEditor && Application.isPlaying) && !stacktrace.Contains("UniversalMediaPlayer"))
                {
                    _genericModalFactory?.ShowDebug(condition, stacktrace);
                }
            }

            if (type is LogType.Exception or LogType.Error or LogType.Warning && !stacktrace.IsNullOrEmpty() && stacktrace.Contains("UniversalMediaPlayer"))
            {
                BDebug.LogError(LogCat.General, $"UMP {type}: condition: {condition} stracktrace: {stacktrace}");
            }
#endif
        }

        private void NotificationEnablingRequestedhandler(NotificationEnablingRequested obj)
        {
            TryRefreshNotifToken();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            // IMPORTANT!
            // we need to call this to let the dispatcher know
            // we need it to kill the thread it's using
            Unsubscribe();
            _eventDispatcher?.Dispose();
            _instance = null;
        }

        private async void RequestAtlas(string tagName, Action<SpriteAtlas> action)
        {
#if UNITY_EDITOR
            if (SimulatedLoader.SimulateAssetBundleInEditor)
            {
                // This should be invoked only if SpritePacker is enabled in editor (in ProjectSettings->Editor).
                var assets = UnityEditor.AssetDatabase.FindAssets(tagName + " t:SpriteAtlas");
                if (assets.Length > 0)
                {
                    var path = UnityEditor.AssetDatabase.GUIDToAssetPath(assets[0]);
                    var asset = UnityEditor.AssetDatabase.LoadAssetAtPath<SpriteAtlas>(path);
                    // Wait 1 frame (for some reason it seems to be working better).
                    Rx.Invoke(0, (_) => { action.Invoke(asset); });
                    return;
                }
            }
#endif
            if (_assetsManager == null)
            {
                BDebug.LogError(LogCat.AssetBundle, "Calling RequestAtlas when GenericResourceProvider is null or not initialized for: " + tagName);
                return;
            }

            BDebug.Log(LogCat.AssetBundle, "RequestAtlas SpriteAtlas " + tagName);
            var loadedAsset = await _assetsManager.LoadAsync<SpriteAtlas>(tagName, AssetLoadPriority.Immediately);
            
            if (loadedAsset == null)
            {
                Debug.LogError($"IAssetLoaded is null for {tagName}");
            }

            if (loadedAsset != null)
            {
                var spriteAtlas = loadedAsset.Get();

                if (spriteAtlas == null)
                {
                    Debug.LogError($"SpriteAtlas is null for {tagName}");
                }

                BDebug.LogFormat(LogCat.AssetBundle,
                    "RequestAtlas sprite callback {0} name: {1} exists:{2} tag:{3} spriteCount:{4} spriteName:{5}"
                    , tagName, spriteAtlas.name, spriteAtlas, spriteAtlas.tag, spriteAtlas.spriteCount,
                    spriteAtlas.name);
                action(spriteAtlas);

                // Dispose this reference and let the sprites hold the reference so that when the sprites are disposed, 
                // the atlas can also be disposed when no other references to it exist.
                loadedAsset.Dispose();
            }
            else
            {
                BDebug.LogFormat(LogCat.AssetBundle, "RequestAtlas sprite callback {0} empty", tagName);
                action(null);
            }
        }

        private void Update()
        {
            var time = Time.time;
            Profiler.BeginSample("_vibrations?.Tick");
            _vibrations?.Tick(time);
            Profiler.EndSample();
            Profiler.BeginSample("_gameEventManager?.Tick");
            _gameEventManager?.Tick();
            Profiler.EndSample();

            Profiler.BeginSample("OnUpdate?.Invoke");
            OnUpdate?.Invoke();
            Profiler.EndSample();
#if BBB_DEBUG
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                // Timescale toggle 1->2->4->0.5->1
                Time.timeScale *= 2f;
                if (Time.timeScale > 15f)
                {
                    Time.timeScale = 0.125f;
                }

                Debug.Log("#TIMESCALE# Set Time scale = " + Time.timeScale.ToString("0.0"));
            }
#endif
        }

        private void OnApplicationQuit()
        {
            AudioProxy.StopMusicImmediately();
            _globalContext?.Releaser?.ReleaseContext();
            _appContext?.Releaser?.ReleaseContext();
        }

        private void LateUpdate()
        {
            //Sending request after Update and only after RpcFramesInterval interval
            if (Time.frameCount - _lastRpcFrameSend > RpcFramesInterval)
            {
                _lastRpcFrameSend = Time.frameCount;
                _rpcCommandManager?.SendAll().Forget();
                _rpcCommandManager?.HandleStashedCommands();
                if (!_restarting)
                {
                    _tickPlayerErrorReported = false;
                    _priorityLoader?.Tick();
                }
                else if (!_tickPlayerErrorReported)
                {
                    _tickPlayerErrorReported = true;
                    BDebug.LogError(LogCat.General, "Trying to Tick PriorityLoader while Game is restarting");
                }
            }

            OnLateUpdate?.Invoke();
        }

        private void Unsubscribe()
        {
            Application.logMessageReceivedThreaded -= HandleLog;
            SpriteAtlasManager.atlasRequested -= RequestAtlas;
            if (_accountManager != null)
            {
                _accountManager.ProfileProcessingStateChanged -= OnProfileProcessingStateChanged;
                _accountManager.ProfileUpdated -= OnProfileUpdated;
            }
        }

        private void FixedUpdate()
        {
            OnFixedUpdate?.Invoke();
        }

        public void OnLoginCompleted()
        {
            try
            {
                IPlayer player = _accountManager.LocalPlayer;
                Profiler.BeginSample("Player UpdateEconomy");
                player.UpdateEconomy();
                Profiler.EndSample();

                Profiler.BeginSample("AdLootboxManager");
                _adLootboxManager = new AdLootboxManager();
                Profiler.EndSample();

                Profiler.BeginSample("DailyTriviaManager");
                _dailyTriviaManager = new DailyTriviaManager();
                Profiler.EndSample();

                Profiler.BeginSample("EpisodeTaskManager");
                _episodeTaskManager = new EpisodeTaskManager();
                Profiler.EndSample();

                Profiler.BeginSample("EpisodicScenesManager");
                _episodicScenesManager = new EpisodicScenesManager();
                Profiler.EndSample();

                Profiler.BeginSample("Player PreAppContextSetup");
                PreAppContextSetup(player);
                Profiler.EndSample();

                Profiler.BeginSample("Player Analytics");
                Analytics.SetUserId(MultiDevice.GetUserId());

                CrashLoggerService.InitializeUser(new ErrorReportingUserMainProperties(userId: MultiDevice.GetUserId(), level: _accountManager.Profile.HighestPassedLevelId,
                    profileId: _brainCloudManager.ProfileId));

                Profiler.EndSample();

                Profiler.BeginSample("Recording");
                _screenRecorder.ConfigsReady();
                Profiler.EndSample();

                UpdateAnalyticsMeasurements();
                
                _loginCompleted = true;
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.General, $"Exception on login callback {e.Message} {e.StackTrace}");
                throw;
            }
        }

        private void UpdateAnalyticsMeasurements()
        {
            if (!_accountManager.Profile.DetailedAnalyticsStateSet)
            {
                var systemConfig = _config.TryGetDefaultFromDictionary<FBConfig.SystemConfig>();
                var userId = _accountManager.Profile.Uid;

                using var md5 = MD5.Create();
                var hashBytes = md5.ComputeHash(Encoding.UTF8.GetBytes(userId));
                var hashValue = BitConverter.ToInt32(hashBytes, 0);
                hashValue = Math.Abs(hashValue);

                _accountManager.Profile.DetailedAnalyticsEnabled =
                    true; //hashValue % 100 < systemConfig.DetailedAnalyticsUsersPercent;
            }

            MetricsManagerHelper.DetailedAnalytics = true; //_accountManager.Profile.DetailedAnalyticsEnabled ;
        }

        private void TryRefreshNotifToken()
        {
            if (AppDefinesConverter.MobileNotEditor && !_tokenSent)
            {
                try
                {
                    _notificationService.RefreshNotificationToken();
                    _tokenSent = true;
                }
                catch (Exception e)
                {
                    Debug.LogError("Exception occured during Push Notification token refresh " + e.Message);
                }
            }
            else if (_tokenSent)
            {
                BDebug.Log(LogCat.Notification, $"Push NotificationToken is already sent on login callback");
            }
        }

        private async void OnProfileProcessingStateChanged(bool processing)
        {
            await UniTask.SwitchToMainThread();
            if (_genericHudManager != null)
            {
                _genericHudManager.SetLoadingViewVisibility(processing);
            }
        }

        private void OnLoginSend()
        {
            _accountManager.SendLoginCommand();
            _rpcCommandManager?.SendAll().Forget();
            Profiler.BeginSample("OnLoginSend");

            Profiler.BeginSample("OnLoginSend-1");
            _livesManager = new LivesManager();
            _boosterManager = new BoostersManager();
            Profiler.EndSample();

            Profiler.BeginSample("LockEvaluationManager");
            var lockEvaluationManager = new LockEvaluationManager(_luaManager, _locationManager, _playerManager);
            Profiler.EndSample();
            var lockTextReasonManager = new LockReasonTextManager(_localizationManager, _config);
            Profiler.BeginSample("new LockManager");
            _lockManager = new LockManager(_config, lockEvaluationManager, lockTextReasonManager);
            Profiler.EndSample();

            Profiler.BeginSample("OnLoginSend-3");
            Profiler.BeginSample("OnLoginSend LocationManager Init");
            _locationManager.Init(_localizationManager, _config, _levelsOrderingManager, _playerManager);
            Profiler.EndSample();
            Profiler.BeginSample("OnLoginSend LevelNarrativeController");
            _levelNarrativeController = new LevelNarrativeController();
            Profiler.EndSample();
            Profiler.BeginSample("OnLoginSend WalletManager");
            _walletManager = new WalletManager();
            Profiler.EndSample();
            Profiler.BeginSample("OnLoginSend WalletManager");
            _videoAdManager = new VideoAdManager();
            Profiler.EndSample();
            Profiler.EndSample();

            _promotionManager = new PromotionManager();
            _endlessTreasureManager = new EndlessTreasureManager();
            _pom = new PlayerObjectivesManager();
            _questManager = new QuestManager();

            _iapManager = new IapManager(new UnityIAPImplementation(), new IapRewardApplier(_luaManager, _walletManager));
            _gachaManager = new GachaManager();
            _objectiveProgressListener = new ObjectiveProgressionTrackerListener();
            _fakeUsersManager = new FakeUsersManager();
            _playerEventsManager = new PlayerComEventsManager(_rpcService);
            AddPlayerComEventHandlers();

            Profiler.BeginSample("new AdsManager");
            _adsManager = new AdsManager();
            Profiler.EndSample();
            Profiler.BeginSample("new InfoModalFactory");
            _infoModalFactory = new InfoModalFactory(_modalsBuilder);
            Profiler.EndSample();

            Profiler.BeginSample("new VideoAdsPlayer");
            _videoAdsPlayer = new VideoAdsPlayer();
            Profiler.EndSample();
            _raceEventManager = new RaceEventManager();
            _royaleEventManager = new RoyaleEventManager();
            _teamEventManager = new TeamEventManager();
            _challengeTriviaManager = new ChallengeTriviaManager();

            var autorestartDaemon = gameObject.GetComponent<AppRestartOnRPCErrorDaemon>();
            if (autorestartDaemon != null)
            {
                // After finishing loading
                // We don't need restart game anymore if connection errors occured.-VK
                Destroy(autorestartDaemon);
            }

            Profiler.EndSample();
        }

        private void UpdatePlayerAnalyticsProperties()
        {
            _analyticsMonitor.UpdatePlayerAnalyticsProperties();
        }

        public void ScenePreInstantiate()
        {
            TryRefreshNotifToken();

            Profiler.BeginSample("FlagsLoader create");
            _flagsLoader = new FlagsLoader(_assetsManager);
            Profiler.EndSample();

            var tutorialPrefab = _genericResourceProvider.GetPreloaded<GameObject>(GenericResKeys.TutorialPrefab);
            Profiler.BeginSample($"Instantiate[{tutorialPrefab.name}]");
            var tutorialGo = Instantiate(tutorialPrefab);
            Profiler.EndSample();

            Profiler.BeginSample("TutorialPlaybackController");
            _tutorialPlaybackController = tutorialGo.GetComponent<ITutorialPlaybackController>();
            Profiler.EndSample();

            var walletPrefab = _genericResourceProvider.GetPreloaded<GameObject>(GenericResKeys.WalletHud);
            Profiler.BeginSample($"Instantiate[{walletPrefab.name}]");
            var walletGo = Instantiate(walletPrefab);
            Profiler.EndSample();

            _uiWalletManager = walletGo.GetComponent<UIWalletManager>();
            _overlayEffectRenderer = walletGo.GetComponent<OverlayEffectRenderer>();
            _modalsManager.ProvideOtherManagers(_tutorialPlaybackController, _episodicScenesManager);

            var hudPrefab = _genericResourceProvider.GetPreloaded<GameObject>(GenericResKeys.HudPrefab);
            Profiler.BeginSample($"Instantiate[{hudPrefab.name}]");
            var hudGo = Instantiate(hudPrefab);
            Profiler.EndSample();
            _genericHudManager = hudGo.GetComponent<GenericHudManager>();
            _hudSlotsManager = hudGo.GetComponent<HudSlotsManager>();

            Profiler.BeginSample("InitializeAppContext");
            _appContext = InitializeAppContext();
            Profiler.EndSample();

            _deepLinkExecutionManager = _appContext.Resolve<DeepLinkExecutionManager>();
            _deepLinkExecutionManager.Init(_appContext);

            Profiler.BeginSample("AppContextInjector.Inject");
            AppContextInjector.Inject(_appContext);
            Profiler.EndSample();

            Profiler.BeginSample("PostAppContextSetup");
            PostAppContextSetup();
            Profiler.EndSample();
            Profiler.BeginSample("UpdatePlayerAnalyticsProperties");
            UpdatePlayerAnalyticsProperties();
            Profiler.EndSample();

            Profiler.BeginSample("LivesManager Init");
            _livesManager.Init();
            Profiler.EndSample();

            Profiler.BeginSample("Ads Init");
            if ((AppDefinesConverter.UnityIos || AppDefinesConverter.UnityAndroid) && !AppDefinesConverter.UnityEditor)
            {
                var ironSourceService = new IronSourceService();
                _adsManager.AddService(ironSourceService);
            }
            else
            {
                var fakeAdsService = new FakeAdsService();
                _adsManager.AddService(fakeAdsService);
            }

            Profiler.EndSample();
            Profiler.BeginSample("Ads listeners");
            _adsManager.AddListener(new AnalyticsAdsListener(_appContext));
            _adsManager.Initialize(_appContext);
            Profiler.EndSample();
#if USE_NUNU_SDK && BBB_DEBUG
            NunuFlayerFunctionsManager.InitializeGameFunctions(_appContext);
#endif
        }

        private void OnProfileUpdated(IPlayer player)
        {
            if (_loginCompleted)
            {
                PreAppContextSetup(player);
            }

            _accountManager.Profile?.UpdateTrophies(player);
        }

        private async void PreAppContextSetup(IPlayer player)
        {
            Profiler.BeginSample("LevelNarrativeController Init");

            _levelNarrativeController.Init(_config, _modalsBuilder);
            Profiler.EndSample();

            Profiler.BeginSample("WalletManager create");
            _analyticsMonitor = _globalContext.Resolve<AnalyticsPropertiesMonitor>();
            _walletManager.Init(_playerManager, _config, _analyticsMonitor, _notificationManager);
            Profiler.EndSample();

            Profiler.BeginSample("PlayerManager Init");
            _playerManager.Init(_walletManager, _protobufSerializer, _gameEventManager);
            Profiler.EndSample();

            Profiler.BeginSample("PlayerManager.Lua RegisterManagers");
            var livesLuaBindings = new LivesLuaBindings(_livesManager);
            _luaManager.RegisterManagers(player, _locationManager,
                livesLuaBindings, _accountManager, _eventDispatcher, _promotionManager,
                _bundleManager, _timeManager, _dailyTriviaManager, _endlessTreasureManager, _episodeTaskManager);
            Profiler.EndSample();

            _timeManager.InstallDate = player.InstallDate;

            Profiler.BeginSample("POM Init");
            _pom.Init(_accountManager.LocalPlayer.ObjectivesProgress, _luaManager, _timeManager);
            Profiler.EndSample();

            Profiler.BeginSample("QuestManager Init");
            _questManager.Init(_config, _pom, _luaManager,
                _eventDispatcher, _walletManager,
                _playerManager,
                _screensManager);
            Profiler.EndSample();

            Profiler.BeginSample("PlayerManager.IAPManager Init");
            _iapManager.Initialize(_config, _playerManager, _eventDispatcher, _timeManager, _brainCloudManager);
            Profiler.EndSample();

            Profiler.BeginSample("GachaManager create");
            _gachaManager.Init(new SystemRandom());
            Profiler.EndSample();

            Profiler.BeginSample("ObjectiveProgressionTrackerListener create");
            _objectiveProgressListener.Init(_eventDispatcher, _pom);
            Profiler.EndSample();

            Profiler.BeginSample("PlayerManager.BundleManager CheckRemainingBundleNeedToBeDownloaded");
            _bundleManager.CheckRemainingBundleNeedToBeDownloaded();
            Profiler.EndSample();

            PlayerProfileLocal.SetupPlayerManager(_playerManager);

            Profiler.BeginSample("BoosterManager Init");
            _boosterManager.Init(_playerManager, _walletManager, _eventDispatcher, _config, _timeManager);
            Profiler.EndSample();
        }

        private void PostAppContextSetup()
        {
            _gameEventManager.Setup();
            _raceEventManager.Setup();
            _royaleEventManager.Setup();
            _teamEventManager.Setup();
            _episodicScenesManager.Setup();
            _challengeTriviaManager.Setup();
            _bundlesBackgroundDownloaderManager.Setup();
            _dailyTasksManager.Setup();

            Profiler.BeginSample("UserSettings Setup");
            _userSettings.Setup(_config, _lockManager, _locationManager);
            Profiler.EndSample();

            Profiler.BeginSample("LeaderboardManager Init");
            _leaderboardManager.Init(_appContext);
            _weeklyLeaderboardManager.Init(_appContext);

            Profiler.EndSample();

            _screensBuilder.SetDefaultContext(_appContext);
            _modalsBuilder.SetDefaultContext(_appContext);
            _assetDownloadManager.StartDowloadProcess();

            Profiler.BeginSample("UIWalletManager Init");
            _uiWalletManager.Init(_appContext);
            Profiler.EndSample();

            _questManager.SetUIWalletManager(_uiWalletManager);
            _gachaManager.SetUIWalletManager(_uiWalletManager);

            Profiler.BeginSample("AsyncCommands Init");
            _asyncCommandManager.Launch(_appContext);
            _asyncCommandGenerator.Init(_appContext);
            Profiler.EndSample();

            _helpDeskManager.Init(_appContext);

            Profiler.BeginSample("NotifierAggregator Init");
            _gameNotificationManager.Init(_appContext);
            Profiler.EndSample();

            Profiler.BeginSample("GenericHudManager Init");
            _genericHudManager.Init(_appContext, _tutorialPlaybackController);
            Profiler.EndSample();
            Profiler.BeginSample("OverlayDialogManager Init");
            _overlayDialogManager.Init(_appContext);
            Profiler.EndSample();
            Profiler.BeginSample("Match3LockListener Init");
            _match3LockListener = new Match3LockListener();
            _match3LockListener.Init(_appContext);
            Profiler.EndSample();

            _performanceManager.AdjustDeviceQualitySettings();

            Profiler.BeginSample("LocalNotificationsScheduler Init");
            _localNotificationsScheduler = new LocalNotificationsScheduler();
            _localNotificationsScheduler.Init(_appContext);
            _localNotificationsScheduler.ProvideNotificationManager(_notificationManager);
            _localNotificationsScheduler?.RemoveAllPendingNotifications();
            Profiler.EndSample();

            _playerManager.IncSessionsNumber();

            var notifMetaConfig = _config.TryGetDefaultFromDictionary<NotificationsMetaConfig>();

            _notificationService.SetUserId(MultiDevice.GetUserId());
            if (notifMetaConfig == null || string.IsNullOrEmpty(notifMetaConfig.PermissionMode))
            {
                TryRefreshNotifToken();
            }
            else if (notifMetaConfig.PermissionMode == "Session" && _playerManager.GetSessionNumber() >= notifMetaConfig.SessionNumber)
            {
                TryRefreshNotifToken();
            }

            _leaderboardManager.LoadScores();

            var themeManager = _appContext.Resolve<IThemeManager>();
            themeManager.Setup(_lockManager, _gameEventManager);

            _tutorialPlaybackController.StartTutorial();

            _deepLinkManager.Start();
        }

        public void OnProfileLoaded()
        {
            var pbPlayer = _accountManager.LocalPlayer.PlayerDO;

            Profiler.BeginSample("OnProfileLoaded: Update from protobud state");
            _accountManager.Profile?.UpdateProfileFromGameState(pbPlayer, _locationManager, _config.TryGetDefaultFromDictionary<FBConfig.SystemConfig>().MaxProgressionLevel);
            Profiler.EndSample();
        }

        // should be called on quiting
        private void ScheduleNotifications()
        {
            BDebug.LogFormat(LogCat.Notification, "[LOCAL NOTIFICATIONS] _blockSchedulingNotifications {0}", _blockSchedulingNotifications);
            try
            {
                _localNotificationsScheduler?.RemoveAllPendingNotifications();
                if (!_blockSchedulingNotifications && _userSettings is { NotificationsON: true })
                {
                    _localNotificationsScheduler?.Schedule();

                    (_notificationManager as NotificationManager)?.NotificationFilter?.SaveTimestamps();
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        private void AddPlayerComEventHandlers()
        {
            Profiler.BeginSample("AddPlayerComEventHandlers");
            _playerEventsManager.AddEventHandler(PlayerComEventType.Invite, new FriendshipRequestedEventHandler(_globalContext));
            _playerEventsManager.AddEventHandler(PlayerComEventType.FriendshipAccepted, new FriendshipAcceptedEventHandler(_globalContext));
            _playerEventsManager.AddEventHandler(PlayerComEventType.LifeReceived, new ReceiveGiftEventHandler(_globalContext));
            _playerEventsManager.AddEventHandler(PlayerComEventType.GiftReceived, new ReceiveGiftEventHandler(_globalContext));
            _playerEventsManager.AddEventHandler(PlayerComEventType.GiftRequest, new RequestGiftEventHandler(_globalContext));
            _playerEventsManager.AddEventHandler(PlayerComEventType.BuddyGiftReceived, new ReceiveBuddyGiftEventHandler(_globalContext));
            Profiler.EndSample();
        }

        private IContext InitializeAppContext()
        {
            var context = new AppContext();

            context.AddServiceToRegister<ITutorialPlaybackController>(_tutorialPlaybackController);
            context.AddServiceToRegister<IWalletManager>(_walletManager);
            context.AddServiceToRegister<ILivesManager>(_livesManager);
            context.AddServiceToRegister<IBoosterManager>(_boosterManager);
            context.AddServiceToRegister<InfoModalFactory>(_infoModalFactory);
            context.AddServiceToRegister<LuaManager>(_luaManager);
            context.AddServiceToRegister<ILockManager>(_lockManager);
            context.AddServiceToRegister<VideoAdManager>(_videoAdManager);
            context.AddServiceToRegister<IapManager>(_iapManager);
            context.AddServiceToRegister<GachaManager>(_gachaManager);
            context.AddServiceToRegister<QuestManager>(_questManager);
            context.AddServiceToRegister<SimpleActionController>(new SimpleActionController());
            context.AddServiceToRegister<FakeUsersManager>(_fakeUsersManager);
            context.AddServiceToRegister<PlayerComEventsManager>(_playerEventsManager);
            context.AddServiceToRegister<FlagsLoader>(_flagsLoader);
            context.AddServiceToRegister<IUIWalletManager>(_uiWalletManager);
            context.AddServiceToRegister<OverlayEffectRenderer>(_overlayEffectRenderer);
            context.AddServiceToRegister<GenericHudManager>(_genericHudManager);
            context.AddServiceToRegister<IOverlayDialogManager>(_overlayDialogManager);
            context.AddServiceToRegister<HudSlotsManager>(_hudSlotsManager);
            context.AddServiceToRegister<IAdsManager>(_adsManager);
            context.AddServiceToRegister<VideoAdsPlayer>(_videoAdsPlayer);
            context.AddServiceToRegister<LevelNarrativeController>(_levelNarrativeController);
            context.AddServiceToRegister<ScoreMultiplierProvider>(new ScoreMultiplierProvider());
            context.AddServiceToRegister<BundlesBackgroundDownloaderManager>(_bundlesBackgroundDownloaderManager);
            context.AddServiceToRegister<IGameEventManager>(_gameEventManager);
            context.AddServiceToRegister<IRaceEventManager>(_raceEventManager);
            context.AddServiceToRegister<IRoyaleEventManager>(_royaleEventManager);
            context.AddServiceToRegister<ITeamEventManager>(_teamEventManager);
            _dailyLoginManager = new DailyLoginManager();
            context.AddServiceToRegister<DailyLoginManager>(_dailyLoginManager);
            context.AddServiceToRegister<BrainCloudAvatarsManager>(new BrainCloudAvatarsManager());
            context.AddServiceToRegister<ChallengeTriviaManager>(_challengeTriviaManager);
            context.AddServiceToRegister<SdbManager>(new SdbManager());
            context.AddServiceToRegister<WeeklyLeaderboardRewardsManager>(new WeeklyLeaderboardRewardsManager());
            context.AddServiceToRegister<ILevelsOrderingManager>(_levelsOrderingManager);
            context.AddServiceToRegister<IExternalLinksOpener>(new ExternalLinksOpener(this, _accountManager, _playerManager));
            var passportManager = new PassportManager();
            context.AddServiceToRegister<PassportManager>(passportManager);
            context.AddServiceToRegister<EndlessTreasureManager>(_endlessTreasureManager);
            context.AddServiceToRegister<IButlerGiftManager>(new ButlerGiftManager());
            context.AddServiceToRegister<ICollectionManager>(new CollectionManager());
            _dailyTasksManager = new DailyTasksManager();
            context.AddServiceToRegister<IDailyTasksManager>(_dailyTasksManager);

            var nextLevelToPlayByProgressionFinder = new LevelByFlowProvider();
            context.AddServiceToRegister<ILevelByFlowProvider>(nextLevelToPlayByProgressionFinder);

            context.AddServiceToRegister<AdCapManager>(new AdCapManager());
            context.AddServiceToRegister<VipProductsManager>(new VipProductsManager());

            context.AddServiceToRegister<IAPBasketManager>(new IAPBasketManager());
            context.AddServiceToRegister<IapPurchaseProcessor>(new IapPurchaseProcessor());
            context.AddServiceToRegister<PromotionManager>(_promotionManager);
            context.AddServiceToRegister<AutoPopupManager>(new AutoPopupManager());
            context.AddServiceToRegister<FlowController>(new FlowController());
            context.AddServiceToRegister<AdLootboxManager>(_adLootboxManager);
            context.AddServiceToRegister<DailyTriviaManager>(_dailyTriviaManager);
            context.AddServiceToRegister<LevelStarter>(new LevelStarter());
            context.AddServiceToRegister<SideMapLevelStarter>(new SideMapLevelStarter());
            context.AddServiceToRegister<DebugLevelStarter>(new DebugLevelStarter());
            context.AddServiceToRegister<IceBreakerManager>(new IceBreakerManager());
            context.AddServiceToRegister<ISocialManager>(new SocialManager());
            context.AddServiceToRegister<IChatManager>(new BrainCloudChatManager());
            context.AddServiceToRegister<BannerManager>(new BannerManager());
            context.AddServiceToRegister<AnalyticsInventoryMonitor>(new AnalyticsInventoryMonitor());
            context.AddServiceToRegister<IEpisodeTaskManager>(_episodeTaskManager);
            context.AddServiceToRegister<IEpisodicScenesManager>(_episodicScenesManager);
            context.AddServiceToRegister<IAvatarDecorationManager>(new AvatarDecorationManager());

            // to have it working correctly, we need to get appcontext for consumer because it needs it for commands
            var defaultDeepLinkConsumer = new DefaultDeepLinkConsumer();
            context.AddServiceToRegister<DefaultDeepLinkConsumer>(defaultDeepLinkConsumer);

            _quickActionsManager = new QuickActionsManager();
            context.AddServiceToRegister<QuickActionsManager>(_quickActionsManager);

            context.RegisterContext(_globalContext);

            // and we need to add listener only after registration of the context so that DefaultDeepLinkConsumer is initialized
            var deepLinkManager = _globalContext.Resolve<IDeepLinkManager>();
            deepLinkManager.AddListener(defaultDeepLinkConsumer);

            return context;
        }

        private void OnApplicationPause(bool pauseState)
        {
            BDebug.Log(LogCat.General, $"====================== OnApplicationPause {pauseState}");
            CrashLoggerService.Log($"OnApplicationPause {pauseState}");
            MetricsManagerHelper.OnApplicationPause(pauseState);
            _adsManager?.OnApplicationPause(pauseState);
            OfflineSessionTrackingManager.OnApplicationPause(pauseState);
            ConnectivityStatusManager.OnApplicationPause(pauseState);
            _raceEventManager?.OnApplicationPause(pauseState);
            _gameEventManager?.OnApplicationPause(pauseState);
            _teamEventManager?.OnApplicationPause(pauseState);

            if (pauseState)
            {
                _rpcCommandManager?.SendAll().Forget();
                BDebug.Log(LogCat.General, "<color=green>OnApplicationFocus = false, first part</color>");

                _pauseStartTime = DateTime.UtcNow;

                if (_accountManager != null && _accountManager.IsLoggedIn())
                {
                    ScheduleNotifications();
                    if (_playerManager is { Player: not null })
                    {
                        BDebug.Log(LogCat.General, "<color=green>App Lose Focus</color>");
                        _playerManager.SendAppToBackground();
                        CrashLoggerService.Log($"SendAppToBackground");
                        UpdatePlayerAnalyticsProperties();
                    }

                    if (_asyncCommandManager != null)
                    {
                        _asyncCommandManager.Pause();
                    }
                }

                RpcCommands.SendAsync<LeavingAppCommand>().Forget();
                _rpcCommandManager?.SendAll().Forget();
                _deepLinkManager?.OnApplicationFocus(false);

                BDebug.Log(LogCat.General, "<color=green>~~~~SendAppToBackground</color>");
            }
            else
            {
                _notificationManager?.OnApplicationInForeground();

                BDebug.Log(LogCat.General, "<color=green>~~~~SendAppToForeground/color>");
                BBBHapticManager.TryStartHapticEngine();
                _userSettings?.RefreshSettingApplication();

                // It seems that on Android OnApplicationFocus it's called with true params when game starts but never calls
                // with false parameter, meaning that hasn't done lose focus gain focus cycle in that case _pauseStartTime is 0
                if (_pauseStartTime == DateTime.MinValue) return;

                var now = DateTime.UtcNow;
                var diff = now - _pauseStartTime;

                const int minSecondsToReconnect = 60;

                // If we were away from the app more than a minute, then we need to reconnect to BrainCloud
                if (diff.TotalSeconds > minSecondsToReconnect)
                {
                    _brainCloudManager.Reconnect();
                }

                var smallSleep = diff.TotalSeconds < _maxSleptTime;
                BDebug.Log(LogCat.General, $"Unpause: diff:{diff} now:{now} _pauseStartTime:{_pauseStartTime} MaxSleepTime:{_maxSleptTime} uid:{MultiDevice.GetUserId()}");
                CrashLoggerService.Log($"Unpause: diff:{diff} now:{now} _pauseStartTime:{_pauseStartTime} MaxSleepTime:{_maxSleptTime} uid:{MultiDevice.GetUserId()}");
                if (!smallSleep)
                {
                    CrashLoggerService.Log("Restart due to large pause duration");
                    _localNotificationsScheduler?.RemoveAllPendingNotifications();

                    // If we are not currently making a purchase, try to get last info from server
                    if (_iapManager == null || !_iapManager.CanPurchase(false)) return;

                    _configsLoader.SendLoadConfigsCommand();
                    _accountManager.SendLoginCommand();
                }
                else
                {
                    if (_accountManager == null || !_accountManager.IsLoggedIn() || _playerManager is not { Player: not null }) return;

                    var asyncCommandManager = _asyncCommandManager;
                    var localNotificationsScheduler = _localNotificationsScheduler;
                    var deepLinkManager = _deepLinkManager;
                    CrashLoggerService.Log("ResumeAppFromBackground");
                    _playerManager.ResumeAppFromBackground(() =>
                    {
                        if (asyncCommandManager != null)
                        {
                            asyncCommandManager.Resume();
                        }

                        BDebug.Log(LogCat.General, "<color=green>ResumeAppFromBackground</color>");
                        localNotificationsScheduler?.RemoveAllPendingNotifications();
                        deepLinkManager?.OnApplicationFocus(true);
                        CrashLoggerService.Log("ResumeAppFromBackground callback");
                    });

                    _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<GetAllMessagesTimerResetEvent>());
                }
            }

            Analytics.OnApplicationPause(pauseState);
        }

#if BBB_DEBUG
#if UNITY_EDITOR
        [UnityEditor.MenuItem("BebopBee/Restart game")]
#endif
        public static void RestartGame()
        {
            _instance.Restart();
        }
#endif
#if BBB_DEBUG
        public static AsyncCommandManager GetAsyncCommandManager()
        {
            return _instance._asyncCommandManager;
        }

        public static ProgressCommandsManager GetProgressCommandsManager()
        {
            return _instance._loadingCommands;
        }
#endif

        public static AccountManager GetAccountManager()
        {
            return _instance._accountManager;
        }

        public static ILeaderboardManager GetLeaderboardManager()
        {
            return _instance._leaderboardManager;
        }

        public static ITeamEventManager GetTeamEventManager()
        {
            return _instance._teamEventManager;
        }

        public static BrainCloudManager GetBrainCloudManager()
        {
            return _instance._brainCloudManager;
        }

        public static LocalNotificationsScheduler GetLocalNotificationsScheduler()
        {
            return _instance._localNotificationsScheduler;
        }

        public void Restart(bool logout = false)
        {
            Unsubscribe();
            if (_restarting)
            {
                CrashLoggerService.Log("Restart Trying to restart game while game is restarting");
                Debug.LogError("Restart Trying to restart game while game is restarting");
                return;
            }

            LoadingTimeMetricsManager.LoadingStarted(false);

            if (AppDefinesConverter.BbbDebug)
            {
                AppDefinesConverter.CacheLastUsedEnvType();
            }

            CrashLoggerService.Log($"Restarting Game");
            _restarting = true;
            _awakeCalled = false;
            _asyncCommandManager?.Pause();
            _asyncCommandManager?.DropCommands();

            _walletManager?.Restart();
            _questManager?.Restart();
            _luaManager?.Restart();
            _modalsManager?.Restart();
            _deepLinkExecutionManager?.Restart();
            _bundlesConfigUpdater?.Restart();
            _screensBuilder?.Restart();

            MemoryAdviceApi.Restart();
            NetworkMetricsManager.Restart();
            LoadingTimeMetricsManager.Restart();

            _globalContext?.Releaser?.ReleaseContext();

            if (_globalContext?.Releaser == null)
            {
                CrashLoggerService.Log($"GlobalContext null: {_globalContext} release:{_globalContext?.Releaser}");
            }

            if (_appContext?.Releaser == null)
            {
                CrashLoggerService.Log($"AppContext null: {_appContext} release:{_appContext?.Releaser}");
            }

            _appContext?.Releaser?.ReleaseContext();
            _priorityLoader?.Restart();
            _gameNotificationManager?.DeInit();
            StandardAudioPlayer.Restart();
            ContextedUiBehaviour.SetupRootContext(null);
            Analytics.Restart();
            CrashLoggerService.Restart();
            if (!ResetPlayer.IsPlayerResetInThisSession)
            {
                _playerManager.MarkDirty();
            }

            _playerManager?.RestartApp();
            _bundlesBackgroundDownloaderManager.Restart();
            _localizationManager?.Restart();
            _leaderboardManager?.Restart();
            _adsManager?.Restart();
            _themeManager?.Restart();
            _match3LockListener?.Restart();
            _signInManager?.Restart();
            LoadingProcessTracker.Restart();
            SoftMask.ResetMasks();
            _quickActionsManager?.Restart();
            _helpDeskManager?.Restart(logout);
            _dailyLoginManager?.Restart();
            _weeklyLeaderboardManager?.Restart();

            if (this != null)
            {
                StopAllCoroutines();
            }

            SceneManager.sceneUnloaded -= OnSceneUnloaded;
            SceneManager.sceneUnloaded += OnSceneUnloaded;
            BugsnagSceneManager.LoadScene("Main");

            BDebug.Log(LogCat.General, $"[RESTART]");
            ResetPlayer.IsPlayerResetInThisSession = false;
            ResetPlayer.ShouldNotRestart = false;
        }

        private void OnSceneUnloaded(Scene current)
        {
            CrashLoggerService.Log($"OnSceneUnloaded restarting:{_restarting} current: {current.name}");
            if (_restarting)
            {
                if (_bundleManager == null)
                {
                    Debug.LogError($"OnSceneUnloaded _bundleManager null");
                    CrashLoggerService.Log($"OnSceneUnloaded _bundleManager null");
                }

                if (_assetsManager == null)
                {
                    Debug.LogError("OnSceneUnloaded _assetsManager null");
                    CrashLoggerService.Log($"OnSceneUnloaded _assetsManager null");
                }

                if (_accountManager == null)
                {
                    Debug.LogError("OnSceneUnloaded _accountManager null");
                    CrashLoggerService.Log($"OnSceneUnloaded _accountManager null");
                }

                _bundleManager?.Restart();
                _assetsManager?.Restart();
                _accountManager?.Restart();
                _genericResourceProvider?.DisposeResources();

                NullifyDependencies();
                AssetBundle.UnloadAllAssetBundles(true);
                Resources.UnloadUnusedAssets();
                _restarting = false;
                SceneManager.sceneUnloaded -= OnSceneUnloaded;
            }
            else
            {
                Debug.LogError("OnSceneUnloaded restarting false");
            }
        }
        
        private static void UniTask_UnhandledException(Exception obj)
        {
            Debug.LogException(obj);
        }

#if UNITY_EDITOR
        [ContextMenu("DebugPrintLocalizationCharactersSet")]
        public void DebugPrintLocalizationCharactersSet()
        {
            _localizationManager.DebugPrintDistictCharactersSetForCurrentConfig();
        }
#endif

        public void OpenUrl(string url)
        {
#if BBB_DEBUG
            if (OpenExternal._debugIsExtrnalUrlOpenDisabled)
            {
                return;
            }
#endif

#if UNITY_EDITOR || BBB_DEBUG

            if (Input.GetKey(KeyCode.LeftShift))
            {
                return;
            }
#endif
            if (!url.IsNullOrEmpty())
            {
                Application.OpenURL(url);
            }
        }

        private void NullifyDependencies()
        {
            _tutorialPlaybackController = null;
            _uiWalletManager = null;
            _overlayEffectRenderer = null;
            _genericHudManager = null;
            _appContext = null;
            _globalContext = null;
            _rpcService = null;
            _config = null;
            _playerManager = null;
            _userSettings = null;
            _leaderboardManager = null;
            _livesManager = null;
            _boosterManager = null;
            _luaManager = null;
            _localizationManager = null;
            _lockManager = null;
            _bundlesConfigUpdater = null;
            _timeManager = null;
            _eventDispatcher = null;
            _iapManager = null;
            _gachaManager = null;
            _questManager = null;
            _assetDownloadManager = null;
            _screensBuilder = null;
            _pom = null;
            _brainCloudManager = null;
            _fakeUsersManager = null;
            _playerEventsManager = null;
            _asyncCommandManager = null;
            _asyncCommandGenerator = null;
            _bundleManager = null;
            _assetsManager = null;
            _flagsLoader = null;
            _locationManager = null;
            _walletManager = null;
            _genericResourceProvider = null;
            _notificationManager = null;
            _levelsOrderingManager = null;
            _accountManager = null;
            _loginManager = null;
            _adsManager = null;
            _infoModalFactory = null;
            _videoAdsPlayer = null;
            _objectiveProgressListener = null;
            _gameNotificationManager = null;
            _localNotificationsScheduler = null;
            _rpcCommandManager = null;
            _genericModalFactory = null;
            _levelNarrativeController = null;
            _performanceManager = null;
            _gameEventManager = null;
            _raceEventManager = null;
            _royaleEventManager = null;
            _teamEventManager = null;
            _loadingCommands = null;
            _adLootboxManager = null;
            _bundlesBackgroundDownloaderManager = null;
            _vibrations = null;
            _priorityLoader = null;
            _analyticsMonitor = null;
            _deepLinkManager = null;
            _notificationService = null;
            _protobufSerializer = null;
            _hudSlotsManager = null;
        }

        public static bool IsDestroyed()
        {
#if UNITY_EDITOR
            if (M3Editor.M3Editor.IsCurrentSceneLevelEditorScene())
            {
                return false;
            }
#endif
            return _instance == null;
        }
    }

    internal interface IAssetsManagerReceiver
    {
        void SetAssetManager(IAssetsManager assetsManager);
    }
}

