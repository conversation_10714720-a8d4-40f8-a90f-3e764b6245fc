using System;
using BBB.Core;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BrainCloud;
using BrainCloud.UnityWebSocketsForWebGL.WebSocketSharp.Net;
using BugsnagUnityPerformance;
using GameAssets.Scripts.Core.NewtonSoftJsonExtensions;

namespace BBB.BrainCloud
{
    public static class BrainCloudExtensions
    {
        public static void RunScript<TResponse>(this BrainCloudWrapper wrapper,
            string scriptName,
            string jsonScriptData,
            Action<TResponse> success = null,
            Action failure = null,
            object cbObject = null) where TResponse : BCResponseBase
        {
            var requestStartTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            wrapper.ScriptService.RunScript(scriptName, jsonScriptData, SuccessCallback, FailureCallback, cbObject);
            return;

            void FailureCallback(int status, int code, string error, object callBackObject)
            {
                BDebug.Log(LogCat.General, $"{scriptName} failed. error={error} code={code}");
                AppController.GetBrainCloudManager().ProcessError(status, code, error, callBackObject);
                NetworkMetricsManager.ReportBrainCloudHttpRequest(scriptName, HttpVerb.POST, requestStartTime, code,jsonScriptData, error);

                failure?.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object responseObject)
            {
                BDebug.Log(LogCat.General, $"{scriptName} success. jsonResponse={jsonResponse}");
                var response = JsonConvertExtensions.TryDeserializeObject<TResponse>(jsonResponse);
                if (!response.IsSuccess)
                {
                    FailureCallback(response.Status, response.ReasonCode, response.Message, cbObject);
                    return;
                }

                NetworkMetricsManager.ReportBrainCloudHttpRequest(scriptName, HttpVerb.POST, requestStartTime, HttpStatusCode.OK.ToInt(), jsonScriptData, jsonResponse);

                success?.SafeInvoke(response);
            }
        }
        
        public static void RunScript(this BrainCloudWrapper wrapper, 
            string scriptName,
            string jsonScriptData,
            SuccessCallback success = null,
            FailureCallback failure = null,
            object cbObject = null)
        {
            var requestStartTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;

            wrapper.ScriptService.RunScript(scriptName, jsonScriptData, SuccessCallback, FailureCallBack, cbObject);
            return;

            void FailureCallBack(int status, int code, string error, object callBackObject)
            {
                BDebug.Log(LogCat.General, $"{scriptName} failed. error={error} code={code}");
                AppController.GetBrainCloudManager().ProcessError(status, code, error, cbObject);
                NetworkMetricsManager.ReportBrainCloudHttpRequest(scriptName, HttpVerb.POST, requestStartTime, code, jsonScriptData,error);

                failure?.Invoke(status, code, error, cbObject);
            }

            void SuccessCallback(string jsonResponse, object responseObject)
            {
                BDebug.Log(LogCat.General, $"{scriptName} success. jsonResponse={jsonResponse}");
                var response = JsonConvertExtensions.TryDeserializeObject<BCRunScriptResponse>(jsonResponse);
                if (!response.IsSuccess)
                {
                    FailureCallBack(response.Status, response.ReasonCode, response.Message, cbObject);
                    return;
                }

                NetworkMetricsManager.ReportBrainCloudHttpRequest(scriptName, HttpVerb.POST, requestStartTime, HttpStatusCode.OK.ToInt(), jsonScriptData, jsonResponse);
                success?.Invoke(jsonResponse, responseObject);
            }
        }
    }
}