using System;
using System.Collections.Generic;
using System.Text;
using BBB.BrainCloud.InputDTOs;
using BBB.GameAssets.Scripts.Player;
using BrainCloud;
using Newtonsoft.Json;
using UnityEngine;

namespace BBB.BrainCloud
{
    public partial class BrainCloudManager
    {
        private readonly List<string> _leaderboardKeys = new ();
        public void GetLeaderboards(List<string> playerLeaderboardIds, string[] groupLeaderboardIds, 
            bool debugMode, Dictionary<string, RotatingLeaderboardPeriodParams> joinedPeriodsParams,
            Action<BCLeaderboardsResponse> success, Action failure)
        {
            var inputDto = new GetLeaderboardsInput
            {
                playerLeaderboardIds = playerLeaderboardIds,
                groupLeaderboardIds = groupLeaderboardIds,
                debugMode = debugMode,
                joinedPeriodsParams = joinedPeriodsParams
            };
            
            var scriptData = JsonConvert.SerializeObject(inputDto);
            _brainCloudWrapper.RunScript("/leaderboard/GetLeaderboardsScore", scriptData, success, failure);
        }

        public void GetLeaderboardConfig(List<string> leaderboardIds, Action<BCLeaderboardConfigData> success, Action failure)
        {
            var scriptData = $"{{\"leaderboardIds\":{JsonConvert.SerializeObject(leaderboardIds)}}}";
            _brainCloudWrapper.RunScript("GetLeaderboardConfig", scriptData, success, failure);
        }

        public void PostScoreToLeaderboard(string leaderboardId, int score, string jsonOtherData, Action successCallback, Action failureCallback)
        {
            if (!IsAuthenticated)
            {
                failureCallback.SafeInvoke();
                return;
            }

            _brainCloudWrapper.LeaderboardService.PostScoreToLeaderboard(leaderboardId, score, jsonOtherData, SuccessCallback, ErrorHandlerCallback((FailureCallback)FailureCallback));
            return;

            void FailureCallback(int status, int code, string error, object cbObject)
            {
                Debug.Log($"PostScoreToLeaderboard Failed | {status}  {code}  {error}");
                failureCallback?.Invoke();
            }

            void SuccessCallback(string response, object cbObject)
            {
                Debug.Log($"PostScoreToLeaderboard Success | {response}");
                successCallback?.Invoke();
            }
        }

        public void PostScoreToDynamicLeaderboardUtc(string leaderboardId,
                                             int score,
                                             string data = EmptyJson,
                                             BrainCloudSocialLeaderboard.SocialLeaderboardType leaderboardType = BrainCloudSocialLeaderboard.SocialLeaderboardType.LAST_VALUE,
                                             BrainCloudSocialLeaderboard.RotationType rotationType = BrainCloudSocialLeaderboard.RotationType.NEVER,
                                             ulong rotationReset = 1,
                                             int retaintedCount = 1,
                                             Action successCallback = null,
                                             Action failureCallback = null)
        {
            if (!IsAuthenticated)
            {
                failureCallback.SafeInvoke();
                return;
            }

            _brainCloudWrapper.LeaderboardService.PostScoreToDynamicLeaderboardUTC(leaderboardId,
                                                                                   score,
                                                                                   data,
                                                                                   leaderboardType,
                                                                                   rotationType,
                                                                                   rotationReset,
                                                                                   retaintedCount,
                                                                                   SuccessCallback,
                                                                                   ErrorHandlerCallback((FailureCallback)FailureCallback));
            return;

            void FailureCallback(int status, int code, string error, object cbObject)
            {
                Debug.Log($"PostScoreToDynamicLeaderboardDaysUTC Failed | {status}  {code}  {error}");
                failureCallback?.Invoke();
            }

            void SuccessCallback(string response, object cbObject)
            {
                Debug.Log($"PostScoreToDynamicLeaderboardDaysUTC Success | {response}");
                successCallback?.Invoke();
            }
        }
    }
}