using BBB;
using BBB.Audio;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.UI;
using BebopBee.Core.Audio;
using Core.RPC;
using GameAssets.Scripts.Core;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class SettingsBar : BbbMonoBehaviour
{
    [SerializeField] private Button _soundToggle;
    [SerializeField] private MaterialSwapper _soundButtonMaterialSwapper;
    [SerializeField] private Button _musicToggle;
    [SerializeField] private MaterialSwapper _musicButtonMaterialSwapper;
    [SerializeField] private Button _voToggle;
    [SerializeField] private MaterialSwapper _voButtonMaterialSwapper;
    [SerializeField] private Button _vibrationToggle;
    [SerializeField] private MaterialSwapper _vibrationButtonMaterialSwapper;
    [SerializeField] private Button _notificationsToggle;
    [SerializeField] private MaterialSwapper _notificationButtonMaterialSwapper;
    [SerializeField] private Button _hintsToggle;
    [SerializeField] private MaterialSwapper _hintsButtonMaterialSwapper;
    [SerializeField] private Button _faqButton;

    private UserSettings _settings;
    private IHelpDeskManager _helpDeskManager;
    private GenericModalFactory _genericModalFactory;

    private string _faqLink;
    public Button VibrationToggle => _vibrationToggle;

    protected override void OnEnable()
    {
        Refresh();
    }

    public void Init(IContext context)
    {
        _settings = context.Resolve<UserSettings>();
        _helpDeskManager = context.Resolve<IHelpDeskManager>();
        _genericModalFactory = context.Resolve<GenericModalFactory>();
        
        SaveDefaultMaterials();
        Subscribe();

        Refresh();
    }

    private void Refresh()
    {
        if (_settings != null)
        {
            RefreshAllTogglesState();
            CheckForVibrationButton();
        }
    }

    private void SaveDefaultMaterials()
    {
        _soundButtonMaterialSwapper?.StoreOriginalValues();
        _musicButtonMaterialSwapper?.StoreOriginalValues();
        _voButtonMaterialSwapper?.StoreOriginalValues();
        _vibrationButtonMaterialSwapper?.StoreOriginalValues();
        _notificationButtonMaterialSwapper?.StoreOriginalValues();
        _hintsButtonMaterialSwapper?.StoreOriginalValues();
    }

    private void RefreshAllTogglesState()
    {
        _soundButtonMaterialSwapper?.SetOriginalValue(_settings.SFXOn);
        _musicButtonMaterialSwapper?.SetOriginalValue(_settings.MusicOn);
        _voButtonMaterialSwapper?.SetOriginalValue(_settings.VOOn);
        _vibrationButtonMaterialSwapper?.SetOriginalValue(_settings.VibrationsON);
        _notificationButtonMaterialSwapper?.SetOriginalValue(_settings.NotificationsON);
        _hintsButtonMaterialSwapper?.SetOriginalValue(_settings.HintsOn);
    }

    private void Subscribe()
    {
        _soundToggle?.ReplaceOnClick(SoundToggleClickedHandler);
        _musicToggle?.ReplaceOnClick(MusicToggleClickedHandler);
        _voToggle?.ReplaceOnClick(VoToggleClickedHandler);
        _vibrationToggle?.ReplaceOnClick(VibrationsToggleClickedHandler);
        _notificationsToggle?.ReplaceOnClick(NotificationsToggleClickedHandler);
        _hintsToggle?.ReplaceOnClick(HintsToggleClickedHandler);

        _faqButton?.ReplaceOnClick(FaqButtonClickedHandler);
    }

    private async void FaqButtonClickedHandler()
    {
        Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Faq, string.Empty));
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        
        await _helpDeskManager.ShowHelpCenter();
    }

    private void SoundToggleClickedHandler()
    {
        Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Sfx, string.Empty));
        _settings.ToggleSFX();
        _soundButtonMaterialSwapper.SetOriginalValue(_settings.SFXOn);
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
    }

    private void MusicToggleClickedHandler()
    {
        Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Bgm, string.Empty));
        _settings.ToggleMusic();
        _musicButtonMaterialSwapper.SetOriginalValue(_settings.MusicOn);
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
    }

    private void VoToggleClickedHandler()
    {
        Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Vo, string.Empty));
        _settings.ToggleVO();
        _voButtonMaterialSwapper.SetOriginalValue(_settings.VOOn);
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
    }

    private void VibrationsToggleClickedHandler()
    {
        Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Vibration, string.Empty));
        _settings.ToggleVibrations();
        _vibrationButtonMaterialSwapper.SetOriginalValue(_settings.VibrationsON);
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
    }

    private void NotificationsToggleClickedHandler()
    {
        Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Notifications, string.Empty));
        _settings.ToggleNotifications();
        _notificationButtonMaterialSwapper.SetOriginalValue(_settings.NotificationsON);
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
    }
    
    private void HintsToggleClickedHandler()
    {
        Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnSettings.Name, DauInteractions.TapOnSettings.Hints, string.Empty));
        _settings.ToggleHints();
        _hintsButtonMaterialSwapper.SetOriginalValue(_settings.HintsOn);
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
    }

    private void CheckForVibrationButton()
    {
        var isVibrationSupported = SystemInfo.supportsVibration;
        if (_vibrationToggle != null)
        {
            _vibrationToggle.gameObject.SetActive(_settings.VibrationsGlobalyON && isVibrationSupported);
        }
    }
}