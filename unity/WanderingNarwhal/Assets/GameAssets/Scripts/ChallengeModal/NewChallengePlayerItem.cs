using System;
using BBB;
using GameAssets.Scripts.GlobeModal;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.ChallengeModal
{
    public class NewChallengePlayerItem : BbbMonoBehaviour
    {
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private GameObject _favoriteHolder;

        [SerializeField] private TextMeshProUGUI _playerName;
        [SerializeField] private TextMeshProUGUI _playerRank;
        [SerializeField] private LocalizedTextPro _challengesPlayed;

        [SerializeField] private Button _challengeButton;
        [SerializeField] private Button _inactiveButton;
        
        [SerializeField] private Transform _floatingTextAnchor;
        
        private Action<ChallengeInfo, Transform> _challengeButtonCallback;
        private ChallengeInfo _challengeInfo;

        public void Init(Action<ChallengeInfo, Transform> challengeButtonCallback)
        {
            _challengeButtonCallback = challengeButtonCallback;
            _challengeButton.ReplaceOnClick(ChallengeButtonHandler);
            _inactiveButton.ReplaceOnClick(ChallengeButtonHandler);
        }

        private void ChallengeButtonHandler()
        {
            _challengeButtonCallback?.Invoke(_challengeInfo, _floatingTextAnchor);
        }

        public void Setup(ChallengeInfo challengeInfo)
        {
            _challengeInfo = challengeInfo;
            _asyncAvatar.Setup(new AvatarInfo(challengeInfo.AvatarUrl, challengeInfo.ChallengeLocationConfig.CountryCode, challengeInfo.AvatarFrame, challengeInfo.BadgeUid));
            _favoriteHolder.SetActive(challengeInfo.Favorite);
            _playerName.text = challengeInfo.PlayerName;
            _playerRank.text = challengeInfo.PlayerRank.ToString();
            
            var canSendChallenge = challengeInfo is { AlreadyPlaying: false, LimitReached: false };
            _challengeButton.gameObject.SetActive(canSendChallenge);
            _inactiveButton.gameObject.SetActive(!canSendChallenge);

            if (challengeInfo.ChallengesPlayed > 0)
            {
                _challengesPlayed.FormatSetArgs(challengeInfo.ChallengesPlayed);
                _challengesPlayed.gameObject.SetActive(true);
            }
            else
            {
                _challengesPlayed.gameObject.SetActive(false);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _challengeButtonCallback = null;
        }
    }
}