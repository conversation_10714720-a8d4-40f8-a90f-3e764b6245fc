using BBB.BrainCloud;
using FBConfig;
using PBGame;
using BBB;
using BebopBee.Social;

namespace GameAssets.Scripts.GlobeModal
{
    public readonly struct ChallengeInfo
    {
        public ChallengeInfo(TeamMemberData teamMemberData, ChallengeLocationConfig challengeLocationConfig, int challengesPlayed, bool favorite, bool alreadyPlaying, bool limitReached)
        {
            PlayerUid = teamMemberData.Uid;
            PlayerName = teamMemberData.Name;
            AvatarUrl = teamMemberData.Avatar;
            PlayerRank = teamMemberData.Trophies;
            AvatarFrame = teamMemberData.AvatarFrame ?? ProfileUtils.DefaultFrameUid;
            BadgeUid = teamMemberData.BadgeUid ?? ProfileUtils.DefaultBadgeUid;

            ChallengeLocationConfig = challengeLocationConfig;
            ChallengesPlayed = challengesPlayed;
            Favorite = favorite;
            TeamMember = true;
            AlreadyPlaying = alreadyPlaying;
            LimitReached = limitReached;
        }

        public ChallengeInfo(BCPlayerProfileData profileData, ChallengeLocationConfig challengeLocationConfig, int challengesPlayed, bool favorite, bool alreadyPlaying, bool limitReached)
        {
            PlayerUid = profileData.UserId;
            PlayerName = profileData.UserName;
            AvatarUrl = profileData.AvatarUrl;
            PlayerRank = profileData.SummaryFriendData.Trophies;
            AvatarFrame = profileData.SummaryFriendData.AvatarFrame ?? ProfileUtils.DefaultFrameUid;
            BadgeUid = profileData.SummaryFriendData.BadgeUid ?? ProfileUtils.DefaultBadgeUid;

            ChallengeLocationConfig = challengeLocationConfig;
            ChallengesPlayed = challengesPlayed;
            Favorite = favorite;
            TeamMember = false;
            AlreadyPlaying = alreadyPlaying;
            LimitReached = limitReached;
        }
        
        public ChallengeInfo(PBFriendData friendData, ChallengeLocationConfig challengeLocationConfig, int challengesPlayed, bool teamMember, bool favorite, bool alreadyPlaying, bool limitReached)
        {
            PlayerUid = friendData.UserId;
            PlayerName = friendData.UserName;
            AvatarUrl = friendData.AvatarUrl;
            PlayerRank = friendData.Trophies;
            AvatarFrame = ProfileUtils.DefaultFrameUid;
            BadgeUid = ProfileUtils.DefaultBadgeUid;

            ChallengeLocationConfig = challengeLocationConfig;
            ChallengesPlayed = challengesPlayed;
            Favorite = favorite;
            TeamMember = teamMember;
            AlreadyPlaying = alreadyPlaying;
            LimitReached = limitReached;
        }

        public readonly string PlayerUid;
        public readonly string PlayerName;
        public readonly string AvatarUrl;
        public readonly int PlayerRank;

        public readonly ChallengeLocationConfig ChallengeLocationConfig;
        public readonly int ChallengesPlayed;
        public readonly bool Favorite;
        public readonly bool TeamMember;
        public readonly bool AlreadyPlaying;
        public readonly bool LimitReached;
        public readonly string AvatarFrame;
        public readonly string BadgeUid;
    }
}