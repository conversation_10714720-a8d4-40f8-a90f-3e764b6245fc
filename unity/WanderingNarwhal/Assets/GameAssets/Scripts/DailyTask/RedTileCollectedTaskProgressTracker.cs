using BBB;

namespace GameAssets.Scripts.DailyTask
{
    public class RedTileCollectedTaskProgressTracker : BaseTileCollectedTaskProgressTracker
    {
        public override string TaskType => "RedTile";
        
        protected override void TileCollectedEventHandler(TileCollectedEvent ev)
        {
            var tile = ev.Arg0;
            if (tile != null && tile.Speciality != TileSpeciality.ColorCrate && tile.Kind == TileKinds.Red)
            {
                AddProgress(1);
            }
        }
    }
}