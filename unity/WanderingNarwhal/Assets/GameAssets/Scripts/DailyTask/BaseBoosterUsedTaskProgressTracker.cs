using BBB;
using BBB.DI;
using BBB.UI;

namespace GameAssets.Scripts.DailyTask
{
    public abstract class BaseBoosterUsedTaskProgressTracker : TaskProgressTracker
    {
        protected IEventDispatcher _eventDispatcher;

        public override void Init(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            
            _eventDispatcher.RemoveListener<BoosterUsedEvent>(BoosterUsedEventHandler);
            _eventDispatcher.AddListener<BoosterUsedEvent>(BoosterUsedEventHandler);
        }

        public override void DeInit()
        {
            base.DeInit();
            _eventDispatcher.RemoveListener<BoosterUsedEvent>(BoosterUsedEventHandler);
        }

        public override void OnFlowRequested()
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<LevelFlowRequestedEvent>());
        }

        protected abstract void BoosterUsedEventHandler(BoosterUsedEvent ev);
    }
}