using BBB;
using BBB.Controller;
using BBB.DI;
using BBB.Tools;
using BebopBee;
using UnityEngine.Profiling;

namespace Loading.Commands
{
    public class ShowLoadingScreenCommand : CommandBase
    {
        public override void Execute(IContext context)
        {

            Profiler.BeginSample($"FunnelTracker.SendFunnels");
            FunnelTracker.SendFunnels();
            Profiler.EndSample();

            var screensBuilder = context.Resolve<IScreensBuilder>();
            var screensManager = context.Resolve<IScreensManager>();
            LoadingProcessTracker.LogShowScreen(ScreenType.LoadingScreen.ToString(), screensManager.GetTrackingPreviousScreenType(), "CmdShowLoadingScreen");
            screensBuilder.ShowScreen<LoadingScreenController>(ScreenType.LoadingScreen);
            CurrentStatus = CommandStatus.Success;
        }
    }
}