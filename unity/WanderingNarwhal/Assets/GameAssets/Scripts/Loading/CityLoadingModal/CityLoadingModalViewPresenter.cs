using System;
using BBB.DI;
using GameAssets.Scripts.Core.AssetBundles;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class CityLoadingModalViewPresenter : Core.ViewPresenter, ICityLoadingModalViewPresenter
    {
        private const string ProcessingHeaderTextLoc = "GETTING_READY_DOTS";
        private const string ProcessingDescriptionTextLoc = "CITY_LOADING_MODAL_BEING_PROCESSED";
        private const string ProcessingButtonTextLoc = "GO_NOW";

        private const string FinishedHeaderTextLoc = "READY_TO_GO";
        private const string FinishedDescriptionTextLoc = "CITY_LOADING_MODAL_FINISHED";
        private const string FinishedButtonTextLoc = "LEVEL_START_GO_BTN";

        public event Action FinishedEvent;
        public event Action GoButtonClicked;

        [SerializeField] private Button _goButton;
        [SerializeField] private ProgressBar _progressBar;
        [SerializeField] private TextMeshProUGUI _progressText;
        
        [SerializeField] private LocalizedTextPro _headerText;
        [SerializeField] private LocalizedTextPro _descriptionText;
        [SerializeField] private LocalizedTextPro _buttonText;

        [SerializeField] private float _progressUpdateRate = 0.5f;
        [SerializeField] private float _minAnimTime = 5f;
        [SerializeField] private float _maxAnimTime = 7f;
        
        private float _animTimer = 0f;

        private int _lastSetPercentProgress = -1;
        private string _locationUid;
        private float _progressUpdateTimer = 0f;

        private float _targetProgressValue = -1f;

        private LocationBundleManager _locationBundleManager;
        private float _moveTimer;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _locationBundleManager = context.Resolve<LocationBundleManager>();

            _goButton.ReplaceOnClick(OnGoButton);
        }

        public void Setup(string locationUid)
        {
            _locationUid = locationUid;
            _animTimer = UnityEngine.Random.Range(_minAnimTime, _maxAnimTime);
            float progress = _locationBundleManager.GetLocationLoadingProgress(_locationUid);
            SetLoadingProgress(progress);
        }

        private void Update()
        {
            if (_targetProgressValue < 1f)
            {
                _progressUpdateTimer += Time.deltaTime;

                if (_progressUpdateTimer > _progressUpdateRate)
                {
                    _progressUpdateTimer = 0f;
                    _moveTimer = 0f;
                    _targetProgressValue = _locationBundleManager.GetLocationLoadingProgress(_locationUid);
                }
            }

            var currentProgressValue = _progressBar.GetProgress();

            if (_targetProgressValue > 0f && currentProgressValue < _targetProgressValue)
            {
                _moveTimer += Time.deltaTime;
                var timerPercentage = Mathf.Clamp01(_moveTimer / _animTimer);
                var progress = Mathf.Clamp01(_targetProgressValue);
                currentProgressValue = Mathf.Lerp(currentProgressValue, progress, timerPercentage);
                SetLoadingProgress(currentProgressValue);
            }
        }

        private void SetLoadingProgress(float progress)
        {
            progress = Mathf.Clamp01(progress);
            
            _progressBar.SetProgress(progress);

            var percent = (int)(progress * 100f);
            percent = Mathf.Clamp(percent, 0, 100);
            if (_lastSetPercentProgress != percent)
            {
                _progressText.text = percent + "%";
                _lastSetPercentProgress = percent;

                if (percent == 100)
                {
                    SetFinishedState();
                }
                else
                {
                    SetProcessingState();
                }
            }
        }

        private void SetFinishedState()
        {
            _headerText.SetTextId(FinishedHeaderTextLoc);
            _descriptionText.SetTextId(FinishedDescriptionTextLoc);
            _buttonText.SetTextId(FinishedButtonTextLoc);
            FinishedEvent.SafeInvoke();
        }

        private void SetProcessingState()
        {
            _headerText.SetTextId(ProcessingHeaderTextLoc);
            _descriptionText.SetTextId(ProcessingDescriptionTextLoc);
            _buttonText.SetTextId(ProcessingButtonTextLoc);
        }

        protected override void OnShow()
        {
        }
		
        private void OnGoButton()
        {
            GoButtonClicked.SafeInvoke();
        }
		
        protected override void OnHide()
        {
            _lastSetPercentProgress = -1;
            _locationUid = null;
            _progressUpdateTimer = 0f;
            _targetProgressValue = -1f;
            _moveTimer = 0f;
        }
    }
}