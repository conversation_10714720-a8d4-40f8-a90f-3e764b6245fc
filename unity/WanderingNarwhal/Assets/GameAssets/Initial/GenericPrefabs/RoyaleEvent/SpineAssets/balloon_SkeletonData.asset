%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1b3b4b945939a54ea0b23d3396115fb, type: 3}
  m_Name: balloon_SkeletonData
  m_EditorClassIdentifier: 
  atlasAssets:
  - {fileID: 11400000, guid: 4c7246e62f6484b0eae212a076e2990d, type: 2}
  scale: 0.01
  skeletonJSON: {fileID: 4900000, guid: 857a00b7df84d41eeb8a15559857aaf4, type: 3}
  isUpgradingBlendModeMaterials: 0
  blendModeMaterials:
    requiresBlendModeMaterials: 1
    applyAdditiveMaterial: 0
    additiveMaterials: []
    multiplyMaterials: []
    screenMaterials:
    - pageName: balloon.png
      material: {fileID: 2100000, guid: e0f12a64dc81d4a688f46ee8a7cc90c9, type: 2}
  skeletonDataModifiers: []
  fromAnimation: []
  toAnimation: []
  duration: []
  defaultMix: 0.2
  controller: {fileID: 0}
