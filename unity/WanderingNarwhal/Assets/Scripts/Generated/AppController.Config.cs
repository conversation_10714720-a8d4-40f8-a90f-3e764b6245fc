using System.Collections.Generic;
using Core.Configs;
using FBConfig;
using PBConfig;
using BundleIndexConfig = FBConfig.BundleIndexConfig;
using BundleIndexConfigDict = FBConfig.BundleIndexConfigDict;
using BundleInfoConfig = FBConfig.BundleInfoConfig;
using BundleInfoConfigDict = FBConfig.BundleInfoConfigDict;
using DefaultNamesConfig = FBConfig.DefaultNamesConfig;
using DefaultNamesConfigDict = FBConfig.DefaultNamesConfigDict;
using GachaConfig = FBConfig.GachaConfig;
using GachaConfigDict = FBConfig.GachaConfigDict;
using GameUpdateConfig = FBConfig.GameUpdateConfig;
using GameUpdateConfigDict = FBConfig.GameUpdateConfigDict;
using IAPStoreMarketItemConfig = FBConfig.IAPStoreMarketItemConfig;
using IAPStoreMarketItemConfigDict = FBConfig.IAPStoreMarketItemConfigDict;
using LocalPushNotificationsTimingConfig = FBConfig.LocalPushNotificationsTimingConfig;
using LocalPushNotificationsTimingConfigDict = FBConfig.LocalPushNotificationsTimingConfigDict;
using LockItemConfig = FBConfig.LockItemConfig;
using LockItemConfigDict = FBConfig.LockItemConfigDict;
using QuestConfig = FBConfig.QuestConfig;
using QuestConfigDict = FBConfig.QuestConfigDict;
using ScreenConfig = FBConfig.ScreenConfig;
using ScreenConfigDict = FBConfig.ScreenConfigDict;
using SpriteAtlasIndexConfig = FBConfig.SpriteAtlasIndexConfig;
using SpriteAtlasIndexConfigDict = FBConfig.SpriteAtlasIndexConfigDict;
using SystemConfig = FBConfig.SystemConfig;
using SystemConfigDict = FBConfig.SystemConfigDict;
using ThemeConfig = FBConfig.ThemeConfig;
using ThemeConfigDict = FBConfig.ThemeConfigDict;
using ThemeMetaConfig = FBConfig.ThemeMetaConfig;
using ThemeMetaConfigDict = FBConfig.ThemeMetaConfigDict;
using TransitionTipsConfig = FBConfig.TransitionTipsConfig;
using TransitionTipsConfigDict = FBConfig.TransitionTipsConfigDict;
using TutorialConfig = FBConfig.TutorialConfig;
using TutorialConfigDict = FBConfig.TutorialConfigDict;
using UnifiedPromotionConfig = FBConfig.UnifiedPromotionConfig;
using UnifiedPromotionConfigDict = FBConfig.UnifiedPromotionConfigDict;
using VIPProductsMetaConfg = FBConfig.VIPProductsMetaConfg;
using VIPProductsMetaConfgDict = FBConfig.VIPProductsMetaConfgDict;
using AdsConfigDict = FBConfig.AdsConfigDict;
using AdsConfig = FBConfig.AdsConfig;
using IAPCategoryConfig = FBConfig.IAPCategoryConfig;
using IAPCategoryConfigDict = FBConfig.IAPCategoryConfigDict;
using IAPStoreCategoryConfigDict = FBConfig.IAPStoreCategoryConfigDict;
using IAPStoreCategoryConfig = FBConfig.IAPStoreCategoryConfig;
using IAPStoreVirtualItemPackConfigDict = FBConfig.IAPStoreVirtualItemPackConfigDict;
using IAPStoreVirtualItemPackConfig = FBConfig.IAPStoreVirtualItemPackConfig;
using OfferConfigDict = FBConfig.OfferConfigDict;
using OfferConfig = FBConfig.OfferConfig;
using BoosterConfigDict = FBConfig.BoosterConfigDict;
using BoosterConfig = FBConfig.BoosterConfig;
using SuperBoostConfigDict = FBConfig.SuperBoostConfigDict;
using SuperBoostConfig = FBConfig.SuperBoostConfig;
using EndlessTreasureConfigDict = FBConfig.EndlessTreasureConfigDict;
using EndlessTreasureConfig = FBConfig.EndlessTreasureConfig;
using RaceStageConfigDict = FBConfig.RaceStageConfigDict;
using RaceStageConfig = FBConfig.RaceStageConfig;
using TeamEventConfigDict = FBConfig.TeamEventConfigDict;
using TeamEventConfig = FBConfig.TeamEventConfig;
using CompetitionGameEventConfigDict = FBConfig.CompetitionGameEventConfigDict;
using CompetitionGameEventConfig = FBConfig.CompetitionGameEventConfig;
using LeadLevelConfigDict = FBConfig.LeadLevelConfigDict;
using LeadLevelConfig = FBConfig.LeadLevelConfig;
using Match3AwardsConfigDict = FBConfig.Match3AwardsConfigDict;
using Match3AwardsConfig = FBConfig.Match3AwardsConfig;
using Match3SpecialVoiceoversConfigDict = FBConfig.Match3SpecialVoiceoversConfigDict;
using Match3SpecialVoiceoversConfig = FBConfig.Match3SpecialVoiceoversConfig;
using LivesConfigDict = FBConfig.LivesConfigDict;
using LivesConfig = FBConfig.LivesConfig;
using HintSystemConfigDict = FBConfig.HintSystemConfigDict;
using HintSystemConfig = FBConfig.HintSystemConfig;
using SuperBoostProgressConfigDict = FBConfig.SuperBoostProgressConfigDict;
using SuperBoostProgressConfig = FBConfig.SuperBoostProgressConfig;
using VIPProductsConfgDict = FBConfig.VIPProductsConfgDict;
using VIPProductsConfg = FBConfig.VIPProductsConfg;
using GoalScoreConfigDict = FBConfig.GoalScoreConfigDict;
using GoalScoreConfig = FBConfig.GoalScoreConfig;
using FakeUsersConfigDict = FBConfig.FakeUsersConfigDict;
using FakeUsersConfig = FBConfig.FakeUsersConfig;
using LocalNotificationsConfigDict = FBConfig.LocalNotificationsConfigDict;
using LocalNotificationsConfig = FBConfig.LocalNotificationsConfig;
using LevelNarrativeConfigDict = FBConfig.LevelNarrativeConfigDict;
using LevelNarrativeConfig = FBConfig.LevelNarrativeConfig;
using CarrotsConfigDict = FBConfig.CarrotsConfigDict;
using CarrotsConfig = FBConfig.CarrotsConfig;
using SdbConfigDict = FBConfig.SdbConfigDict;
using SdbConfig = FBConfig.SdbConfig;
using SweepstakesDailyLoginConfigDict = FBConfig.SweepstakesDailyLoginConfigDict;
using SweepstakesDailyLoginConfig = FBConfig.SweepstakesDailyLoginConfig;
using SweepStakesGameEventConfigDict = FBConfig.SweepStakesGameEventConfigDict;
using SweepStakesGameEventConfig = FBConfig.SweepStakesGameEventConfig;
using SweepstakesVideoConfigDict = FBConfig.SweepstakesVideoConfigDict;
using SweepstakesVideoConfig = FBConfig.SweepstakesVideoConfig;
using GiantPinataOutcomeConfigDict = FBConfig.GiantPinataOutcomeConfigDict;
using GiantPinataOutcomeConfig = FBConfig.GiantPinataOutcomeConfig;
using ProfileCustomizationConfig = FBConfig.ProfileCustomizationConfig;
using ProfileCustomizationConfigDict = FBConfig.ProfileCustomizationConfigDict;

// <auto-generated> DO NOT EDIT. Run BebopBee/Configs/Generate Resolver Registry </auto-generated>
namespace BBB
{
    public partial class AppController
    {
        private static readonly IConfigResolver[] AllResolvers =
        {
            FlatBufferGreedyConfigResolver<BundleIndexConfigDict, BundleIndexConfig, BundleIndexConfigT>.Instance,
            FlatBufferGreedyConfigResolver<SpriteAtlasIndexConfigDict, SpriteAtlasIndexConfig, SpriteAtlasIndexConfigT>.Instance,
            FlatBufferGreedyConfigResolver<BundleInfoConfigDict, BundleInfoConfig, BundleInfoConfigT>.Instance,
            FlatBufferConfigResolver<IAPStoreMarketItemConfigDict, IAPStoreMarketItemConfig, IAPStoreMarketItemConfigT>.Instance,
            FlatBufferLazyConfigResolver<IAPCategoryConfigDict, IAPCategoryConfig, IAPCategoryConfigT>.Instance,
            FlatBufferLazyConfigResolver<IAPStoreCategoryConfigDict, IAPStoreCategoryConfig, IAPStoreCategoryConfigT>.Instance,
            FlatBufferLazyConfigResolver<IAPStoreVirtualItemPackConfigDict, IAPStoreVirtualItemPackConfig, IAPStoreVirtualItemPackConfigT>.Instance,
            FlatBufferLazyConfigResolver<OfferConfigDict, OfferConfig, OfferConfigT>.Instance,
            FlatBufferLazyConfigResolver<BoosterConfigDict, BoosterConfig, BoosterConfigT>.Instance,
            FlatBufferLazyConfigResolver<GachaConfigDict, GachaConfig, GachaConfigT>.Instance,
            FlatBufferConfigResolver<QuestConfigDict, QuestConfig, QuestConfigT>.Instance,
            FlatBufferLazyConfigResolver<ProgressionLevelConfigDict, ProgressionLevelConfig, ProgressionLevelConfigT>.Instance,
            FlatBufferGreedyConfigResolver<SlotMachineOutcomeConfigDict, SlotMachineOutcomeConfig, SlotMachineOutcomeConfigT>.Instance,
            FlatBufferGreedyConfigResolver<MechanicTargetingConfigDict, MechanicTargetingConfig, MechanicTargetingConfigT>.Instance,
            FlatBufferLazyConfigResolver<LeadLevelConfigDict, LeadLevelConfig, LeadLevelConfigT>.Instance,
            FlatBufferLazyConfigResolver<Match3AwardsConfigDict, Match3AwardsConfig, Match3AwardsConfigT>.Instance,
            FlatBufferLazyConfigResolver<Match3SpecialVoiceoversConfigDict, Match3SpecialVoiceoversConfig, Match3SpecialVoiceoversConfigT>.Instance,
            FlatBufferLazyConfigResolver<LivesConfigDict, LivesConfig, LivesConfigT>.Instance,
            FlatBufferLazyConfigResolver<HintSystemConfigDict, HintSystemConfig, HintSystemConfigT>.Instance,
            FlatBufferLazyConfigResolver<SuperBoostProgressConfigDict, SuperBoostProgressConfig, SuperBoostProgressConfigT>.Instance,
            FlatBufferLazyConfigResolver<SuperBoostConfigDict, SuperBoostConfig, SuperBoostConfigT>.Instance,
            FlatBufferLazyConfigResolver<GoalScoreConfigDict, GoalScoreConfig, GoalScoreConfigT>.Instance,
            FlatBufferConfigResolver<LockItemConfigDict, LockItemConfig, LockItemConfigT>.Instance,
            FlatBufferLazyConfigResolver<ScreenConfigDict, ScreenConfig, ScreenConfigT>.Instance,
            FlatBufferLazyConfigResolver<TransitionTipsConfigDict, TransitionTipsConfig, TransitionTipsConfigT>.Instance,
            FlatBufferLazyConfigResolver<AdsConfigDict, AdsConfig, AdsConfigT>.Instance,
            FlatBufferLazyConfigResolver<FakeUsersConfigDict, FakeUsersConfig, FakeUsersConfigT>.Instance,
            FlatBufferLazyConfigResolver<LocalNotificationsConfigDict, LocalNotificationsConfig, LocalNotificationsConfigT>.Instance,
            FlatBufferLazyConfigResolver<LocalPushNotificationsTimingConfigDict, LocalPushNotificationsTimingConfig, LocalPushNotificationsTimingConfigT>.Instance,
            FlatBufferConfigResolver<GameUpdateConfigDict, GameUpdateConfig, GameUpdateConfigT>.Instance,
            FlatBufferLazyConfigResolver<LevelNarrativeConfigDict, LevelNarrativeConfig, LevelNarrativeConfigT>.Instance,
            ProtobufConfigResolver<NarrativeDialogConfig>.Instance,
            ProtobufConfigResolver<AudioMixerConfig>.Instance,
            ProtobufConfigResolver<DeviceGradingConfig>.Instance,
            ProtobufConfigResolver<CountriesTiersConfig>.Instance,
            ProtobufConfigResolver<NotificationsMetaConfig>.Instance,
            ProtobufConfigResolver<LevelAdsConfig>.Instance,
            FlatBufferLazyConfigResolver<SystemConfigDict, SystemConfig, SystemConfigT>.Instance,
            ProtobufConfigResolver<LevelAssistWeightsConfig>.Instance,
            ProtobufConfigResolver<GameEventConfig>.Instance,
            ProtobufConfigResolver<GameEventMetaConfig>.Instance,
            FlatBufferLazyConfigResolver<CompetitionGameEventConfigDict, CompetitionGameEventConfig, CompetitionGameEventConfigT>.Instance,
            ProtobufConfigResolver<DailyTriviaConfig>.Instance,
            FlatBufferLazyConfigResolver<ChallengeTriviaRewardsConfigDict, ChallengeTriviaRewardsConfig, ChallengeTriviaRewardsConfigT>.Instance,
            FlatBufferGreedyConfigResolver<ChallengeTriviaConfigDict, ChallengeTriviaConfig, ChallengeTriviaConfigT>.Instance,
            FlatBufferLazyConfigResolver<ChallengeConfigDict, ChallengeConfig, ChallengeConfigT>.Instance,
            ProtobufConfigResolver<MiscConfig>.Instance,
            FlatBufferLazyConfigResolver<UnifiedPromotionConfigDict, UnifiedPromotionConfig, UnifiedPromotionConfigT>.Instance,
            ProtobufConfigResolver<IAPBasketConfig>.Instance,
            FlatBufferLazyConfigResolver<VIPProductsConfgDict, VIPProductsConfg, VIPProductsConfgT>.Instance,
            FlatBufferLazyConfigResolver<VIPProductsMetaConfgDict, VIPProductsMetaConfg, VIPProductsMetaConfgT>.Instance,
            FlatBufferLazyConfigResolver<ThemeConfigDict, ThemeConfig, ThemeConfigT>.Instance,
            FlatBufferLazyConfigResolver<ThemeMetaConfigDict, ThemeMetaConfig, ThemeMetaConfigT>.Instance,
            FlatBufferLazyConfigResolver<DefaultNamesConfigDict, DefaultNamesConfig, DefaultNamesConfigT>.Instance,
            FlatBufferLazyConfigResolver<SocialConfigDict, SocialConfig, SocialConfigT>.Instance,
            FlatBufferLazyConfigResolver<EndlessTreasureConfigDict, EndlessTreasureConfig, EndlessTreasureConfigT>.Instance,
            ProtobufConfigResolver<RaceGameEventConfig>.Instance,
            FlatBufferLazyConfigResolver<RaceStageConfigDict, RaceStageConfig, RaceStageConfigT>.Instance,
            ProtobufConfigResolver<RoyaleGameEventConfig>.Instance,
            FlatBufferLazyConfigResolver<TeamEventConfigDict, TeamEventConfig, TeamEventConfigT>.Instance,
            FlatBufferLazyConfigResolver<PlayerSkillConfigDict, PlayerSkillConfig, PlayerSkillConfigT>.Instance,
            FlatBufferConfigResolver<IceBreakerConfigDict, IceBreakerConfig, IceBreakerConfigT>.Instance,
            FlatBufferLazyConfigResolver<WeeklyLeaderboardConfigDict, WeeklyLeaderboardConfig, WeeklyLeaderboardConfigT>.Instance,
            FlatBufferLazyConfigResolver<AdPlacementConfigDict, AdPlacementConfig, AdPlacementConfigT>.Instance,
            FlatBufferLazyConfigResolver<ButlerGiftConfigDict, ButlerGiftConfig, ButlerGiftConfigT>.Instance,
            FlatBufferConfigResolver<AutoPopupPriorityConfigDict, AutoPopupPriorityConfig, AutoPopupPriorityConfigT>.Instance,
            FlatBufferConfigResolver<HudAssetReferenceConfigDict, HudAssetReferenceConfig, HudAssetReferenceConfigT>.Instance,
            FlatBufferConfigResolver<HudConfigDict, HudConfig, HudConfigT>.Instance,
            FlatBufferGreedyConfigResolver<CollectionSetConfigDict, CollectionSetConfig, CollectionSetConfigT>.Instance,
            FlatBufferGreedyConfigResolver<CollectionCardsConfigDict, CollectionCardsConfig, CollectionCardsConfigT>.Instance,
            FlatBufferLazyConfigResolver<AssistSystemConfigDict, AssistSystemConfig, AssistSystemConfigT>.Instance,
            FlatBufferConfigResolver<TutorialConfigDict, TutorialConfig, TutorialConfigT>.Instance,
            FlatBufferGreedyConfigResolver<CollectionConfigDict, CollectionConfig, CollectionConfigT>.Instance,
            FlatBufferLazyConfigResolver<SceneTaskConfigDict, SceneTaskConfig, SceneTaskConfigT>.Instance,
            FlatBufferLazyConfigResolver<ScenesConfigDict, ScenesConfig, ScenesConfigT>.Instance,
            FlatBufferGreedyConfigResolver<DailyTasksConfigDict, DailyTasksConfig, DailyTasksConfigT>.Instance,
            FlatBufferLazyConfigResolver<DailyTaskSettingsConfigDict, DailyTaskSettingsConfig, DailyTaskSettingsConfigT>.Instance,
            FlatBufferConfigResolver<ChallengeLocationConfigDict, ChallengeLocationConfig, ChallengeLocationConfigT>.Instance,
            FlatBufferLazyConfigResolver<QuickActionsConfigDict, QuickActionsConfig, QuickActionsConfigT>.Instance,
            FlatBufferLazyConfigResolver<CarrotsConfigDict, CarrotsConfig, CarrotsConfigT>.Instance,
            FlatBufferLazyConfigResolver<SdbConfigDict, SdbConfig, SdbConfigT>.Instance,
            FlatBufferConfigResolver<SweepstakesDailyLoginConfigDict, SweepstakesDailyLoginConfig, SweepstakesDailyLoginConfigT>.Instance,
            FlatBufferLazyConfigResolver<SweepStakesGameEventConfigDict, SweepStakesGameEventConfig, SweepStakesGameEventConfigT>.Instance,
            FlatBufferLazyConfigResolver<SweepstakesVideoConfigDict, SweepstakesVideoConfig, SweepstakesVideoConfigT>.Instance,
            FlatBufferGreedyConfigResolver<GiantPinataOutcomeConfigDict, GiantPinataOutcomeConfig, GiantPinataOutcomeConfigT>.Instance,
            FlatBufferLazyConfigResolver<ProfileCustomizationConfigDict, ProfileCustomizationConfig, ProfileCustomizationConfigT>.Instance,
         };

         public static IReadOnlyList<IConfigResolver> GetConfigTypesToLoad() => AllResolvers;
   }
}
