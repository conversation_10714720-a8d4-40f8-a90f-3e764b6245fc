%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a8038d7a393d814b887bec61ad77979, type: 3}
  m_Name: Style.Personal
  m_EditorClassIdentifier: 
  _pro: 0
  DependencyWindow:
    LookupBtn:
      Content:
        m_Text: 
        m_Image: {fileID: 2800000, guid: 48b12aa9f8e67a5419b212fcae22754b, type: 3}
        m_Tooltip: Look for dependencies in Project
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Active:
          m_Background: {fileID: -7527060558648309217, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnNormal:
          m_Background: {fileID: -4454209017672384243, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 4047951448802137905, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 4
          m_Right: 4
          m_Top: 3
          m_Bottom: 3
        m_Padding:
          m_Left: 1
          m_Right: 1
          m_Top: 1
          m_Bottom: 3
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 0
        m_FontStyle: 0
        m_Alignment: 4
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 2
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 20
        m_FixedHeight: 20
        m_StretchWidth: 0
        m_StretchHeight: 0
    TabBreadcrumb0:
      m_Name: GUIEditor.BreadcrumbLeft
      m_Normal:
        m_Background: {fileID: -8730859949539617441, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Hover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Active:
        m_Background: {fileID: 3607839988326647129, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Focused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnNormal:
        m_Background: {fileID: -2909435724611740479, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnHover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnActive:
        m_Background: {fileID: 658594365626841568, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnFocused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Border:
        m_Left: 3
        m_Right: 10
        m_Top: 0
        m_Bottom: 0
      m_Margin:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Padding:
        m_Left: 5
        m_Right: 11
        m_Top: 2
        m_Bottom: 0
      m_Overflow:
        m_Left: 6
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Font: {fileID: 0}
      m_FontSize: 0
      m_FontStyle: 0
      m_Alignment: 3
      m_WordWrap: 0
      m_RichText: 1
      m_TextClipping: 1
      m_ImagePosition: 0
      m_ContentOffset: {x: 0, y: -1}
      m_FixedWidth: 0
      m_FixedHeight: 18
      m_StretchWidth: 0
      m_StretchHeight: 0
    TabBreadcrumb1:
      m_Name: GUIEditor.BreadcrumbMid
      m_Normal:
        m_Background: {fileID: 298390163510713244, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Hover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Active:
        m_Background: {fileID: -5694940394960273964, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Focused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnNormal:
        m_Background: {fileID: 4917697211602105510, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnHover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnActive:
        m_Background: {fileID: 2726902712204841013, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnFocused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Border:
        m_Left: 10
        m_Right: 10
        m_Top: 0
        m_Bottom: 0
      m_Margin:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Padding:
        m_Left: 10
        m_Right: 10
        m_Top: 2
        m_Bottom: 0
      m_Overflow:
        m_Left: 10
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Font: {fileID: 0}
      m_FontSize: 0
      m_FontStyle: 0
      m_Alignment: 3
      m_WordWrap: 0
      m_RichText: 1
      m_TextClipping: 1
      m_ImagePosition: 0
      m_ContentOffset: {x: 0, y: -1}
      m_FixedWidth: 0
      m_FixedHeight: 18
      m_StretchWidth: 0
      m_StretchHeight: 0
    RowMainAssetBtn:
      m_Name: button
      m_Normal:
        m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Hover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Active:
        m_Background: {fileID: -7527060558648309217, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Focused:
        m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 1, g: 1, b: 1, a: 1}
      m_OnNormal:
        m_Background: {fileID: -4454209017672384243, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 1, g: 1, b: 1, a: 1}
      m_OnHover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 1, g: 1, b: 1, a: 1}
      m_OnActive:
        m_Background: {fileID: 4047951448802137905, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 1, g: 1, b: 1, a: 1}
      m_OnFocused:
        m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Border:
        m_Left: 6
        m_Right: 6
        m_Top: 4
        m_Bottom: 4
      m_Margin:
        m_Left: 4
        m_Right: 4
        m_Top: 3
        m_Bottom: 3
      m_Padding:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Overflow:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 1
      m_Font: {fileID: 0}
      m_FontSize: 0
      m_FontStyle: 0
      m_Alignment: 3
      m_WordWrap: 0
      m_RichText: 0
      m_TextClipping: 1
      m_ImagePosition: 0
      m_ContentOffset: {x: 0, y: 0}
      m_FixedWidth: 0
      m_FixedHeight: 20
      m_StretchWidth: 1
      m_StretchHeight: 0
    Size: {x: 400, y: 800}
    FeedbackPopupBtn:
      Content:
        m_Text: 
        m_Image: {fileID: 2800000, guid: e8633cc5ea1d4984cb0ba9806d800bcc, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Active:
          m_Background: {fileID: -7527060558648309217, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnNormal:
          m_Background: {fileID: -4454209017672384243, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 4047951448802137905, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 0
          m_Bottom: 4
        m_Margin:
          m_Left: 4
          m_Right: 4
          m_Top: 0
          m_Bottom: 4
        m_Padding:
          m_Left: 2
          m_Right: 2
          m_Top: 2
          m_Bottom: 2
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 0
        m_FontStyle: 0
        m_Alignment: 3
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 24
        m_FixedHeight: 24
        m_StretchWidth: 0
        m_StretchHeight: 0
    RowLabel:
      m_Name: label
      m_Normal:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Hover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Active:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Focused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 0.01960784}
      m_OnNormal:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnHover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnActive:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 0.01960784}
      m_OnFocused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 0.01960784}
      m_Border:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Margin:
        m_Left: 4
        m_Right: 4
        m_Top: 2
        m_Bottom: 2
      m_Padding:
        m_Left: 2
        m_Right: 2
        m_Top: 1
        m_Bottom: 2
      m_Overflow:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Font: {fileID: 3459068928204737762, guid: 0000000000000000d000000000000000,
        type: 0}
      m_FontSize: 0
      m_FontStyle: 0
      m_Alignment: 3
      m_WordWrap: 0
      m_RichText: 0
      m_TextClipping: 1
      m_ImagePosition: 0
      m_ContentOffset: {x: 0, y: 0}
      m_FixedWidth: 0
      m_FixedHeight: 0
      m_StretchWidth: 1
      m_StretchHeight: 0
  Popup:
    HeaderLabel:
      Content:
        m_Text: Tell us your feedback
        m_Image: {fileID: 0}
        m_Tooltip: 
      Style:
        m_Name: TL Selection H1
        m_Normal:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Active:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
        m_Focused:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnNormal:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnActive:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
        m_OnFocused:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 79
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Margin:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Padding:
          m_Left: 10
          m_Right: 0
          m_Top: 10
          m_Bottom: 18
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Font: {fileID: 0}
        m_FontSize: 22
        m_FontStyle: 0
        m_Alignment: 3
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 0
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 15.07}
        m_FixedWidth: 0
        m_FixedHeight: 0
        m_StretchWidth: 1
        m_StretchHeight: 0
    ExperienceLabel:
      Content:
        m_Text: How was your experience?
        m_Image: {fileID: 0}
        m_Tooltip: 
      Style:
        m_Name: TL Selection H1
        m_Normal:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Active:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
        m_Focused:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnNormal:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnActive:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
        m_OnFocused:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 79
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Margin:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Padding:
          m_Left: 10
          m_Right: 0
          m_Top: 28
          m_Bottom: 18
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Font: {fileID: 0}
        m_FontSize: 15
        m_FontStyle: 0
        m_Alignment: 3
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 0
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 21.98}
        m_FixedWidth: 0
        m_FixedHeight: 0
        m_StretchWidth: 1
        m_StretchHeight: 0
    HappyBtn:
      Content:
        m_Text: 
        m_Image: {fileID: 2800000, guid: 52af95482509b79408296bc7fa8e3e7e, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_Active:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnNormal:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 4
          m_Right: 4
          m_Top: 3
          m_Bottom: 3
        m_Padding:
          m_Left: 3
          m_Right: 3
          m_Top: 2
          m_Bottom: 3
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 0
        m_FontStyle: 0
        m_Alignment: 4
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 32
        m_FixedHeight: 32
        m_StretchWidth: 0
        m_StretchHeight: 0
    UnhappyBtn:
      Content:
        m_Text: 
        m_Image: {fileID: 2800000, guid: 10a4358273a11fd4486ed40d1c527a0a, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_Active:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnNormal:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 4
          m_Right: 4
          m_Top: 3
          m_Bottom: 3
        m_Padding:
          m_Left: 3
          m_Right: 3
          m_Top: 2
          m_Bottom: 3
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 0
        m_FontStyle: 0
        m_Alignment: 4
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 32
        m_FixedHeight: 32
        m_StretchWidth: 0
        m_StretchHeight: 0
    IdeaToggle:
      Content:
        m_Text: 
        m_Image: {fileID: 2800000, guid: 463d8aa78c5e7a242a1402582395f3ef, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_Active:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnNormal:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 4
          m_Right: 4
          m_Top: 3
          m_Bottom: 3
        m_Padding:
          m_Left: 3
          m_Right: 3
          m_Top: 2
          m_Bottom: 3
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 0
        m_FontStyle: 0
        m_Alignment: 4
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 32
        m_FixedHeight: 32
        m_StretchWidth: 0
        m_StretchHeight: 0
    RateUsBtn:
      Content:
        m_Text: Rate us on Asset Store (Asset Usage Finder)
        m_Image: {fileID: 2800000, guid: dea1399a226d17b419fd77e9fed23845, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_Active:
          m_Background: {fileID: -7527060558648309217, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnNormal:
          m_Background: {fileID: -4454209017672384243, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 4047951448802137905, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 16
          m_Right: 4
          m_Top: 5
          m_Bottom: 3
        m_Padding:
          m_Left: 1
          m_Right: 6
          m_Top: 2
          m_Bottom: 3
        m_Overflow:
          m_Left: 4
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 15
        m_FontStyle: 0
        m_Alignment: 3
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 360
        m_FixedHeight: 29.05
        m_StretchWidth: 0
        m_StretchHeight: 0
    WindowSize: {x: 490, y: 364}
    WindowSmallerSize: {x: 302, y: 200}
    VerticalScope:
      m_Name: 
      m_Normal:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Hover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Active:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Focused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnNormal:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnHover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnActive:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnFocused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Border:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Margin:
        m_Left: 5
        m_Right: 0
        m_Top: 4
        m_Bottom: 0
      m_Padding:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Overflow:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Font: {fileID: 0}
      m_FontSize: 0
      m_FontStyle: 0
      m_Alignment: 0
      m_WordWrap: 0
      m_RichText: 1
      m_TextClipping: 0
      m_ImagePosition: 0
      m_ContentOffset: {x: 0, y: 0}
      m_FixedWidth: 0
      m_FixedHeight: 0
      m_StretchWidth: 1
      m_StretchHeight: 0
    ExperienceHorizontalScope:
      m_Name: 
      m_Normal:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Hover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Active:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Focused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnNormal:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnHover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnActive:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnFocused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Border:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Margin:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Padding:
        m_Left: 12
        m_Right: 0
        m_Top: 34
        m_Bottom: 0
      m_Overflow:
        m_Left: 0
        m_Right: 0
        m_Top: 0
        m_Bottom: 0
      m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
      m_FontSize: 0
      m_FontStyle: 0
      m_Alignment: 0
      m_WordWrap: 0
      m_RichText: 1
      m_TextClipping: 0
      m_ImagePosition: 0
      m_ContentOffset: {x: 0, y: 0}
      m_FixedWidth: 0
      m_FixedHeight: 0
      m_StretchWidth: 1
      m_StretchHeight: 0
    TweetArea:
      m_Name: WindowBackground
      m_Normal:
        m_Background: {fileID: 2575269581626704993, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Hover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Active:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Focused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnNormal:
        m_Background: {fileID: -4058938869769607538, guid: 0000000000000000d000000000000000,
          type: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnHover:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnActive:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_OnFocused:
        m_Background: {fileID: 0}
        m_ScaledBackgrounds: []
        m_TextColor: {r: 0, g: 0, b: 0, a: 1}
      m_Border:
        m_Left: 2
        m_Right: 17
        m_Top: 2
        m_Bottom: 17
      m_Margin:
        m_Left: 12
        m_Right: 0
        m_Top: 9
        m_Bottom: 0
      m_Padding:
        m_Left: 10
        m_Right: 10
        m_Top: 10
        m_Bottom: 10
      m_Overflow:
        m_Left: 1
        m_Right: 1
        m_Top: 1
        m_Bottom: 1
      m_Font: {fileID: 0}
      m_FontSize: 16
      m_FontStyle: 0
      m_Alignment: 0
      m_WordWrap: 1
      m_RichText: 1
      m_TextClipping: 1
      m_ImagePosition: 0
      m_ContentOffset: {x: 0, y: 0}
      m_FixedWidth: 456.01
      m_FixedHeight: 125.8
      m_StretchWidth: 1
      m_StretchHeight: 1
    TweetLabel:
      Content:
        m_Text: Tweet about Asset Usage Finder
        m_Image: {fileID: 0}
        m_Tooltip: 
      Style:
        m_Name: 
        m_Normal:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Active:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnNormal:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnActive:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_OnFocused:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Margin:
          m_Left: 14
          m_Right: 0
          m_Top: 10
          m_Bottom: 0
        m_Padding:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Font: {fileID: 0}
        m_FontSize: 15
        m_FontStyle: 0
        m_Alignment: 0
        m_WordWrap: 0
        m_RichText: 1
        m_TextClipping: 0
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 0
        m_FixedHeight: 0
        m_StretchWidth: 1
        m_StretchHeight: 0
    TweetBtn:
      Content:
        m_Text: 
        m_Image: {fileID: 2800000, guid: bae37753aef2a0347a6472a82d9be6ca, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_Active:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0.191, g: 0.191, b: 0.191, a: 1}
        m_Focused:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnNormal:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnFocused:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 11
          m_Right: 18
          m_Top: 2
          m_Bottom: 3
        m_Padding:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 0
        m_Overflow:
          m_Left: 1
          m_Right: 1
          m_Top: -3
          m_Bottom: -1
        m_Font: {fileID: 0}
        m_FontSize: 0
        m_FontStyle: 0
        m_Alignment: 4
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 117.38
        m_FixedHeight: 44
        m_StretchWidth: 0
        m_StretchHeight: 0
    ForumBtn:
      Content:
        m_Text: ' Post on forum'
        m_Image: {fileID: 2800000, guid: c91c6ae44f462284ab4cdec30c5666bc, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_Active:
          m_Background: {fileID: -7527060558648309217, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnNormal:
          m_Background: {fileID: -4454209017672384243, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 4047951448802137905, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 12
          m_Right: 4
          m_Top: 5
          m_Bottom: 3
        m_Padding:
          m_Left: 6
          m_Right: 6
          m_Top: 2
          m_Bottom: 6
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 15
        m_FontStyle: 0
        m_Alignment: 4
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 260
        m_FixedHeight: 30
        m_StretchWidth: 0
        m_StretchHeight: 0
    EmailBtn:
      Content:
        m_Text: <EMAIL>
        m_Image: {fileID: 2800000, guid: 2e15029019ded034e8701272937cfb00, type: 3}
        m_Tooltip: 
      Style:
        m_Name: button
        m_Normal:
          m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Hover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_Active:
          m_Background: {fileID: -7527060558648309217, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Focused:
          m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnNormal:
          m_Background: {fileID: -4454209017672384243, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnHover:
          m_Background: {fileID: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnActive:
          m_Background: {fileID: 4047951448802137905, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 1, g: 1, b: 1, a: 1}
        m_OnFocused:
          m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000,
            type: 0}
          m_ScaledBackgrounds: []
          m_TextColor: {r: 0, g: 0, b: 0, a: 1}
        m_Border:
          m_Left: 6
          m_Right: 6
          m_Top: 4
          m_Bottom: 4
        m_Margin:
          m_Left: 12
          m_Right: 4
          m_Top: 5
          m_Bottom: 3
        m_Padding:
          m_Left: 6
          m_Right: 6
          m_Top: 2
          m_Bottom: 6
        m_Overflow:
          m_Left: 0
          m_Right: 0
          m_Top: 0
          m_Bottom: 1
        m_Font: {fileID: 0}
        m_FontSize: 15
        m_FontStyle: 0
        m_Alignment: 4
        m_WordWrap: 0
        m_RichText: 0
        m_TextClipping: 1
        m_ImagePosition: 0
        m_ContentOffset: {x: 0, y: 0}
        m_FixedWidth: 260
        m_FixedHeight: 30
        m_StretchWidth: 0
        m_StretchHeight: 0
